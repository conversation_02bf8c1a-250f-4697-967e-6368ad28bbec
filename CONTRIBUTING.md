# Contributing to ORBIS Suivi Travaux

We welcome contributions to ORBIS Suivi Travaux! This document provides guidelines for contributing to the project.

## Getting Started

1. Fork the repository
2. Clone your fork: `git clone https://github.com/YOUR_USERNAME/orbis-suivi-travaux.git`
3. Create a feature branch: `git checkout -b feature/your-feature-name`
4. Make your changes
5. Test your changes
6. Commit your changes: `git commit -am 'Add some feature'`
7. Push to the branch: `git push origin feature/your-feature-name`
8. Submit a pull request

## Development Setup

Please refer to the [README.md](README.md) for detailed setup instructions.

## Code Style

- Follow PEP 8 for Python code
- Use meaningful variable and function names
- Add docstrings to functions and classes
- Keep functions small and focused
- Write comprehensive tests

## Testing

- Write tests for new features
- Ensure all existing tests pass
- Aim for high test coverage
- Run tests with: `pytest`

## Pull Request Process

1. Ensure your code follows the style guidelines
2. Update documentation if needed
3. Add tests for new functionality
4. Ensure all tests pass
5. Update the README.md if needed
6. Submit your pull request with a clear description

## Reporting Issues

When reporting issues, please include:
- Clear description of the problem
- Steps to reproduce
- Expected vs actual behavior
- System information (OS, Python version, etc.)
- Error messages or logs

## Feature Requests

For feature requests, please:
- Check if the feature already exists
- Describe the use case
- Explain why it would be useful
- Consider implementation complexity

## Code of Conduct

Please be respectful and constructive in all interactions.

Thank you for contributing to ORBIS Suivi Travaux!