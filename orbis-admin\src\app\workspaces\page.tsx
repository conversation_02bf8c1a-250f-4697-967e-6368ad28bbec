'use client'

import { useState, useEffect } from 'react'
import { Building2, Plus, Search, Eye, MapPin, Phone, Mail, Globe, Edit, Trash2, Power } from 'lucide-react'
import { useToast } from '@/components/ui/Toast'
import { FastWorkspaceService } from '@/lib/fast-auth'
import { useRouter } from 'next/navigation'
import AuthGuard from '@/components/AuthGuard'

interface Workspace {
  id: number
  name: string
  code: string
  description?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  is_active: boolean
  user_count: number
}

function WorkspacesPageContent() {
  const { success, error: showError } = useToast()
  const [workspaces, setWorkspaces] = useState<Workspace[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [actionLoading, setActionLoading] = useState<number | null>(null)
  const router = useRouter()

  useEffect(() => {
    if (workspaces.length === 0) {
      loadWorkspaces()
    }
  }, [])

  const loadWorkspaces = async () => {
    try {
      setLoading(true)
      console.log('🔄 Chargement des espaces de travail...')
      const workspacesData = await FastWorkspaceService.getWorkspaces()
      console.log('✅ Espaces de travail chargés:', workspacesData.length)
      setWorkspaces(workspacesData)
    } catch (error) {
      console.error('❌ Erreur chargement espaces de travail:', error)
      showError('Erreur', 'Impossible de charger les espaces de travail')
    } finally {
      setLoading(false)
    }
  }

  const handleToggleActive = async (workspace: Workspace) => {
    try {
      setActionLoading(workspace.id)
      console.log(`🔄 ${workspace.is_active ? 'Désactivation' : 'Activation'} de l'espace de travail ${workspace.id}...`)
      
      await FastWorkspaceService.updateWorkspace(workspace.id, {
        is_active: !workspace.is_active
      })
      
      success('Succès', `Espace de travail ${workspace.is_active ? 'désactivé' : 'activé'} avec succès`)
      await loadWorkspaces()
    } catch (error) {
      console.error('❌ Erreur toggle active:', error)
      showError('Erreur', `Impossible de ${workspace.is_active ? 'désactiver' : 'activer'} l'espace de travail`)
    } finally {
      setActionLoading(null)
    }
  }

  const handleDelete = async (workspace: Workspace) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer l'espace de travail "${workspace.name}" ?`)) {
      return
    }

    try {
      setActionLoading(workspace.id)
      console.log(`🔄 Suppression de l'espace de travail ${workspace.id}...`)
      
      await FastWorkspaceService.deleteWorkspace(workspace.id)
      
      success('Succès', 'Espace de travail supprimé avec succès')
      await loadWorkspaces()
    } catch (error) {
      console.error('❌ Erreur suppression:', error)
      showError('Erreur', 'Impossible de supprimer l\'espace de travail')
    } finally {
      setActionLoading(null)
    }
  }

  const filteredWorkspaces = workspaces.filter(workspace =>
    workspace.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workspace.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workspace.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Building2 className="h-8 w-8 text-blue-600" />
            Espaces de travail
          </h1>
          <p className="text-gray-600 mt-2">
            Gérez les espaces de travail de votre organisation
          </p>
        </div>
        <button
          onClick={() => router.push('/workspaces/new')}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
        >
          <Plus className="h-4 w-4" />
          Nouvel espace de travail
        </button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <input
          type="text"
          placeholder="Rechercher un espace de travail..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total espaces</p>
              <p className="text-2xl font-bold text-gray-900">{workspaces.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Power className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Actifs</p>
              <p className="text-2xl font-bold text-gray-900">
                {workspaces.filter(w => w.is_active).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Building2 className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Utilisateurs total</p>
              <p className="text-2xl font-bold text-gray-900">
                {workspaces.reduce((sum, w) => sum + w.user_count, 0)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Workspaces List */}
      <div className="bg-white shadow-sm rounded-lg border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Liste des espaces de travail ({filteredWorkspaces.length})
          </h2>
        </div>
        
        {filteredWorkspaces.length === 0 ? (
          <div className="p-8 text-center">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun espace de travail trouvé</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'Aucun espace de travail ne correspond à votre recherche.' : 'Commencez par créer votre premier espace de travail.'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => router.push('/workspaces/new')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Créer un espace de travail
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredWorkspaces.map((workspace) => (
              <div key={workspace.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{workspace.name}</h3>
                      <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {workspace.code}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        workspace.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {workspace.is_active ? 'Actif' : 'Inactif'}
                      </span>
                    </div>
                    
                    {workspace.description && (
                      <p className="text-gray-600 mb-2">{workspace.description}</p>
                    )}
                    
                    <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                      {workspace.address && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {workspace.address}
                        </div>
                      )}
                      {workspace.phone && (
                        <div className="flex items-center gap-1">
                          <Phone className="h-4 w-4" />
                          {workspace.phone}
                        </div>
                      )}
                      {workspace.email && (
                        <div className="flex items-center gap-1">
                          <Mail className="h-4 w-4" />
                          {workspace.email}
                        </div>
                      )}
                      {workspace.website && (
                        <div className="flex items-center gap-1">
                          <Globe className="h-4 w-4" />
                          {workspace.website}
                        </div>
                      )}
                    </div>
                    
                    <div className="mt-2 text-sm text-gray-500">
                      {workspace.user_count} utilisateur{workspace.user_count > 1 ? 's' : ''}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <button
                      onClick={() => router.push(`/workspaces/${workspace.id}`)}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="Voir les détails"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    
                    <button
                      onClick={() => router.push(`/workspaces/${workspace.id}/edit`)}
                      className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      title="Modifier"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    
                    <button
                      onClick={() => handleToggleActive(workspace)}
                      disabled={actionLoading === workspace.id}
                      className={`p-2 rounded-lg transition-colors ${
                        workspace.is_active
                          ? 'text-gray-400 hover:text-orange-600 hover:bg-orange-50'
                          : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                      }`}
                      title={workspace.is_active ? 'Désactiver' : 'Activer'}
                    >
                      {actionLoading === workspace.id ? (
                        <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                      ) : (
                        <Power className="h-4 w-4" />
                      )}
                    </button>
                    
                    <button
                      onClick={() => handleDelete(workspace)}
                      disabled={actionLoading === workspace.id}
                      className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Supprimer"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default function WorkspacesPage() {
  return (
    <AuthGuard>
      <WorkspacesPageContent />
    </AuthGuard>
  )
}
