#!/usr/bin/env node
/**
 * Script pour tester les composants frontend Workspace
 */

const fs = require('fs');
const path = require('path');

function testFileExists(filePath, description) {
  const fullPath = path.join(__dirname, filePath);
  if (fs.existsSync(fullPath)) {
    console.log(`   ✅ ${description}: ${filePath}`);
    return true;
  } else {
    console.log(`   ❌ ${description}: ${filePath} - MANQUANT`);
    return false;
  }
}

function testFileContent(filePath, searchTerms, description) {
  const fullPath = path.join(__dirname, filePath);
  if (!fs.existsSync(fullPath)) {
    console.log(`   ❌ ${description}: ${filePath} - FICHIER MANQUANT`);
    return false;
  }

  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    const missingTerms = searchTerms.filter(term => !content.includes(term));
    
    if (missingTerms.length === 0) {
      console.log(`   ✅ ${description}: Tous les termes trouvés`);
      return true;
    } else {
      console.log(`   ❌ ${description}: Termes manquants - ${missingTerms.join(', ')}`);
      return false;
    }
  } catch (error) {
    console.log(`   ❌ ${description}: Erreur lecture - ${error.message}`);
    return false;
  }
}

function testWorkspaceFiles() {
  console.log("🔍 Test des fichiers Workspace...");
  
  const files = [
    { path: 'src/app/workspaces/page.tsx', desc: 'Page principale workspaces' },
    { path: 'src/app/workspaces/new/page.tsx', desc: 'Page création workspace' },
    { path: 'src/components/WorkspaceForm.tsx', desc: 'Composant formulaire workspace' }
  ];

  let results = [];
  for (const file of files) {
    results.push(testFileExists(file.path, file.desc));
  }

  return results.every(r => r);
}

function testServiceUpdates() {
  console.log("\n🔧 Test des mises à jour de service...");
  
  const serviceTests = [
    {
      path: 'src/lib/fast-auth.ts',
      terms: ['FastWorkspaceService', 'getWorkspaces', 'createWorkspace', 'updateWorkspace', 'deleteWorkspace'],
      desc: 'Service FastWorkspaceService'
    }
  ];

  let results = [];
  for (const test of serviceTests) {
    results.push(testFileContent(test.path, test.terms, test.desc));
  }

  return results.every(r => r);
}

function testNavigationUpdates() {
  console.log("\n🧭 Test des mises à jour de navigation...");
  
  const navTests = [
    {
      path: 'src/components/layout/Sidebar.tsx',
      terms: ['Espaces de travail', '/workspaces'],
      desc: 'Navigation sidebar'
    }
  ];

  let results = [];
  for (const test of navTests) {
    results.push(testFileContent(test.path, test.terms, test.desc));
  }

  return results.every(r => r);
}

function testComponentContent() {
  console.log("\n📦 Test du contenu des composants...");
  
  const componentTests = [
    {
      path: 'src/app/workspaces/page.tsx',
      terms: ['FastWorkspaceService', 'getWorkspaces', 'Espaces de travail', 'workspace'],
      desc: 'Page workspaces - contenu'
    },
    {
      path: 'src/components/WorkspaceForm.tsx',
      terms: ['interface Workspace', 'WorkspaceFormProps', 'espace de travail'],
      desc: 'WorkspaceForm - contenu'
    },
    {
      path: 'src/app/workspaces/new/page.tsx',
      terms: ['FastWorkspaceService.createWorkspace', 'WorkspaceForm', 'Nouvel espace de travail'],
      desc: 'Page création - contenu'
    }
  ];

  let results = [];
  for (const test of componentTests) {
    results.push(testFileContent(test.path, test.terms, test.desc));
  }

  return results.every(r => r);
}

function testImportStatements() {
  console.log("\n📥 Test des imports...");
  
  const importTests = [
    {
      path: 'src/app/workspaces/page.tsx',
      terms: ['FastWorkspaceService', 'useToast', 'AuthGuard'],
      desc: 'Imports page workspaces'
    },
    {
      path: 'src/app/workspaces/new/page.tsx',
      terms: ['FastWorkspaceService', 'WorkspaceForm', 'useToast'],
      desc: 'Imports page création'
    }
  ];

  let results = [];
  for (const test of importTests) {
    results.push(testFileContent(test.path, test.terms, test.desc));
  }

  return results.every(r => r);
}

function testCompatibilityFiles() {
  console.log("\n🔄 Test des fichiers de compatibilité...");
  
  const compatibilityFiles = [
    { path: 'src/app/companies/page.tsx', desc: 'Page companies (compatibilité)' },
    { path: 'src/components/CompanyForm.tsx', desc: 'CompanyForm (compatibilité)' }
  ];

  let results = [];
  for (const file of compatibilityFiles) {
    results.push(testFileExists(file.path, file.desc));
  }

  return results.every(r => r);
}

function main() {
  console.log("🧪 TEST DES COMPOSANTS FRONTEND WORKSPACE");
  console.log("="*50);

  const tests = [
    { name: "Fichiers Workspace", fn: testWorkspaceFiles },
    { name: "Services", fn: testServiceUpdates },
    { name: "Navigation", fn: testNavigationUpdates },
    { name: "Contenu des composants", fn: testComponentContent },
    { name: "Imports", fn: testImportStatements },
    { name: "Compatibilité", fn: testCompatibilityFiles }
  ];

  const results = [];
  for (const test of tests) {
    try {
      const result = test.fn();
      results.push(result);
    } catch (error) {
      console.log(`   💥 Erreur inattendue dans ${test.name}: ${error.message}`);
      results.push(false);
    }
  }

  // Résumé
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`\n📊 RÉSUMÉ: ${passed}/${total} tests réussis`);

  if (passed === total) {
    console.log("🎉 Tous les tests sont passés!");
    return true;
  } else {
    console.log("❌ Certains tests ont échoué");
    return false;
  }
}

if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}
