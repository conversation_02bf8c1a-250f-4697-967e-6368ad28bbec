# 📋 GUIDE COMPLET : Ajout d'un Nouvel Objet dans ORBIS

Basé sur l'expérience de l'implémentation des Stakeholders, voici le guide complet pour ajouter un nouvel objet et ses liaisons.

## 🚨 RÈGLES OBLIGATOIRES POUR QUE TOUT FONCTIONNE

### **1. COHÉRENCE D'AUTHENTIFICATION**
- ✅ **TOUJOURS** utiliser `FastAuthService.getToken()` dans les services API frontend
- ❌ **JAMAIS** utiliser `localStorage.getItem('token')` directement
- ✅ Suivre le pattern des services existants (lots, stakeholders, tcompanies)

### **2. ARCHITECTURE ASYNCHRONE COHÉRENTE**
- ✅ **Backend** : Utiliser `AsyncSession` avec `deps.get_db`
- ✅ **Endpoints** : Fonctions `async def` avec `await`
- ✅ **Requêtes SQLAlchemy** : `await db.execute()` et `result.scalars()`
- ❌ **JAMAIS** mélanger sessions synchrones/asynchrones

### **3. GESTION DES ESPACES DE TRAVAIL**
- ✅ **TOUJOURS** récupérer le `workspace_id` de l'utilisateur
- ✅ Filtrer les données par workspace pour l'isolation multi-tenant
- ✅ Utiliser `UserWorkspace` pour récupérer le workspace de l'utilisateur

---

## 📝 ÉTAPES POUR AJOUTER UN NOUVEL OBJET

### **PHASE 1 : BACKEND (FastAPI)**

#### **1.1 Modèle de Données**
```python
# fastapi_template/app/models/mon_objet.py
from sqlalchemy import Column, Integer, String, ForeignKey, Boolean, DateTime
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class MonObjet(Base):
    __tablename__ = "mon_objet"
    
    id = Column(Integer, primary_key=True, index=True)
    nom = Column(String, nullable=False)
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False)
    created_by = Column(Integer, ForeignKey("users.id"))
    is_active = Column(Boolean, default=True)
    
    # Relations
    workspace = relationship("Workspace", back_populates="mon_objets")
    creator = relationship("User")
```

#### **1.2 Schémas Pydantic**
```python
# fastapi_template/app/schemas/mon_objet.py
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class MonObjetBase(BaseModel):
    nom: str
    description: Optional[str] = None

class MonObjetCreate(MonObjetBase):
    pass

class MonObjetUpdate(MonObjetBase):
    nom: Optional[str] = None

class MonObjetResponse(MonObjetBase):
    id: int
    workspace_id: int
    created_by: Optional[int]
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True
```

#### **1.3 Endpoints API (OBLIGATOIRE : Architecture Asynchrone)**
```python
# fastapi_template/app/api/api_v1/endpoints/mon_objet.py
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload

from app.api import deps
from app.middleware.auth_sync_middleware import require_auth
from app.models.mon_objet import MonObjet
from app.schemas.mon_objet import MonObjetCreate, MonObjetResponse

router = APIRouter()

@router.get("/mon-objets", response_model=List[MonObjetResponse])
async def get_mon_objets(
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        
        # OBLIGATOIRE : Récupérer le workspace
        from app.models.workspace import UserWorkspace
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        workspace_id = user_workspace.workspace_id
        
        # Requête avec filtrage workspace
        result = await db.execute(
            select(MonObjet).where(
                and_(
                    MonObjet.workspace_id == workspace_id,
                    MonObjet.is_active == True
                )
            )
        )
        objets = result.scalars().all()
        return objets
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/mon-objets", response_model=MonObjetResponse, status_code=status.HTTP_201_CREATED)
async def create_mon_objet(
    objet_data: MonObjetCreate,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth)
):
    try:
        user_id = current_user.get("id") or current_user.get("user_id")
        
        # Récupérer le workspace
        from app.models.workspace import UserWorkspace
        user_workspace_result = await db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.is_active == True
                )
            ).limit(1)
        )
        user_workspace = user_workspace_result.scalar_one_or_none()
        workspace_id = user_workspace.workspace_id

        # Créer l'objet
        db_objet = MonObjet(
            **objet_data.dict(),
            workspace_id=workspace_id,
            created_by=user_id
        )

        db.add(db_objet)
        await db.commit()
        
        # Récupérer l'objet créé avec ses relations
        result = await db.execute(
            select(MonObjet).options(
                selectinload(MonObjet.workspace),
                selectinload(MonObjet.creator)
            ).where(MonObjet.id == db_objet.id)
        )
        created_objet = result.scalar_one()

        return created_objet

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
```

#### **1.4 Enregistrement dans l'API**
```python
# fastapi_template/app/api/api_v1/api.py
from app.api.api_v1.endpoints import mon_objet

api_router.include_router(mon_objet.router, prefix="/mon-objets", tags=["mon-objets"])
```

### **PHASE 2 : FRONTEND (Next.js + TypeScript)**

#### **2.1 Types TypeScript**
```typescript
// orbis-frontend/src/types/mon-objet.ts
export interface MonObjet {
  id: number;
  nom: string;
  description?: string;
  workspace_id: number;
  created_by?: number;
  is_active: boolean;
  created_at: string;
}

export interface MonObjetCreate {
  nom: string;
  description?: string;
}

export interface MonObjetUpdate {
  nom?: string;
  description?: string;
}
```

#### **2.2 Service API (OBLIGATOIRE : FastAuthService)**
```typescript
// orbis-frontend/src/lib/api/mon-objet.ts
import { FastAuthService } from '@/lib/auth';
import { MonObjet, MonObjetCreate, MonObjetUpdate } from '@/types/mon-objet';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

class MonObjetApiService {
  private getAuthHeaders() {
    const token = FastAuthService.getToken(); // OBLIGATOIRE
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  async getAll(): Promise<MonObjet[]> {
    const response = await fetch(`${API_BASE_URL}/api/v1/mon-objets`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Erreur lors de la récupération: ${response.statusText}`);
    }

    return response.json();
  }

  async getById(id: number): Promise<MonObjet> {
    const response = await fetch(`${API_BASE_URL}/api/v1/mon-objets/${id}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Erreur lors de la récupération: ${response.statusText}`);
    }

    return response.json();
  }

  async create(data: MonObjetCreate): Promise<MonObjet> {
    const response = await fetch(`${API_BASE_URL}/api/v1/mon-objets`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Erreur lors de la création: ${response.statusText}`);
    }

    return response.json();
  }

  async update(id: number, data: MonObjetUpdate): Promise<MonObjet> {
    const response = await fetch(`${API_BASE_URL}/api/v1/mon-objets/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Erreur lors de la mise à jour: ${response.statusText}`);
    }

    return response.json();
  }

  async delete(id: number): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/api/v1/mon-objets/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Erreur lors de la suppression: ${response.statusText}`);
    }
  }
}

export const monObjetApiService = new MonObjetApiService();
```

#### **2.3 Composants React**
```typescript
// orbis-frontend/src/components/mon-objet/MonObjetList.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { MonObjet } from '@/types/mon-objet';
import { monObjetApiService } from '@/lib/api/mon-objet';

export function MonObjetList() {
  const [objets, setObjets] = useState<MonObjet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadObjets();
  }, []);

  const loadObjets = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await monObjetApiService.getAll();
      setObjets(data);
    } catch (err: any) {
      setError(err.message || 'Erreur lors du chargement');
      console.error('Error loading objets:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
      return;
    }

    try {
      await monObjetApiService.delete(id);
      await loadObjets(); // Recharger la liste
    } catch (err: any) {
      alert('Erreur lors de la suppression : ' + err.message);
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-red-600">
          <h3 className="text-lg font-medium mb-2">Erreur</h3>
          <p>{error}</p>
          <button
            onClick={loadObjets}
            className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">
          Mes Objets ({objets.length})
        </h3>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          Ajouter
        </button>
      </div>

      <div className="p-6">
        {objets.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">Aucun objet trouvé</p>
          </div>
        ) : (
          <div className="space-y-4">
            {objets.map((objet) => (
              <div
                key={objet.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="text-lg font-medium text-gray-900">
                      {objet.nom}
                    </h4>
                    {objet.description && (
                      <p className="text-sm text-gray-600 mt-1">
                        {objet.description}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={() => handleDelete(objet.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                    title="Supprimer"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
```

---

## 🔗 GESTION DES LIAISONS ENTRE OBJETS

### **Exemple : Liaison Lot ↔ Stakeholder ↔ TCompany**

#### **1. Table de Liaison (Many-to-Many)**
```python
# Modèle Stakeholder = table de liaison
class Stakeholder(Base):
    __tablename__ = "stakeholders"
    
    id = Column(Integer, primary_key=True)
    lot_id = Column(Integer, ForeignKey("lots.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("tcompanies.id"), nullable=False)
    role = Column(String)  # Champ spécifique à la liaison
    
    # Relations
    lot = relationship("Lot", back_populates="stakeholders")
    company = relationship("TCompany", back_populates="stakeholders")
```

#### **2. Relations dans les Modèles Principaux**
```python
# Dans Lot
class Lot(Base):
    stakeholders = relationship("Stakeholder", back_populates="lot")

# Dans TCompany  
class TCompany(Base):
    stakeholders = relationship("Stakeholder", back_populates="company")
```

#### **3. Endpoints avec Relations**
```python
@router.get("/lots/{lot_id}/stakeholders")
async def get_lot_stakeholders(lot_id: int, db: AsyncSession = Depends(deps.get_db)):
    result = await db.execute(
        select(Stakeholder).options(
            selectinload(Stakeholder.company),  # OBLIGATOIRE pour éviter N+1
            selectinload(Stakeholder.lot)
        ).where(Stakeholder.lot_id == lot_id)
    )
    return result.scalars().all()
```

#### **4. Frontend avec Relations**
```typescript
// Types avec relations
export interface Stakeholder {
  id: number;
  lot_id: number;
  company_id: number;
  role?: string;
  lot?: Lot;
  company?: TCompany;
}

// Service API pour récupérer avec relations
async getByLot(lotId: number): Promise<Stakeholder[]> {
  const response = await fetch(`${API_BASE_URL}/api/v1/lot/${lotId}/stakeholders`, {
    method: 'GET',
    headers: this.getAuthHeaders()
  });
  return response.json();
}
```

---

## ⚠️ PIÈGES À ÉVITER

### **1. Erreurs d'Authentification**
- ❌ `localStorage.getItem('token')` → ✅ `FastAuthService.getToken()`
- ❌ Headers manquants → ✅ `getAuthHeaders()` systématique

### **2. Erreurs de Session**
- ❌ `Session` synchrone → ✅ `AsyncSession` asynchrone
- ❌ `db.query()` → ✅ `await db.execute(select())`

### **3. Erreurs de Workspace**
- ❌ Pas de filtrage → ✅ Toujours filtrer par `workspace_id`
- ❌ Données globales → ✅ Isolation multi-tenant

### **4. Erreurs de Relations**
- ❌ Requêtes N+1 → ✅ `selectinload()` pour charger les relations
- ❌ Relations manquantes → ✅ Définir `back_populates` des deux côtés

### **5. Erreurs de Nommage**
- ❌ Exports incohérents → ✅ Pattern `monObjetApiService`
- ❌ Noms de fichiers incohérents → ✅ Suivre la convention

---

## ✅ CHECKLIST FINALE

### **Backend**
- [ ] Modèle avec `workspace_id` et `created_by`
- [ ] Relations SQLAlchemy avec `back_populates`
- [ ] Schémas Pydantic complets (Create, Update, Response)
- [ ] Endpoints async avec `AsyncSession`
- [ ] Gestion workspace dans tous les endpoints
- [ ] Utilisation de `selectinload()` pour les relations
- [ ] Enregistrement dans `api.py`

### **Frontend**
- [ ] Types TypeScript complets
- [ ] Service API avec `FastAuthService.getToken()`
- [ ] Classe de service avec `getAuthHeaders()`
- [ ] Composants React avec gestion d'erreurs
- [ ] États de loading et error
- [ ] Gestion des relations dans les types

### **Tests**
- [ ] Test d'authentification
- [ ] Test de création/lecture/mise à jour/suppression
- [ ] Test des relations
- [ ] Test de l'isolation workspace

**Cette approche garantit la cohérence et évite les erreurs d'authentification et d'architecture rencontrées lors de l'implémentation des Stakeholders.**
