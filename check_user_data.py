#!/usr/bin/env python3
"""
Script pour vérifier les données utilisateur dans la base
"""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def check_user_data():
    """Vérifier les données utilisateur"""
    
    DATABASE_URL = os.getenv("DATABASE_URL")
    if not DATABASE_URL:
        print("❌ DATABASE_URL non trouvé dans .env")
        return False
    
    print("🔍 Vérification des données utilisateur")
    print(f"📍 Base de données: {DATABASE_URL.split('@')[1] if '@' in DATABASE_URL else 'localhost'}")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        print("✅ Connexion à la base de données réussie")
        
        # 1. Vérifier les utilisateurs
        print("\n1. 👥 Utilisateurs dans la base:")
        users = await conn.fetch("SELECT id, email, role, is_superuser, is_active FROM users")
        
        for user in users:
            print(f"  - ID: {user['id']}")
            print(f"    Email: {user['email']}")
            print(f"    Role: {user['role']} (type: {type(user['role'])})")
            print(f"    Is Superuser: {user['is_superuser']}")
            print(f"    Is Active: {user['is_active']}")
            print()
        
        # 2. Vérifier l'enum
        print("2. 📝 Valeurs de l'enum userrole:")
        enum_values = await conn.fetch("""
            SELECT enumlabel 
            FROM pg_enum 
            WHERE enumtypid = (
                SELECT oid 
                FROM pg_type 
                WHERE typname = 'userrole'
            )
            ORDER BY enumsortorder
        """)
        
        for enum_val in enum_values:
            print(f"  - '{enum_val['enumlabel']}'")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(check_user_data())
