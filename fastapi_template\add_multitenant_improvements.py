#!/usr/bin/env python3
"""
Script pour ajouter les améliorations multi-tenant à la base de données
"""

import asyncio
import asyncpg
from datetime import datetime
import sys
from app.core.config import settings

DATABASE_URL = settings.DATABASE_URL


async def add_multitenant_improvements():
    """Ajouter les améliorations multi-tenant"""
    print("🔧 Ajout des améliorations multi-tenant...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Ajouter les nouvelles colonnes à user_companies si elles n'existent pas
        print("   📊 Mise à jour de la table user_companies...")
        
        # Vérifier et ajouter la colonne role
        role_exists = await conn.fetchval("""
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_name = 'user_companies' AND column_name = 'role'
        """)
        
        if role_exists == 0:
            await conn.execute("""
                ALTER TABLE user_companies 
                ADD COLUMN role VARCHAR(50) DEFAULT 'user'
            """)
            print("   ✅ Colonne 'role' ajoutée")
        
        # Vérifier et ajouter la colonne is_active
        active_exists = await conn.fetchval("""
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_name = 'user_companies' AND column_name = 'is_active'
        """)
        
        if active_exists == 0:
            await conn.execute("""
                ALTER TABLE user_companies 
                ADD COLUMN is_active BOOLEAN DEFAULT true
            """)
            print("   ✅ Colonne 'is_active' ajoutée")
        
        # Vérifier et ajouter la colonne invited_by
        invited_by_exists = await conn.fetchval("""
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_name = 'user_companies' AND column_name = 'invited_by'
        """)
        
        if invited_by_exists == 0:
            await conn.execute("""
                ALTER TABLE user_companies 
                ADD COLUMN invited_by INTEGER REFERENCES users(id)
            """)
            print("   ✅ Colonne 'invited_by' ajoutée")
        
        # Vérifier et ajouter la colonne invited_at
        invited_at_exists = await conn.fetchval("""
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_name = 'user_companies' AND column_name = 'invited_at'
        """)
        
        if invited_at_exists == 0:
            await conn.execute("""
                ALTER TABLE user_companies 
                ADD COLUMN invited_at TIMESTAMP
            """)
            print("   ✅ Colonne 'invited_at' ajoutée")
        
        # Vérifier et ajouter la colonne joined_at
        joined_at_exists = await conn.fetchval("""
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_name = 'user_companies' AND column_name = 'joined_at'
        """)
        
        if joined_at_exists == 0:
            await conn.execute("""
                ALTER TABLE user_companies 
                ADD COLUMN joined_at TIMESTAMP
            """)
            print("   ✅ Colonne 'joined_at' ajoutée")
        
        # Vérifier et ajouter la colonne updated_at
        updated_at_exists = await conn.fetchval("""
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_name = 'user_companies' AND column_name = 'updated_at'
        """)
        
        if updated_at_exists == 0:
            await conn.execute("""
                ALTER TABLE user_companies 
                ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            """)
            print("   ✅ Colonne 'updated_at' ajoutée")
        
        # 2. Créer la table audit_logs
        print("   📋 Création de la table audit_logs...")
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS audit_logs (
                id SERIAL PRIMARY KEY,
                company_id INTEGER REFERENCES companies(id) NOT NULL,
                user_id INTEGER REFERENCES users(id),
                action VARCHAR(50) NOT NULL,
                resource_type VARCHAR(100) NOT NULL,
                resource_id VARCHAR(100),
                details JSONB,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Créer des index pour les performances
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_audit_logs_company_id ON audit_logs(company_id);
        """)
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
        """)
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
        """)
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
        """)
        
        print("   ✅ Table audit_logs créée avec index")
        
        # 3. Créer la table company_invitations
        print("   📧 Création de la table company_invitations...")
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS company_invitations (
                id SERIAL PRIMARY KEY,
                company_id INTEGER REFERENCES companies(id) NOT NULL,
                email VARCHAR(255) NOT NULL,
                role VARCHAR(50) DEFAULT 'user',
                invited_by INTEGER REFERENCES users(id) NOT NULL,
                invitation_token VARCHAR(255) UNIQUE NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                accepted_at TIMESTAMP,
                rejected_at TIMESTAMP,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Créer des index
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_company_invitations_email ON company_invitations(email);
        """)
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_company_invitations_token ON company_invitations(invitation_token);
        """)
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_company_invitations_company_id ON company_invitations(company_id);
        """)
        
        print("   ✅ Table company_invitations créée avec index")
        
        # 4. Mettre à jour les données existantes
        print("   🔄 Mise à jour des données existantes...")
        
        # Mettre à jour les rôles existants (admin pour le premier utilisateur de chaque entreprise)
        await conn.execute("""
            UPDATE user_companies 
            SET role = 'admin', joined_at = created_at
            WHERE id IN (
                SELECT DISTINCT ON (company_id) id 
                FROM user_companies 
                ORDER BY company_id, created_at ASC
            )
        """)
        
        # Mettre à jour les autres utilisateurs
        await conn.execute("""
            UPDATE user_companies 
            SET joined_at = created_at
            WHERE joined_at IS NULL
        """)
        
        print("   ✅ Données existantes mises à jour")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Améliorations Multi-Tenant")
    print("="*50)
    
    if await add_multitenant_improvements():
        print("\n✅ Améliorations multi-tenant appliquées avec succès!")
        print("\n📋 Nouvelles fonctionnalités:")
        print("   - Rôles par entreprise (admin, manager, user, viewer)")
        print("   - Audit trail complet")
        print("   - Système d'invitations")
        print("   - Middleware de contexte tenant")
        print("   - Sécurité renforcée")
        
        print("\n🔧 Prochaines étapes:")
        print("   1. Redémarrer le serveur backend")
        print("   2. Tester les nouvelles fonctionnalités")
        print("   3. Créer l'Admin Portal")
        
        return True
    else:
        print("\n❌ Échec de l'application des améliorations")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
