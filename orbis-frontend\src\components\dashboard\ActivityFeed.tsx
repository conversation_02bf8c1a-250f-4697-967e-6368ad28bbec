import React from 'react'
import { Card } from '@/components/ui/Card'
import Link from 'next/link'

interface Activity {
  id: number
  type: 'project' | 'employee' | 'document' | 'task'
  title: string
  description: string
  timestamp: string
  user: string
  status?: 'success' | 'warning' | 'error' | 'info'
}

interface ActivityFeedProps {
  activities?: Activity[]
  isLoading?: boolean
}

const activityIcons = {
  project: (
    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5" />
    </svg>
  ),
  employee: (
    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
    </svg>
  ),
  document: (
    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  ),
  task: (
    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
    </svg>
  )
}

const statusColors = {
  success: 'bg-primary-100 text-primary-700 border-primary-200',
  warning: 'bg-amber-100 text-amber-700 border-amber-200',
  error: 'bg-red-100 text-red-700 border-red-200',
  info: 'bg-primary-100 text-primary-700 border-primary-200'
}

export const ActivityFeed: React.FC<ActivityFeedProps> = ({ 
  activities = [], 
  isLoading = false 
}) => {
  const defaultActivities: Activity[] = [
    {
      id: 1,
      type: 'project',
      title: 'Nouveau projet créé',
      description: 'Rénovation Bureau Central a été ajouté',
      timestamp: 'Il y a 2 heures',
      user: 'Marie Dubois',
      status: 'success'
    },
    {
      id: 2,
      type: 'employee',
      title: 'Employé assigné',
      description: 'Jean Martin assigné au projet Construction Entrepôt',
      timestamp: 'Il y a 4 heures',
      user: 'Pierre Durand',
      status: 'info'
    },
    {
      id: 3,
      type: 'document',
      title: 'Document téléchargé',
      description: 'Plans techniques mis à jour',
      timestamp: 'Il y a 6 heures',
      user: 'Sophie Lefebvre',
      status: 'success'
    },
    {
      id: 4,
      type: 'task',
      title: 'Tâche terminée',
      description: 'Inspection électrique complétée',
      timestamp: 'Il y a 8 heures',
      user: 'Luc Bernard',
      status: 'success'
    },
    {
      id: 5,
      type: 'project',
      title: 'Retard signalé',
      description: 'Projet Toiture Est en retard de 2 jours',
      timestamp: 'Il y a 1 jour',
      user: 'Système',
      status: 'warning'
    }
  ]

  const displayActivities = activities.length > 0 ? activities : defaultActivities

  return (
    <Card className="h-full">
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Activité Récente</h3>
            <p className="text-sm text-gray-500 mt-1">Dernières actions sur la plateforme</p>
          </div>
          <Link 
            href="/activity" 
            className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors"
          >
            Voir tout
          </Link>
        </div>
      </div>
      
      <div className="max-h-96 overflow-y-auto">
        {isLoading ? (
          <div className="p-6 space-y-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="animate-pulse flex items-start space-x-4">
                <div className="rounded-full bg-gray-200 h-10 w-10"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {displayActivities.map((activity, index) => (
              <div 
                key={activity.id} 
                className="p-4 hover:bg-gray-50 transition-colors group animate-fadeIn"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-start space-x-4">
                  <div className={`flex-shrink-0 p-2 rounded-lg ${
                    activity.status === 'success' ? 'bg-primary-100 text-primary-600' :
                    activity.status === 'warning' ? 'bg-amber-100 text-amber-600' :
                    activity.status === 'error' ? 'bg-red-100 text-red-600' :
                    'bg-primary-100 text-primary-600'
                  } group-hover:scale-110 transition-transform`}>
                    {activityIcons[activity.type]}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 group-hover:text-gray-700">
                        {activity.title}
                      </p>
                      {activity.status && (
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${statusColors[activity.status]}`}>
                          {activity.status === 'success' && '✓'}
                          {activity.status === 'warning' && '⚠'}
                          {activity.status === 'error' && '✗'}
                          {activity.status === 'info' && 'ℹ'}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <span>{activity.user}</span>
                      <span className="mx-1">•</span>
                      <span>{activity.timestamp}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Card>
  )
}