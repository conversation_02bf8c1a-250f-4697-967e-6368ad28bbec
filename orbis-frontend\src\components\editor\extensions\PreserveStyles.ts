import { Extension } from '@tiptap/core'

export const PreserveStyles = Extension.create({
  name: 'preserveStyles',

  addGlobalAttributes() {
    return [
      {
        types: ['div', 'table', 'tableRow', 'tableCell', 'tableHeader'],
        attributes: {
          style: {
            default: null,
            parseHTML: element => element.getAttribute('style'),
            renderHTML: attributes => {
              if (!attributes.style) {
                return {}
              }
              return {
                style: attributes.style,
              }
            },
          },
          class: {
            default: null,
            parseHTML: element => element.getAttribute('class'),
            renderHTML: attributes => {
              if (!attributes.class) {
                return {}
              }
              return {
                class: attributes.class,
              }
            },
          },
        },
      },
    ]
  },

  addCommands() {
    return {
      insertHTMLWithStyles: (html: string) => ({ commands }) => {
        return commands.insertContent(html, {
          parseOptions: {
            preserveWhitespace: 'full',
          },
        })
      },
    }
  },
})
