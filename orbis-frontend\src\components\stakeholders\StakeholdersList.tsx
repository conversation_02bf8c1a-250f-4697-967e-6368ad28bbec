'use client';

import React, { useState, useEffect } from 'react';
import { Stakeholder } from '@/types/stakeholder';
import { stakeholdersApiService } from '@/lib/api/stakeholders';
import { AddStakeholderModal } from './AddStakeholderModal';
import { EditStakeholderModal } from './EditStakeholderModal';

interface StakeholdersListProps {
  lotId: number;
}

export function StakeholdersList({ lotId }: StakeholdersListProps) {
  const [stakeholders, setStakeholders] = useState<Stakeholder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedStakeholder, setSelectedStakeholder] = useState<Stakeholder | null>(null);

  useEffect(() => {
    loadStakeholders();
  }, [lotId]);

  const loadStakeholders = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await stakeholdersApiService.getByLot(lotId);
      setStakeholders(data);
    } catch (err: any) {
      setError(err.message || 'Erreur lors du chargement des intervenants');
      console.error('Error loading stakeholders:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteStakeholder = async (stakeholderId: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet intervenant ?')) {
      return;
    }

    try {
      await stakeholdersApiService.delete(stakeholderId);
      await loadStakeholders(); // Recharger la liste
    } catch (err: any) {
      alert('Erreur lors de la suppression : ' + err.message);
    }
  };

  const handleStakeholderAdded = () => {
    loadStakeholders(); // Recharger la liste après ajout
  };

  const handleEditStakeholder = (stakeholder: Stakeholder) => {
    setSelectedStakeholder(stakeholder);
    setIsEditModalOpen(true);
  };

  const handleStakeholderUpdated = () => {
    loadStakeholders(); // Recharger la liste après modification
    setIsEditModalOpen(false);
    setSelectedStakeholder(null);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-red-600">
          <h3 className="text-lg font-medium mb-2">Erreur</h3>
          <p>{error}</p>
          <button
            onClick={loadStakeholders}
            className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">
            Intervenants ({stakeholders.length})
          </h3>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            <span>Ajouter un intervenant</span>
          </button>
        </div>

        <div className="p-6">
          {stakeholders.length === 0 ? (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun intervenant</h3>
              <p className="mt-1 text-sm text-gray-500">
                Commencez par ajouter un intervenant à ce lot.
              </p>
              <div className="mt-6">
                <button
                  onClick={() => setIsAddModalOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Ajouter un intervenant
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {stakeholders.map((stakeholder) => (
                <div
                  key={stakeholder.id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h4 className="text-lg font-medium text-gray-900">
                          {stakeholder.company?.company_name || 'Entreprise inconnue'}
                        </h4>
                        {!stakeholder.is_active && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Inactif
                          </span>
                        )}
                      </div>
                      
                      <div className="mt-2 space-y-1">
                        {stakeholder.role && (
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Rôle:</span> {stakeholder.role}
                          </p>
                        )}
                        {stakeholder.company?.activity && (
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Activité:</span> {stakeholder.company.activity}
                          </p>
                        )}
                        {stakeholder.company?.email && (
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Email:</span> {stakeholder.company.email}
                          </p>
                        )}
                        {stakeholder.company?.phone && (
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Téléphone:</span> {stakeholder.company.phone}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditStakeholder(stakeholder)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                        title="Modifier le rôle de l'intervenant"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => handleDeleteStakeholder(stakeholder.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                        title="Supprimer l'intervenant"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <AddStakeholderModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        lotId={lotId}
        onStakeholderAdded={handleStakeholderAdded}
      />

      <EditStakeholderModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        stakeholder={selectedStakeholder}
        onStakeholderUpdated={handleStakeholderUpdated}
      />
    </>
  );
}
