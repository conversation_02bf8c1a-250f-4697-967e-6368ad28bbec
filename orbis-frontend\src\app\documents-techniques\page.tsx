'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/ProtectedRoute'
import { ModernDocumentEditor } from '@/components/editor'
import { DocumentType, TechnicalDocumentResponse } from '@/types/technical-document'
import { api } from '@/lib/api'

function DocumentsTechniquesContent() {
  const { user } = useAuth()
  const searchParams = useSearchParams()
  const router = useRouter()

  // Récupérer les IDs depuis l'URL
  const selectedProjectId = searchParams.get('project_id') ? parseInt(searchParams.get('project_id')!) : null
  const selectedLotId = searchParams.get('lot_id') ? parseInt(searchParams.get('lot_id')!) : null
  const documentIdFromUrl = searchParams.get('document_id') ? parseInt(searchParams.get('document_id')!) : null

  // États
  const [selectedDocument, setSelectedDocument] = useState<TechnicalDocumentResponse | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [hasPendingChanges, setHasPendingChanges] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [enhancing, setEnhancing] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Refs pour la sauvegarde automatique
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const contentRef = useRef<string>('')

  // Fonctions API
  const updateDocument = async (id: number, data: { content: string }) => {
    return await api.updateTechnicalDocument(id, data)
  }



  // Gérer les modifications du contenu
  const handleContentChange = (content: string) => {
    contentRef.current = content
    setHasUnsavedChanges(true)
    setHasPendingChanges(true)
  }

  // Fonction de sauvegarde
  const handleSave = async () => {
    if (!selectedDocument || !contentRef.current) return

    try {
      setIsSaving(true)
      await updateDocument(selectedDocument.id, { content: contentRef.current })
      setHasUnsavedChanges(false)
      setHasPendingChanges(false)
      setLastSaved(new Date())
      console.log('✅ Document sauvegardé')
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde:', error)
      alert('Erreur lors de la sauvegarde du document')
    } finally {
      setIsSaving(false)
    }
  }

  // Fonction pour fermer l'éditeur
  const handleClose = () => {
    // Retourner au lot en cours
    if (selectedProjectId && selectedLotId) {
      router.push(`/projects/${selectedProjectId}/lots/${selectedLotId}`)
    } else if (selectedProjectId) {
      router.push(`/projects/${selectedProjectId}`)
    } else {
      router.push('/projects')
    }
  }

  // Gérer la sélection de texte
  const handleTextSelection = (selectedText: string) => {
    // Cette fonction peut être utilisée pour des fonctionnalités futures
    console.log('Texte sélectionné:', selectedText)
  }

  // Gérer l'ajout d'article (optionnel - l'insertion se fait maintenant dans l'éditeur)
  const handleArticleSubmit = async (articleData: any) => {
    // Cette fonction peut être utilisée pour des traitements supplémentaires
    // L'insertion du contenu se fait maintenant directement dans ModernDocumentEditor
    console.log('📝 Article ajouté via l\'éditeur:', articleData)

    // Marquer le document comme ayant des modifications non sauvegardées
    setHasUnsavedChanges(true)
  }



  // Charger le document depuis l'URL au démarrage
  useEffect(() => {
    const loadDocument = async () => {
      if (documentIdFromUrl && !selectedDocument) {
        try {
          console.log('🔄 Chargement du document depuis l\'URL:', documentIdFromUrl)
          const doc = await api.getTechnicalDocument(documentIdFromUrl) as TechnicalDocumentResponse
          setSelectedDocument(doc)
          console.log('✅ Document chargé:', doc)
        } catch (error) {
          console.error('❌ Erreur lors du chargement du document:', error)
        }
      }
    }

    loadDocument()
  }, [documentIdFromUrl, selectedDocument])

  // Nettoyage
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
    }
  }, [])

  // Vérifier que les paramètres requis sont présents
  if (!selectedProjectId || !selectedLotId) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Paramètres manquants
          </h3>
          <p className="text-gray-500 mb-4">
            Cette page nécessite un project_id et un lot_id dans l'URL.
          </p>
          <p className="text-sm text-gray-400">
            Exemple: /documents-techniques?project_id=1&lot_id=1
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
        {selectedDocument ? (
          // Mode édition avec le nouvel éditeur moderne
          <div className="h-full">
            <ModernDocumentEditor
              value={selectedDocument.content || ''}
              onChange={handleContentChange}
              documentType={selectedDocument.type_document!}
              onTextSelection={handleTextSelection}
              workspaceName="Mon Workspace" // TODO: récupérer depuis le contexte
              workspaceLogo="" // TODO: récupérer depuis le contexte
              onAddArticle={handleArticleSubmit}
              onSave={handleSave}
              onClose={handleClose}
              hasUnsavedChanges={hasUnsavedChanges}
              lotId={selectedLotId}
            />
          </div>
        ) : (
          // État vide
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="text-gray-400 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Aucun document sélectionné
              </h3>
              <p className="text-gray-500">
                Utilisez les paramètres d'URL pour charger un document
              </p>
              <p className="text-sm text-gray-400 mt-2">
                Exemple: /documents-techniques?project_id=1&lot_id=1&document_id=5
              </p>
            </div>
          </div>
        )}

      {/* Indicateur de chargement global */}
      {enhancing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span>Amélioration du texte en cours...</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default function DocumentsTechniquesPage() {
  return (
    <ProtectedRoute>
      <DocumentsTechniquesContent />
    </ProtectedRoute>
  )
}
