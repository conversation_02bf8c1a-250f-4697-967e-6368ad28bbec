'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { BusinessDataService } from '@/lib/auth'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import TCompanyModal from '@/components/TCompanyModal'
import DataList, { FilterField, DataListAction } from '@/components/ui/DataList'
import { 
  PlusIcon, 
  BuildingOfficeIcon, 
  PhoneIcon, 
  EnvelopeIcon,
  PencilIcon
} from '@heroicons/react/24/outline'
import Image from 'next/image'
import { cleanFormData } from '@/utils/formUtils'

interface TCompany {
  id: number
  company_name: string
  activity?: string
  address?: string
  postal_code?: string
  city?: string
  country?: string
  phone?: string
  fax?: string
  email?: string
  siret?: string
  vat_number?: string
  legal_representative_name?: string
  legal_representative_email?: string
  logo_url?: string
  logo_filename?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

interface TCompanyFormData {
  company_name: string
  activity: string
  address: string
  postal_code: string
  city: string
  country: string
  phone: string
  fax: string
  email: string
  siret: string
  vat_number: string
  logo_url?: string
  logo_filename?: string
}

export default function TCompaniesPage() {
  const { user, signOut } = useAuth()
  const [tcompanies, setTCompanies] = useState<TCompany[]>([])
  const [filteredTCompanies, setFilteredTCompanies] = useState<TCompany[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [selectedTCompany, setSelectedTCompany] = useState<TCompany | null>(null)
  const [formData, setFormData] = useState<TCompanyFormData>({
    company_name: '',
    activity: '',
    address: '',
    postal_code: '',
    city: '',
    country: 'France',
    phone: '',
    fax: '',
    email: '',
    siret: '',
    vat_number: '',
    logo_url: '',
    logo_filename: ''
  })

  // Fonction de déconnexion
  const handleLogout = () => {
    signOut()
  }

  // Charger les TCompanies
  const fetchTCompanies = async () => {
    try {
      setLoading(true)
      const response = await BusinessDataService.makeAuthenticatedRequest('/tcompanies/')

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des TCompanies')
      }

      const data = await response.json()
      const companies = Array.isArray(data) ? data : []
      setTCompanies(companies)
      setFilteredTCompanies(companies)
      setError(null)
    } catch (err) {
      console.error('Erreur:', err)
      setError('Erreur lors du chargement des TCompanies')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTCompanies()
  }, [])

  // Configuration des filtres
  const filters: FilterField[] = [
    {
      key: 'company_name',
      label: 'Nom de l\'entreprise',
      type: 'text',
      placeholder: 'Rechercher par nom...'
    },
    {
      key: 'activity',
      label: 'Activité',
      type: 'text',
      placeholder: 'Rechercher par activité...'
    },
    {
      key: 'city',
      label: 'Ville',
      type: 'text',
      placeholder: 'Rechercher par ville...'
    },
    {
      key: 'is_active',
      label: 'Statut',
      type: 'select',
      options: [
        { value: 'true', label: 'Actif' },
        { value: 'false', label: 'Inactif' }
      ]
    }
  ]

  // Gérer les filtres
  const handleFiltersChange = (filterValues: Record<string, string>) => {
    let filtered = [...tcompanies]

    Object.entries(filterValues).forEach(([key, value]) => {
      if (value) {
        filtered = filtered.filter(company => {
          switch (key) {
            case 'company_name':
              return company.company_name?.toLowerCase().includes(value.toLowerCase())
            case 'activity':
              return company.activity?.toLowerCase().includes(value.toLowerCase())
            case 'city':
              return company.city?.toLowerCase().includes(value.toLowerCase())
            case 'is_active':
              return company.is_active.toString() === value
            default:
              return true
          }
        })
      }
    })

    setFilteredTCompanies(filtered)
  }

  // Ouvrir le modal de création
  const handleCreate = () => {
    resetForm()
    setSelectedTCompany(null)
    setIsCreateModalOpen(true)
  }

  // Gérer la soumission du formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const endpoint = selectedTCompany
        ? `/tcompanies/${selectedTCompany.id}`
        : '/tcompanies/'

      const method = selectedTCompany ? 'PUT' : 'POST'

      // Nettoyer les données : remplacer les chaînes vides par null
      const cleanedFormData = cleanFormData(formData)

      console.log('🔄 Envoi de la requête:', { endpoint, method, formData: cleanedFormData })

      const response = await BusinessDataService.makeAuthenticatedRequest(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanedFormData)
      })

      console.log('📡 Réponse reçue:', { status: response.status, statusText: response.statusText })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Erreur détaillée:', errorText)
        throw new Error(`Erreur ${response.status}: ${errorText}`)
      }

      // Recharger la liste
      await fetchTCompanies()

      // Fermer les modales et réinitialiser
      setIsCreateModalOpen(false)
      setIsEditModalOpen(false)
      setSelectedTCompany(null)
      resetForm()

    } catch (err) {
      console.error('Erreur:', err)
      setError('Erreur lors de la sauvegarde')
    }
  }

  // Réinitialiser le formulaire
  const resetForm = () => {
    setFormData({
      company_name: '',
      activity: '',
      address: '',
      postal_code: '',
      city: '',
      country: 'France',
      phone: '',
      fax: '',
      email: '',
      siret: '',
      vat_number: '',
      logo_url: '',
      logo_filename: ''
    })
  }

  // Ouvrir le modal d'édition
  const handleEdit = (tcompany: TCompany) => {
    setSelectedTCompany(tcompany)
    setFormData({
      company_name: tcompany.company_name || '',
      activity: tcompany.activity || '',
      address: tcompany.address || '',
      postal_code: tcompany.postal_code || '',
      city: tcompany.city || '',
      country: tcompany.country || 'France',
      phone: tcompany.phone || '',
      fax: tcompany.fax || '',
      email: tcompany.email || '',
      siret: tcompany.siret || '',
      vat_number: tcompany.vat_number || '',
      logo_url: tcompany.logo_url || '',
      logo_filename: tcompany.logo_filename || ''
    })
    setIsEditModalOpen(true)
  }

  // Configuration des actions
  const actions: DataListAction[] = [
    {
      label: 'Nouvelle entreprise',
      onClick: handleCreate,
      icon: <PlusIcon className="h-5 w-5" />,
      variant: 'primary'
    }
  ]

  // Rendu d'un élément en grille
  const renderGridItem = (tcompany: TCompany) => (
    <div className="p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start gap-3 flex-1">
          {/* Logo de l'entreprise */}
          {tcompany.logo_url ? (
            <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
              <Image
                src={`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${tcompany.logo_url}`}
                alt={`Logo ${tcompany.company_name}`}
                width={48}
                height={48}
                className="w-full h-full object-contain"
                onError={(e) => {
                  console.error('Erreur de chargement du logo:', tcompany.logo_url)
                }}
              />
            </div>
          ) : (
            <BuildingOfficeIcon className="h-12 w-12 text-primary-600 flex-shrink-0" />
          )}

          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {tcompany.company_name}
            </h3>
            {tcompany.activity && (
              <p className="text-sm text-gray-600 mb-3">{tcompany.activity}</p>
            )}
          </div>
        </div>
        <div className={`px-2 py-1 text-xs rounded-full ${
          tcompany.is_active
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {tcompany.is_active ? 'Actif' : 'Inactif'}
        </div>
      </div>

      <div className="space-y-2 mb-4">
        {tcompany.address && (
          <div className="text-sm text-gray-600">
            <p>{tcompany.address}</p>
            {(tcompany.postal_code || tcompany.city) && (
              <p>{tcompany.postal_code} {tcompany.city}</p>
            )}
          </div>
        )}

        {tcompany.phone && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <PhoneIcon className="h-4 w-4" />
            <span>{tcompany.phone}</span>
          </div>
        )}

        {tcompany.email && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <EnvelopeIcon className="h-4 w-4" />
            <span>{tcompany.email}</span>
          </div>
        )}

        {tcompany.siret && (
          <div className="text-xs text-gray-500">
            SIRET: {tcompany.siret}
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end pt-4 border-t border-gray-100">
        <button
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            handleEdit(tcompany)
          }}
          className="flex items-center text-sm text-gray-600 hover:text-gray-700"
        >
          <PencilIcon className="w-4 h-4 mr-1" />
          Modifier
        </button>
      </div>
    </div>
  )

  // Rendu d'un élément en liste
  const renderListItem = (tcompany: TCompany) => (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 flex-1">
          <div className="flex-shrink-0">
            {tcompany.logo_url ? (
              <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${tcompany.logo_url}`}
                  alt={`Logo ${tcompany.company_name}`}
                  width={48}
                  height={48}
                  className="w-full h-full object-contain"
                />
              </div>
            ) : (
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <BuildingOfficeIcon className="h-6 w-6 text-primary-600" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-1">
              <h3 className="text-lg font-semibold text-gray-900">{tcompany.company_name}</h3>
              <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                tcompany.is_active
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {tcompany.is_active ? 'Actif' : 'Inactif'}
              </div>
            </div>
            {tcompany.activity && (
              <p className="text-sm text-gray-600 mb-2">{tcompany.activity}</p>
            )}
            <div className="flex items-center space-x-6 text-sm text-gray-500">
              {tcompany.email && (
                <span className="flex items-center">
                  <EnvelopeIcon className="w-4 h-4 mr-1" />
                  {tcompany.email}
                </span>
              )}
              {tcompany.phone && (
                <span className="flex items-center">
                  <PhoneIcon className="w-4 h-4 mr-1" />
                  {tcompany.phone}
                </span>
              )}
              {(tcompany.city || tcompany.postal_code) && (
                <span>{tcompany.postal_code} {tcompany.city}</span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              handleEdit(tcompany)
            }}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <PencilIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )

  if (error) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-white">
          <div className="flex">
            <ModernSidebar user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined} />

            <div className="flex-1 lg:ml-72">
              <ModernHeader
                title="Carnet d'adresses"
                subtitle="Erreur de chargement"
                user={user ? {
                  name: `${user.first_name} ${user.last_name}`,
                  email: user.email
                } : undefined}
                onLogout={handleLogout}
              />

              <main className="p-6">
                <div className="text-center py-12">
                  <div className="text-red-500 text-4xl mb-4">⚠️</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Erreur de chargement</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <button 
                    onClick={fetchTCompanies}
                    className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
                  >
                    Réessayer
                  </button>
                </div>
              </main>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-white">
        <div className="flex">
          <ModernSidebar user={user ? {
            name: `${user.first_name} ${user.last_name}`,
            email: user.email
          } : undefined} />

          <div className="flex-1 lg:ml-72">
            <ModernHeader
              title="Carnet d'adresses"
              subtitle={`Gestion de vos entreprises • ${filteredTCompanies.length} entreprise${filteredTCompanies.length > 1 ? 's' : ''}`}
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={handleLogout}
            />

            <main className="p-6">
              <DataList
                items={filteredTCompanies}
                loading={loading}
                title="Entreprises"
                subtitle={`${filteredTCompanies.length} entreprise${filteredTCompanies.length > 1 ? 's' : ''} trouvée${filteredTCompanies.length > 1 ? 's' : ''}`}
                emptyMessage="Aucune entreprise trouvée. Commencez par ajouter une entreprise."
                emptyIcon={<BuildingOfficeIcon />}
                getItemUrl={(item) => `/tcompanies/${item.id}`}
                filters={filters}
                onFiltersChange={handleFiltersChange}
                actions={actions}
                renderGridItem={renderGridItem}
                renderListItem={renderListItem}
                defaultViewMode="grid"
                gridCols={3}
              />
            </main>
          </div>
        </div>

        {/* Modales */}
        <TCompanyModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onSubmit={handleSubmit}
          formData={formData}
          setFormData={setFormData}
          isEdit={false}
        />

        <TCompanyModal
          key={`edit-modal-${selectedTCompany?.id || 'none'}`}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={handleSubmit}
          formData={formData}
          setFormData={setFormData}
          isEdit={true}
          tcompanyId={selectedTCompany?.id}
          onRefresh={fetchTCompanies}
        />
      </div>
    </ProtectedRoute>
  )
}
