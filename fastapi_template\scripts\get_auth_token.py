#!/usr/bin/env python3
"""
Script pour récupérer un token d'authentification
"""

import requests
import json

def get_auth_token():
    """Récupérer un token d'authentification"""
    
    print("🔐 Récupération du token d'authentification...")
    
    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "orbis123!"
    }
    
    try:
        # Faire la requête de login avec FastAuth
        response = requests.post(
            "http://localhost:8000/api/v1/fast-auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            auth_data = response.json()
            token = auth_data.get("access_token")
            user_info = auth_data.get("user", {})
            
            print(f"✅ Connexion réussie!")
            print(f"👤 Utilisateur: {user_info.get('email', 'N/A')}")
            print(f"🔑 Token: {token[:50]}..." if token else "❌ Pas de token")
            
            return token
        else:
            print(f"❌ Erreur de connexion: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Détail: {error_data}")
            except:
                print(f"Response text: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur. Vérifiez que le backend est démarré sur http://localhost:8000")
        return None
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None

def test_api_with_token(token):
    """Tester l'API avec le token"""
    
    if not token:
        print("❌ Pas de token disponible pour tester l'API")
        return
    
    print(f"\n🔍 Test de l'API avec le token...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test 1: Récupérer les projets
    try:
        print("📋 Test: Récupération des projets...")
        response = requests.get(
            "http://localhost:8000/api/v1/projects",
            headers=headers
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            projects = response.json()
            print(f"   ✅ {len(projects)} projet(s) trouvé(s)")
            for project in projects[:3]:  # Afficher les 3 premiers
                print(f"      - {project.get('name')} (ID: {project.get('id')})")
        else:
            print(f"   ❌ Erreur: {response.text}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 2: Récupérer les documents techniques du projet 7
    try:
        print("\n📄 Test: Récupération des documents techniques (projet 7)...")
        response = requests.get(
            "http://localhost:8000/api/v1/technical-documents?project_id=7",
            headers=headers
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            documents = response.json()
            print(f"   ✅ {len(documents)} document(s) technique(s) trouvé(s)")
            for doc in documents:
                print(f"      - {doc.get('name')} ({doc.get('type_document')})")
        else:
            print(f"   ❌ Erreur: {response.text}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 3: Récupérer les entreprises tierces
    try:
        print("\n🏢 Test: Récupération des entreprises tierces...")
        response = requests.get(
            "http://localhost:8000/api/v1/entreprises-tiers",
            headers=headers
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            entreprises = response.json()
            print(f"   ✅ {len(entreprises)} entreprise(s) tierce(s) trouvée(s)")
            for ent in entreprises[:3]:  # Afficher les 3 premières
                print(f"      - {ent.get('nom_entreprise')} (ID: {ent.get('id')})")
        else:
            print(f"   ❌ Erreur: {response.text}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")

if __name__ == "__main__":
    token = get_auth_token()
    if token:
        test_api_with_token(token)
        print(f"\n🔑 Token pour tests manuels:")
        print(f"Authorization: Bearer {token}")
    else:
        print("\n❌ Impossible de récupérer le token")
