#!/usr/bin/env python3
"""
Script pour vérifier l'utilisateur Jeremy dans la base de données
"""

import asyncio
import asyncpg
from app.core.config import settings

async def check_user():
    """Vérifier l'utilisateur Jeremy"""
    print("🔍 Vérification de l'utilisateur Jeremy...")
    
    conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier dans la table users
        user = await conn.fetchrow(
            "SELECT email, role, is_superuser, is_active FROM users WHERE email = $1", 
            "<EMAIL>"
        )
        
        if user:
            print("✅ Utilisateur trouvé dans la base locale:")
            print(f"   Email: {user['email']}")
            print(f"   Role: {user['role']}")
            print(f"   is_superuser: {user['is_superuser']}")
            print(f"   is_active: {user['is_active']}")
        else:
            print("❌ Utilisateur non trouvé dans la base locale")
            
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(check_user())
