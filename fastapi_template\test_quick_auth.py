"""
Test rapide pour vérifier si le problème 403 est résolu
"""

import requests

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "orbis123!"

def test_quick():
    print("🔐 Test rapide d'authentification")
    
    # 1. Login
    login_data = {"email": TEST_EMAIL, "password": TEST_PASSWORD}
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
        print(f"Login: {response.status_code}")
        
        if response.status_code == 200:
            token = response.json().get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
            
            # 2. Test workspaces
            ws_response = requests.get(f"{BASE_URL}/api/v1/workspaces/", headers=headers)
            print(f"Workspaces: {ws_response.status_code}")
            
            if ws_response.status_code == 200:
                print("✅ PROBLÈME 403 RÉSOLU!")
                workspaces = ws_response.json()
                print(f"📁 {len(workspaces)} workspaces trouvés")
            else:
                print(f"❌ Erreur: {ws_response.text}")
        else:
            print(f"❌ Login failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    test_quick()
