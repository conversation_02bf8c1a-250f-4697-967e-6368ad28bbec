'use client'

import React, { useRef } from 'react'
import { DocumentArrowUpIcon } from '@heroicons/react/24/outline'

interface WordImportButtonProps {
  onImport: (file: File) => Promise<void>
  disabled?: boolean
  className?: string
}

export default function WordImportButton({ 
  onImport, 
  disabled = false, 
  className = '' 
}: WordImportButtonProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      try {
        await onImport(file)
        // Réinitialiser l'input pour permettre de sélectionner le même fichier à nouveau
        if (fileInputRef.current) {
          fileInputRef.current.value = ''
        }
      } catch (error) {
        console.error('Erreur lors de l\'import:', error)
        alert('Erreur lors de l\'import du fichier Word')
      }
    }
  }

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />
      <button
        onClick={handleClick}
        disabled={disabled}
        className={`inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
        title="Importer un document Word (.doc, .docx)"
      >
        <DocumentArrowUpIcon className="h-4 w-4 mr-2" />
        Importer Word
      </button>
    </>
  )
}
