#!/usr/bin/env python3
"""
Audit complet des tables de la base de données ORBIS
Analyse les tables existantes, manquantes et inutiles
"""

import asyncio
import asyncpg
from datetime import datetime
import sys
from pathlib import Path

# Ajouter le répertoire app au path Python
sys.path.insert(0, str(Path(__file__).parent))

# Configuration
DATABASE_URL = "**************************************************************************************************/postgres"

# Tables attendues selon les modèles de l'application
EXPECTED_TABLES = {
    # Tables principales métier
    "companies": {
        "description": "Entreprises clientes",
        "priority": "HIGH",
        "required_columns": ["id", "name", "code", "siret", "address", "phone", "email"]
    },
    "users": {
        "description": "Utilisateurs de l'application",
        "priority": "HIGH", 
        "required_columns": ["id", "email", "first_name", "last_name", "role", "is_active", "supabase_user_id"]
    },
    "projects": {
        "description": "Projets de construction",
        "priority": "HIGH",
        "required_columns": ["id", "name", "code", "description", "status", "budget_total", "company_id"]
    },
    "employees": {
        "description": "Employés des entreprises",
        "priority": "HIGH",
        "required_columns": ["id", "employee_number", "first_name", "last_name", "position", "hourly_rate", "company_id"]
    },
    "suppliers": {
        "description": "Fournisseurs",
        "priority": "HIGH",
        "required_columns": ["id", "name", "code", "type", "phone", "email", "company_id"]
    },
    "materials": {
        "description": "Matériaux de construction",
        "priority": "HIGH",
        "required_columns": ["id", "name", "code", "unit", "unit_price", "supplier_id"]
    },
    "budgets": {
        "description": "Budgets des projets",
        "priority": "MEDIUM",
        "required_columns": ["id", "project_id", "category", "estimated_amount", "actual_amount"]
    },
    "invoices": {
        "description": "Factures",
        "priority": "MEDIUM",
        "required_columns": ["id", "invoice_number", "project_id", "supplier_id", "amount", "status"]
    },
    "purchase_orders": {
        "description": "Bons de commande",
        "priority": "MEDIUM",
        "required_columns": ["id", "order_number", "project_id", "supplier_id", "total_amount", "status"]
    },
    "quotes": {
        "description": "Devis",
        "priority": "MEDIUM",
        "required_columns": ["id", "quote_number", "project_id", "supplier_id", "total_amount", "status"]
    },
    "documents": {
        "description": "Documents du projet",
        "priority": "LOW",
        "required_columns": ["id", "name", "file_path", "project_id", "document_type"]
    },
    
    # Tables de liaison
    "user_companies": {
        "description": "Association utilisateurs-entreprises",
        "priority": "HIGH",
        "required_columns": ["user_id", "company_id", "is_default"]
    },
    "project_materials": {
        "description": "Matériaux utilisés dans les projets",
        "priority": "MEDIUM",
        "required_columns": ["project_id", "material_id", "quantity", "unit_price"]
    },
    "project_employees": {
        "description": "Employés assignés aux projets",
        "priority": "MEDIUM",
        "required_columns": ["project_id", "employee_id", "role", "start_date"]
    },
    
    # Tables Supabase Auth (si nécessaires)
    "user_profiles": {
        "description": "Profils utilisateurs Supabase",
        "priority": "MEDIUM",
        "required_columns": ["id", "first_name", "last_name", "company", "phone"]
    }
}

# Tables système Supabase à ignorer
SUPABASE_SYSTEM_TABLES = [
    "auth.users", "auth.sessions", "auth.refresh_tokens", "auth.audit_log_entries",
    "storage.buckets", "storage.objects", "realtime.subscription",
    "supabase_functions.hooks", "supabase_migrations.schema_migrations"
]


async def get_existing_tables():
    """Récupérer toutes les tables existantes"""
    print("🔍 Analyse des tables existantes...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Récupérer toutes les tables publiques
        tables = await conn.fetch("""
            SELECT 
                table_name,
                table_type,
                (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
            FROM information_schema.tables t
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        existing_tables = {}
        for table in tables:
            table_name = table['table_name']
            
            # Récupérer les colonnes de la table
            columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = $1 AND table_schema = 'public'
                ORDER BY ordinal_position
            """, table_name)
            
            # Compter les enregistrements
            try:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
            except:
                count = 0
            
            existing_tables[table_name] = {
                "type": table['table_type'],
                "column_count": table['column_count'],
                "record_count": count,
                "columns": [col['column_name'] for col in columns],
                "column_details": columns
            }
        
        return existing_tables
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        return {}
    finally:
        await conn.close()


def analyze_tables(existing_tables):
    """Analyser les tables existantes vs attendues"""
    print("\n📊 Analyse des Tables")
    print("="*80)
    
    # Tables présentes et conformes
    present_tables = []
    # Tables présentes mais avec des colonnes manquantes
    incomplete_tables = []
    # Tables manquantes
    missing_tables = []
    # Tables inconnues (potentiellement inutiles)
    unknown_tables = []
    
    # Analyser les tables attendues
    for table_name, table_info in EXPECTED_TABLES.items():
        if table_name in existing_tables:
            existing_cols = existing_tables[table_name]["columns"]
            required_cols = table_info["required_columns"]
            missing_cols = [col for col in required_cols if col not in existing_cols]
            
            if missing_cols:
                incomplete_tables.append({
                    "name": table_name,
                    "missing_columns": missing_cols,
                    "priority": table_info["priority"],
                    "description": table_info["description"]
                })
            else:
                present_tables.append({
                    "name": table_name,
                    "record_count": existing_tables[table_name]["record_count"],
                    "priority": table_info["priority"],
                    "description": table_info["description"]
                })
        else:
            missing_tables.append({
                "name": table_name,
                "priority": table_info["priority"],
                "description": table_info["description"],
                "required_columns": table_info["required_columns"]
            })
    
    # Identifier les tables inconnues
    for table_name in existing_tables:
        if table_name not in EXPECTED_TABLES:
            unknown_tables.append({
                "name": table_name,
                "record_count": existing_tables[table_name]["record_count"],
                "column_count": existing_tables[table_name]["column_count"]
            })
    
    return present_tables, incomplete_tables, missing_tables, unknown_tables


def print_analysis_results(present_tables, incomplete_tables, missing_tables, unknown_tables):
    """Afficher les résultats de l'analyse"""
    
    # Tables présentes et conformes
    print(f"\n✅ TABLES PRÉSENTES ET CONFORMES ({len(present_tables)})")
    print("-" * 60)
    for table in sorted(present_tables, key=lambda x: x["priority"]):
        priority_icon = "🔴" if table["priority"] == "HIGH" else "🟡" if table["priority"] == "MEDIUM" else "🟢"
        print(f"{priority_icon} {table['name']:<20} | {table['record_count']:>6} enreg. | {table['description']}")
    
    # Tables incomplètes
    if incomplete_tables:
        print(f"\n⚠️  TABLES INCOMPLÈTES ({len(incomplete_tables)})")
        print("-" * 60)
        for table in sorted(incomplete_tables, key=lambda x: x["priority"]):
            priority_icon = "🔴" if table["priority"] == "HIGH" else "🟡" if table["priority"] == "MEDIUM" else "🟢"
            print(f"{priority_icon} {table['name']:<20} | Colonnes manquantes: {', '.join(table['missing_columns'])}")
    
    # Tables manquantes
    if missing_tables:
        print(f"\n❌ TABLES MANQUANTES ({len(missing_tables)})")
        print("-" * 60)
        for table in sorted(missing_tables, key=lambda x: x["priority"]):
            priority_icon = "🔴" if table["priority"] == "HIGH" else "🟡" if table["priority"] == "MEDIUM" else "🟢"
            print(f"{priority_icon} {table['name']:<20} | {table['description']}")
    
    # Tables inconnues
    if unknown_tables:
        print(f"\n❓ TABLES INCONNUES/POTENTIELLEMENT INUTILES ({len(unknown_tables)})")
        print("-" * 60)
        for table in sorted(unknown_tables, key=lambda x: x["name"]):
            print(f"🔍 {table['name']:<20} | {table['record_count']:>6} enreg. | {table['column_count']:>2} col.")


def generate_recommendations(present_tables, incomplete_tables, missing_tables, unknown_tables):
    """Générer des recommandations"""
    print(f"\n💡 RECOMMANDATIONS")
    print("="*80)
    
    # Priorités
    high_priority_missing = [t for t in missing_tables if t["priority"] == "HIGH"]
    high_priority_incomplete = [t for t in incomplete_tables if t["priority"] == "HIGH"]
    
    if high_priority_missing or high_priority_incomplete:
        print("🔴 ACTIONS URGENTES:")
        
        for table in high_priority_missing:
            print(f"   - Créer la table '{table['name']}' (critique pour l'application)")
        
        for table in high_priority_incomplete:
            print(f"   - Ajouter les colonnes manquantes à '{table['name']}': {', '.join(table['missing_columns'])}")
    
    medium_priority_missing = [t for t in missing_tables if t["priority"] == "MEDIUM"]
    if medium_priority_missing:
        print("\n🟡 ACTIONS MOYENNES:")
        for table in medium_priority_missing:
            print(f"   - Créer la table '{table['name']}' (fonctionnalité importante)")
    
    if unknown_tables:
        print("\n🔍 À VÉRIFIER:")
        for table in unknown_tables:
            if table["record_count"] == 0:
                print(f"   - Table '{table['name']}' vide - peut être supprimée si inutile")
            else:
                print(f"   - Table '{table['name']}' contient {table['record_count']} enreg. - vérifier l'utilité")
    
    # Score de santé
    total_expected = len(EXPECTED_TABLES)
    total_present = len(present_tables)
    total_incomplete = len(incomplete_tables)
    
    health_score = ((total_present + (total_incomplete * 0.5)) / total_expected) * 100
    
    print(f"\n📊 SCORE DE SANTÉ DE LA BASE: {health_score:.1f}%")
    if health_score >= 90:
        print("   🎉 Excellente santé - base de données bien structurée")
    elif health_score >= 70:
        print("   ✅ Bonne santé - quelques améliorations possibles")
    elif health_score >= 50:
        print("   ⚠️  Santé moyenne - actions recommandées")
    else:
        print("   🚨 Santé critique - actions urgentes requises")


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Audit des Tables de Base de Données")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # Récupérer les tables existantes
    existing_tables = await get_existing_tables()
    
    if not existing_tables:
        print("❌ Impossible de récupérer les tables")
        return False
    
    print(f"   📋 {len(existing_tables)} tables trouvées dans la base de données")
    
    # Analyser les tables
    present_tables, incomplete_tables, missing_tables, unknown_tables = analyze_tables(existing_tables)
    
    # Afficher les résultats
    print_analysis_results(present_tables, incomplete_tables, missing_tables, unknown_tables)
    
    # Générer les recommandations
    generate_recommendations(present_tables, incomplete_tables, missing_tables, unknown_tables)
    
    print(f"\n🏁 Audit terminé - {datetime.now().strftime('%H:%M:%S')}")
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
