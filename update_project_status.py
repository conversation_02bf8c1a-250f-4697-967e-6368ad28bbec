#!/usr/bin/env python3
"""
Script pour mettre à jour les statuts des projets existants
"""

import asyncio
import asyncpg

DATABASE_URL = "**************************************************************************************************/postgres"

async def update_project_status():
    """Mettre à jour les statuts des projets existants"""
    print("🚀 Mise à jour des statuts des projets...")
    
    try:
        # Connexion à la base de données
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Connexion à la base de données réussie")
        
        # 1. Vérifier les statuts actuels
        current_statuses = await conn.fetch("""
            SELECT status, COUNT(*) as count
            FROM projects 
            GROUP BY status
            ORDER BY status
        """)
        
        print(f"\n📊 Statuts actuels:")
        for row in current_statuses:
            print(f"   • {row['status']}: {row['count']} projet(s)")
        
        # 2. Mettre à jour les anciens statuts vers les nouveaux
        status_mapping = {
            'dao': 'En cours',
            'exe': 'En cours', 
            'completed': 'Terminé',
            'archived': 'Archivé'
        }
        
        total_updated = 0
        for old_status, new_status in status_mapping.items():
            try:
                updated_count = await conn.fetchval("""
                    UPDATE projects 
                    SET status = $1
                    WHERE status = $2
                    RETURNING (SELECT COUNT(*) FROM projects WHERE status = $1)
                """, new_status, old_status)
                
                if updated_count and updated_count > 0:
                    print(f"✅ Mis à jour {updated_count} projet(s) de '{old_status}' vers '{new_status}'")
                    total_updated += updated_count
                    
            except Exception as e:
                print(f"⚠️ Erreur lors de la mise à jour {old_status} -> {new_status}: {e}")
        
        print(f"\n📈 Total mis à jour: {total_updated} projet(s)")
        
        # 3. Vérifier les nouveaux statuts
        new_statuses = await conn.fetch("""
            SELECT status, COUNT(*) as count
            FROM projects 
            GROUP BY status
            ORDER BY status
        """)
        
        print(f"\n📊 Nouveaux statuts:")
        for row in new_statuses:
            print(f"   • {row['status']}: {row['count']} projet(s)")
        
        # 4. Vérifier s'il reste des statuts non reconnus
        valid_statuses = ['En cours', 'En attente', 'Terminé', 'Archivé']
        invalid_statuses = await conn.fetch("""
            SELECT status, COUNT(*) as count
            FROM projects 
            WHERE status NOT IN ('En cours', 'En attente', 'Terminé', 'Archivé')
            GROUP BY status
        """)
        
        if invalid_statuses:
            print(f"\n⚠️ Statuts non reconnus trouvés:")
            for row in invalid_statuses:
                print(f"   • {row['status']}: {row['count']} projet(s)")
                
            # Mettre à jour les statuts non reconnus vers 'En cours'
            for row in invalid_statuses:
                await conn.execute("""
                    UPDATE projects 
                    SET status = 'En cours'
                    WHERE status = $1
                """, row['status'])
                print(f"✅ Mis à jour {row['count']} projet(s) de '{row['status']}' vers 'En cours'")
        
        await conn.close()
        print("\n🎉 Mise à jour des statuts terminée avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_updated_statuses():
    """Tester les statuts mis à jour"""
    print("\n🧪 Test des statuts mis à jour...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Test: récupérer les projets avec leurs nouveaux statuts
        projects = await conn.fetch("""
            SELECT name, code, status, nature
            FROM projects
            LIMIT 5
        """)
        
        print("✅ Test de lecture réussi:")
        for project in projects:
            print(f"   • {project['name']} ({project['code']}) → {project['status']} / {project['nature']}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Mise à jour des statuts des projets")
    print("=" * 50)
    
    async def main():
        # Mettre à jour les statuts
        success = await update_project_status()
        
        if success:
            # Tester les nouveaux statuts
            await test_updated_statuses()
        
        return success
    
    result = asyncio.run(main())
    
    if result:
        print("\n✅ Statuts mis à jour avec succès!")
        print("🚀 Le CRUD des projets devrait maintenant fonctionner")
    else:
        print("\n❌ Échec de la mise à jour des statuts")
