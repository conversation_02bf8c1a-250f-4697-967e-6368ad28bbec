#!/usr/bin/env python3
"""
Test du flux complet avec messages de confirmation
"""

import requests
import json
from datetime import datetime
import webbrowser
import time

# Configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"
API_V1_URL = f"{BACKEND_URL}/api/v1"


def test_registration_flow():
    """Test du flux d'inscription avec message de confirmation"""
    print("🚀 TEST DU FLUX D'INSCRIPTION AVEC MESSAGES")
    print("="*60)
    
    # Données utilisateur de test
    user_data = {
        "email": f"testmsg{datetime.now().strftime('%H%M%S')}@gmail.com",
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "Message",
        "company": "Test Company",
        "phone": "0123456789"
    }
    
    print(f"📧 Email de test: {user_data['email']}")
    print(f"🔑 Mot de passe: {user_data['password']}")
    
    # Test 1: Inscription
    print("\n1️⃣ Test d'inscription...")
    try:
        response = requests.post(f"{API_V1_URL}/auth/register", json=user_data, timeout=10)
        if response.status_code == 200:
            print("   ✅ Inscription réussie")
            result = response.json()
            print(f"   📝 Message: {result.get('message', 'N/A')}")
            return True, user_data
        else:
            print(f"   ❌ Inscription échouée: {response.status_code}")
            print(f"   📄 Réponse: {response.text}")
            return False, user_data
    except Exception as e:
        print(f"   ❌ Erreur inscription: {e}")
        return False, user_data


def print_test_instructions(user_data):
    """Instructions pour tester manuellement le flux complet"""
    print("\n" + "="*60)
    print("🎯 INSTRUCTIONS POUR TESTER LE FLUX COMPLET")
    print("="*60)
    
    print(f"\n📋 ÉTAPES À SUIVRE :")
    print(f"1️⃣ Ouvrir: {FRONTEND_URL}/auth/register")
    print(f"2️⃣ Créer un compte avec ces données :")
    print(f"   📧 Email: {user_data['email']}")
    print(f"   🔑 Mot de passe: {user_data['password']}")
    print(f"   👤 Prénom: {user_data['first_name']}")
    print(f"   👤 Nom: {user_data['last_name']}")
    print(f"   🏢 Entreprise: {user_data['company']}")
    print(f"   📞 Téléphone: {user_data['phone']}")
    
    print(f"\n3️⃣ Après inscription, vous devriez :")
    print(f"   ✅ Être redirigé vers: {FRONTEND_URL}/auth/login?message=account-created")
    print(f"   ✅ Voir un message vert de confirmation")
    print(f"   ✅ Le message contient une icône de validation")
    print(f"   ✅ Le message disparaît après 10 secondes")
    
    print(f"\n4️⃣ Ensuite, tester la connexion :")
    print(f"   📧 Email: {user_data['email']}")
    print(f"   🔑 Mot de passe: {user_data['password']}")
    print(f"   ✅ Redirection vers: {FRONTEND_URL}/test-dashboard")
    print(f"   ✅ Affichage des données utilisateur")
    
    print(f"\n🎨 ÉLÉMENTS À VÉRIFIER :")
    print(f"   ✅ Message de succès avec icône verte")
    print(f"   ✅ Style attrayant et professionnel")
    print(f"   ✅ Message disparaît automatiquement")
    print(f"   ✅ URL nettoyée après disparition")
    print(f"   ✅ Gestion d'erreurs spécifiques")
    
    print(f"\n🔧 Services actifs :")
    print(f"   - Backend API: {BACKEND_URL}")
    print(f"   - Frontend: {FRONTEND_URL}")
    print(f"   - Documentation API: {BACKEND_URL}/docs")


def open_test_pages():
    """Ouvrir les pages de test dans le navigateur"""
    print(f"\n🌐 Ouverture des pages de test...")
    
    try:
        # Ouvrir la page d'inscription
        webbrowser.open(f"{FRONTEND_URL}/auth/register")
        print(f"   ✅ Page d'inscription ouverte")
        
        time.sleep(2)
        
        # Ouvrir la page de connexion avec message
        webbrowser.open(f"{FRONTEND_URL}/auth/login?message=account-created")
        print(f"   ✅ Page de connexion avec message ouverte")
        
    except Exception as e:
        print(f"   ⚠️  Erreur ouverture navigateur: {e}")
        print(f"   💡 Ouvrez manuellement: {FRONTEND_URL}/auth/register")


def main():
    """Fonction principale"""
    success, user_data = test_registration_flow()
    
    print_test_instructions(user_data)
    
    # Demander si on veut ouvrir le navigateur
    print(f"\n❓ Voulez-vous ouvrir les pages de test dans le navigateur ? (y/n)")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', 'o', 'oui']:
            open_test_pages()
    except KeyboardInterrupt:
        print(f"\n👋 Test interrompu")
    
    if success:
        print(f"\n🎉 SYSTÈME DE MESSAGES PRÊT À TESTER !")
        print(f"✅ Backend + Frontend + Messages fonctionnels")
    else:
        print(f"\n⚠️  PROBLÈME AVEC L'INSCRIPTION")
        print(f"🔧 Vérifiez que le backend est démarré")
    
    print(f"\n🏆 FONCTIONNALITÉS IMPLÉMENTÉES :")
    print(f"   ✅ Message de confirmation d'inscription")
    print(f"   ✅ Style professionnel avec icône")
    print(f"   ✅ Disparition automatique après 10s")
    print(f"   ✅ Nettoyage de l'URL")
    print(f"   ✅ Gestion d'erreurs spécifiques")
    print(f"   ✅ Redirection après inscription")
    
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
