{"tabs": {"00": {"tab_id": "00", "cwd": "/data/chats/u2nimd/workspace/fastapi_template", "observer": {"block": "Terminal", "uuid": "fcba4250-5b6d-451a-afa3-e00e64eaac1f", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "01": {"tab_id": "01", "cwd": "/data/chats/u2nimd/workspace/fastapi_template", "observer": {"block": "Terminal", "uuid": "863881aa-df56-49c1-b824-43d96d281ba0", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "02": {"tab_id": "02", "cwd": "/data/chats/u2nimd/workspace", "observer": {"block": "Terminal", "uuid": "fa442d4f-bc10-482c-a794-39fb190df430", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "03": {"tab_id": "03", "cwd": "/data/chats/u2nimd/workspace/orbis-frontend", "observer": {"block": "Terminal", "uuid": "4409b51e-677b-4f31-b604-0a533799a156", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "04": {"tab_id": "04", "cwd": "/data/chats/u2nimd/workspace/fastapi_template", "observer": {"block": "Terminal", "uuid": "0221e491-2a7d-43d1-8c55-0d3f5fe4ae08", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "05": {"tab_id": "05", "cwd": "/data/chats/u2nimd/workspace/fastapi_template", "observer": {"block": "Terminal", "uuid": "1b241996-ed3f-47f0-b043-8d798297af59", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "06": {"tab_id": "06", "cwd": "/data/chats/u2nimd/workspace/fastapi_template", "observer": {"block": "Terminal", "uuid": "8fd2947f-b06f-4a3b-82b3-bdb8edeb6ead", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "07": {"tab_id": "07", "cwd": "/data/chats/u2nimd/workspace/orbis-frontend", "observer": {"block": "Terminal", "uuid": "1ee8d150-321c-4437-af4e-e3d680aa3609", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "08": {"tab_id": "08", "cwd": "/data/chats/u2nimd/workspace/fastapi_template", "observer": {"block": "Terminal", "uuid": "3b95887a-55f6-4e63-bf0e-1c7e759f975d", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "09": {"tab_id": "09", "cwd": "/data/chats/u2nimd/workspace/fastapi_template", "observer": {"block": "Terminal", "uuid": "c7435089-606d-4e9e-a781-133b2531ed04", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "10": {"tab_id": "10", "cwd": "/data/chats/u2nimd/workspace/fastapi_template", "observer": {"block": "Terminal", "uuid": "c047b5a8-c0d7-446d-9044-ccb06964fb6b", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "11": {"tab_id": "11", "cwd": "/data/chats/u2nimd/workspace/orbis-frontend", "observer": {"block": "Terminal", "uuid": "e22bad3c-a3a7-48cf-ab00-6ff445ef5f63", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "12": {"tab_id": "12", "cwd": "/data/chats/u2nimd/workspace/fastapi_template", "observer": {"block": "Terminal", "uuid": "825fa35a-3761-4f96-9682-d6b1244d7137", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "13": {"tab_id": "13", "cwd": "/data/chats/u2nimd/workspace/orbis-frontend", "observer": {"block": "Terminal", "uuid": "d6a0b423-fcd2-4670-b151-f158118e2b45", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}, "14": {"tab_id": "14", "cwd": "/data/chats/u2nimd/workspace/orbis-frontend", "observer": {"block": "Terminal", "uuid": "4a0bbc1e-4ca3-4fd7-853f-cf6065710440", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}}, "current_tab_id": "14", "forbidden_commands": {"run preview": "Use Deployer.deploy_to_public instead.", "serve ": "Use Deployer.deploy_to_public instead.", "python -m http.server": "Use python -u -m http.server port_number instead."}, "timeout": 300.0}