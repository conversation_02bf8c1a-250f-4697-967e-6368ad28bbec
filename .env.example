# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/orbis_db

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application Settings
API_V1_STR=/api/v1
PROJECT_NAME=ORBIS Suivi Travaux

# CORS Settings
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]

# File Upload Settings
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB in bytes

# Email Settings (Optional)
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Development Settings
DEBUG=True
TESTING=False