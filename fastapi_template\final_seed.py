#!/usr/bin/env python3
"""
Script final de peuplement - utilise asyncpg directement
"""

import asyncio
import asyncpg
from datetime import datetime, timedelta

# Configuration
DATABASE_URL = "**************************************************************************************************/postgres"


async def seed_database():
    """Peupler la base de données"""
    print("🚀 ORBIS - Peuplement Final de la Base de Données")
    print("="*60)
    
    # Connexion avec statement_cache_size=0
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier les données existantes
        print("🔍 Vérification des données existantes...")
        company_count = await conn.fetchval("SELECT COUNT(*) FROM companies")
        user_count = await conn.fetchval("SELECT COUNT(*) FROM users")
        project_count = await conn.fetchval("SELECT COUNT(*) FROM projects")
        
        print(f"   📊 Entreprises: {company_count}")
        print(f"   📊 Utilisateurs: {user_count}")
        print(f"   📊 Projets: {project_count}")
        
        # 1. Obtenir ou créer l'entreprise
        print("\n📊 Gestion de l'entreprise...")
        company_id = await conn.fetchval("SELECT id FROM companies WHERE code = $1", "EITP001")
        
        if not company_id:
            company_id = await conn.fetchval("""
                INSERT INTO companies (name, code, siret, address, phone, email, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING id
            """, 
            "EITP Construction", "EITP001", "12345678901234", 
            "123 Rue de la Construction, 75001 Paris", 
            "***********.89", "<EMAIL>",
            datetime.utcnow(), datetime.utcnow())
            print(f"   ✅ Nouvelle entreprise créée: ID {company_id}")
        else:
            print(f"   ✅ Entreprise existante trouvée: ID {company_id}")
        
        # 2. Créer des utilisateurs (si pas déjà existants)
        print("\n👥 Gestion des utilisateurs...")
        users_data = [
            ("<EMAIL>", "Admin", "EITP", "ADMIN"),
            ("<EMAIL>", "Chef", "Projet", "CHEF_PROJET"),
            ("<EMAIL>", "Employé", "Test", "EMPLOYE")
        ]
        
        user_ids = []
        for email, first_name, last_name, role in users_data:
            # Vérifier si l'utilisateur existe
            existing_user = await conn.fetchval("SELECT id FROM users WHERE email = $1", email)
            
            if existing_user:
                user_ids.append(existing_user)
                print(f"   ✅ Utilisateur existant: {email}")
            else:
                user_id = await conn.fetchval("""
                    INSERT INTO users (email, first_name, last_name, hashed_password, role, is_active, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    RETURNING id
                """, 
                email, first_name, last_name, f"hashed_{email.split('@')[0]}", 
                role, True, datetime.utcnow(), datetime.utcnow())
                user_ids.append(user_id)
                print(f"   ✅ Nouvel utilisateur créé: {email} (ID: {user_id})")
                
                # Associer à l'entreprise
                await conn.execute("""
                    INSERT INTO user_companies (user_id, company_id, is_default, created_at)
                    VALUES ($1, $2, $3, $4)
                """, user_id, company_id, True, datetime.utcnow())
        
        # 3. Créer des projets
        print("\n🏗️ Gestion des projets...")
        projects_data = [
            ("Rénovation Immeuble Haussmann", "PROJ001", "Rénovation complète d'un immeuble haussmannien", 500000.00),
            ("Construction Villa Moderne", "PROJ002", "Construction d'une villa moderne avec piscine", 750000.00)
        ]
        
        for name, code, description, budget in projects_data:
            existing_project = await conn.fetchval("SELECT id FROM projects WHERE code = $1", code)
            
            if existing_project:
                print(f"   ✅ Projet existant: {name}")
            else:
                project_id = await conn.fetchval("""
                    INSERT INTO projects (name, code, description, status, budget_total, company_id, start_date, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    RETURNING id
                """, 
                name, code, description, "DAO", budget, company_id,
                datetime.utcnow() - timedelta(days=30), datetime.utcnow(), datetime.utcnow())
                print(f"   ✅ Nouveau projet créé: {name} (ID: {project_id})")
        
        # 4. Créer des employés
        print("\n👷 Gestion des employés...")
        employees_data = [
            ("EMP001", "Jean", "Dupont", "Maçon", 25.50),
            ("EMP002", "Marie", "Martin", "Électricienne", 28.00)
        ]
        
        for emp_number, first_name, last_name, position, hourly_rate in employees_data:
            existing_employee = await conn.fetchval("SELECT id FROM employees WHERE employee_number = $1", emp_number)
            
            if existing_employee:
                print(f"   ✅ Employé existant: {first_name} {last_name}")
            else:
                employee_id = await conn.fetchval("""
                    INSERT INTO employees (employee_number, first_name, last_name, position, hourly_rate, company_id, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    RETURNING id
                """, 
                emp_number, first_name, last_name, position, hourly_rate, company_id,
                datetime.utcnow(), datetime.utcnow())
                print(f"   ✅ Nouvel employé créé: {first_name} {last_name} (ID: {employee_id})")
        
        # 5. Créer des fournisseurs
        print("\n🏪 Gestion des fournisseurs...")
        suppliers_data = [
            ("Matériaux Pro", "SUPP001", "Fournisseur de matériaux", "01.11.22.33.44", "<EMAIL>"),
            ("Électro Services", "SUPP002", "Matériel électrique", "01.55.66.77.88", "<EMAIL>")
        ]
        
        for name, code, supplier_type, phone, email in suppliers_data:
            existing_supplier = await conn.fetchval("SELECT id FROM suppliers WHERE code = $1", code)
            
            if existing_supplier:
                print(f"   ✅ Fournisseur existant: {name}")
            else:
                supplier_id = await conn.fetchval("""
                    INSERT INTO suppliers (name, code, type, phone, email, company_id, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    RETURNING id
                """, 
                name, code, "supplier", phone, email, company_id,
                datetime.utcnow(), datetime.utcnow())
                print(f"   ✅ Nouveau fournisseur créé: {name} (ID: {supplier_id})")
        
        # Vérification finale
        print("\n🔍 Vérification finale...")
        final_counts = {
            'companies': await conn.fetchval("SELECT COUNT(*) FROM companies"),
            'users': await conn.fetchval("SELECT COUNT(*) FROM users"),
            'projects': await conn.fetchval("SELECT COUNT(*) FROM projects"),
            'employees': await conn.fetchval("SELECT COUNT(*) FROM employees"),
            'suppliers': await conn.fetchval("SELECT COUNT(*) FROM suppliers")
        }
        
        for table, count in final_counts.items():
            print(f"   📊 {table}: {count} enregistrements")
        
        print("\n🎉 Peuplement terminé avec succès!")
        print("\n🔑 Identifiants de test:")
        print("   - <EMAIL> / admin")
        print("   - <EMAIL> / chef")
        print("   - <EMAIL> / employe")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


if __name__ == "__main__":
    success = asyncio.run(seed_database())
    exit(0 if success else 1)
