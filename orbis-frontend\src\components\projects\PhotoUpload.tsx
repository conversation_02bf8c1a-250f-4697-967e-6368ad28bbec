import React, { useRef, useState } from 'react'
import { PhotoIcon, TrashIcon } from '@heroicons/react/24/outline'

interface PhotoUploadProps {
  currentPhotoUrl?: string | null
  onUpload: (file: File) => Promise<void>
  onDelete?: () => Promise<void>
  loading?: boolean
  disabled?: boolean
  className?: string
  // Nouvelle prop pour forcer la mise à jour
  photoDeleted?: boolean
  // Dimensions spécifiques pour les projets
  width?: number
  height?: number
}

export default function PhotoUpload({
  currentPhotoUrl,
  onUpload,
  onDelete,
  loading = false,
  disabled = false,
  className = '',
  photoDeleted = false,
  width = 300,
  height = 200
}: PhotoUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [dragOver, setDragOver] = useState(false)
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

  const handleClick = () => {
    if (!disabled && !loading) {
      fileInputRef.current?.click()
    }
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      await handleFileUpload(file)
    }
    // Reset l'input pour permettre de sélectionner le même fichier
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleFileUpload = async (file: File) => {
    // Vérifier le type de fichier
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      alert('Type de fichier non autorisé. Utilisez: JPG, PNG, GIF ou WebP')
      return
    }

    // Vérifier la taille (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('Fichier trop volumineux (max 10MB)')
      return
    }

    try {
      await onUpload(file)
    } catch (error) {
      console.error('Erreur upload:', error)
      alert('Erreur lors de l\'upload de la photo')
    }
  }

  const handleDrop = async (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(false)

    if (disabled || loading) return

    const files = Array.from(event.dataTransfer.files)
    const imageFile = files.find(file => file.type.startsWith('image/'))

    if (imageFile) {
      await handleFileUpload(imageFile)
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    if (!disabled && !loading) {
      setDragOver(true)
    }
  }

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(false)
  }

  const handleDelete = async (event: React.MouseEvent) => {
    event.stopPropagation()
    if (onDelete && !loading && !disabled) {
      try {
        await onDelete()
      } catch (error) {
        console.error('Erreur suppression:', error)
        alert('Erreur lors de la suppression de la photo')
      }
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled || loading}
      />

      {/* Zone d'affichage de la photo actuelle */}
      {currentPhotoUrl && !photoDeleted && (
        <div className="relative inline-block">
          <div 
            className="border-2 border-gray-200 rounded-lg overflow-hidden bg-white cursor-pointer hover:border-gray-400 transition-colors"
            style={{ width: `${width}px`, height: `${height}px` }}
            onClick={handleClick}
            title="Cliquer pour remplacer la photo"
          >
            <img
              src={`${API_BASE_URL}${currentPhotoUrl}`}
              alt="Photo du projet"
              className="w-full h-full object-cover"
              onError={(e) => {
                console.error('Erreur de chargement de la photo:', currentPhotoUrl)
                // Fallback en cas d'erreur
                e.currentTarget.style.display = 'none'
              }}
            />
          </div>
          
          {/* Bouton de suppression */}
          {onDelete && (
            <button
              onClick={handleDelete}
              disabled={loading || disabled}
              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 disabled:opacity-50"
              title="Supprimer la photo"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          )}
          
          {/* Indicateur de remplacement */}
          <div className="mt-2 text-center">
            <p className="text-xs text-gray-500">
              Cliquer sur l'image pour remplacer
            </p>
          </div>
        </div>
      )}

      {/* Zone de drop/upload */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${dragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300'}
          ${disabled || loading ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400'}
        `}
        style={{ width: `${width}px`, minHeight: currentPhotoUrl && !photoDeleted ? '80px' : `${height}px` }}
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {loading ? (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <p className="mt-2 text-sm text-gray-600">Upload en cours...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full">
            <PhotoIcon className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-sm text-gray-600 mb-2">
              {currentPhotoUrl && !photoDeleted ? 'Ajouter une nouvelle photo' : 'Cliquez ou glissez-déposez une photo'}
            </p>
            <p className="text-xs text-gray-500">
              PNG, JPG, GIF ou WebP (max 10MB)
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Dimensions recommandées: {width}x{height}px
            </p>
          </div>
        )}
      </div>

      {/* Affichage des erreurs */}
      {/* Note: Les erreurs sont gérées par le composant parent via le hook */}
    </div>
  )
}
