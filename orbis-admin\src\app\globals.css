@import "tailwindcss";

/* ORBIS Admin - Design System */
:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Teal Color Palette - Primary */
  --teal-50: #f0fdfa;
  --teal-100: #ccfbf1;
  --teal-200: #99f6e4;
  --teal-300: #5eead4;
  --teal-400: #2dd4bf;
  --teal-500: #14b8a6;
  --teal-600: #0d9488;
  --teal-700: #0f766e;
  --teal-800: #115e59;
  --teal-900: #134e4a;
  --teal-950: #042f2e;

  /* Cyan Color Palette - Secondary */
  --cyan-50: #ecfeff;
  --cyan-100: #cffafe;
  --cyan-200: #a5f3fc;
  --cyan-300: #67e8f9;
  --cyan-400: #22d3ee;
  --cyan-500: #06b6d4;
  --cyan-600: #0891b2;
  --cyan-700: #0e7490;
  --cyan-800: #155e75;
  --cyan-900: #164e63;
  --cyan-950: #083344;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

/* Modern Components */
@layer components {
  .modern-card {
    @apply bg-white rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300;
  }

  .sidebar-item {
    @apply flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200;
  }

  .sidebar-item.active {
    @apply bg-teal-50 text-teal-700 border-l-4 border-teal-600 pl-2;
  }

  .sidebar-item:not(.active) {
    @apply text-gray-700 hover:bg-gray-50 hover:text-teal-600;
  }

  .btn-primary {
    @apply bg-teal-600 text-white hover:bg-teal-700 active:bg-teal-800 focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-cyan-100 text-cyan-700 hover:bg-cyan-200 active:bg-cyan-300 focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }

  .btn-outline {
    @apply border border-teal-300 bg-white text-teal-700 hover:border-teal-400 hover:bg-teal-50 active:bg-teal-100 focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }

  .form-input {
    @apply rounded-lg border-gray-300 shadow-sm focus:border-teal-500 focus:ring-2 focus:ring-teal-500/25 transition-all duration-200;
  }

  .badge-success {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800;
  }

  .badge-warning {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800;
  }

  .badge-info {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
  }
}

/* Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}
