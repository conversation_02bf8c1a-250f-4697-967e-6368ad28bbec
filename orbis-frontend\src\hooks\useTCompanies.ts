import { useState, useEffect, useCallback } from 'react'
import { FastAuthService } from '@/lib/auth'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export interface TCompany {
  id: number
  company_name: string
  activity?: string
  address?: string
  postal_code?: string
  city?: string
  country?: string
  phone?: string
  fax?: string
  email?: string
  siret?: string
  vat_number?: string
  legal_representative_id?: number
  logo_url?: string
  logo_filename?: string
  workspace_id: number
  is_active: boolean
  created_at: string
  updated_at: string
  created_by?: number
  legal_representative_name?: string
  legal_representative_email?: string
}

export function useTCompanies() {
  const [tcompanies, setTCompanies] = useState<TCompany[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTCompanies = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const token = FastAuthService.getToken()
      if (!token) {
        throw new Error('Token d\'authentification manquant')
      }

      console.log('🔄 Récupération des TCompanies...')
      
      const response = await fetch(`${API_BASE_URL}/api/v1/tcompanies/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      console.log('📊 Réponse TCompanies:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Erreur API TCompanies:', errorText)
        throw new Error(`Erreur lors de la récupération des TCompanies: ${response.status}`)
      }

      const data = await response.json()
      console.log('✅ TCompanies récupérées:', data.length)
      setTCompanies(data)
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la récupération des TCompanies'
      console.error('❌ Erreur:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchTCompanies()
  }, [fetchTCompanies])

  return {
    tcompanies,
    loading,
    error,
    refresh: fetchTCompanies
  }
}
