#!/usr/bin/env python3
"""
Script simple pour corriger la table technical_documents
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(__file__))

from sqlalchemy import text
from app.core.database import get_db

async def fix_table():
    """Corrige la table en plusieurs étapes séparées"""
    print("🔄 Correction de la table technical_documents...")
    
    async for db in get_db():
        try:
            # Étape 1: Vérifier l'état actuel
            print("\n1. État actuel de la table:")
            result = await db.execute(text("""
                SELECT column_name, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'technical_documents' 
                AND column_name IN ('project_id', 'lot_id')
                ORDER BY column_name
            """))
            columns = {row[0]: row[1] for row in result.fetchall()}
            print(f"Colonnes trouvées: {columns}")
            
            # Étape 2: Migrer les données si nécessaire
            if 'project_id' in columns and 'lot_id' in columns:
                print("\n2. Migration des données...")
                await db.execute(text("""
                    UPDATE technical_documents 
                    SET lot_id = (
                        SELECT l.id 
                        FROM lots l 
                        WHERE l.project_id = technical_documents.project_id 
                        LIMIT 1
                    )
                    WHERE lot_id IS NULL
                """))
                await db.commit()
                print("✅ Données migrées")
            
            # Étape 3: Rendre lot_id NOT NULL
            if 'lot_id' in columns and columns['lot_id'] == 'YES':
                print("\n3. Modification de lot_id en NOT NULL...")
                await db.execute(text("""
                    ALTER TABLE technical_documents 
                    ALTER COLUMN lot_id SET NOT NULL
                """))
                await db.commit()
                print("✅ lot_id est maintenant NOT NULL")
            
            # Étape 4: Supprimer project_id
            if 'project_id' in columns:
                print("\n4. Suppression de project_id...")
                
                # D'abord, essayer de supprimer les contraintes FK possibles
                constraints_to_try = [
                    'technical_documents_project_id_fkey',
                    'fk_technical_documents_project_id'
                ]
                
                for constraint in constraints_to_try:
                    try:
                        await db.execute(text(f"""
                            ALTER TABLE technical_documents 
                            DROP CONSTRAINT {constraint}
                        """))
                        print(f"✅ Contrainte {constraint} supprimée")
                        await db.commit()
                        break
                    except Exception:
                        await db.rollback()
                        continue
                
                # Maintenant supprimer la colonne
                try:
                    await db.execute(text("""
                        ALTER TABLE technical_documents 
                        DROP COLUMN project_id
                    """))
                    await db.commit()
                    print("✅ Colonne project_id supprimée")
                except Exception as e:
                    await db.rollback()
                    print(f"⚠️  Erreur lors de la suppression de project_id: {e}")
            
            # Étape 5: Vérification finale
            print("\n5. Vérification finale...")
            result = await db.execute(text("""
                SELECT column_name, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'technical_documents' 
                ORDER BY ordinal_position
            """))
            columns = result.fetchall()
            
            print("Structure finale:")
            for col in columns:
                print(f"  - {col[0]}: nullable={col[1]}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur: {str(e)}")
            await db.rollback()
            return False

async def main():
    """Fonction principale"""
    print("🚀 Correction simple de la table technical_documents")
    print("=" * 50)
    
    success = await fix_table()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 CORRECTION RÉUSSIE!")
    else:
        print("❌ CORRECTION ÉCHOUÉE")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
