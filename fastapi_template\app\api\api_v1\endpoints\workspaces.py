# app/api/api_v1/endpoints/workspaces.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.api import deps
from app.models.user import User
from app.models.workspace import Workspace, UserWorkspace, WorkspaceSettings
from app.schemas.workspace import (
    Workspace as WorkspaceSchema, 
    WorkspaceCreate, 
    WorkspaceUpdate, 
    WorkspaceSettings as WorkspaceSettingsSchema,
    WorkspaceWithUsers,
    UserWorkspace as UserWorkspaceSchema,
    UserWorkspaceCreate
)
from app.middleware.auth_sync_middleware import get_current_user

router = APIRouter()

@router.get("/", response_model=List[WorkspaceSchema])
async def read_workspaces(
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Retrieve workspaces accessible by current user.
    """
    # Récupérer l'utilisateur depuis le middleware
    current_user = get_current_user(request)
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid user token")

    # Récupérer les associations user-workspace
    result = await db.execute(
        select(UserWorkspace).where(UserWorkspace.user_id == user_id)
    )
    user_workspaces = result.scalars().all()
    
    workspace_ids = [uw.workspace_id for uw in user_workspaces]
    
    if not workspace_ids:
        return []
    
    # Récupérer les workspaces
    result = await db.execute(
        select(Workspace).where(Workspace.id.in_(workspace_ids)).offset(skip).limit(limit)
    )
    workspaces = result.scalars().all()
    return workspaces

@router.post("/", response_model=WorkspaceSchema)
async def create_workspace(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    workspace_in: WorkspaceCreate,
) -> Any:
    """
    Create new workspace.
    """
    # Récupérer l'utilisateur depuis le middleware
    current_user = get_current_user(request)
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid user token")

    # Check if code already exists
    result = await db.execute(select(Workspace).where(Workspace.code == workspace_in.code))
    if result.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="Workspace code already exists")
    
    # Check workspace limit
    result = await db.execute(select(UserWorkspace).where(UserWorkspace.user_id == user_id))
    user_workspace_count = len(result.scalars().all())
    if user_workspace_count >= 10:
        raise HTTPException(status_code=400, detail="Maximum number of workspaces reached (10)")
    
    workspace = Workspace(**workspace_in.dict())
    db.add(workspace)
    await db.commit()
    await db.refresh(workspace)
    
    # Add current user as admin of the new workspace
    user_workspace = UserWorkspace(
        user_id=user_id,
        workspace_id=workspace.id,
        role_name="ADMIN",
        is_default=user_workspace_count == 0,  # First workspace is default
        is_active=True
    )
    db.add(user_workspace)
    
    # Create default settings for the workspace
    workspace_settings = WorkspaceSettings(
        workspace_id=workspace.id,
        default_currency="EUR",
        language="fr"
    )
    db.add(workspace_settings)
    
    await db.commit()
    return workspace

@router.get("/{workspace_id}", response_model=WorkspaceSchema)
async def read_workspace(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    workspace_id: int,
) -> Any:
    """
    Get workspace by ID.
    """
    # Récupérer l'utilisateur depuis le middleware
    current_user = get_current_user(request)
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid user token")

    # Check if user has access to this workspace
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == user_id,
            UserWorkspace.workspace_id == workspace_id,
            UserWorkspace.is_active == True
        )
    )
    user_workspace = result.scalar_one_or_none()
    
    if not user_workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    result = await db.execute(select(Workspace).where(Workspace.id == workspace_id))
    workspace = result.scalar_one_or_none()
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    return workspace

@router.put("/{workspace_id}", response_model=WorkspaceSchema)
async def update_workspace(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    workspace_id: int,
    workspace_in: WorkspaceUpdate,
) -> Any:
    """
    Update a workspace.
    """
    # Récupérer l'utilisateur depuis le middleware
    current_user = get_current_user(request)
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid user token")

    # Check if user has admin access to this workspace
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == user_id,
            UserWorkspace.workspace_id == workspace_id,
            UserWorkspace.role_name.in_(["ADMIN", "MANAGER"]),
            UserWorkspace.is_active == True
        )
    )
    user_workspace = result.scalar_one_or_none()
    
    if not user_workspace:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    result = await db.execute(select(Workspace).where(Workspace.id == workspace_id))
    workspace = result.scalar_one_or_none()
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    # Check if new code already exists (if provided)
    if workspace_in.code and workspace_in.code != workspace.code:
        result = await db.execute(select(Workspace).where(Workspace.code == workspace_in.code))
        if result.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="Workspace code already exists")
    
    update_data = workspace_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(workspace, field, value)
    
    db.add(workspace)
    await db.commit()
    await db.refresh(workspace)
    return workspace

@router.delete("/{workspace_id}")
async def delete_workspace(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    workspace_id: int,
) -> Any:
    """
    Delete a workspace.
    """
    # Récupérer l'utilisateur depuis le middleware
    current_user = get_current_user(request)
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid user token")

    # Check if user is admin of this workspace
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == user_id,
            UserWorkspace.workspace_id == workspace_id,
            UserWorkspace.role_name == "ADMIN",
            UserWorkspace.is_active == True
        )
    )
    user_workspace = result.scalar_one_or_none()
    
    if not user_workspace:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    result = await db.execute(select(Workspace).where(Workspace.id == workspace_id))
    workspace = result.scalar_one_or_none()
    if not workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    # Soft delete by setting is_active to False
    workspace.is_active = False
    db.add(workspace)
    await db.commit()
    
    return {"message": "Workspace deleted successfully"}

@router.get("/{workspace_id}/users", response_model=List[UserWorkspaceSchema])
async def read_workspace_users(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    workspace_id: int,
) -> Any:
    """
    Get all users in a workspace.
    """
    # Récupérer l'utilisateur depuis le middleware
    current_user = get_current_user(request)
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid user token")

    # Check if user has access to this workspace
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == user_id,
            UserWorkspace.workspace_id == workspace_id,
            UserWorkspace.is_active == True
        )
    )
    user_workspace = result.scalar_one_or_none()
    
    if not user_workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.workspace_id == workspace_id,
            UserWorkspace.is_active == True
        )
    )
    workspace_users = result.scalars().all()
    
    return workspace_users

@router.post("/{workspace_id}/users", response_model=UserWorkspaceSchema)
async def add_user_to_workspace(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    workspace_id: int,
    user_workspace_in: UserWorkspaceCreate,
) -> Any:
    """
    Add a user to a workspace.
    """
    # Récupérer l'utilisateur depuis le middleware
    current_user = get_current_user(request)
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid user token")

    # Check if current user is admin/manager of this workspace
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == user_id,
            UserWorkspace.workspace_id == workspace_id,
            UserWorkspace.role_name.in_(["ADMIN", "MANAGER"]),
            UserWorkspace.is_active == True
        )
    )
    current_user_workspace = result.scalar_one_or_none()
    
    if not current_user_workspace:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    # Check if user is already in workspace
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == user_workspace_in.user_id,
            UserWorkspace.workspace_id == workspace_id
        )
    )
    existing = result.scalar_one_or_none()
    
    if existing:
        if existing.is_active:
            raise HTTPException(status_code=400, detail="User already in workspace")
        else:
            # Reactivate existing relationship
            existing.is_active = True
            existing.role_name = user_workspace_in.role_name
            db.add(existing)
            await db.commit()
            await db.refresh(existing)
            return existing
    
    # Create new user-workspace relationship
    user_workspace = UserWorkspace(
        user_id=user_workspace_in.user_id,
        workspace_id=workspace_id,
        role_name=user_workspace_in.role_name,
        invited_by=user_id,
        is_active=True
    )
    db.add(user_workspace)
    await db.commit()
    await db.refresh(user_workspace)
    
    return user_workspace

@router.get("/{workspace_id}/settings", response_model=WorkspaceSettingsSchema)
async def read_workspace_settings(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    workspace_id: int,
) -> Any:
    """
    Get workspace settings.
    """
    # Récupérer l'utilisateur depuis le middleware
    current_user = get_current_user(request)
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Invalid user token")

    # Check if user has access to this workspace
    result = await db.execute(
        select(UserWorkspace).where(
            UserWorkspace.user_id == user_id,
            UserWorkspace.workspace_id == workspace_id,
            UserWorkspace.is_active == True
        )
    )
    user_workspace = result.scalar_one_or_none()
    
    if not user_workspace:
        raise HTTPException(status_code=404, detail="Workspace not found")
    
    result = await db.execute(
        select(WorkspaceSettings).where(WorkspaceSettings.workspace_id == workspace_id)
    )
    settings = result.scalar_one_or_none()
    
    if not settings:
        # Create default settings if they don't exist
        settings = WorkspaceSettings(
            workspace_id=workspace_id,
            default_currency="EUR",
            language="fr"
        )
        db.add(settings)
        await db.commit()
        await db.refresh(settings)
    
    return settings
