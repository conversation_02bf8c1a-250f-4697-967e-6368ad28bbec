'use client'

import { useRouter } from 'next/navigation'
import { Building2 } from 'lucide-react'

export default function DevLoginPage() {
  const router = useRouter()

  const handleDevLogin = () => {
    // Simuler une connexion en mode développement
    localStorage.setItem('dev-auth', JSON.stringify({
      user: {
        id: 'dev-admin',
        email: '<EMAIL>',
        user_metadata: {
          first_name: 'Admin',
          last_name: 'ORBIS',
          role: 'admin',
          company: 'ORBIS'
        }
      },
      session: {
        access_token: 'dev-token',
        expires_at: Date.now() + 24 * 60 * 60 * 1000 // 24h
      }
    }))
    
    alert('Mode développement activé! Redirection vers le dashboard...')
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-600 rounded-full p-3">
                <Building2 className="h-8 w-8 text-white" />
              </div>
            </div>
            <h2 className="text-3xl font-bold text-gray-900">Mode Développement</h2>
            <p className="mt-2 text-gray-600">
              Connexion sans authentification Supabase
            </p>
          </div>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
            <h4 className="text-sm font-medium text-orange-800 mb-2">⚠️ Mode Développement</h4>
            <p className="text-xs text-orange-700">
              Cette page permet de contourner l'authentification Supabase pour tester l'interface.
              À utiliser uniquement en développement.
            </p>
          </div>

          <button
            onClick={handleDevLogin}
            className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
          >
            Connexion Mode Développement
          </button>

          <div className="mt-6 text-center">
            <a
              href="/login"
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              ← Retour à la connexion normale
            </a>
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Identifiants simulés:</h4>
            <div className="text-xs text-gray-600 space-y-1">
              <div>Email: <EMAIL></div>
              <div>Rôle: Admin</div>
              <div>Entreprise: ORBIS</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
