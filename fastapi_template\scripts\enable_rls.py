#!/usr/bin/env python3
"""
Script pour activer Row Level Security (RLS) sur toutes les tables Supabase
"""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration de la base de données
DATABASE_URL = os.getenv("DATABASE_URL")

# Liste des tables qui nécessitent RLS
TABLES_TO_SECURE = [
    "tcompanies",
  
]

async def enable_rls_on_table(conn, table_name):
    """Active RLS sur une table spécifique"""
    try:
        # Activer RLS
        await conn.execute(f"ALTER TABLE public.{table_name} ENABLE ROW LEVEL SECURITY;")
        print(f"✅ RLS activé sur {table_name}")
        
        # Créer une politique par défaut pour permettre l'accès aux utilisateurs authentifiés
        policy_name = f"{table_name}_policy"
        
        # Supprimer la politique si elle existe déjà
        await conn.execute(f"""
            DROP POLICY IF EXISTS {policy_name} ON public.{table_name};
        """)
        
        # Créer une nouvelle politique
        await conn.execute(f"""
            CREATE POLICY {policy_name} ON public.{table_name}
            FOR ALL 
            USING (auth.role() = 'authenticated')
            WITH CHECK (auth.role() = 'authenticated');
        """)
        print(f"✅ Politique créée pour {table_name}")
        
    except Exception as e:
        print(f"❌ Erreur sur {table_name}: {e}")

async def main():
    """Fonction principale"""
    if not DATABASE_URL:
        print("❌ DATABASE_URL non trouvée dans les variables d'environnement")
        return
    
    try:
        # Connexion à la base de données
        conn = await asyncpg.connect(DATABASE_URL)
        print("🔗 Connexion à la base de données établie")
        
        # Activer RLS sur toutes les tables
        for table in TABLES_TO_SECURE:
            await enable_rls_on_table(conn, table)
        
        print("\n🎉 RLS activé sur toutes les tables avec succès!")
        
        # Fermer la connexion
        await conn.close()
        
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")

if __name__ == "__main__":
    asyncio.run(main())
