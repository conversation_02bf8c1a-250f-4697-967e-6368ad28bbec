# CRUD des Projets - Implémentation Complète

## Vue d'ensemble

J'ai implémenté un CRUD complet pour les projets avec authentification JWT sécurisée. Voici les détails de l'implémentation :

## 🔐 Authentification JWT

### Méthode d'identification utilisée :
- **JWT (JSON Web Tokens)** avec la bibliothèque `PyJWT`
- **Clé secrète** : Configurée via `settings.SECRET_KEY`
- **Algorithme** : HS256
- **Durée de vie** : 24 heures par défaut
- **Validation** : Vérification de l'expiration et de la signature

### Structure du token JWT :
```json
{
  "user_id": 123,
  "email": "<EMAIL>",
  "role": "ADMIN",
  "is_superuser": false,
  "token_type": "access",
  "exp": 1640995200,
  "iat": 1640991600
}
```

### Dépendance d'authentification :
- `require_auth` : Dépendance FastAPI qui extrait et valide le token JWT
- Gestion automatique des erreurs 401 (Unauthorized)
- Support des rôles et permissions

## 🗄️ Modèle de données mis à jour

### Nouveaux champs ajoutés au modèle Project :
```python
class ProjectStatus(str, enum.Enum):
    EN_COURS = "En cours"
    EN_ATTENTE = "En attente"
    TERMINE = "Terminé"
    ARCHIVE = "Archivé"

class ProjectNature(str, enum.Enum):
    DEVIS = "Devis"
    AO = "AO"
    AFFAIRE = "Affaire"

class Project(Base):
    # ... champs existants ...
    status = Column(Enum(ProjectStatus), default=ProjectStatus.EN_COURS)
    nature = Column(Enum(ProjectNature), default=ProjectNature.DEVIS)  # NOUVEAU
```

## 🚀 Endpoints CRUD implémentés

### 1. GET /api/v1/projects/
**Récupérer la liste des projets avec filtres**
- **Authentification** : JWT requis
- **Filtres** : status, nature, search (recherche textuelle)
- **Pagination** : skip, limit
- **Sécurité** : Filtrage automatique par entreprise (sauf super admins)

### 2. POST /api/v1/projects/
**Créer un nouveau projet**
- **Authentification** : JWT requis
- **Génération automatique** : Code unique du projet
- **Validation** : Unicité du code par entreprise
- **Association automatique** : company_id depuis le token JWT

### 3. GET /api/v1/projects/{project_id}
**Récupérer un projet spécifique**
- **Authentification** : JWT requis
- **Sécurité** : Accès limité aux projets de l'entreprise de l'utilisateur

### 4. PUT /api/v1/projects/{project_id}
**Mettre à jour un projet**
- **Authentification** : JWT requis
- **Mise à jour partielle** : Seuls les champs fournis sont modifiés
- **Sécurité** : Modification limitée aux projets de l'entreprise

### 5. DELETE /api/v1/projects/{project_id}
**Supprimer un projet (soft delete)**
- **Authentification** : JWT requis
- **Soft delete** : Archivage au lieu de suppression définitive
- **Sécurité** : Suppression limitée aux projets de l'entreprise

### 6. POST /api/v1/projects/{project_id}/restore
**Restaurer un projet archivé**
- **Authentification** : JWT requis
- **Fonctionnalité** : Désarchivage d'un projet

### 7. GET /api/v1/projects/stats/summary
**Statistiques des projets**
- **Authentification** : JWT requis
- **Données** : Compteurs par statut, nature, total actif/archivé

## 🎨 Frontend mis à jour

### Service API TypeScript
- **Fichier** : `orbis-frontend/src/lib/api/projects.ts`
- **Classe** : `ProjectsAPI` avec toutes les méthodes CRUD
- **Authentification** : Gestion automatique des headers JWT
- **Types** : Interfaces TypeScript complètes

### Pages mises à jour
1. **Liste des projets** (`/projects`) : Utilise la nouvelle API
2. **Création de projet** (`/projects/create`) : Utilise la nouvelle API
3. **Sidebar** : Renommage "Projets" → "Dossiers" avec sous-menus

### Fonctionnalités frontend
- **Filtrage** : Par nature (Devis, AO, Affaires) via URL et boutons
- **Recherche** : Textuelle dans nom, code, description, client
- **Tri** : Boutons TOUT, DEVIS, AO, AFFAIRES
- **Navigation** : Sous-menus dans la sidebar

## 🔧 Migration de base de données

### Fichier de migration
- **Fichier** : `alembic/versions/add_project_nature.py`
- **Ajouts** : 
  - Enum `ProjectNature`
  - Colonne `nature` dans la table `projects`
  - Mise à jour des valeurs `ProjectStatus`

### Commandes pour appliquer la migration
```bash
cd fastapi_template
alembic upgrade head
```

## 🧪 Tests

### Script de test complet
- **Fichier** : `test_projects_crud.py`
- **Tests** :
  - Authentification JWT
  - Création, lecture, mise à jour, suppression
  - Filtres et recherche
  - Statistiques
  - Gestion des erreurs d'authentification

### Exécution des tests
```bash
python test_projects_crud.py
```

## 🔒 Sécurité implémentée

1. **Authentification obligatoire** : Tous les endpoints nécessitent un JWT valide
2. **Isolation par entreprise** : Les utilisateurs ne voient que les projets de leur entreprise
3. **Validation des permissions** : Vérification du rôle et des permissions
4. **Soft delete** : Archivage au lieu de suppression définitive
5. **Validation des données** : Schémas Pydantic pour la validation

## 🚀 Démarrage

### Backend
```bash
cd fastapi_template
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### Frontend
```bash
cd orbis-frontend
npm run dev
```

### Test de l'authentification
1. Se connecter avec : `<EMAIL>` / `orbis123!`
2. Naviguer vers `/projects`
3. Tester la création, modification, suppression de projets
4. Vérifier les filtres par nature (Devis, AO, Affaires)

## 📝 Notes importantes

- **Compatibilité** : L'ancien endpoint `/projects-old` est conservé pour référence
- **Performance** : Requêtes optimisées avec `selectinload` pour les relations
- **Évolutivité** : Architecture prête pour l'ajout de nouvelles fonctionnalités
- **Maintenance** : Code bien structuré et documenté
