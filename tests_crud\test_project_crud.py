"""
Tests CRUD complets pour l'entité Project
Tests SQLAlchemy et Pydantic avec relations multi-tenant
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from fastapi.testclient import TestClient
from fastapi import status

# Imports des modèles et schémas
from app.models.project import Project, ProjectStatus, ProjectNature, ProjectCompany
from app.models.workspace import Workspace
from app.models.entreprise_tiers import EntrepriseTiers
from app.models.user import User, UserRole
from app.schemas.project import ProjectCreate, ProjectUpdate, ProjectResponse
from app.core.database import get_db, engine
from app.core.security import create_access_token
from app.main import app

# Configuration des tests
pytestmark = pytest.mark.asyncio

class TestProjectCRUD:
    """Tests CRUD pour l'entité Project"""
    
    @pytest.fixture
    async def db_session(self):
        """Fixture pour la session de base de données"""
        async with AsyncSession(engine) as session:
            yield session
            await session.rollback()
    
    @pytest.fixture
    async def test_workspace(self, db_session: AsyncSession):
        """Fixture pour créer un workspace de test"""
        workspace = Workspace(
            name="Test Workspace",
            code="TEST_WS",
            description="Workspace pour les tests",
            is_active=True
        )
        db_session.add(workspace)
        await db_session.commit()
        await db_session.refresh(workspace)
        return workspace
    
    @pytest.fixture
    async def test_entreprise_tiers(self, db_session: AsyncSession, test_workspace: Workspace):
        """Fixture pour créer une entreprise tierce de test"""
        entreprise = EntrepriseTiers(
            nom_entreprise="Entreprise Test",
            activite="Construction",
            adresse="123 Rue de Test",
            code_postal="75001",
            ville="Paris",
            email="<EMAIL>",
            siret="12345678901234",
            workspace_id=test_workspace.id,
            is_active=True
        )
        db_session.add(entreprise)
        await db_session.commit()
        await db_session.refresh(entreprise)
        return entreprise
    
    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        """Fixture pour créer un utilisateur de test"""
        user = User(
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            role=UserRole.CHEF_PROJET,
            is_active=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user
    
    @pytest.fixture
    def client(self):
        """Client de test FastAPI"""
        return TestClient(app)
    
    # Tests SQLAlchemy
    
    async def test_create_project_sqlalchemy(self, db_session: AsyncSession, test_workspace: Workspace):
        """Test création d'un projet avec SQLAlchemy"""
        # Données de test
        project_data = {
            "name": "Projet Test",
            "code": "PROJ_001",
            "description": "Description du projet de test",
            "status": ProjectStatus.EN_COURS,
            "nature": ProjectNature.AFFAIRE,
            "start_date": datetime.utcnow(),
            "end_date": datetime.utcnow() + timedelta(days=90),
            "budget_total": Decimal("150000.00"),
            "address": "123 Rue du Projet",
            "client_name": "Client Test",
            "client_contact": "<EMAIL>",
            "workspace_id": test_workspace.id,
            "is_archived": False
        }
        
        # Création
        project = Project(**project_data)
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)
        
        # Vérifications
        assert project.id is not None
        assert project.name == "Projet Test"
        assert project.code == "PROJ_001"
        assert project.status == ProjectStatus.EN_COURS
        assert project.nature == ProjectNature.AFFAIRE
        assert project.budget_total == Decimal("150000.00")
        assert project.workspace_id == test_workspace.id
        assert project.created_at is not None
        assert project.updated_at is not None
        assert not project.is_archived
    
    async def test_read_project_sqlalchemy(self, db_session: AsyncSession, test_workspace: Workspace):
        """Test lecture d'un projet avec SQLAlchemy"""
        # Création d'un projet
        project = Project(
            name="Projet Lecture",
            code="READ_001",
            workspace_id=test_workspace.id,
            status=ProjectStatus.EN_ATTENTE
        )
        db_session.add(project)
        await db_session.commit()
        
        # Lecture par ID
        result = await db_session.execute(select(Project).where(Project.id == project.id))
        found_project = result.scalar_one_or_none()
        
        assert found_project is not None
        assert found_project.name == "Projet Lecture"
        assert found_project.code == "READ_001"
        
        # Lecture par code
        result = await db_session.execute(select(Project).where(Project.code == "READ_001"))
        found_project = result.scalar_one_or_none()
        
        assert found_project is not None
        assert found_project.id == project.id
    
    async def test_update_project_sqlalchemy(self, db_session: AsyncSession, test_workspace: Workspace):
        """Test mise à jour d'un projet avec SQLAlchemy"""
        # Création
        project = Project(
            name="Projet Original",
            code="UPDATE_001",
            workspace_id=test_workspace.id,
            status=ProjectStatus.EN_COURS,
            budget_total=Decimal("100000.00")
        )
        db_session.add(project)
        await db_session.commit()
        
        # Mise à jour
        project.name = "Projet Modifié"
        project.status = ProjectStatus.TERMINE
        project.budget_total = Decimal("120000.00")
        project.client_name = "Nouveau Client"
        
        await db_session.commit()
        await db_session.refresh(project)
        
        # Vérifications
        assert project.name == "Projet Modifié"
        assert project.status == ProjectStatus.TERMINE
        assert project.budget_total == Decimal("120000.00")
        assert project.client_name == "Nouveau Client"
        assert project.updated_at > project.created_at
    
    async def test_delete_project_sqlalchemy(self, db_session: AsyncSession, test_workspace: Workspace):
        """Test suppression d'un projet avec SQLAlchemy"""
        # Création
        project = Project(
            name="Projet à Supprimer",
            code="DELETE_001",
            workspace_id=test_workspace.id
        )
        db_session.add(project)
        await db_session.commit()
        project_id = project.id
        
        # Suppression logique (recommandée)
        project.is_archived = True
        await db_session.commit()
        
        # Vérification suppression logique
        result = await db_session.execute(select(Project).where(Project.id == project_id))
        found_project = result.scalar_one_or_none()
        assert found_project is not None
        assert found_project.is_archived is True
        
        # Suppression physique (pour les tests)
        await db_session.delete(project)
        await db_session.commit()
        
        # Vérification suppression physique
        result = await db_session.execute(select(Project).where(Project.id == project_id))
        found_project = result.scalar_one_or_none()
        assert found_project is None
    
    async def test_project_company_relationship(
        self, 
        db_session: AsyncSession, 
        test_workspace: Workspace, 
        test_entreprise_tiers: EntrepriseTiers
    ):
        """Test des relations Project-EntrepriseTiers"""
        # Création projet
        project = Project(
            name="Projet avec Entreprise",
            code="REL_001",
            workspace_id=test_workspace.id
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)
        
        # Association avec entreprise tierce
        project_company = ProjectCompany(
            project_id=project.id,
            company_id=test_entreprise_tiers.id,
            role="OWNER",
            is_active=True
        )
        db_session.add(project_company)
        await db_session.commit()
        
        # Vérification de la relation
        await db_session.refresh(project)
        assert len(project.companies) == 1
        assert project.companies[0].company_id == test_entreprise_tiers.id
        assert project.companies[0].role == "OWNER"
        
        # Test propriété primary_company
        assert project.primary_company is not None
        assert project.primary_company.id == test_entreprise_tiers.id
        assert project.primary_company_id == test_entreprise_tiers.id
    
    # Tests Pydantic
    
    def test_project_create_schema_validation(self):
        """Test validation du schéma ProjectCreate"""
        # Données valides
        valid_data = {
            "name": "Nouveau Projet",
            "code": "NEW_001",
            "description": "Description du nouveau projet",
            "status": ProjectStatus.EN_COURS,
            "nature": ProjectNature.DEVIS,
            "budget_total": "75000.50",
            "client_name": "Client Valide"
        }
        
        project_create = ProjectCreate(**valid_data)
        assert project_create.name == "Nouveau Projet"
        assert project_create.code == "NEW_001"
        assert project_create.status == ProjectStatus.EN_COURS
        assert project_create.nature == ProjectNature.DEVIS
        assert project_create.budget_total == Decimal("75000.50")
        
        # Test avec données minimales
        minimal_data = {"name": "Projet Minimal"}
        project_minimal = ProjectCreate(**minimal_data)
        assert project_minimal.name == "Projet Minimal"
        assert project_minimal.code is None  # Sera généré automatiquement
        assert project_minimal.status == ProjectStatus.EN_COURS  # Valeur par défaut
        assert project_minimal.nature == ProjectNature.DEVIS  # Valeur par défaut
    
    def test_project_update_schema_validation(self):
        """Test validation du schéma ProjectUpdate"""
        # Mise à jour partielle valide
        update_data = {
            "name": "Projet Modifié",
            "status": ProjectStatus.TERMINE,
            "budget_total": "85000.00"
        }
        
        project_update = ProjectUpdate(**update_data)
        assert project_update.name == "Projet Modifié"
        assert project_update.status == ProjectStatus.TERMINE
        assert project_update.budget_total == Decimal("85000.00")
        assert project_update.code is None  # Non modifié
        
        # Test avec toutes les données optionnelles
        full_update = ProjectUpdate(
            name="Projet Complet",
            description="Nouvelle description",
            status=ProjectStatus.ARCHIVE,
            nature=ProjectNature.AO,
            address="Nouvelle adresse",
            client_name="Nouveau client",
            is_archived=True
        )
        assert full_update.name == "Projet Complet"
        assert full_update.status == ProjectStatus.ARCHIVE
        assert full_update.is_archived is True
    
    def test_project_response_schema(self):
        """Test du schéma de réponse ProjectResponse"""
        project_data = {
            "id": 1,
            "name": "Projet Réponse",
            "code": "RESP_001",
            "status": ProjectStatus.EN_COURS,
            "nature": ProjectNature.AFFAIRE,
            "budget_total": Decimal("100000.00"),
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "is_archived": False
        }
        
        project_response = ProjectResponse(**project_data)
        assert project_response.id == 1
        assert project_response.name == "Projet Réponse"
        assert project_response.code == "RESP_001"
        assert project_response.budget_total == Decimal("100000.00")
        assert not project_response.is_archived
    
    # Tests API avec authentification
    
    async def test_create_project_api(
        self, 
        client: TestClient, 
        db_session: AsyncSession, 
        test_workspace: Workspace,
        test_user: User
    ):
        """Test création projet via API"""
        # Données de création
        project_data = {
            "name": "Projet API",
            "code": "API_001",
            "description": "Projet créé via API",
            "status": "EN_COURS",
            "nature": "AFFAIRE",
            "budget_total": "200000.00",
            "client_name": "Client API"
        }
        
        # Création d'un token avec permissions
        token = create_access_token(
            data={
                "sub": str(test_user.id),
                "workspace_id": test_workspace.id,
                "permissions": ["projects.create"]
            }
        )
        
        # Requête de création
        response = client.post(
            "/api/v1/projects/",
            json=project_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data["name"] == "Projet API"
        assert response_data["code"] == "API_001"
        assert response_data["budget_total"] == "200000.00"
        assert "id" in response_data
    
    async def test_get_projects_api(
        self, 
        client: TestClient, 
        db_session: AsyncSession, 
        test_workspace: Workspace,
        test_user: User
    ):
        """Test récupération des projets via API"""
        # Création de projets de test
        projects = [
            Project(
                name=f"Projet {i}",
                code=f"GET_{i:03d}",
                workspace_id=test_workspace.id,
                status=ProjectStatus.EN_COURS
            )
            for i in range(1, 4)
        ]
        db_session.add_all(projects)
        await db_session.commit()
        
        # Token avec permissions
        token = create_access_token(
            data={
                "sub": str(test_user.id),
                "workspace_id": test_workspace.id,
                "permissions": ["projects.read"]
            }
        )
        
        # Requête de récupération
        response = client.get(
            "/api/v1/projects/",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert len(response_data) >= 3
        
        # Vérification que tous les projets appartiennent au bon workspace
        for project in response_data:
            # Note: Cette vérification dépend de l'implémentation de l'API
            # qui devrait filtrer par workspace_id automatiquement
            assert "name" in project
            assert "code" in project
    
    async def test_get_project_by_id_api(
        self, 
        client: TestClient, 
        db_session: AsyncSession, 
        test_workspace: Workspace,
        test_user: User
    ):
        """Test récupération d'un projet par ID via API"""
        # Création d'un projet
        project = Project(
            name="Projet Spécifique",
            code="SPEC_001",
            workspace_id=test_workspace.id,
            description="Description spécifique",
            budget_total=Decimal("50000.00")
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)
        
        # Token avec permissions
        token = create_access_token(
            data={
                "sub": str(test_user.id),
                "workspace_id": test_workspace.id,
                "permissions": ["projects.read"]
            }
        )
        
        # Requête de récupération
        response = client.get(
            f"/api/v1/projects/{project.id}",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["id"] == project.id
        assert response_data["name"] == "Projet Spécifique"
        assert response_data["code"] == "SPEC_001"
        assert response_data["budget_total"] == "50000.00"
    
    async def test_update_project_api(
        self, 
        client: TestClient, 
        db_session: AsyncSession, 
        test_workspace: Workspace,
        test_user: User
    ):
        """Test mise à jour d'un projet via API"""
        # Création d'un projet
        project = Project(
            name="Projet à Modifier",
            code="UPD_001",
            workspace_id=test_workspace.id,
            status=ProjectStatus.EN_COURS
        )
        db_session.add(project)
        await db_session.commit()
        await db_session.refresh(project)
        
        # Données de mise à jour
        update_data = {
            "name": "Projet Modifié API",
            "status": "TERMINE",
            "budget_total": "75000.00"
        }
        
        # Token avec permissions
        token = create_access_token(
            data={
                "sub": str(test_user.id),
                "workspace_id": test_workspace.id,
                "permissions": ["projects.update"]
            }
        )
        
        # Requête de mise à jour
        response = client.put(
            f"/api/v1/projects/{project.id}",
            json=update_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["name"] == "Projet Modifié API"
        assert response_data["status"] == "TERMINE"
        assert response_data["budget_total"] == "75000.00"
    
    # Tests de sécurité et isolation multi-tenant
    
    async def test_project_workspace_isolation(self, db_session: AsyncSession):
        """Test isolation des projets par workspace"""
        # Création de deux workspaces
        workspace1 = Workspace(name="Workspace 1", code="WS1")
        workspace2 = Workspace(name="Workspace 2", code="WS2")
        db_session.add_all([workspace1, workspace2])
        await db_session.commit()
        
        # Création de projets dans chaque workspace
        project1 = Project(
            name="Projet WS1",
            code="WS1_001",
            workspace_id=workspace1.id
        )
        project2 = Project(
            name="Projet WS2",
            code="WS2_001",
            workspace_id=workspace2.id
        )
        db_session.add_all([project1, project2])
        await db_session.commit()
        
        # Vérification isolation - projets du workspace 1
        result = await db_session.execute(
            select(Project).where(Project.workspace_id == workspace1.id)
        )
        ws1_projects = result.scalars().all()
        assert len(ws1_projects) == 1
        assert ws1_projects[0].name == "Projet WS1"
        
        # Vérification isolation - projets du workspace 2
        result = await db_session.execute(
            select(Project).where(Project.workspace_id == workspace2.id)
        )
        ws2_projects = result.scalars().all()
        assert len(ws2_projects) == 1
        assert ws2_projects[0].name == "Projet WS2"
    
    async def test_project_status_transitions(self, db_session: AsyncSession, test_workspace: Workspace):
        """Test des transitions de statut de projet"""
        project = Project(
            name="Projet Transitions",
            code="TRANS_001",
            workspace_id=test_workspace.id,
            status=ProjectStatus.EN_COURS
        )
        db_session.add(project)
        await db_session.commit()
        
        # Transition EN_COURS -> TERMINE
        project.status = ProjectStatus.TERMINE
        await db_session.commit()
        await db_session.refresh(project)
        assert project.status == ProjectStatus.TERMINE
        
        # Transition TERMINE -> ARCHIVE
        project.status = ProjectStatus.ARCHIVE
        project.is_archived = True
        await db_session.commit()
        await db_session.refresh(project)
        assert project.status == ProjectStatus.ARCHIVE
        assert project.is_archived is True
    
    # Tests de performance
    
    async def test_bulk_project_operations(self, db_session: AsyncSession, test_workspace: Workspace):
        """Test des opérations en lot sur les projets"""
        # Création en lot
        projects = []
        for i in range(50):
            project = Project(
                name=f"Projet Bulk {i}",
                code=f"BULK_{i:03d}",
                workspace_id=test_workspace.id,
                status=ProjectStatus.EN_COURS,
                budget_total=Decimal(f"{10000 + i * 1000}.00")
            )
            projects.append(project)
        
        db_session.add_all(projects)
        await db_session.commit()
        
        # Lecture en lot avec filtres
        result = await db_session.execute(
            select(Project).where(
                Project.workspace_id == test_workspace.id,
                Project.name.like("Projet Bulk%")
            )
        )
        found_projects = result.scalars().all()
        
        assert len(found_projects) == 50
        
        # Mise à jour en lot
        for project in found_projects[:10]:
            project.status = ProjectStatus.TERMINE
        
        await db_session.commit()
        
        # Vérification mise à jour
        result = await db_session.execute(
            select(Project).where(
                Project.workspace_id == test_workspace.id,
                Project.status == ProjectStatus.TERMINE
            )
        )
        terminated_projects = result.scalars().all()
        assert len(terminated_projects) >= 10
        
        # Nettoyage
        await db_session.execute(
            delete(Project).where(Project.name.like("Projet Bulk%"))
        )
        await db_session.commit()


# Tests de régression et edge cases
class TestProjectEdgeCases:
    """Tests des cas limites et de régression"""
    
    async def test_project_with_null_dates(self, db_session: AsyncSession, test_workspace: Workspace):
        """Test projet avec dates nulles"""
        project = Project(
            name="Projet Sans Dates",
            code="NULL_001",
            workspace_id=test_workspace.id,
            start_date=None,
            end_date=None
        )
        db_session.add(project)
        await db_session.commit()
        
        assert project.start_date is None
        assert project.end_date is None
        assert project.created_at is not None  # Métadonnée toujours présente
    
    async def test_project_code_uniqueness_per_workspace(self, db_session: AsyncSession):
        """Test unicité du code projet par workspace"""
        # Création de deux workspaces
        workspace1 = Workspace(name="WS1", code="WS1")
        workspace2 = Workspace(name="WS2", code="WS2")
        db_session.add_all([workspace1, workspace2])
        await db_session.commit()
        
        # Même code dans différents workspaces (devrait être autorisé)
        project1 = Project(
            name="Projet 1",
            code="SAME_CODE",
            workspace_id=workspace1.id
        )
        project2 = Project(
            name="Projet 2",
            code="SAME_CODE",
            workspace_id=workspace2.id
        )
        db_session.add_all([project1, project2])
        await db_session.commit()  # Ne devrait pas lever d'exception
        
        # Même code dans le même workspace (devrait échouer si contrainte existe)
        project3 = Project(
            name="Projet 3",
            code="SAME_CODE",
            workspace_id=workspace1.id
        )
        db_session.add(project3)
        
        # Note: Cette partie dépend de l'implémentation des contraintes DB
        # Si une contrainte unique (workspace_id, code) existe, cela devrait échouer
        try:
            await db_session.commit()
            # Si pas de contrainte, le test passe
            assert True
        except Exception:
            # Si contrainte existe, l'exception est attendue
            await db_session.rollback()
            assert True
    
    async def test_project_with_extreme_budget(self, db_session: AsyncSession, test_workspace: Workspace):
        """Test projet avec budget extrême"""
        # Budget très élevé
        high_budget_project = Project(
            name="Projet Budget Élevé",
            code="HIGH_001",
            workspace_id=test_workspace.id,
            budget_total=Decimal("999999999999.99")
        )
        db_session.add(high_budget_project)
        await db_session.commit()
        
        assert high_budget_project.budget_total == Decimal("999999999999.99")
        
        # Budget zéro
        zero_budget_project = Project(
            name="Projet Budget Zéro",
            code="ZERO_001",
            workspace_id=test_workspace.id,
            budget_total=Decimal("0.00")
        )
        db_session.add(zero_budget_project)
        await db_session.commit()
        
        assert zero_budget_project.budget_total == Decimal("0.00")


if __name__ == "__main__":
    # Exécution des tests
    pytest.main([__file__, "-v"])
