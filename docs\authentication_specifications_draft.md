# ORBIS Suivi Travaux - Spécifications d'Authentification (Brouillon)

## Vue d'ensemble
Système d'authentification JWT complet pour l'application ORBIS Suivi Travaux avec gestion des rôles et permissions multi-niveaux.

## 1. Architecture JWT

### Structure des Tokens
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": "uuid",
    "email": "<EMAIL>",
    "role": "admin|chef_projet|employe|client",
    "permissions": ["read_projects", "write_projects", "manage_users"],
    "exp": 1640995200,
    "iat": 1640991600,
    "jti": "token_uuid"
  }
}
```

### Configuration Sécurité
- **Algorithme**: HS256
- **Durée de vie**: 24h (access token), 7 jours (refresh token)
- **<PERSON>lé secrète**: Variable d'environnement sécurisée
- **Blacklisting**: Support des tokens révoqués

## 2. Modèles de Données Utilisateur

### Table Users
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role user_role NOT NULL DEFAULT 'employe',
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);
```

### Énumération des Rôles
```sql
CREATE TYPE user_role AS ENUM (
    'super_admin',
    'admin', 
    'chef_projet',
    'employe',
    'client'
);
```

### Table Permissions
```sql
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL
);
```

### Table Role_Permissions
```sql
CREATE TABLE role_permissions (
    role user_role NOT NULL,
    permission_id UUID NOT NULL,
    FOREIGN KEY (permission_id) REFERENCES permissions(id),
    PRIMARY KEY (role, permission_id)
);
```

## 3. Système de Rôles et Permissions

### Hiérarchie des Rôles
1. **Super Admin** - Accès complet système
2. **Admin** - Gestion utilisateurs et projets
3. **Chef de Projet** - Gestion projets assignés
4. **Employé** - Accès projets et tâches assignées
5. **Client** - Consultation projets propres

### Matrice des Permissions

| Ressource | Super Admin | Admin | Chef Projet | Employé | Client |
|-----------|-------------|-------|-------------|---------|--------|
| **Utilisateurs** |
| Créer utilisateurs | ✅ | ✅ | ❌ | ❌ | ❌ |
| Modifier utilisateurs | ✅ | ✅ | ❌ | ❌ | ❌ |
| Supprimer utilisateurs | ✅ | ✅ | ❌ | ❌ | ❌ |
| Voir tous utilisateurs | ✅ | ✅ | ❌ | ❌ | ❌ |
| **Projets** |
| Créer projets | ✅ | ✅ | ✅ | ❌ | ❌ |
| Modifier tous projets | ✅ | ✅ | ❌ | ❌ | ❌ |
| Modifier projets assignés | ✅ | ✅ | ✅ | ✅ | ❌ |
| Voir tous projets | ✅ | ✅ | ❌ | ❌ | ❌ |
| Voir projets assignés | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Documents** |
| Upload documents | ✅ | ✅ | ✅ | ✅ | ❌ |
| Télécharger documents | ✅ | ✅ | ✅ | ✅ | ✅ |
| Supprimer documents | ✅ | ✅ | ✅ | ❌ | ❌ |

## 4. Flux d'Authentification

### 4.1 Inscription Utilisateur
```mermaid
sequenceDiagram
    participant C as Client
    participant API as API Backend
    participant DB as Database
    participant Email as Service Email
    
    C->>API: POST /auth/register
    API->>API: Valider données
    API->>API: Hasher mot de passe
    API->>DB: Créer utilisateur
    API->>Email: Envoyer email vérification
    API-->>C: Réponse succès
```

### 4.2 Connexion Utilisateur
```mermaid
sequenceDiagram
    participant C as Client
    participant API as API Backend
    participant DB as Database
    participant Redis as Cache Redis
    
    C->>API: POST /auth/login
    API->>DB: Vérifier utilisateur
    API->>API: Vérifier mot de passe
    API->>API: Générer JWT tokens
    API->>Redis: Stocker refresh token
    API->>DB: Mettre à jour last_login
    API-->>C: Access + Refresh tokens
```

### 4.3 Refresh Token
```mermaid
sequenceDiagram
    participant C as Client
    participant API as API Backend
    participant Redis as Cache Redis
    
    C->>API: POST /auth/refresh
    API->>API: Valider refresh token
    API->>Redis: Vérifier token validité
    API->>API: Générer nouveau access token
    API-->>C: Nouveau access token
```

### 4.4 Déconnexion
```mermaid
sequenceDiagram
    participant C as Client
    participant API as API Backend
    participant Redis as Cache Redis
    
    C->>API: POST /auth/logout
    API->>Redis: Blacklister access token
    API->>Redis: Supprimer refresh token
    API-->>C: Confirmation déconnexion
```

## 5. Structure des Endpoints API

### Authentification
```
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/logout
POST /api/v1/auth/refresh
POST /api/v1/auth/forgot-password
POST /api/v1/auth/reset-password
POST /api/v1/auth/verify-email
```

### Gestion Utilisateurs
```
GET /api/v1/users/me
PUT /api/v1/users/me
GET /api/v1/users (Admin only)
POST /api/v1/users (Admin only)
PUT /api/v1/users/{id} (Admin only)
DELETE /api/v1/users/{id} (Admin only)
```

### Protection des Routes
```
GET /api/v1/projects (Authentifié + Permission)
POST /api/v1/projects (Chef Projet+)
PUT /api/v1/projects/{id} (Propriétaire ou Admin)
```

## 6. Modèles Pydantic

### User Models
```python
class UserBase(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    role: UserRole = UserRole.EMPLOYE

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None

class UserInDB(UserBase):
    id: UUID
    is_active: bool
    is_verified: bool
    created_at: datetime
    last_login: Optional[datetime]

class UserResponse(UserInDB):
    pass
```

### Auth Models
```python
class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int

class RefreshTokenRequest(BaseModel):
    refresh_token: str
```

## 7. Middleware de Sécurité

### Protection des Routes
```python
@app.middleware("http")
async def auth_middleware(request: Request, call_next):
    # Vérification JWT pour routes protégées
    # Injection des données utilisateur
    # Vérification des permissions
    pass
```

### Décorateurs de Permission
```python
@require_permission("read_projects")
@require_role("chef_projet")
async def get_projects():
    pass
```

## 8. Configuration Frontend

### Context d'Authentification React
```javascript
const AuthContext = createContext({
  user: null,
  login: () => {},
  logout: () => {},
  isAuthenticated: false,
  permissions: [],
  hasPermission: () => false
});
```

### Protection des Routes Frontend
```javascript
const ProtectedRoute = ({ children, requiredRole, requiredPermission }) => {
  const { user, hasPermission } = useAuth();
  
  if (!user) return <Navigate to="/login" />;
  if (requiredRole && user.role !== requiredRole) return <Unauthorized />;
  if (requiredPermission && !hasPermission(requiredPermission)) return <Unauthorized />;
  
  return children;
};
```

## 9. Sécurité et Bonnes Pratiques

### Hash des Mots de Passe
- **Algorithme**: bcrypt avec salt
- **Coût**: 12 rounds minimum
- **Validation**: Complexité minimum requise

### Protection CSRF
- **Tokens CSRF** pour requêtes sensibles
- **SameSite cookies** configuration
- **Headers sécurisés** validation

### Rate Limiting
- **Login**: 5 tentatives/15 minutes par IP
- **Registration**: 3 comptes/heure par IP
- **API générale**: 1000 requêtes/heure par utilisateur

### Validation des Données
- **Sanitisation** inputs utilisateur
- **Validation** email et formats
- **Échappement** données affichées

## 10. Tests et Monitoring

### Tests Unitaires
- Tests des endpoints d'authentification
- Tests des permissions et rôles
- Tests des middlewares de sécurité

### Monitoring
- **Logs d'authentification** (succès/échecs)
- **Alertes** tentatives de connexion suspectes
- **Métriques** utilisation des tokens

---

**Status**: 🚧 Brouillon en cours - Version 0.1
**Prochaine étape**: Implémentation backend FastAPI
**Estimation**: 3-4 jours de développement