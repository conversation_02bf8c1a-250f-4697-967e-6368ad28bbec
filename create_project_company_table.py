#!/usr/bin/env python3
"""
Script pour créer la table project_company et migrer les données
"""

import asyncio
import asyncpg
from datetime import datetime

DATABASE_URL = "**************************************************************************************************/postgres"

async def create_project_company_table():
    """Créer la table project_company et migrer les données"""
    print("🚀 Création de la table project_company...")
    
    try:
        # Connexion à la base de données
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Connexion à la base de données réussie")
        
        # 1. Créer la table project_company
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS project_company (
                id SERIAL PRIMARY KEY,
                project_id INTEGER NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
                company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
                role VARCHAR(50) NOT NULL DEFAULT 'OWNER',
                is_active BOOLEAN NOT NULL DEFAULT true,
                created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
                UNIQUE(project_id, company_id)
            )
        """)
        print("✅ Table project_company créée")
        
        # 2. Créer les index
        await conn.execute("CREATE INDEX IF NOT EXISTS ix_project_company_project_id ON project_company(project_id)")
        await conn.execute("CREATE INDEX IF NOT EXISTS ix_project_company_company_id ON project_company(company_id)")
        print("✅ Index créés")
        
        # 3. Vérifier si la colonne company_id existe dans projects
        column_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'projects' AND column_name = 'company_id'
            )
        """)
        
        if column_exists:
            print("📋 Migration des données existantes...")
            
            # Migrer les données existantes
            migrated_count = await conn.fetchval("""
                INSERT INTO project_company (project_id, company_id, role, is_active, created_at, updated_at)
                SELECT 
                    p.id as project_id,
                    p.company_id,
                    'OWNER' as role,
                    true as is_active,
                    COALESCE(p.created_at, NOW()) as created_at,
                    COALESCE(p.updated_at, NOW()) as updated_at
                FROM projects p
                WHERE p.company_id IS NOT NULL
                ON CONFLICT (project_id, company_id) DO NOTHING
                RETURNING (SELECT COUNT(*) FROM project_company)
            """)
            print(f"✅ {migrated_count or 0} relations projet-entreprise migrées")
            
            # Supprimer la colonne company_id de projects
            await conn.execute("ALTER TABLE projects DROP COLUMN IF EXISTS company_id")
            print("✅ Colonne company_id supprimée de la table projects")
        else:
            print("ℹ️ Colonne company_id n'existe pas dans projects - pas de migration nécessaire")
        
        # 4. Vérifier le résultat
        project_company_count = await conn.fetchval("SELECT COUNT(*) FROM project_company")
        projects_count = await conn.fetchval("SELECT COUNT(*) FROM projects")
        
        print(f"\n📊 Résultats:")
        print(f"   • Projets: {projects_count}")
        print(f"   • Relations projet-entreprise: {project_company_count}")
        
        # 5. Afficher quelques exemples
        if project_company_count > 0:
            examples = await conn.fetch("""
                SELECT 
                    p.name as project_name,
                    c.name as company_name,
                    pc.role,
                    pc.is_active
                FROM project_company pc
                JOIN projects p ON pc.project_id = p.id
                JOIN companies c ON pc.company_id = c.id
                LIMIT 5
            """)
            
            print(f"\n🔍 Exemples de relations:")
            for ex in examples:
                print(f"   • {ex['project_name']} → {ex['company_name']} ({ex['role']})")
        
        await conn.close()
        print("\n🎉 Migration terminée avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la migration: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_new_structure():
    """Tester la nouvelle structure"""
    print("\n🧪 Test de la nouvelle structure...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Test: récupérer les projets avec leurs entreprises
        projects = await conn.fetch("""
            SELECT 
                p.id,
                p.name,
                p.code,
                c.name as company_name,
                pc.role
            FROM projects p
            JOIN project_company pc ON p.id = pc.project_id
            JOIN companies c ON pc.company_id = c.id
            WHERE pc.is_active = true
            LIMIT 3
        """)
        
        print("✅ Test de lecture réussi:")
        for project in projects:
            print(f"   • {project['name']} ({project['code']}) → {project['company_name']} ({project['role']})")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Migration vers la structure project_company")
    print("=" * 50)
    
    async def main():
        # Créer la table et migrer
        success = await create_project_company_table()
        
        if success:
            # Tester la nouvelle structure
            await test_new_structure()
        
        return success
    
    result = asyncio.run(main())
    
    if result:
        print("\n✅ Migration complète réussie!")
        print("🚀 Vous pouvez maintenant redémarrer le serveur FastAPI")
    else:
        print("\n❌ Migration échouée")
