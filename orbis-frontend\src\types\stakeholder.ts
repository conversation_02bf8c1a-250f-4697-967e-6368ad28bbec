export interface Stakeholder {
  id: number;
  lot_id: number;
  company_id: number;
  role?: string;
  is_active: boolean;
  created_at: string;
  created_by?: number;
  
  // Relations
  lot?: {
    id: number;
    name: string;
  };
  company?: {
    id: number;
    company_name: string;
    activity?: string;
    email?: string;
    phone?: string;
  };
}

export interface StakeholderCreate {
  lot_id: number;
  company_id?: number;
  company_data?: {
    company_name: string;
    activity?: string;
    email?: string;
    phone?: string;
    address?: string;
    postal_code?: string;
    city?: string;
    country?: string;
    siret?: string;
  };
  role?: string;
  is_active?: boolean;
}

export interface StakeholderUpdate {
  role?: string;
  is_active?: boolean;
  company_id?: number;
}

export interface StakeholderStats {
  total_active: number;
  total_inactive: number;
  by_role: Record<string, number>;
  by_lot: Record<string, number>;
}
