#!/usr/bin/env python3
"""
Test pour vérifier que la correction des documents techniques fonctionne
avec le frontend (simulation de l'appel API)
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'fastapi_template'))

from fastapi.testclient import TestClient
from fastapi_template.app.main import app
from fastapi_template.app.core.database import get_db
from sqlalchemy import text

def test_frontend_technical_documents_api():
    """Test de l'API comme si elle était appelée par le frontend"""
    
    print("🚀 Test de l'API documents techniques avec lot_id")
    print("=" * 60)
    
    client = TestClient(app)
    
    # 1. Test de création avec lot_id (comme le frontend le fait maintenant)
    print("\n1. Test de création avec lot_id...")
    
    # Données comme envoyées par le frontend après correction
    document_data = {
        "name": "Test CCTP - Frontend Fix",
        "type_document": "CCTP",
        "lot_id": 2,  # Utilise lot_id au lieu de project_id
        "content": "Contenu de test depuis le frontend",
        "company_ids": [5]
    }
    
    # Headers d'authentification (simulé)
    headers = {
        "Authorization": "Bearer test-token",
        "Content-Type": "application/json"
    }
    
    print(f"📝 Données envoyées: {document_data}")
    
    try:
        response = client.post(
            "/api/v1/technical-documents",
            json=document_data,
            headers=headers
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Document créé avec succès!")
            print(f"   - ID: {result.get('id')}")
            print(f"   - Nom: {result.get('name')}")
            print(f"   - Type: {result.get('type_document')}")
            print(f"   - Lot ID: {result.get('lot_id')}")
            print(f"   - Lot: {result.get('lot', {}).get('name', 'N/A')}")
            
            # 2. Test de récupération
            print(f"\n2. Test de récupération du document {result['id']}...")
            get_response = client.get(
                f"/api/v1/technical-documents/{result['id']}",
                headers=headers
            )
            
            if get_response.status_code == 200:
                doc = get_response.json()
                print("✅ Document récupéré avec succès!")
                print(f"   - Nom: {doc.get('name')}")
                print(f"   - Lot: {doc.get('lot', {}).get('name', 'N/A')}")
                print(f"   - Projet (via lot): {doc.get('lot', {}).get('project', {}).get('name', 'N/A')}")
            else:
                print(f"❌ Erreur lors de la récupération: {get_response.status_code}")
                print(f"   Réponse: {get_response.text}")
            
        elif response.status_code == 422:
            error_detail = response.json()
            print(f"❌ Erreur de validation (422):")
            print(f"   Détails: {error_detail}")
            
            # Vérifier si c'est encore l'erreur lot_id manquant
            if "lot_id" in str(error_detail):
                print("❌ L'erreur lot_id persiste - le frontend n'est pas encore corrigé")
                return False
            else:
                print("❌ Autre erreur de validation")
                return False
                
        else:
            print(f"❌ Erreur HTTP {response.status_code}")
            print(f"   Réponse: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception lors du test: {e}")
        return False
    
    # 3. Test de listage avec filtrage par lot
    print(f"\n3. Test de listage avec filtrage par lot...")
    try:
        list_response = client.get(
            "/api/v1/technical-documents?lot_id=2",
            headers=headers
        )
        
        if list_response.status_code == 200:
            docs = list_response.json()
            print(f"✅ Listage réussi: {len(docs)} document(s) trouvé(s)")
            for doc in docs[:3]:  # Afficher les 3 premiers
                print(f"   - {doc.get('name')} (Lot: {doc.get('lot', {}).get('name', 'N/A')})")
        else:
            print(f"⚠️  Erreur lors du listage: {list_response.status_code}")
            
    except Exception as e:
        print(f"⚠️  Exception lors du listage: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 TEST RÉUSSI!")
    print("✅ L'API accepte maintenant lot_id au lieu de project_id")
    print("✅ Le frontend peut créer des documents techniques")
    print("✅ L'erreur 'project_id is an invalid keyword argument' est corrigée")
    
    return True

async def test_database_structure():
    """Vérifier que la structure de la base de données est correcte"""
    print("\n🔍 Vérification de la structure de la base de données...")
    
    async for db in get_db():
        try:
            # Vérifier la structure de la table
            result = await db.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'technical_documents' 
                ORDER BY ordinal_position
            """))
            columns = result.fetchall()
            
            print("📋 Structure de la table technical_documents:")
            has_lot_id = False
            has_project_id = False
            
            for col in columns:
                print(f"   - {col[0]}: {col[1]} (nullable: {col[2]})")
                if col[0] == 'lot_id':
                    has_lot_id = True
                elif col[0] == 'project_id':
                    has_project_id = True
            
            if has_lot_id and not has_project_id:
                print("✅ Structure correcte: lot_id présent, project_id supprimé")
                return True
            elif has_project_id:
                print("❌ Structure incorrecte: project_id encore présent")
                return False
            else:
                print("❌ Structure incorrecte: lot_id manquant")
                return False
                
        except Exception as e:
            print(f"❌ Erreur lors de la vérification: {e}")
            return False

def main():
    """Fonction principale"""
    print("🚀 Test complet de la correction des documents techniques")
    print("=" * 70)
    
    # Test de la structure de la base de données
    db_ok = asyncio.run(test_database_structure())
    
    if not db_ok:
        print("❌ La structure de la base de données n'est pas correcte")
        return False
    
    # Test de l'API
    api_ok = test_frontend_technical_documents_api()
    
    if api_ok:
        print("\n🎉 TOUS LES TESTS SONT PASSÉS!")
        print("✅ La correction est complète et fonctionnelle")
        return True
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
