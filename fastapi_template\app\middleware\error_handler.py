# app/middleware/error_handler.py
"""
Middleware de gestion d'erreur globale pour encapsuler les erreurs
et les retourner proprement au frontend
"""

from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError, ResponseValidationError
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from pydantic import ValidationError
import traceback
import logging
import json

logger = logging.getLogger(__name__)

class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware pour gérer les erreurs de manière uniforme"""
    
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except HTTPException as e:
            # Les HTTPException sont déjà bien formatées
            return JSONResponse(
                status_code=e.status_code,
                content={
                    "error": True,
                    "message": e.detail,
                    "status_code": e.status_code,
                    "type": "http_exception"
                }
            )
        except RequestValidationError as e:
            # Erreurs de validation des requêtes (données d'entrée)
            logger.error(f"Request validation error: {e}")
            return JSONResponse(
                status_code=422,
                content={
                    "error": True,
                    "message": "Données d'entrée invalides",
                    "details": e.errors(),
                    "status_code": 422,
                    "type": "validation_error"
                }
            )
        except ResponseValidationError as e:
            # Erreurs de validation des réponses (problème côté serveur)
            logger.error(f"Response validation error: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            # Extraire les détails de l'erreur
            error_details = []
            if hasattr(e, 'errors'):
                for error in e.errors():
                    error_details.append({
                        "field": ".".join(str(loc) for loc in error.get('loc', [])),
                        "message": error.get('msg', 'Erreur de validation'),
                        "type": error.get('type', 'unknown'),
                        "input": str(error.get('input', 'N/A'))
                    })
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": True,
                    "message": "Erreur de validation de la réponse du serveur",
                    "details": error_details,
                    "status_code": 500,
                    "type": "response_validation_error",
                    "help": "Cette erreur indique un problème dans la structure des données retournées par le serveur. Veuillez contacter l'administrateur."
                }
            )
        except ValidationError as e:
            # Erreurs de validation Pydantic génériques
            logger.error(f"Pydantic validation error: {e}")
            return JSONResponse(
                status_code=422,
                content={
                    "error": True,
                    "message": "Erreur de validation des données",
                    "details": e.errors() if hasattr(e, 'errors') else str(e),
                    "status_code": 422,
                    "type": "pydantic_validation_error"
                }
            )
        except Exception as e:
            # Toutes les autres erreurs
            logger.error(f"Unhandled exception: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            # En mode développement, inclure plus de détails
            error_response = {
                "error": True,
                "message": "Erreur interne du serveur",
                "status_code": 500,
                "type": "internal_server_error"
            }
            
            # Ajouter des détails en mode debug (à configurer selon l'environnement)
            if hasattr(request.app.state, 'debug') and request.app.state.debug:
                error_response["debug_info"] = {
                    "exception_type": type(e).__name__,
                    "exception_message": str(e),
                    "traceback": traceback.format_exc()
                }
            
            return JSONResponse(
                status_code=500,
                content=error_response
            )

def format_validation_error(error: dict) -> dict:
    """Formate une erreur de validation pour le frontend"""
    return {
        "field": ".".join(str(loc) for loc in error.get('loc', [])),
        "message": error.get('msg', 'Erreur de validation'),
        "type": error.get('type', 'unknown'),
        "input": error.get('input')
    }

def create_error_response(
    message: str,
    status_code: int = 500,
    error_type: str = "error",
    details: list = None
) -> JSONResponse:
    """Crée une réponse d'erreur standardisée"""
    content = {
        "error": True,
        "message": message,
        "status_code": status_code,
        "type": error_type
    }
    
    if details:
        content["details"] = details
    
    return JSONResponse(
        status_code=status_code,
        content=content
    )
