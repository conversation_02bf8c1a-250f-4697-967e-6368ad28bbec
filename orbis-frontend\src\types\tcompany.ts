export interface TCompany {
  id: number;
  company_name: string;
  activity?: string;
  address?: string;
  postal_code?: string;
  city?: string;
  country?: string;
  phone?: string;
  fax?: string;
  email?: string;
  siret?: string;
  vat_number?: string;
  legal_representative_id?: number;
  logo_url?: string;
  logo_filename?: string;
  workspace_id: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by?: number;
}

export interface TCompanyCreate {
  company_name: string;
  activity?: string;
  address?: string;
  postal_code?: string;
  city?: string;
  country?: string;
  phone?: string;
  fax?: string;
  email?: string;
  siret?: string;
  vat_number?: string;
  legal_representative_id?: number;
  logo_url?: string;
  logo_filename?: string;
}

export interface TCompanyUpdate {
  company_name?: string;
  activity?: string;
  address?: string;
  postal_code?: string;
  city?: string;
  country?: string;
  phone?: string;
  fax?: string;
  email?: string;
  siret?: string;
  vat_number?: string;
  legal_representative_id?: number;
  logo_url?: string;
  logo_filename?: string;
  is_active?: boolean;
}
