"""
Script pour tester l'<NAME_EMAIL> avec le bon mot de passe
Mot de passe: orbis123!
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "orbis123!"  # Mot de passe correct

def test_login_and_workspaces():
    """Test de connexion et accès aux workspaces"""
    print("🔐 TEST D'AUTHENTIFICATION <EMAIL>")
    print("=" * 50)
    
    # 1. Test de connexion
    print(f"\n1️⃣ Connexion avec {TEST_EMAIL}")
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        # Login
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code Login: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            print(f"✅ Connexion réussie!")
            print(f"🔑 Token: {access_token[:50]}...")
            
            # 2. Test accès workspaces
            print(f"\n2️⃣ Test accès aux workspaces")
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            ws_response = requests.get(
                f"{BASE_URL}/api/v1/workspaces/",
                headers=headers
            )
            
            print(f"Status Code Workspaces: {ws_response.status_code}")
            
            if ws_response.status_code == 200:
                workspaces = ws_response.json()
                print(f"✅ Accès workspaces réussi!")
                print(f"📁 Workspaces trouvés: {len(workspaces)}")
                for ws in workspaces:
                    print(f"   - {ws.get('name', 'N/A')} (ID: {ws.get('id', 'N/A')})")
            else:
                print(f"❌ ERREUR 403 - Accès workspaces refusé!")
                print(f"Response: {ws_response.text}")
                
                # Test endpoint /me
                print(f"\n3️⃣ Test endpoint /me")
                me_response = requests.get(
                    f"{BASE_URL}/api/v1/auth/me",
                    headers=headers
                )
                print(f"Status Code /me: {me_response.status_code}")
                print(f"Response /me: {me_response.text}")
                
                # Test endpoint projects
                print(f"\n4️⃣ Test endpoint /projects")
                projects_response = requests.get(
                    f"{BASE_URL}/api/v1/projects/",
                    headers=headers
                )
                print(f"Status Code /projects: {projects_response.status_code}")
                print(f"Response /projects: {projects_response.text[:200]}...")
                
        else:
            print(f"❌ Erreur de connexion: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Serveur non accessible sur http://localhost:8000")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def save_password_info():
    """Sauvegarde les informations de mot de passe"""
    password_info = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD,
        "note": "Mot de passe pour l'utilisateur test ORBIS",
        "date": "2025-07-11"
    }
    
    with open("test_user_credentials.json", "w") as f:
        json.dump(password_info, f, indent=2)
    
    print(f"💾 Informations sauvegardées dans test_user_credentials.json")

if __name__ == "__main__":
    print("🚀 Test d'authentification ORBIS")
    
    # Sauvegarder les credentials
    save_password_info()
    
    # Tester l'authentification
    test_login_and_workspaces()
    
    print("\n✅ Test terminé!")
