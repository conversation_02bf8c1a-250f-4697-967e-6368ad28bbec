/* Styles pour le custom node CoverPage */

.cover-page-wrapper {
  position: relative;
  margin: 20px 0;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.cover-page-wrapper.ProseMirror-selectednode {
  border-color: #0F766E;
  box-shadow: 0 0 0 2px rgba(15, 118, 110, 0.2);
}

.cover-page-selected-indicator {
  position: absolute;
  top: -30px;
  left: 0;
  background: #0F766E;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px 4px 0 0;
  z-index: 10;
}

.cover-page-container {
  position: relative;
  background: white;
}

.cover-page-content {
  pointer-events: none;
  user-select: none;
}

.cover-page-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.cover-page-wrapper:hover .cover-page-overlay {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

.cover-page-controls {
  display: flex;
  gap: 8px;
}

.cover-page-button {
  background: #0F766E;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.cover-page-button:hover {
  background: #0d5d56;
}

/* Styles pour le contenu de la page de garde */
.cover-page-content .pagedegarde {
  margin: 0;
  border: none;
  width: 100%;
  min-height: auto;
  padding: 20px;
  box-sizing: border-box;
  transform: scale(0.5);
  transform-origin: top left;
  width: 200%;
  font-family: Arial, sans-serif;
}

.cover-page-content .pagedegarde table {
  border-collapse: collapse;
  width: 100%;
}

.cover-page-content .pagedegarde p {
  margin: 0;
  line-height: 1.2;
}

/* Colonne gauche - Entreprises */
.cover-page-content .pagedegarde .col-entreprises {
  width: 45%;
  vertical-align: top;
  padding: 15px;
  background-color: #0F766E;
}

.cover-page-content .pagedegarde .col-entreprises .entreprise-item {
  width: 100%;
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(255,255,255,0.3);
  padding-bottom: 6px;
}

.cover-page-content .pagedegarde .col-entreprises .logo-cell {
  width: 25px;
  vertical-align: top;
  padding-right: 6px;
}

.cover-page-content .pagedegarde .col-entreprises .logo-cell img {
  height: 20px;
  width: auto;
  border: 1px solid rgba(255,255,255,0.5);
  background-color: white;
  padding: 1px;
}

.cover-page-content .pagedegarde .col-entreprises .logo-placeholder {
  width: 20px;
  height: 20px;
  background-color: rgba(255,255,255,0.2);
  border: 1px solid rgba(255,255,255,0.5);
}

.cover-page-content .pagedegarde .col-entreprises .role {
  font-size: 8px;
  font-weight: bold;
  color: white;
  margin-bottom: 1px;
}

.cover-page-content .pagedegarde .col-entreprises .nom {
  font-size: 7px;
  font-weight: bold;
  color: white;
  margin-bottom: 1px;
}

.cover-page-content .pagedegarde .col-entreprises .activite {
  font-size: 6px;
  color: rgba(255,255,255,0.8);
  font-style: italic;
  margin-bottom: 1px;
}

.cover-page-content .pagedegarde .col-entreprises .adresse {
  font-size: 6px;
  color: rgba(255,255,255,0.8);
  margin-bottom: 1px;
}

.cover-page-content .pagedegarde .col-entreprises .contact {
  font-size: 6px;
  color: rgba(255,255,255,0.8);
}

/* Colonne droite - Projet */
.cover-page-content .pagedegarde .col-projet {
  width: 55%;
  vertical-align: top;
  padding: 15px;
  background-color: white;
}

.cover-page-content .pagedegarde .col-projet .titre-projet {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 6px;
  text-align: center;
}

.cover-page-content .pagedegarde .col-projet .adresse-projet {
  font-size: 13px;
  margin-bottom: 15px;
  font-weight: 500;
  text-align: center;
}

.cover-page-content .pagedegarde .col-projet .image-projet {
  width: 100%;
  max-height: 180px;
  object-fit: cover;
  border: 1px solid #ccc;
}

.cover-page-content .pagedegarde .col-projet .moa-titre {
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 6px;
  text-align: center;
}

.cover-page-content .pagedegarde .col-projet .moa-nom {
  font-size: 10px;
  font-weight: bold;
  margin-bottom: 2px;
}

.cover-page-content .pagedegarde .col-projet .moa-adresse {
  font-size: 9px;
  color: #666;
}

.cover-page-content .pagedegarde .col-projet .lot-numero {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 6px;
  color: #333;
  text-align: center;
}

.cover-page-content .pagedegarde .col-projet .lot-description {
  font-size: 13px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  color: #555;
  text-align: center;
}

.cover-page-content .pagedegarde .col-projet .document-titre {
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 1px;
  text-align: center;
}

/* Tableaux d'informations */
.cover-page-content .pagedegarde .info-table {
  border: 2px solid #000;
  font-size: 12px;
  margin-top: 10px;
}

.cover-page-content .pagedegarde .info-table td {
  padding: 10px;
  border: 1px solid #000;
}

.cover-page-content .pagedegarde .info-table .label {
  font-weight: bold;
  background-color: #e8e8e8;
  width: 40%;
}

.cover-page-content .pagedegarde .info-table .value {
  text-align: center;
  font-weight: bold;
}

.cover-page-content .pagedegarde .info-table .phase {
  color: #0066cc;
}

/* Conteneurs avec bordures */
.cover-page-content .pagedegarde .bordered {
  border: 2px solid #000;
  margin-bottom: 15px;
  background-color: #fafafa;
}

.cover-page-content .pagedegarde .lot-container {
  border: 1px solid #ccc;
  background-color: #f9f9f9;
  margin-bottom: 15px;
}

.cover-page-content .pagedegarde .document-container {
  border: 2px solid #000;
  border-radius: 12px;
  background-color: #f5f5f5;
  margin-bottom: 15px;
}

/* Indicateur de version */
.cover-page-content .pagedegarde .version-indicator {
  position: absolute;
  top: 5px;
  left: 5px;
  font-size: 8px;
  color: #0F766E;
  font-weight: bold;
  background-color: rgba(15, 118, 110, 0.1);
  padding: 2px 5px;
  border-radius: 3px;
}
