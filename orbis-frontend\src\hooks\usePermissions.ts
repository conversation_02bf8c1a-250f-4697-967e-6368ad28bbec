/**
 * Hook React pour la gestion des permissions
 */

import { useState, useEffect, useCallback } from 'react'
import { PermissionsService, UserPermissions, PERMISSIONS, PERMISSION_MESSAGES } from '@/lib/permissions'
import { useAuth } from '@/contexts/AuthContext'

export interface UsePermissionsReturn {
  permissions: UserPermissions | null
  loading: boolean
  error: string | null
  hasPermission: (permission: string) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  hasAllPermissions: (permissions: string[]) => boolean
  canPerformAction: (resource: string, action: string) => boolean
  isSuperAdmin: boolean
  isWorkspaceAdmin: boolean
  checkPermissionOrThrow: (permission: string, customMessage?: string) => void
  refreshPermissions: () => Promise<void>
}

export function usePermissions(): UsePermissionsReturn {
  const { user, isAuthenticated } = useAuth()
  const [permissions, setPermissions] = useState<UserPermissions | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Charger les permissions
  const loadPermissions = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setPermissions(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      const userPermissions = await PermissionsService.getUserPermissions()
      setPermissions(userPermissions)
    } catch (err) {
      console.error('❌ Erreur chargement permissions:', err)
      setError('Impossible de charger les permissions')
      setPermissions(null)
    } finally {
      setLoading(false)
    }
  }, [isAuthenticated, user])

  // Charger les permissions au montage et quand l'utilisateur change
  useEffect(() => {
    loadPermissions()
  }, [loadPermissions])

  // Fonctions de vérification des permissions
  const hasPermission = useCallback((permission: string): boolean => {
    if (!permissions) return false
    return permissions.permissions.includes(permission)
  }, [permissions])

  const hasAnyPermission = useCallback((permissionList: string[]): boolean => {
    if (!permissions) return false
    return permissionList.some(permission => permissions.permissions.includes(permission))
  }, [permissions])

  const hasAllPermissions = useCallback((permissionList: string[]): boolean => {
    if (!permissions) return false
    return permissionList.every(permission => permissions.permissions.includes(permission))
  }, [permissions])

  const canPerformAction = useCallback((resource: string, action: string): boolean => {
    const permissionName = `${resource}.${action}`
    return hasPermission(permissionName)
  }, [hasPermission])

  // Vérifications de rôles
  const isSuperAdmin = PermissionsService.isSuperAdmin()
  const isWorkspaceAdmin = PermissionsService.isWorkspaceAdmin()

  // Fonction pour vérifier une permission et lever une erreur si elle n'est pas accordée
  const checkPermissionOrThrow = useCallback((permission: string, customMessage?: string) => {
    if (!hasPermission(permission)) {
      const message = customMessage || PERMISSION_MESSAGES.INSUFFICIENT_RIGHTS
      throw new Error(message)
    }
  }, [hasPermission])

  // Fonction pour rafraîchir les permissions
  const refreshPermissions = useCallback(async () => {
    PermissionsService.clearCache()
    await loadPermissions()
  }, [loadPermissions])

  return {
    permissions,
    loading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canPerformAction,
    isSuperAdmin,
    isWorkspaceAdmin,
    checkPermissionOrThrow,
    refreshPermissions
  }
}

/**
 * Hook pour vérifier une permission spécifique
 */
export function useHasPermission(permission: string): {
  hasPermission: boolean
  loading: boolean
  error: string | null
} {
  const { hasPermission, loading, error } = usePermissions()
  
  return {
    hasPermission: hasPermission(permission),
    loading,
    error
  }
}

/**
 * Hook pour vérifier plusieurs permissions
 */
export function useHasAnyPermission(permissionList: string[]): {
  hasAnyPermission: boolean
  loading: boolean
  error: string | null
} {
  const { hasAnyPermission, loading, error } = usePermissions()
  
  return {
    hasAnyPermission: hasAnyPermission(permissionList),
    loading,
    error
  }
}

/**
 * Hook pour vérifier les permissions sur une ressource
 */
export function useResourcePermissions(resource: string): {
  canCreate: boolean
  canRead: boolean
  canUpdate: boolean
  canDelete: boolean
  loading: boolean
  error: string | null
} {
  const { canPerformAction, loading, error } = usePermissions()
  
  return {
    canCreate: canPerformAction(resource, 'create'),
    canRead: canPerformAction(resource, 'read'),
    canUpdate: canPerformAction(resource, 'update'),
    canDelete: canPerformAction(resource, 'delete'),
    loading,
    error
  }
}

/**
 * Hook pour les permissions de projets
 */
export function useProjectPermissions() {
  return useResourcePermissions('projects')
}

/**
 * Hook pour les permissions de documents
 */
export function useDocumentPermissions() {
  return useResourcePermissions('documents')
}

/**
 * Hook pour les permissions d'entreprises
 */
export function useEntreprisePermissions() {
  return useResourcePermissions('entreprises')
}

/**
 * Hook pour les permissions d'employés
 */
export function useEmployeePermissions() {
  return useResourcePermissions('employees')
}
