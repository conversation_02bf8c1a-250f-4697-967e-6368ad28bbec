import React, { useState, useCallback } from 'react'
import {
  TextEnhancementRequest,
  TextEnhancementResponse,
  DocumentType,
  PromptType,
  UseTextEnhancementReturn
} from '@/types/technical-document'
import { FastAuthService } from '@/lib/auth'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export function useChatGPT(): UseTextEnhancementReturn {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const enhanceText = useCallback(async (request: TextEnhancementRequest): Promise<TextEnhancementResponse> => {
    try {
      setLoading(true)
      setError(null)

      const token = FastAuthService.getToken()
      const response = await fetch(`${API_BASE_URL}/api/v1/technical-documents/enhance-text`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || 'Erreur lors de l\'amélioration du texte')
      }

      const result = await response.json()
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de l\'amélioration du texte'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    enhanceText,
    loading,
    error
  }
}

// Hook pour gérer l'historique des améliorations
export function useEnhancementHistory() {
  const [history, setHistory] = useState<TextEnhancementResponse[]>([])

  const addToHistory = useCallback((enhancement: TextEnhancementResponse) => {
    setHistory(prev => [enhancement, ...prev.slice(0, 9)]) // Garder les 10 dernières
  }, [])

  const clearHistory = useCallback(() => {
    setHistory([])
  }, [])

  const removeFromHistory = useCallback((index: number) => {
    setHistory(prev => prev.filter((_, i) => i !== index))
  }, [])

  return {
    history,
    addToHistory,
    clearHistory,
    removeFromHistory
  }
}

// Hook pour les suggestions d'amélioration en temps réel
export function useTextSuggestions(
  text: string,
  documentType: DocumentType,
  debounceMs: number = 1000
) {
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const { enhanceText } = useChatGPT()

  const generateSuggestions = useCallback(async (inputText: string) => {
    if (!inputText.trim() || inputText.length < 50) {
      setSuggestions([])
      return
    }

    try {
      setLoading(true)
      
      // Générer des suggestions pour différents types de prompts
      const promptTypes: PromptType[] = ['enhance', 'rephrase', 'simplify']
      const suggestionPromises = promptTypes.map(async (promptType) => {
        try {
          const result = await enhanceText({
            text: inputText,
            prompt_type: promptType,
            document_type: documentType,
            context: 'Suggestion automatique'
          })
          return result.enhanced_text
        } catch {
          return null
        }
      })

      const results = await Promise.all(suggestionPromises)
      const validSuggestions = results.filter((s): s is string => s !== null && s !== inputText)
      
      setSuggestions(validSuggestions)
    } catch (err) {
      console.error('Erreur lors de la génération de suggestions:', err)
      setSuggestions([])
    } finally {
      setLoading(false)
    }
  }, [enhanceText, documentType])

  // Debounce pour éviter trop d'appels API
  const debouncedGenerateSuggestions = useCallback(
    debounce(generateSuggestions, debounceMs),
    [generateSuggestions, debounceMs]
  )

  // Générer des suggestions quand le texte change
  React.useEffect(() => {
    debouncedGenerateSuggestions(text)
  }, [text, debouncedGenerateSuggestions])

  return {
    suggestions,
    loading,
    generateSuggestions: debouncedGenerateSuggestions
  }
}

// Hook pour la validation de texte technique
export function useTextValidation(documentType: DocumentType) {
  const validateText = useCallback((text: string) => {
    const issues: Array<{ type: 'warning' | 'error' | 'info'; message: string; position?: number }> = []

    if (!text.trim()) {
      return issues
    }

    // Validation commune
    if (text.length < 10) {
      issues.push({
        type: 'warning',
        message: 'Le texte semble très court pour un document technique'
      })
    }

    // Vérifier la présence de majuscules en début de phrase
    const sentences = text.split(/[.!?]+/)
    sentences.forEach((sentence, index) => {
      const trimmed = sentence.trim()
      if (trimmed && !/^[A-ZÀÁÂÄÈÉÊËÌÍÎÏÒÓÔÖÙÚÛÜ]/.test(trimmed)) {
        issues.push({
          type: 'warning',
          message: `Phrase ${index + 1}: devrait commencer par une majuscule`
        })
      }
    })

    // Validations spécifiques par type de document
    if (documentType === DocumentType.CCTP) {
      // Vérifier la présence de spécifications techniques
      if (!text.toLowerCase().includes('spécification') && 
          !text.toLowerCase().includes('norme') &&
          !text.toLowerCase().includes('technique')) {
        issues.push({
          type: 'info',
          message: 'Considérez ajouter des spécifications techniques ou références aux normes'
        })
      }

      // Vérifier la structure d'article
      if (text.length > 200 && !text.includes('Article') && !text.includes('§')) {
        issues.push({
          type: 'info',
          message: 'Considérez structurer le contenu en articles pour un CCTP'
        })
      }
    }

    if (documentType === DocumentType.DPGF) {
      // Vérifier la présence d'unités de mesure
      const units = ['m²', 'm³', 'ml', 'kg', 'u', 'unité', 'forfait']
      const hasUnits = units.some(unit => text.toLowerCase().includes(unit))
      
      if (!hasUnits && text.length > 100) {
        issues.push({
          type: 'warning',
          message: 'Un DPGF devrait contenir des unités de mesure (m², m³, ml, kg, u, etc.)'
        })
      }

      // Vérifier la présence de quantités
      if (!/\d+[,.]?\d*/.test(text) && text.length > 50) {
        issues.push({
          type: 'info',
          message: 'Considérez ajouter des quantités chiffrées pour un DPGF'
        })
      }
    }

    return issues
  }, [documentType])

  return { validateText }
}

// Fonction utilitaire pour le debounce
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

// Hook pour gérer les raccourcis clavier d'amélioration
export function useEnhancementShortcuts(
  onEnhance: (promptType: PromptType) => void,
  enabled: boolean = true
) {
  React.useEffect(() => {
    if (!enabled) return

    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+Shift+A pour améliorer
      if (event.ctrlKey && event.shiftKey && event.key === 'A') {
        event.preventDefault()
        onEnhance('enhance')
      }
      
      // Ctrl+Shift+R pour reformuler
      if (event.ctrlKey && event.shiftKey && event.key === 'R') {
        event.preventDefault()
        onEnhance('rephrase')
      }
      
      // Ctrl+Shift+E pour développer
      if (event.ctrlKey && event.shiftKey && event.key === 'E') {
        event.preventDefault()
        onEnhance('expand')
      }
      
      // Ctrl+Shift+S pour simplifier
      if (event.ctrlKey && event.shiftKey && event.key === 'S') {
        event.preventDefault()
        onEnhance('simplify')
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onEnhance, enabled])
}
