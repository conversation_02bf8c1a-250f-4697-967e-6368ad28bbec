import React from 'react'
import { Card } from '@/components/ui/Card'

interface StatsCardProps {
  title: string
  value: number | string
  icon: React.ReactNode
  trend?: {
    value: number
    isPositive: boolean
  }
  className?: string
  isLoading?: boolean
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  trend,
  className = '',
  isLoading = false
}) => {
  return (
    <Card className={`overflow-hidden relative group ${className}`}>
      {isLoading ? (
        <div className="animate-pulse p-5">
          <div className="flex items-center justify-between">
            <div className="h-12 w-12 rounded-lg bg-gray-200"></div>
            <div className="h-4 w-16 bg-gray-200 rounded"></div>
          </div>
          <div className="mt-4 h-8 bg-gray-200 rounded w-2/5"></div>
          <div className="mt-2 h-4 bg-gray-200 rounded w-3/5"></div>
        </div>
      ) : (
        <>
          {/* Background gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white via-white to-gray-50 opacity-50"></div>
          
          {/* Card content */}
          <div className="relative z-10 p-5">
            <div className="flex items-center justify-between">
              {/* Icon with gradient background */}
              <div className="p-3 rounded-xl bg-gradient-to-br from-primary-50 to-primary-100 shadow-sm border border-primary-200 text-primary-700 group-hover:scale-105 transition-transform">
                {icon}
              </div>
              
              {/* Trend indicator with improved styling */}
              {trend && (
                <div 
                  className={`flex items-center text-xs font-medium px-2.5 py-1.5 rounded-full transition-all ${
                    trend.isPositive 
                      ? 'text-green-700 bg-green-50 border border-green-200' 
                      : 'text-red-700 bg-red-50 border border-red-200'
                  }`}
                >
                  {trend.isPositive ? (
                    <svg className="w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                    </svg>
                  ) : (
                    <svg className="w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                    </svg>
                  )}
                  <span className="font-semibold">{trend.isPositive ? '+' : ''}{trend.value}%</span>
                </div>
              )}
            </div>
            
            {/* Values with improved typography */}
            <div className="mt-4 group-hover:translate-y-[-2px] transition-transform">
              <div className="text-3xl font-bold text-gray-900 tracking-tight">{value}</div>
              <div className="mt-1.5 text-sm font-medium text-gray-500">{title}</div>
            </div>
          </div>
          
          {/* Bottom accent bar */}
          <div className="h-1 w-full bg-gradient-to-r from-primary-400 to-primary-600 group-hover:from-primary-500 group-hover:to-primary-700 transition-colors"></div>
        </>
      )}
    </Card>
  )
}