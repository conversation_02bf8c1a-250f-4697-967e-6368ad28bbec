// Types pour les lots dans ORBIS

export enum LotPhase {
  ESQ = 'ESQ',      // Esquisse
  APD = 'APD',      // Avant-Projet Détaillé
  PRODCE = 'PRODCE', // Projet de Conception et d'Exécution
  EXE = 'EXE'       // Exécution
}

export interface LotIntervenant {
  id: number;
  lot_id: number;
  company_id: number;
  role?: string;
  is_active: boolean;
  created_at: string;
  company?: {
    id: number;
    name: string;
    email?: string;
    phone?: string;
  };
}

export interface LotDocument {
  id: number;
  lot_id: number;
  document_id: number;
  phase?: LotPhase;
  created_at: string;
  document?: {
    id: number;
    name: string;
    type: string;
  };
}

export interface Lot {
  id: number;
  name: string;
  code: string;
  description?: string;
  project_id: number;
  workspace_id: number;

  // Photo du lot
  photo_url?: string;
  photo_filename?: string;

  // Phase actuelle
  current_phase: LotPhase;
  
  // Historique des phases validées
  esq_validated: boolean;
  esq_validated_at?: string;
  esq_validated_by?: number;
  
  apd_validated: boolean;
  apd_validated_at?: string;
  apd_validated_by?: number;
  
  prodce_validated: boolean;
  prodce_validated_at?: string;
  prodce_validated_by?: number;
  
  exe_validated: boolean;
  exe_validated_at?: string;
  exe_validated_by?: number;
  
  // Métadonnées
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by?: number;
  
  // Relations
  intervenants?: LotIntervenant[];
  documents?: LotDocument[];
  project?: {
    id: number;
    name: string;
  };
}

export interface LotCreate {
  name: string;
  code?: string;
  description?: string;
  project_id: number;
  photo_url?: string;
  photo_filename?: string;
}

export interface LotUpdate {
  name?: string;
  code?: string;
  description?: string;
  current_phase?: LotPhase;
  photo_url?: string;
  photo_filename?: string;
}

export interface LotPhaseValidation {
  phase: LotPhase;
  validated: boolean;
}

export interface LotStats {
  total_active: number;
  total_inactive: number;
  by_phase: Record<string, number>;
}

// Utilitaires pour les phases
export const PHASE_LABELS: Record<LotPhase, string> = {
  [LotPhase.ESQ]: 'ESQ',
  [LotPhase.APD]: 'APD',
  [LotPhase.PRODCE]: 'PRODCE',
  [LotPhase.EXE]: 'EXE'
};

export const PHASE_COLORS: Record<LotPhase, string> = {
  [LotPhase.ESQ]: 'bg-blue-100 text-blue-800',
  [LotPhase.APD]: 'bg-yellow-100 text-yellow-800',
  [LotPhase.PRODCE]: 'bg-orange-100 text-orange-800',
  [LotPhase.EXE]: 'bg-green-100 text-green-800'
};

export const PHASE_ORDER: LotPhase[] = [
  LotPhase.ESQ,
  LotPhase.APD,
  LotPhase.PRODCE,
  LotPhase.EXE
];

// Fonctions utilitaires
export function getPhaseProgress(lot: Lot): Record<string, boolean> {
  return {
    ESQ: lot.esq_validated,
    APD: lot.apd_validated,
    PRODCE: lot.prodce_validated,
    EXE: lot.exe_validated
  };
}

export function getNextPhase(lot: Lot): LotPhase | null {
  if (!lot.esq_validated) return LotPhase.ESQ;
  if (!lot.apd_validated) return LotPhase.APD;
  if (!lot.prodce_validated) return LotPhase.PRODCE;
  if (!lot.exe_validated) return LotPhase.EXE;
  return null;
}

export function canValidatePhase(lot: Lot, phase: LotPhase): boolean {
  switch (phase) {
    case LotPhase.ESQ:
      return true;
    case LotPhase.APD:
      return lot.esq_validated;
    case LotPhase.PRODCE:
      return lot.apd_validated;
    case LotPhase.EXE:
      return lot.prodce_validated;
    default:
      return false;
  }
}

export function getPhaseProgressPercentage(lot: Lot): number {
  const phases = [lot.esq_validated, lot.apd_validated, lot.prodce_validated, lot.exe_validated];
  const validatedCount = phases.filter(Boolean).length;
  return (validatedCount / phases.length) * 100;
}
