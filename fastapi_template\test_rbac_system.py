#!/usr/bin/env python3
"""
Script de test pour le système RBAC complet
"""
import asyncio
import asyncpg
from app.core.config import settings

async def test_rbac_system():
    print("🧪 TEST DU SYSTÈME RBAC COMPLET")
    print("=" * 50)
    
    try:
        conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)
        
        # 1. Vérifier la structure des tables
        print("\n1. 📋 Vérification de la structure des tables...")
        
        # Vérifier les nouvelles tables RBAC
        rbac_tables = ['roles', 'permissions', 'company_role_permissions']
        for table in rbac_tables:
            count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
            print(f"   ✅ Table {table}: {count} enregistrements")
        
        # 2. Vérifier les données de base
        print("\n2. 📊 Vérification des données de base...")
        
        # Compter les rôles
        roles_count = await conn.fetchval("SELECT COUNT(*) FROM roles")
        print(f"   ✅ Rôles disponibles: {roles_count}")
        
        # Compter les permissions
        permissions_count = await conn.fetchval("SELECT COUNT(*) FROM permissions")
        print(f"   ✅ Permissions disponibles: {permissions_count}")
        
        # Compter les entreprises
        companies_count = await conn.fetchval("SELECT COUNT(*) FROM companies")
        print(f"   ✅ Entreprises créées: {companies_count}")
        
        # Compter les utilisateurs
        users_count = await conn.fetchval("SELECT COUNT(*) FROM users")
        print(f"   ✅ Utilisateurs créés: {users_count}")
        
        # 3. Tester les permissions par entreprise
        print("\n3. 🔐 Test des permissions par entreprise...")
        
        # Récupérer une entreprise de test
        company = await conn.fetchrow("SELECT id, name FROM companies LIMIT 1")
        if company:
            company_id = company['id']
            company_name = company['name']
            print(f"   🏢 Test avec l'entreprise: {company_name} (ID: {company_id})")
            
            # Vérifier les permissions configurées pour cette entreprise
            permissions_in_company = await conn.fetchval("""
                SELECT COUNT(*) FROM company_role_permissions 
                WHERE company_id = $1
            """, company_id)
            print(f"   ✅ Permissions configurées: {permissions_in_company}")
            
            # Lister les rôles utilisés dans cette entreprise
            roles_in_company = await conn.fetch("""
                SELECT DISTINCT role_name, COUNT(*) as permission_count
                FROM company_role_permissions 
                WHERE company_id = $1
                GROUP BY role_name
                ORDER BY role_name
            """, company_id)
            
            print(f"   📋 Rôles dans {company_name}:")
            for role in roles_in_company:
                print(f"      - {role['role_name']}: {role['permission_count']} permissions")
        
        # 4. Tester les utilisateurs et leurs rôles
        print("\n4. 👥 Test des utilisateurs et leurs rôles...")
        
        # Récupérer les utilisateurs avec leurs rôles d'entreprise
        users_with_roles = await conn.fetch("""
            SELECT 
                u.email, u.role as system_role,
                uc.role_name as company_role,
                c.name as company_name
            FROM users u
            JOIN user_companies uc ON u.id = uc.user_id
            JOIN companies c ON uc.company_id = c.id
            WHERE u.role != 'SUPER_ADMIN'
            ORDER BY c.name, u.email
        """)
        
        print(f"   📊 Utilisateurs avec rôles d'entreprise:")
        current_company = None
        for user in users_with_roles:
            if user['company_name'] != current_company:
                current_company = user['company_name']
                print(f"   🏢 {current_company}:")
            print(f"      - {user['email']}: {user['company_role']} (système: {user['system_role']})")
        
        # 5. Test des permissions spécifiques
        print("\n5. 🔍 Test des permissions spécifiques...")
        
        # Tester quelques permissions pour un utilisateur
        test_user = await conn.fetchrow("""
            SELECT u.id, u.email, uc.company_id, uc.role_name
            FROM users u
            JOIN user_companies uc ON u.id = uc.user_id
            WHERE u.role != 'SUPER_ADMIN'
            LIMIT 1
        """)
        
        if test_user:
            user_id = test_user['id']
            user_email = test_user['email']
            company_id = test_user['company_id']
            role_name = test_user['role_name']
            
            print(f"   👤 Test avec utilisateur: {user_email} (rôle: {role_name})")
            
            # Récupérer les permissions de cet utilisateur
            user_permissions = await conn.fetch("""
                SELECT p.name, p.resource, p.action
                FROM permissions p
                JOIN company_role_permissions crp ON p.id = crp.permission_id
                WHERE crp.company_id = $1 AND crp.role_name = $2
                ORDER BY p.resource, p.action
            """, company_id, role_name)
            
            print(f"   ✅ Permissions de {user_email}:")
            current_resource = None
            for perm in user_permissions:
                if perm['resource'] != current_resource:
                    current_resource = perm['resource']
                    print(f"      📁 {current_resource}:")
                print(f"         - {perm['action']} ({perm['name']})")
        
        # 6. Vérifier l'intégrité du système
        print("\n6. ✅ Vérification de l'intégrité du système...")
        
        # Vérifier qu'il n'y a pas de permissions orphelines
        orphan_permissions = await conn.fetchval("""
            SELECT COUNT(*) FROM company_role_permissions crp
            LEFT JOIN permissions p ON crp.permission_id = p.id
            WHERE p.id IS NULL
        """)
        
        if orphan_permissions == 0:
            print("   ✅ Aucune permission orpheline détectée")
        else:
            print(f"   ⚠️  {orphan_permissions} permissions orphelines détectées")
        
        # Vérifier qu'il n'y a pas d'utilisateurs sans entreprise
        users_without_company = await conn.fetchval("""
            SELECT COUNT(*) FROM users u
            LEFT JOIN user_companies uc ON u.id = uc.user_id
            WHERE uc.user_id IS NULL AND u.role != 'SUPER_ADMIN'
        """)
        
        if users_without_company == 0:
            print("   ✅ Tous les utilisateurs sont associés à une entreprise")
        else:
            print(f"   ⚠️  {users_without_company} utilisateurs sans entreprise")
        
        # 7. Test de performance
        print("\n7. ⚡ Test de performance...")
        
        import time
        start_time = time.time()
        
        # Test de requête complexe de permissions
        complex_query_result = await conn.fetchval("""
            SELECT COUNT(DISTINCT u.id) 
            FROM users u
            JOIN user_companies uc ON u.id = uc.user_id
            JOIN company_role_permissions crp ON uc.company_id = crp.company_id AND uc.role_name = crp.role_name
            JOIN permissions p ON crp.permission_id = p.id
            WHERE p.resource = 'projects' AND p.action = 'read'
        """)
        
        end_time = time.time()
        query_time = (end_time - start_time) * 1000  # en millisecondes
        
        print(f"   ✅ Requête complexe exécutée en {query_time:.2f}ms")
        print(f"   📊 {complex_query_result} utilisateurs peuvent lire les projets")
        
        await conn.close()
        
        # 8. Résumé final
        print("\n" + "=" * 50)
        print("✅ TEST DU SYSTÈME RBAC TERMINÉ AVEC SUCCÈS!")
        print("=" * 50)
        print("\n📊 Résumé des tests:")
        print(f"   - {roles_count} rôles configurés")
        print(f"   - {permissions_count} permissions définies")
        print(f"   - {companies_count} entreprises créées")
        print(f"   - {users_count} utilisateurs (dont super admin)")
        print(f"   - Système d'intégrité: ✅")
        print(f"   - Performance: ✅ ({query_time:.2f}ms)")
        
        print("\n🎉 Le système RBAC est opérationnel!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_rbac_system())
    if success:
        print("\n🚀 Vous pouvez maintenant démarrer l'application!")
        print("   Backend: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000")
        print("   Admin: npm run dev (dans orbis-admin)")
    else:
        print("\n❌ Des problèmes ont été détectés. Vérifiez les logs ci-dessus.")
