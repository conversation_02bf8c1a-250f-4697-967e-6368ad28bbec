# app/models/employee.py
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, ForeignKey, Text, Numeric, Date
from sqlalchemy.orm import relationship
from datetime import datetime
from app.core.database import Base

class Employee(Base):
    __tablename__ = "employees"

    id = Column(Integer, primary_key=True, index=True)
    tcompany_id = Column(Integer, ForeignKey("tcompanies.id"), nullable=False)
    employee_number = Column(String, nullable=False, index=True)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    email = Column(String)
    phone = Column(String)
    address = Column(Text)
    position = Column(String)
    department = Column(String)
    hire_date = Column(Date)
    hourly_rate = Column(Numeric(10, 2))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    tcompany = relationship("TCompany", back_populates="employees")
    time_entries = relationship("TimeEntry", back_populates="employee")
    project_assignments = relationship("ProjectEmployee", back_populates="employee")
    assignments = relationship("EmployeeAssignment", back_populates="employee")

class TimeEntry(Base):
    __tablename__ = "time_entries"

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    project_id = Column(Integer, ForeignKey("projects.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    date = Column(Date, nullable=False)
    hours = Column(Numeric(5, 2), nullable=False)
    description = Column(Text)
    is_overtime = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    employee = relationship("Employee", back_populates="time_entries")
    project = relationship("Project", back_populates="time_entries")
    user = relationship("User", back_populates="time_entries")

class EmployeeAssignment(Base):
    __tablename__ = "employee_assignments"

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"))
    task_name = Column(String, nullable=False)
    description = Column(Text)
    assigned_date = Column(Date, nullable=False)
    due_date = Column(Date)
    status = Column(String, default="assigned")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    employee = relationship("Employee", back_populates="assignments")
    user = relationship("User", back_populates="employee_assignments")