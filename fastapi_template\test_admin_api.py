#!/usr/bin/env python3
"""
Script pour tester l'API admin directement
"""

import asyncio
import asyncpg
from app.core.config import settings
from app.core.database import get_db
from app.models.workspace import Company, UserCompany
from app.models.user import User
from sqlalchemy import select

async def test_database_direct():
    """Tester l'accès direct à la base de données"""
    print("🔍 Test direct de la base de données...")
    
    conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)
    
    try:
        # Test 1: Compter les entreprises
        company_count = await conn.fetchval("SELECT COUNT(*) FROM companies")
        print(f"📊 Nombre d'entreprises: {company_count}")
        
        # Test 2: Lister les entreprises
        companies = await conn.fetch("SELECT id, name, code FROM companies LIMIT 5")
        print("🏢 Entreprises:")
        for company in companies:
            print(f"  - {company['name']} ({company['code']}) - ID: {company['id']}")
        
        # Test 3: Compter les utilisateurs
        user_count = await conn.fetchval("SELECT COUNT(*) FROM users")
        print(f"👤 Nombre d'utilisateurs: {user_count}")
        
        # Test 4: Lister les associations utilisateur-entreprise
        associations = await conn.fetch("""
            SELECT uc.company_id, uc.user_id, u.email, c.name as company_name
            FROM user_companies uc
            JOIN users u ON uc.user_id = u.id
            JOIN companies c ON uc.company_id = c.id
            LIMIT 10
        """)
        print("🔗 Associations utilisateur-entreprise:")
        for assoc in associations:
            print(f"  - {assoc['email']} -> {assoc['company_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()

async def test_sqlalchemy_models():
    """Tester l'accès via SQLAlchemy"""
    print("\n🔍 Test via SQLAlchemy...")
    
    try:
        async for db in get_db():
            # Test 1: Récupérer les entreprises
            result = await db.execute(select(Company))
            companies = result.scalars().all()
            print(f"📊 Entreprises via SQLAlchemy: {len(companies)}")
            
            for company in companies[:3]:
                print(f"  - {company.name} ({company.code}) - ID: {company.id}")
                
                # Test 2: Récupérer les utilisateurs de cette entreprise
                user_result = await db.execute(
                    select(User, UserCompany)
                    .join(UserCompany, User.id == UserCompany.user_id)
                    .where(UserCompany.company_id == company.id)
                )
                user_data = user_result.all()
                print(f"    👥 {len(user_data)} utilisateur(s)")
                
                for user, user_company in user_data:
                    print(f"      - {user.email} ({user.role})")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur SQLAlchemy: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Fonction principale"""
    print("🚀 Test de l'API Admin ORBIS")
    print("="*50)
    
    # Test 1: Base de données directe
    db_ok = await test_database_direct()
    
    # Test 2: SQLAlchemy
    sqlalchemy_ok = await test_sqlalchemy_models()
    
    print("\n📋 Résumé des tests:")
    print(f"  Base de données directe: {'✅' if db_ok else '❌'}")
    print(f"  SQLAlchemy: {'✅' if sqlalchemy_ok else '❌'}")
    
    if db_ok and sqlalchemy_ok:
        print("\n🎯 Les données sont accessibles. Le problème est probablement dans l'authentification ou la sérialisation.")
        print("\n💡 Suggestions:")
        print("  1. Vérifier l'authentification Supabase")
        print("  2. Vérifier les logs FastAPI pour les erreurs de sérialisation")
        print("  3. Tester l'endpoint avec un token valide")
    else:
        print("\n❌ Problème d'accès aux données détecté.")

if __name__ == "__main__":
    asyncio.run(main())
