"""Add logo fields to entreprise_tiers

⚠️ OBSOLETE: Cette migration est remplacée par TCompanies
Cette migration est conservée pour l'historique mais ne doit plus être utilisée.

Revision ID: add_logo_entreprise_tiers
Revises:
Create Date: 2025-01-07 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_logo_entreprise_tiers'
down_revision = None  # À remplacer par la dernière révision
branch_labels = None
depends_on = None


def upgrade():
    """Add logo_url and logo_filename columns to entreprise_tiers table"""
    op.add_column('entreprise_tiers', sa.Column('logo_url', sa.String(length=500), nullable=True))
    op.add_column('entreprise_tiers', sa.Column('logo_filename', sa.String(length=255), nullable=True))


def downgrade():
    """Remove logo_url and logo_filename columns from entreprise_tiers table"""
    op.drop_column('entreprise_tiers', 'logo_filename')
    op.drop_column('entreprise_tiers', 'logo_url')
