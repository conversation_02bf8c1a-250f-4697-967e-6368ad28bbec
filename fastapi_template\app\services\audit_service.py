# app/services/audit_service.py
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Request
import logging

from app.models.audit import AuditLog, AuditAction
from app.models.user import User

logger = logging.getLogger(__name__)


class AuditService:
    """
    Service pour gérer l'audit trail
    """
    
    @staticmethod
    async def log_action(
        db: AsyncSession,
        workspace_id: int,
        action: AuditAction,
        resource_type: str,
        user_id: Optional[int] = None,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> AuditLog:
        """
        Log an audit action
        """
        try:
            audit_log = AuditLog(
                workspace_id=workspace_id,
                user_id=user_id,
                action=action.value,
                resource_type=resource_type,
                resource_id=str(resource_id) if resource_id else None,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.add(audit_log)
            await db.commit()
            await db.refresh(audit_log)
            
            logger.info(f"Audit log created: {action.value} on {resource_type}:{resource_id} by user {user_id}")
            return audit_log
            
        except Exception as e:
            logger.error(f"Failed to create audit log: {e}")
            await db.rollback()
            raise

    @staticmethod
    async def log_from_request(
        db: AsyncSession,
        request: Request,
        action: AuditAction,
        resource_type: str,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> Optional[AuditLog]:
        """
        Log an audit action from a FastAPI request
        """
        try:
            # Get context from request state (set by tenant middleware)
            workspace_id = getattr(request.state, 'workspace_id', None)
            user = getattr(request.state, 'user', None)
            
            if not workspace_id:
                logger.warning("No company context available for audit log")
                return None
            
            # Extract client info
            ip_address = request.client.host if request.client else None
            user_agent = request.headers.get("user-agent")
            
            return await AuditService.log_action(
                db=db,
                workspace_id=workspace_id,
                action=action,
                resource_type=resource_type,
                user_id=user.id if user else None,
                resource_id=resource_id,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
        except Exception as e:
            logger.error(f"Failed to log audit from request: {e}")
            return None

    @staticmethod
    async def log_user_action(
        db: AsyncSession,
        user: User,
        workspace_id: int,
        action: AuditAction,
        resource_type: str,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> AuditLog:
        """
        Log an audit action for a specific user
        """
        return await AuditService.log_action(
            db=db,
            workspace_id=workspace_id,
            action=action,
            resource_type=resource_type,
            user_id=user.id,
            resource_id=resource_id,
            details=details
        )

    @staticmethod
    async def log_login(
        db: AsyncSession,
        user: User,
        workspace_id: int,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> AuditLog:
        """
        Log user login
        """
        return await AuditService.log_action(
            db=db,
            workspace_id=workspace_id,
            action=AuditAction.LOGIN,
            resource_type="auth",
            user_id=user.id,
            resource_id=str(user.id),
            details={"email": user.email},
            ip_address=ip_address,
            user_agent=user_agent
        )

    @staticmethod
    async def log_invitation(
        db: AsyncSession,
        inviter: User,
        workspace_id: int,
        invited_email: str,
        role: str
    ) -> AuditLog:
        """
        Log user invitation
        """
        return await AuditService.log_action(
            db=db,
            workspace_id=workspace_id,
            action=AuditAction.INVITE,
            resource_type="user_invitation",
            user_id=inviter.id,
            details={
                "invited_email": invited_email,
                "role": role,
                "inviter_email": inviter.email
            }
        )
