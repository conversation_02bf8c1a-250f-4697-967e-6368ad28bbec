import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import CoverPageNodeView from '../components/CoverPageNodeView'

export interface CoverPageOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    coverPage: {
      /**
       * Insérer une page de garde
       */
      insertCoverPage: (content: string) => ReturnType
    }
  }
}

export const CoverPageNode = Node.create<CoverPageOptions>({
  name: 'coverPage',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  group: 'block',

  content: '',

  marks: '',

  atom: true,

  addAttributes() {
    return {
      content: {
        default: '',
        parseHTML: element => element.getAttribute('data-content') || element.innerHTML,
        renderHTML: attributes => {
          return {
            'data-content': attributes.content,
          }
        },
      },
      class: {
        default: 'cover-page-container',
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          return {
            class: attributes.class,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="cover-page"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(
        {
          'data-type': 'cover-page',
          'data-content': HTMLAttributes.content,
          class: 'cover-page-container',
        },
        this.options.HTMLAttributes,
        HTMLAttributes
      ),
      HTMLAttributes.content || '',
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(CoverPageNodeView)
  },

  addCommands() {
    return {
      insertCoverPage:
        (content: string) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: {
              content,
            },
          })
        },
    }
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Shift-C': () => {
        // Raccourci pour insérer une page de garde
        const content = '<div class="pagedegarde">Page de garde...</div>'
        return this.editor.commands.insertCoverPage(content)
      },
    }
  },
})

export default CoverPageNode
