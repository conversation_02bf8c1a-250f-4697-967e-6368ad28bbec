"""
Test complet de l'API des lots après correction du DetachedInstanceError
Utilisateur: <EMAIL>
Mot de passe: orbis123!
"""

import requests
import json
import time
from typing import List, Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "orbis123!"

def authenticate() -> str:
    """Authentification et récupération du token"""
    print("🔐 Authentification...")
    
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/auth/login",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        token_data = response.json()
        access_token = token_data.get("access_token")
        print(f"✅ Authentification réussie!")
        return access_token
    else:
        print(f"❌ Erreur d'authentification: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def get_projects(token: str) -> List[Dict[str, Any]]:
    """Récupérer les projets"""
    print(f"\n🏗️ Récupération des projets...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/projects/",
        headers=headers
    )
    
    if response.status_code == 200:
        projects = response.json()
        print(f"✅ {len(projects)} projet(s) trouvé(s)")
        return projects
    else:
        print(f"❌ Erreur récupération projets: {response.status_code}")
        return []

def test_create_lot(token: str, project_id: int) -> Dict[str, Any]:
    """Test de création de lot"""
    print(f"\n🔨 Test création de lot pour projet {project_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    timestamp = int(time.time())
    lot_data = {
        "name": f"Lot Test Complet ({timestamp})",
        "code": f"TEST_LOT_{timestamp}",
        "description": f"Lot de test complet créé le {timestamp}",
        "project_id": project_id
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/lots/",
        json=lot_data,
        headers=headers
    )
    
    if response.status_code == 201:
        lot = response.json()
        print(f"✅ Lot créé: ID {lot.get('id')}, Nom: {lot.get('name')}")
        return lot
    else:
        print(f"❌ Erreur création lot: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_get_lot(token: str, lot_id: int) -> Dict[str, Any]:
    """Test de récupération d'un lot par ID"""
    print(f"\n📋 Test récupération lot {lot_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/lots/{lot_id}",
        headers=headers
    )
    
    if response.status_code == 200:
        lot = response.json()
        print(f"✅ Lot récupéré: {lot.get('name')} (Phase: {lot.get('current_phase')})")
        return lot
    else:
        print(f"❌ Erreur récupération lot: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_update_lot(token: str, lot_id: int) -> Dict[str, Any]:
    """Test de mise à jour d'un lot"""
    print(f"\n✏️ Test mise à jour lot {lot_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    update_data = {
        "description": f"Description mise à jour le {int(time.time())}"
    }
    
    response = requests.put(
        f"{BASE_URL}/api/v1/lots/{lot_id}",
        json=update_data,
        headers=headers
    )
    
    if response.status_code == 200:
        lot = response.json()
        print(f"✅ Lot mis à jour: {lot.get('description')}")
        return lot
    else:
        print(f"❌ Erreur mise à jour lot: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_validate_phase(token: str, lot_id: int, phase: str) -> Dict[str, Any]:
    """Test de validation d'une phase"""
    print(f"\n✅ Test validation phase {phase} pour lot {lot_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    validation_data = {
        "phase": phase,
        "validated": True
    }
    
    response = requests.put(
        f"{BASE_URL}/api/v1/lots/{lot_id}/validate-phase",
        json=validation_data,
        headers=headers
    )
    
    if response.status_code == 200:
        lot = response.json()
        print(f"✅ Phase {phase} validée. Phase actuelle: {lot.get('current_phase')}")
        return lot
    else:
        print(f"❌ Erreur validation phase: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_get_lots_by_project(token: str, project_id: int) -> List[Dict[str, Any]]:
    """Test de récupération des lots par projet"""
    print(f"\n📊 Test récupération lots du projet {project_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/lots/project/{project_id}",
        headers=headers
    )
    
    if response.status_code == 200:
        lots = response.json()
        print(f"✅ {len(lots)} lot(s) trouvé(s) dans le projet {project_id}")
        for lot in lots:
            print(f"   - {lot.get('name')} (Phase: {lot.get('current_phase')})")
        return lots
    else:
        print(f"❌ Erreur récupération lots par projet: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def test_get_lots_stats(token: str) -> Dict[str, Any]:
    """Test de récupération des statistiques des lots"""
    print(f"\n📈 Test récupération statistiques lots...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/lots/stats",
        headers=headers
    )
    
    if response.status_code == 200:
        stats = response.json()
        print(f"✅ Statistiques récupérées:")
        print(f"   - Lots actifs: {stats.get('total_active')}")
        print(f"   - Lots inactifs: {stats.get('total_inactive')}")
        print(f"   - Par phase: {stats.get('by_phase')}")
        return stats
    else:
        print(f"❌ Erreur récupération statistiques: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_get_all_lots(token: str) -> List[Dict[str, Any]]:
    """Test de récupération de tous les lots"""
    print(f"\n📋 Test récupération de tous les lots...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/lots/",
        headers=headers
    )
    
    if response.status_code == 200:
        lots = response.json()
        print(f"✅ {len(lots)} lot(s) total trouvé(s)")
        return lots
    else:
        print(f"❌ Erreur récupération tous les lots: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def test_delete_lot(token: str, lot_id: int) -> bool:
    """Test de suppression d'un lot (soft delete)"""
    print(f"\n🗑️ Test suppression lot {lot_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.delete(
        f"{BASE_URL}/api/v1/lots/{lot_id}",
        headers=headers
    )
    
    if response.status_code == 204:
        print(f"✅ Lot {lot_id} supprimé (soft delete)")
        return True
    else:
        print(f"❌ Erreur suppression lot: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def run_complete_test():
    """Exécuter tous les tests"""
    print("🚀 TEST COMPLET DE L'API LOTS")
    print("=" * 50)
    
    # 1. Authentification
    token = authenticate()
    if not token:
        return
    
    # 2. Récupération des projets
    projects = get_projects(token)
    if not projects:
        print("❌ Aucun projet trouvé")
        return
    
    project_id = projects[0].get('id')
    print(f"\n🎯 Utilisation du projet ID: {project_id}")
    
    # 3. Test de création de lot
    lot = test_create_lot(token, project_id)
    if not lot:
        return
    
    lot_id = lot.get('id')
    
    # 4. Test de récupération du lot
    test_get_lot(token, lot_id)
    
    # 5. Test de mise à jour du lot
    test_update_lot(token, lot_id)
    
    # 6. Test de validation des phases
    test_validate_phase(token, lot_id, "ESQ")
    test_validate_phase(token, lot_id, "APD")
    
    # 7. Test de récupération des lots par projet
    test_get_lots_by_project(token, project_id)
    
    # 8. Test de récupération de tous les lots
    test_get_all_lots(token)
    
    # 9. Test des statistiques
    test_get_lots_stats(token)
    
    # 10. Test de suppression (optionnel)
    # test_delete_lot(token, lot_id)
    
    print(f"\n🎉 TESTS TERMINÉS")
    print("=" * 30)
    print("✅ Tous les endpoints testés avec succès!")
    print(f"📊 Lot de test créé: ID {lot_id}")

if __name__ == "__main__":
    try:
        run_complete_test()
    except requests.exceptions.ConnectionError:
        print("❌ Serveur non accessible sur http://localhost:8000")
        print("💡 Assurez-vous que le serveur FastAPI est démarré")
    except Exception as e:
        print(f"❌ Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
