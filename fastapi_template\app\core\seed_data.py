# app/core/seed_data.py
import asyncio
from datetime import datetime, timedelta
from sqlalchemy import select
from app.core.database import AsyncSessionLocal
from app.models.user import User
from app.models.workspace import Workspace, UserWorkspace, WorkspaceRole
from app.models.project import Project
from app.models.employee import Employee
from app.models.supplier import Supplier, SupplierContact
from app.models.material import Material
from app.models.financial import Budget, Invoice, Payment
from app.models.purchase_order import PurchaseOrder
from app.models.quote import Quote
from app.models.document import Document
from app.core.security import get_password_hash

async def create_seed_data():
    """Create comprehensive test data for ORBIS Suivi Travaux"""
    async with AsyncSessionLocal() as session:
        try:
            # Check if data already exists
            result = await session.execute(select(User).limit(1))
            if result.scalar_one_or_none():
                print("✓ Seed data already exists, skipping...")
                return

            print("🌱 Creating seed data...")

            # 1. Create Companies
            companies_data = [
                {
                    "name": "EITP Construction",
                    "code": "EITP001",
                    "email": "<EMAIL>",
                    "phone": "+33 1 42 86 75 30",
                    "address": "15 Avenue des Champs-Élysées, 75008 Paris",
                    "siret": "12345678901234"
                },
                {
                    "name": "BTP Solutions",
                    "code": "BTP002",
                    "email": "<EMAIL>",
                    "phone": "+33 1 45 67 89 10",
                    "address": "25 Rue de la République, 69002 Lyon",
                    "siret": "23456789012345"
                },
                {
                    "name": "Matériaux Pro",
                    "code": "MAT003",
                    "email": "<EMAIL>",
                    "phone": "+33 4 91 23 45 67",
                    "address": "50 Boulevard Michelet, 13008 Marseille",
                    "siret": "34567890123456"
                }
            ]

            companies = []
            for company_data in companies_data:
                company = Workspace(**company_data)
                session.add(company)
                companies.append(company)
            await session.flush()

            # 2. Create Users
            users_data = [
                {
                    "email": "<EMAIL>",
                    "hashed_password": get_password_hash("admin123"),
                    "first_name": "Jean",
                    "last_name": "Dupont",
                    "role": "admin",
                    "is_active": True,
                    "is_superuser": True
                },
                {
                    "email": "<EMAIL>",
                    "hashed_password": get_password_hash("chef123"),
                    "first_name": "Marie",
                    "last_name": "Martin",
                    "role": "chef_projet",
                    "is_active": True
                },
                {
                    "email": "<EMAIL>",
                    "hashed_password": get_password_hash("conducteur123"),
                    "first_name": "Pierre",
                    "last_name": "Bernard",
                    "role": "employe",
                    "is_active": True
                },
                {
                    "email": "<EMAIL>",
                    "hashed_password": get_password_hash("ouvrier123"),
                    "first_name": "Antoine",
                    "last_name": "Moreau",
                    "role": "employe",
                    "is_active": True
                }
            ]

            users = []
            for user_data in users_data:
                user = User(**user_data)
                session.add(user)
                users.append(user)
            await session.flush()

            # 3. Create Projects
            projects_data = [
                {
                    "name": "Résidence Les Jardins",
                    "code": "RLJ2024",
                    "description": "Construction de 120 logements collectifs avec espaces verts",
                    "address": "Avenue de la Liberté, 94300 Vincennes",
                    "start_date": datetime.now() - timedelta(days=60),
                    "end_date": datetime.now() + timedelta(days=300),
                    "budget_total": 15000000.00,
                    "status": "exe",
                    "client_name": "Société Immobilière des Jardins",
                    "client_contact": "<EMAIL>",
                    "workspace_id": companies[0].id
                },
                {
                    "name": "Centre Commercial Atlantis",
                    "code": "CCA2024",
                    "description": "Rénovation complète du centre commercial avec extension",
                    "address": "Zone Atlantis, 44800 Saint-Herblain",
                    "start_date": datetime.now() - timedelta(days=30),
                    "end_date": datetime.now() + timedelta(days=180),
                    "budget_total": 8500000.00,
                    "status": "exe",
                    "client_name": "Atlantis Commercial",
                    "client_contact": "<EMAIL>",
                    "workspace_id": companies[0].id
                },
                {
                    "name": "Immeuble de Bureaux Horizon",
                    "code": "IBH2024",
                    "description": "Construction d'un immeuble de bureaux de 8 étages",
                    "address": "Quartier d'affaires, 69003 Lyon",
                    "start_date": datetime.now() + timedelta(days=15),
                    "end_date": datetime.now() + timedelta(days=450),
                    "budget_total": 22000000.00,
                    "status": "dao",
                    "client_name": "Horizon Business Center",
                    "client_contact": "<EMAIL>",
                    "workspace_id": companies[0].id
                }
            ]

            projects = []
            for project_data in projects_data:
                project = Project(**project_data)
                session.add(project)
                projects.append(project)
            await session.flush()

            # 4. Create Employees
            employees_data = [
                {
                    "employee_number": "EMP001",
                    "first_name": "Julien",
                    "last_name": "Leroy",
                    "email": "<EMAIL>",
                    "phone": "+33 6 12 34 56 78",
                    "position": "Maçon",
                    "department": "Construction",
                    "hourly_rate": 25.50,
                    "hire_date": (datetime.now() - timedelta(days=365)).date(),
                    "is_active": True,
                    "workspace_id": companies[0].id
                },
                {
                    "employee_number": "EMP002",
                    "first_name": "Sophie",
                    "last_name": "Dubois",
                    "email": "<EMAIL>",
                    "phone": "+33 6 23 45 67 89",
                    "position": "Électricienne",
                    "department": "Électricité",
                    "hourly_rate": 28.00,
                    "hire_date": (datetime.now() - timedelta(days=200)).date(),
                    "is_active": True,
                    "workspace_id": companies[0].id
                },
                {
                    "employee_number": "EMP003",
                    "first_name": "Marc",
                    "last_name": "Rousseau",
                    "email": "<EMAIL>",
                    "phone": "+33 6 34 56 78 90",
                    "position": "Plombier",
                    "department": "Plomberie",
                    "hourly_rate": 26.75,
                    "hire_date": (datetime.now() - timedelta(days=150)).date(),
                    "is_active": True,
                    "workspace_id": companies[0].id
                }
            ]

            employees = []
            for employee_data in employees_data:
                employee = Employee(**employee_data)
                session.add(employee)
                employees.append(employee)
            await session.flush()

            # 5. Create Suppliers
            suppliers_data = [
                {
                    "name": "Béton Express",
                    "code": "BE001",
                    "type": "supplier",
                    "email": "<EMAIL>",
                    "phone": "+33 1 56 78 90 12",
                    "address": "Zone Industrielle, 77100 Meaux",
                    "siret": "12345678901234",
                    "payment_terms": "30 jours",
                    "rating": 4.5,
                    "workspace_id": companies[0].id
                },
                {
                    "name": "Électro Pro",
                    "code": "EP002",
                    "type": "supplier",
                    "email": "<EMAIL>",
                    "phone": "+33 4 67 89 01 23",
                    "address": "15 Rue de l'Industrie, 34000 Montpellier",
                    "siret": "23456789012345",
                    "payment_terms": "45 jours",
                    "rating": 4.2,
                    "workspace_id": companies[0].id
                }
            ]

            suppliers = []
            for supplier_data in suppliers_data:
                supplier = Supplier(**supplier_data)
                session.add(supplier)
                suppliers.append(supplier)
            await session.flush()

            # 6. Create Materials
            materials_data = [
                {
                    "name": "Béton C25/30",
                    "code": "BET001",
                    "description": "Béton haute résistance pour structure",
                    "unit": "m³",
                    "current_price": 85.50,
                    "minimum_quantity": 20,
                    "maximum_quantity": 500,
                    "workspace_id": companies[0].id,
                    "supplier_id": suppliers[0].id
                },
                {
                    "name": "Câble électrique 16mm²",
                    "code": "CAB001",
                    "description": "Câble rigide R2V 3G16mm²",
                    "unit": "mètre",
                    "current_price": 4.25,
                    "minimum_quantity": 100,
                    "maximum_quantity": 2000,
                    "workspace_id": companies[0].id,
                    "supplier_id": suppliers[1].id
                },
                {
                    "name": "Parpaing 20x20x50",
                    "code": "PAR001",
                    "description": "Bloc béton creux standard",
                    "unit": "unité",
                    "current_price": 1.85,
                    "minimum_quantity": 200,
                    "maximum_quantity": 5000,
                    "workspace_id": companies[0].id,
                    "supplier_id": suppliers[0].id
                }
            ]

            materials = []
            for material_data in materials_data:
                material = Material(**material_data)
                session.add(material)
                materials.append(material)
            await session.flush()

            # 7. Create Budgets
            budget_data = [
                {
                    "workspace_id": companies[0].id,
                    "project_id": projects[0].id,
                    "name": "Budget Résidence Les Jardins",
                    "description": "Budget détaillé pour la construction des 120 logements",
                    "total_amount": 15000000.00,
                    "status": "active",
                    "start_date": datetime.now() - timedelta(days=60),
                    "end_date": datetime.now() + timedelta(days=300)
                },
                {
                    "workspace_id": companies[0].id,
                    "project_id": projects[1].id,
                    "name": "Budget Centre Commercial Atlantis",
                    "description": "Budget pour la rénovation et extension du centre commercial",
                    "total_amount": 8500000.00,
                    "status": "active",
                    "start_date": datetime.now() - timedelta(days=30),
                    "end_date": datetime.now() + timedelta(days=180)
                },
                {
                    "workspace_id": companies[0].id,
                    "project_id": projects[2].id,
                    "name": "Budget Immeuble Horizon",
                    "description": "Budget prévisionnel pour l'immeuble de bureaux",
                    "total_amount": 22000000.00,
                    "status": "draft",
                    "start_date": datetime.now() + timedelta(days=15),
                    "end_date": datetime.now() + timedelta(days=450)
                }
            ]

            budgets = []
            for budget_item in budget_data:
                budget = Budget(**budget_item)
                session.add(budget)
                budgets.append(budget)
            await session.flush()

            # 8. Create Purchase Orders
            purchase_orders_data = [
                {
                    "workspace_id": companies[0].id,
                    "project_id": projects[0].id,
                    "supplier_id": suppliers[0].id,
                    "order_number": "PO-2024-001",
                    "order_date": datetime.now() - timedelta(days=10),
                    "expected_delivery_date": datetime.now() + timedelta(days=5),
                    "total_amount_ht": 13125.00,
                    "vat_amount": 2625.00,
                    "total_amount_ttc": 15750.00,
                    "status": "confirmed",
                    "notes": "Livraison sur site principal - Prévenir 24h avant"
                },
                {
                    "workspace_id": companies[0].id,
                    "project_id": projects[1].id,
                    "supplier_id": suppliers[1].id,
                    "order_number": "PO-2024-002",
                    "order_date": datetime.now() - timedelta(days=5),
                    "expected_delivery_date": datetime.now() + timedelta(days=10),
                    "total_amount_ht": 7416.67,
                    "vat_amount": 1483.33,
                    "total_amount_ttc": 8900.00,
                    "status": "sent",
                    "notes": "Installation électrique - Étage 2 et 3"
                }
            ]

            purchase_orders = []
            for po_data in purchase_orders_data:
                purchase_order = PurchaseOrder(**po_data)
                session.add(purchase_order)
                purchase_orders.append(purchase_order)
            await session.flush()

            # 9. Create Quotes
            quotes_data = [
                {
                    "workspace_id": companies[0].id,
                    "project_id": projects[2].id,
                    "supplier_id": suppliers[0].id,
                    "quote_number": "DEVIS-2024-001",
                    "title": "Fourniture béton pour fondations",
                    "description": "Fourniture béton pour fondations - Immeuble Horizon",
                    "quote_date": datetime.now() - timedelta(days=3),
                    "expiry_date": datetime.now() + timedelta(days=30),
                    "total_amount_ht": 104166.67,
                    "vat_amount": 20833.33,
                    "total_amount_ttc": 125000.00,
                    "status": "sent",
                    "notes": "Devis pour béton C25/30 - Quantité importante"
                },
                {
                    "workspace_id": companies[0].id,
                    "project_id": projects[2].id,
                    "supplier_id": suppliers[1].id,
                    "quote_number": "DEVIS-2024-002",
                    "title": "Installation électrique complète",
                    "description": "Installation électrique complète - 8 étages",
                    "quote_date": datetime.now() - timedelta(days=1),
                    "expiry_date": datetime.now() + timedelta(days=45),
                    "total_amount_ht": 65416.67,
                    "vat_amount": 13083.33,
                    "total_amount_ttc": 78500.00,
                    "status": "draft",
                    "notes": "Installation complète avec tableau électrique"
                }
            ]

            quotes = []
            for quote_data in quotes_data:
                quote = Quote(**quote_data)
                session.add(quote)
                quotes.append(quote)
            await session.flush()

            # 10. Create Documents
            documents_data = [
                {
                    "workspace_id": companies[0].id,
                    "name": "Plan architectural - Résidence Les Jardins",
                    "original_name": "residence_jardins_v2.pdf",
                    "category": "DAO",
                    "file_path": "/uploads/plans/residence_jardins_v2.pdf",
                    "file_size": 2048576,
                    "mime_type": "application/pdf",
                    "uploaded_by": users[1].id,
                    "description": "Plans architecturaux mis à jour avec modifications client"
                },
                {
                    "workspace_id": companies[0].id,
                    "name": "Rapport de contrôle béton",
                    "original_name": "controle_beton_sem23.pdf",
                    "category": "EXE",
                    "file_path": "/uploads/reports/controle_beton_sem23.pdf",
                    "file_size": 512000,
                    "mime_type": "application/pdf",
                    "uploaded_by": users[2].id,
                    "description": "Rapport de contrôle qualité béton - Semaine 23"
                },
                {
                    "workspace_id": companies[0].id,
                    "name": "Facture Matériaux Pro",
                    "original_name": "facture_MP_2024_156.pdf",
                    "category": "FT",
                    "file_path": "/uploads/invoices/facture_MP_2024_156.pdf",
                    "file_size": 256000,
                    "mime_type": "application/pdf",
                    "uploaded_by": users[0].id,
                    "description": "Facture matériaux électriques - Commande PO-2024-002"
                }
            ]

            documents = []
            for doc_data in documents_data:
                document = Document(**doc_data)
                session.add(document)
                documents.append(document)

            await session.commit()
            print("✅ Seed data created successfully!")
            print(f"   - {len(companies)} companies")
            print(f"   - {len(users)} users")
            print(f"   - {len(projects)} projects")
            print(f"   - {len(employees)} employees")
            print(f"   - {len(suppliers)} suppliers")
            print(f"   - {len(materials)} materials")
            print(f"   - {len(budgets)} budgets")
            print(f"   - {len(purchase_orders)} purchase orders")
            print(f"   - {len(quotes)} quotes")
            print(f"   - {len(documents)} documents")

        except Exception as e:
            await session.rollback()
            print(f"❌ Error creating seed data: {e}")
            raise
        finally:
            await session.close()

if __name__ == "__main__":
    asyncio.run(create_seed_data())
