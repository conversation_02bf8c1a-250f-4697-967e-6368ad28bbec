'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { FastAuthService, User } from '@/lib/auth'

// Utiliser le type User du FastAuthService
type AuthUser = User

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastCheck, setLastCheck] = useState<number>(0)

  useEffect(() => {
    let isMounted = true // Éviter les mises à jour si le composant est démonté
    let checkAuthTimeout: NodeJS.Timeout | null = null

    // Vérifier l'utilisateur connecté avec cache de 30 secondes
    const checkAuth = async () => {
      if (!isMounted) return

      const now = Date.now()
      // Cache de 30 secondes pour éviter les requêtes en double
      if (now - lastCheck < 30000 && user) {
        console.log('🔍 AuthContext - Cache valide, pas de vérification')
        setLoading(false)
        return
      }

      console.log('🔍 AuthContext - Vérification auth...')
      try {
        const isAuth = FastAuthService.isAuthenticated()
        console.log('🔍 AuthContext - isAuthenticated:', isAuth)

        if (isAuth) {
          // Vérifier la validité du token avec le backend
          const tokenValid = await FastAuthService.verifyToken()
          console.log('🔍 AuthContext - Token valid:', tokenValid)

          if (!isMounted) return // Vérifier encore après l'appel async

          if (tokenValid) {
            const currentUser = FastAuthService.getUser()
            console.log('🔍 AuthContext - Utilisateur trouvé:', currentUser?.email)
            setUser(currentUser)
            setLastCheck(now)
          } else {
            console.log('🔍 AuthContext - Token invalide ou serveur indisponible, déconnexion...')
            FastAuthService.logout()
            setUser(null)
            // Rediriger vers la page de login si on n'y est pas déjà
            if (typeof window !== 'undefined' && !window.location.pathname.includes('/auth/login')) {
              window.location.href = '/auth/login'
            }
          }
        } else {
          console.log('🔍 AuthContext - Aucun utilisateur connecté')
          setUser(null)
        }
      } catch (error) {
        if (!isMounted) return
        console.error('❌ AuthContext - Erreur vérification auth (serveur probablement indisponible):', error)
        FastAuthService.logout() // Nettoyer si erreur
        setUser(null)
        // Rediriger vers la page de login en cas d'erreur réseau
        if (typeof window !== 'undefined' && !window.location.pathname.includes('/auth/login')) {
          console.log('🔄 Redirection vers /auth/login à cause de l\'erreur réseau')
          window.location.href = '/auth/login'
        }
      } finally {
        if (isMounted) {
          console.log('🔍 AuthContext - Fin du loading')
          setLoading(false)
        }
      }
    }

    checkAuth()

    // Écouter les changements de localStorage pour détecter les connexions/déconnexions
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'orbis_jwt_token' || e.key === 'orbis_user') {
        console.log('🔍 AuthContext - Changement localStorage détecté:', e.key)

        // Debounce pour éviter les appels multiples
        if (checkAuthTimeout) {
          clearTimeout(checkAuthTimeout)
        }

        checkAuthTimeout = setTimeout(() => {
          if (isMounted) checkAuth()
        }, 200)
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      isMounted = false
      if (checkAuthTimeout) {
        clearTimeout(checkAuthTimeout)
      }
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      console.log('🚀 AuthContext - Tentative de connexion pour:', email)
      await FastAuthService.login(email, password)

      // Récupérer directement l'utilisateur après login
      const currentUser = FastAuthService.getUser()
      console.log('✅ AuthContext - Connexion réussie, utilisateur:', currentUser?.email)

      if (currentUser) {
        setUser(currentUser)
        setLoading(false)
      } else {
        throw new Error('Impossible de récupérer les données utilisateur')
      }
    } catch (error) {
      console.error('❌ AuthContext - Erreur de connexion:', error)
      setLoading(false)
      throw error
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      FastAuthService.logout()
      setUser(null)
    } catch (error) {
      console.error('Sign out error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const value = {
    user,
    loading,
    signIn,
    signOut,
    isAuthenticated: !!user
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook pour protéger les routes
export function useRequireAuth() {
  const { user, loading } = useAuth()

  useEffect(() => {
    if (!loading && !user) {
      window.location.href = '/auth/login'
    }
  }, [user, loading])

  return { user, loading }
}
