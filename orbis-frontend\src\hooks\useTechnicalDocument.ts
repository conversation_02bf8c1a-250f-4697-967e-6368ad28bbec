import { useState, useEffect, useCallback } from 'react'
import {
  TechnicalDocumentResponse,
  TechnicalDocumentUpdate,
  TechnicalDocumentCreate,
  CompanySimple,
  UseTechnicalDocumentReturn
} from '@/types/technical-document'
import { FastAuthService } from '@/lib/auth'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export function useTechnicalDocument(documentId?: number): UseTechnicalDocumentReturn {
  const [document, setDocument] = useState<TechnicalDocumentResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Charger un document
  const loadDocument = useCallback(async (id: number) => {
    try {
      setLoading(true)
      setError(null)

      const token = FastAuthService.getToken()
      const response = await fetch(`${API_BASE_URL}/api/v1/technical-documents/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Erreur lors du chargement du document')
      }

      const data = await response.json()
      setDocument(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur inconnue')
    } finally {
      setLoading(false)
    }
  }, [])

  // Sauvegarder les modifications
  const save = useCallback(async (updateData: Partial<TechnicalDocumentUpdate>) => {
    if (!document) {
      throw new Error('Aucun document à sauvegarder')
    }

    try {
      setLoading(true)
      setError(null)

      const token = FastAuthService.getToken()
      const response = await fetch(`${API_BASE_URL}/api/v1/technical-documents/${document.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la sauvegarde')
      }

      const updatedDocument = await response.json()
      setDocument(updatedDocument)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la sauvegarde')
      throw err
    } finally {
      setLoading(false)
    }
  }, [document])

  // Rafraîchir le document
  const refresh = useCallback(async () => {
    if (documentId) {
      await loadDocument(documentId)
    }
  }, [documentId, loadDocument])

  // Charger le document au montage
  useEffect(() => {
    if (documentId) {
      loadDocument(documentId)
    }
  }, [documentId, loadDocument])

  return {
    document,
    loading,
    error,
    save,
    refresh
  }
}

// Hook pour créer un nouveau document
export function useCreateTechnicalDocument() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createDocument = useCallback(async (documentData: TechnicalDocumentCreate): Promise<TechnicalDocumentResponse> => {
    try {
      setLoading(true)
      setError(null)

      const token = FastAuthService.getToken()
      console.log('🔑 Token pour création:', token ? `${token.substring(0, 20)}...` : 'null')
      console.log('📝 Données du document:', documentData)

      const response = await fetch(`${API_BASE_URL}/api/v1/technical-documents`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(documentData)
      })

      console.log('📊 Réponse API:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Erreur API:', errorText)
        throw new Error(`Erreur lors de la création du document: ${response.status}`)
      }

      const newDocument = await response.json()
      console.log('✅ Document créé par l\'API:', newDocument)
      return newDocument
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la création'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    createDocument,
    loading,
    error
  }
}

// Hook pour la sauvegarde automatique
export function useAutoSave(
  document: TechnicalDocumentResponse | null,
  onSave: (data: Partial<TechnicalDocumentUpdate>) => Promise<void>,
  delay: number = 2000
) {
  const [pendingChanges, setPendingChanges] = useState<Partial<TechnicalDocumentUpdate>>({})
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Ajouter des modifications en attente
  const addPendingChange = useCallback((changes: Partial<TechnicalDocumentUpdate>) => {
    setPendingChanges(prev => ({ ...prev, ...changes }))
  }, [])

  // Sauvegarder les modifications en attente
  const saveChanges = useCallback(async () => {
    if (Object.keys(pendingChanges).length === 0 || !document) {
      return
    }

    try {
      setIsSaving(true)
      await onSave(pendingChanges)
      setPendingChanges({})
      setLastSaved(new Date())
    } catch (err) {
      console.error('Erreur lors de la sauvegarde automatique:', err)
    } finally {
      setIsSaving(false)
    }
  }, [pendingChanges, document, onSave])

  // Déclencher la sauvegarde automatique
  useEffect(() => {
    if (Object.keys(pendingChanges).length === 0) {
      return
    }

    const timeoutId = setTimeout(saveChanges, delay)
    return () => clearTimeout(timeoutId)
  }, [pendingChanges, saveChanges, delay])

  // Sauvegarder avant de quitter la page
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (Object.keys(pendingChanges).length > 0) {
        event.preventDefault()
        event.returnValue = 'Vous avez des modifications non sauvegardées. Voulez-vous vraiment quitter ?'
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [pendingChanges])

  return {
    addPendingChange,
    saveChanges,
    isSaving,
    lastSaved,
    hasPendingChanges: Object.keys(pendingChanges).length > 0
  }
}

// Hook pour gérer les relations avec les entreprises
export function useCompanySelection(documentId?: number) {
  const [selectedCompanies, setSelectedCompanies] = useState<CompanySimple[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Mettre à jour les entreprises associées
  const updateCompanies = useCallback(async (companyIds: number[]) => {
    if (!documentId) {
      throw new Error('Aucun document spécifié')
    }

    try {
      setLoading(true)
      setError(null)

      const token = FastAuthService.getToken()
      const response = await fetch(`${API_BASE_URL}/api/v1/technical-documents/${documentId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ company_ids: companyIds })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la mise à jour des entreprises')
      }

      const updatedDocument = await response.json()
      setSelectedCompanies(updatedDocument.companies || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur inconnue')
      throw err
    } finally {
      setLoading(false)
    }
  }, [documentId])

  // Ajouter une entreprise
  const addCompany = useCallback(async (companyId: number) => {
    if (!documentId) return

    try {
      const token = FastAuthService.getToken()
      const response = await fetch(`${API_BASE_URL}/api/v1/technical-documents/${documentId}/companies`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify([companyId])
      })

      if (!response.ok) {
        throw new Error('Erreur lors de l\'ajout de l\'entreprise')
      }

      // Recharger les entreprises
      const currentIds = selectedCompanies.map(c => c.id)
      if (!currentIds.includes(companyId)) {
        await updateCompanies([...currentIds, companyId])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de l\'ajout')
      throw err
    }
  }, [documentId, selectedCompanies, updateCompanies])

  // Retirer une entreprise
  const removeCompany = useCallback(async (companyId: number) => {
    if (!documentId) return

    try {
      const token = FastAuthService.getToken()
      const response = await fetch(`${API_BASE_URL}/api/v1/technical-documents/${documentId}/companies/${companyId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression de l\'entreprise')
      }

      // Mettre à jour la liste locale
      setSelectedCompanies(prev => prev.filter(c => c.id !== companyId))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la suppression')
      throw err
    }
  }, [documentId])

  return {
    selectedCompanies,
    setSelectedCompanies,
    updateCompanies,
    addCompany,
    removeCompany,
    loading,
    error
  }
}
