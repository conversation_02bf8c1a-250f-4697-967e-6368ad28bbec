'use client'

import { useState, useEffect } from 'react'

export default function DebugPage() {
  const [envVars, setEnvVars] = useState<Record<string, string>>({})

  useEffect(() => {
    // Récupérer toutes les variables d'environnement côté client
    const clientEnvVars = {
      'NEXT_PUBLIC_SUPABASE_URL': process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set (hidden)' : 'Not set',
      'NEXT_PUBLIC_API_URL': process.env.NEXT_PUBLIC_API_URL || 'Not set',
      'NEXT_PUBLIC_CUSTOMER_APP_URL': process.env.NEXT_PUBLIC_CUSTOMER_APP_URL || 'Not set',
      'NODE_ENV': process.env.NODE_ENV || 'Not set'
    }
    setEnvVars(clientEnvVars)
  }, [])

  const testSupabaseConnection = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`, {
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`
        }
      })
      
      console.log('Supabase connection test:', response.status, response.statusText)
      return response.ok
    } catch (error) {
      console.error('Supabase connection error:', error)
      return false
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Debug Information</h1>
        
        {/* Environment Variables */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Environment Variables</h2>
          <div className="space-y-2">
            {Object.entries(envVars).map(([key, value]) => (
              <div key={key} className="flex justify-between items-center py-2 border-b border-gray-200">
                <span className="font-mono text-sm text-gray-600">{key}</span>
                <span className="font-mono text-sm text-gray-900 bg-gray-100 px-2 py-1 rounded">
                  {value}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Browser Info */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Browser Information</h2>
          <div className="space-y-2">
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-gray-600">User Agent</span>
              <span className="text-sm text-gray-900 max-w-md truncate">
                {typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-gray-600">Current URL</span>
              <span className="text-sm text-gray-900">
                {typeof window !== 'undefined' ? window.location.href : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-gray-600">Local Storage Available</span>
              <span className="text-sm text-gray-900">
                {typeof window !== 'undefined' && window.localStorage ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="space-y-3">
            <button
              onClick={testSupabaseConnection}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-left"
            >
              Test Supabase Connection
            </button>
            
            <button
              onClick={() => window.location.href = '/test-supabase'}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg text-left"
            >
              Go to Supabase Test Page
            </button>
            
            <button
              onClick={() => window.location.href = '/login'}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-left"
            >
              Go to Login Page
            </button>
            
            <button
              onClick={() => {
                if (typeof window !== 'undefined') {
                  localStorage.clear()
                  sessionStorage.clear()
                  alert('Local storage cleared')
                }
              }}
              className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg text-left"
            >
              Clear Local Storage
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
