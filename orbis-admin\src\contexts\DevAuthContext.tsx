'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'

interface DevAuthUser {
  id: string
  email: string
  user_metadata: {
    first_name?: string
    last_name?: string
    role?: string
    company?: string
  }
}

interface DevAuthContextType {
  user: DevA<PERSON>U<PERSON> | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  isAdmin: boolean
  isSuperAdmin: boolean
}

const DevAuthContext = createContext<DevAuthContextType | undefined>(undefined)

export function DevAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<DevAuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Vérifier s'il y a une session de développement
    const devAuth = localStorage.getItem('dev-auth')
    if (devAuth) {
      try {
        const authData = JSON.parse(devAuth)
        if (authData.session.expires_at > Date.now()) {
          setUser(authData.user)
        } else {
          localStorage.removeItem('dev-auth')
        }
      } catch (error) {
        console.error('Error parsing dev auth:', error)
        localStorage.removeItem('dev-auth')
      }
    }
    setLoading(false)
  }, [])

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    
    // Simuler une authentification
    if (email === '<EMAIL>' && password === 'orbis123!') {
      const devUser: DevAuthUser = {
        id: 'dev-admin-id',
        email: email,
        user_metadata: {
          first_name: 'Admin',
          last_name: 'ORBIS',
          role: 'admin',
          company: 'ORBIS'
        }
      }
      
      const authData = {
        user: devUser,
        session: {
          access_token: 'dev-token',
          expires_at: Date.now() + 24 * 60 * 60 * 1000 // 24h
        }
      }
      
      localStorage.setItem('dev-auth', JSON.stringify(authData))
      setUser(devUser)
    } else {
      throw new Error('Invalid credentials')
    }
    
    setLoading(false)
  }

  const signOut = async () => {
    localStorage.removeItem('dev-auth')
    setUser(null)
  }

  const isAdmin = user?.user_metadata?.role === 'admin' || user?.user_metadata?.role === 'super_admin'
  const isSuperAdmin = user?.user_metadata?.role === 'super_admin'

  const value = {
    user,
    loading,
    signIn,
    signOut,
    isAdmin,
    isSuperAdmin
  }

  return (
    <DevAuthContext.Provider value={value}>
      {children}
    </DevAuthContext.Provider>
  )
}

export function useDevAuth() {
  const context = useContext(DevAuthContext)
  if (context === undefined) {
    throw new Error('useDevAuth must be used within a DevAuthProvider')
  }
  return context
}
