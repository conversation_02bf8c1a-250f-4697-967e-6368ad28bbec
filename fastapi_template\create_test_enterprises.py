#!/usr/bin/env python3
"""
Script pour créer 3 entreprises de test avec leurs administrateurs
pour valider l'interface Enterprise-first
"""

import asyncio
import asyncpg
from datetime import datetime
import sys
import uuid
from app.core.config import settings

DATABASE_URL = settings.DATABASE_URL

async def create_test_enterprises():
    """Créer 3 entreprises de test avec leurs administrateurs"""
    print("🏢 Création des entreprises de test pour l'interface Enterprise-first...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Données des entreprises de test
        companies_data = [
            {
                "name": "TechCorp Solutions",
                "code": "TECH001",
                "address": "123 Avenue des Champs-Élysées, 75008 Paris",
                "phone": "+33 1 42 56 78 90",
                "email": "<EMAIL>",
                "admins": [
                    {"email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "role": "admin"},
                    {"email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "<PERSON>", "role": "admin"},
                    {"email": "<EMAIL>", "first_name": "Sophie", "last_name": "Bernard", "role": "admin"}
                ]
            },
            {
                "name": "BuildPro Construction",
                "code": "BUILD002",
                "address": "456 Rue de Rivoli, 75001 Paris",
                "phone": "+33 1 45 67 89 01",
                "email": "<EMAIL>",
                "admins": [
                    {"email": "<EMAIL>", "first_name": "Jean", "last_name": "Dupont", "role": "admin"},
                    {"email": "<EMAIL>", "first_name": "Claire", "last_name": "Moreau", "role": "admin"}
                ]
            },
            {
                "name": "GreenEnergy SARL",
                "code": "GREEN003",
                "address": "789 Boulevard Saint-Germain, 75007 Paris",
                "phone": "+33 1 56 78 90 12",
                "email": "<EMAIL>",
                "admins": [
                    {"email": "<EMAIL>", "first_name": "Lucas", "last_name": "Petit", "role": "admin"}
                ]
            }
        ]
        
        created_companies = []
        
        for company_data in companies_data:
            print(f"\n📋 Création de l'entreprise: {company_data['name']}")
            
            # Vérifier si l'entreprise existe déjà
            existing_company = await conn.fetchval(
                "SELECT id FROM companies WHERE code = $1", 
                company_data['code']
            )
            
            if existing_company:
                print(f"   ⚠️ Entreprise {company_data['name']} existe déjà (ID: {existing_company})")
                company_id = existing_company
            else:
                # Créer l'entreprise
                company_id = await conn.fetchval("""
                    INSERT INTO companies (
                        name, code, address, phone, email,
                        is_active, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    RETURNING id
                """,
                company_data['name'],
                company_data['code'],
                company_data['address'],
                company_data['phone'],
                company_data['email'],
                True,
                datetime.utcnow(),
                datetime.utcnow()
                )
                print(f"   ✅ Entreprise créée (ID: {company_id})")
            
            # Créer les paramètres de l'entreprise
            settings_exists = await conn.fetchval(
                "SELECT id FROM company_settings WHERE company_id = $1", 
                company_id
            )
            
            if not settings_exists:
                await conn.execute("""
                    INSERT INTO company_settings (company_id, created_at, updated_at)
                    VALUES ($1, $2, $3)
                """,
                company_id, datetime.utcnow(), datetime.utcnow()
                )
                print(f"   ✅ Paramètres de l'entreprise créés")
            
            # Créer les administrateurs
            admin_count = 0
            for admin_data in company_data['admins']:
                print(f"   👤 Création de l'admin: {admin_data['email']}")
                
                # Vérifier si l'utilisateur existe déjà
                existing_user = await conn.fetchval(
                    "SELECT id FROM users WHERE email = $1", 
                    admin_data['email']
                )
                
                if existing_user:
                    print(f"      ⚠️ Utilisateur {admin_data['email']} existe déjà")
                    user_id = existing_user
                else:
                    # Créer l'utilisateur
                    user_id = await conn.fetchval("""
                        INSERT INTO users (
                            email, first_name, last_name, role, 
                            is_active, is_verified, created_at, updated_at
                        )
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                        RETURNING id
                    """,
                    admin_data['email'],
                    admin_data['first_name'],
                    admin_data['last_name'],
                    admin_data['role'].upper(),
                    True,
                    True,
                    datetime.utcnow(),
                    datetime.utcnow()
                    )
                    print(f"      ✅ Utilisateur créé (ID: {user_id})")
                
                # Associer l'utilisateur à l'entreprise
                existing_association = await conn.fetchval("""
                    SELECT id FROM user_companies 
                    WHERE user_id = $1 AND company_id = $2
                """, user_id, company_id)
                
                if not existing_association:
                    await conn.execute("""
                        INSERT INTO user_companies (
                            user_id, company_id, role, is_default, is_active, 
                            joined_at, created_at, updated_at
                        )
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    """,
                    user_id, company_id, admin_data['role'].upper(), 
                    True, True, datetime.utcnow(), datetime.utcnow(), datetime.utcnow()
                    )
                    print(f"      ✅ Association utilisateur-entreprise créée")
                    admin_count += 1
                else:
                    print(f"      ⚠️ Association existe déjà")
                    admin_count += 1
            
            created_companies.append({
                "id": company_id,
                "name": company_data['name'],
                "admin_count": admin_count
            })
        
        return created_companies
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        await conn.close()


async def verify_data():
    """Vérifier les données créées"""
    print("\n🔍 Vérification des données créées...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Compter les entreprises
        company_count = await conn.fetchval("SELECT COUNT(*) FROM companies")
        print(f"   📊 Total entreprises: {company_count}")
        
        # Lister les entreprises avec leurs admins
        companies = await conn.fetch("""
            SELECT c.id, c.name, c.code, 
                   COUNT(uc.user_id) as admin_count
            FROM companies c
            LEFT JOIN user_companies uc ON c.id = uc.company_id
            WHERE c.code IN ('TECH001', 'BUILD002', 'GREEN003')
            GROUP BY c.id, c.name, c.code
            ORDER BY c.name
        """)
        
        for company in companies:
            print(f"   🏢 {company['name']} ({company['code']}) - {company['admin_count']} admin(s)")
            
            # Lister les admins de cette entreprise
            admins = await conn.fetch("""
                SELECT u.email, u.first_name, u.last_name, uc.role
                FROM users u
                JOIN user_companies uc ON u.id = uc.user_id
                WHERE uc.company_id = $1
                ORDER BY u.first_name, u.last_name
            """, company['id'])
            
            for admin in admins:
                print(f"      👤 {admin['first_name']} {admin['last_name']} ({admin['email']}) - {admin['role']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Création d'entreprises de test")
    print("="*60)
    
    created_companies = await create_test_enterprises()
    
    if created_companies:
        print(f"\n✅ {len(created_companies)} entreprises créées avec succès!")
        
        for company in created_companies:
            print(f"   🏢 {company['name']} - {company['admin_count']} admin(s)")
        
        # Vérifier les données
        if await verify_data():
            print("\n🎯 Prochaines étapes:")
            print("   1. Démarrer orbis-admin: cd orbis-admin && npm run dev")
            print("   2. Aller sur http://localhost:3000")
            print("   3. Se <NAME_EMAIL>")
            print("   4. Aller dans 'Entreprises & Admins'")
            print("   5. Tester l'expansion des entreprises")
            
            print("\n💡 Interface Enterprise-first:")
            print("   ✅ Les entreprises sont affichées en premier")
            print("   ✅ Cliquer sur une entreprise pour voir ses admins")
            print("   ✅ Les super admins sont exclus des listes")
            
            return True
    else:
        print("\n❌ Échec de la création des entreprises")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
