#!/usr/bin/env python3
"""
Test rapide de connexion à la base de données ORBIS
Script simple pour vérifier rapidement si la base de données est accessible
"""

import asyncio
import sys
from pathlib import Path

# Ajouter le répertoire app au path Python
sys.path.insert(0, str(Path(__file__).parent))

from app.core.config import settings
from app.db.session import AsyncSessionLocal
from sqlalchemy import text


async def quick_test():
    """Test rapide de connexion"""
    print("🔍 Test rapide de connexion à la base de données...")
    print(f"🎯 Cible: {settings.SUPABASE_URL}")
    
    try:
        async with AsyncSessionLocal() as session:
            # Test simple
            result = await session.execute(text("SELECT 'Connexion OK' as status, NOW() as timestamp"))
            row = result.fetchone()
            
            print(f"✅ {row[0]} - {row[1]}")
            
            # Test de comptage des tables
            result = await session.execute(text("""
                SELECT COUNT(*) as table_count
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """))
            table_count = result.fetchone()[0]
            
            print(f"📊 {table_count} tables trouvées dans la base de données")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(quick_test())
    if success:
        print("🎉 Test de connexion réussi!")
    else:
        print("💥 Test de connexion échoué!")
    
    sys.exit(0 if success else 1)
