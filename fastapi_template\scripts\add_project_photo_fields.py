#!/usr/bin/env python3
"""
Script pour ajouter les champs photo_url et photo_filename à la table projects
"""

import asyncio
import sys
from pathlib import Path

# Ajouter le répertoire parent au PYTHONPATH
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.core.database import engine

async def add_project_photo_fields():
    """Ajouter les champs photo aux projets"""
    
    async with engine.begin() as conn:
        try:
            print("🔄 Ajout des champs photo à la table projects...")
            
            # Vérifier si les colonnes existent déjà
            check_columns_query = """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'projects' 
            AND column_name IN ('photo_url', 'photo_filename');
            """
            
            result = await conn.execute(text(check_columns_query))
            existing_columns = [row[0] for row in result.fetchall()]
            
            # Ajouter photo_url si elle n'existe pas
            if 'photo_url' not in existing_columns:
                await conn.execute(text("""
                    ALTER TABLE projects 
                    ADD COLUMN photo_url VARCHAR(500);
                """))
                print("✅ Colonne photo_url ajoutée")
            else:
                print("ℹ️ Colonne photo_url existe déjà")
            
            # Ajouter photo_filename si elle n'existe pas
            if 'photo_filename' not in existing_columns:
                await conn.execute(text("""
                    ALTER TABLE projects 
                    ADD COLUMN photo_filename VARCHAR(255);
                """))
                print("✅ Colonne photo_filename ajoutée")
            else:
                print("ℹ️ Colonne photo_filename existe déjà")
            
            print("🎯 Migration terminée avec succès!")
            
        except Exception as e:
            print(f"❌ Erreur lors de la migration: {e}")
            raise

async def main():
    """Point d'entrée principal"""
    try:
        await add_project_photo_fields()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
