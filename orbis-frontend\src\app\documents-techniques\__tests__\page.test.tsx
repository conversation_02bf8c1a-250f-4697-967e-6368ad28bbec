import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useSearchParams } from 'next/navigation'
import DocumentsTechniquesPageContent from '../page'
import { useAuth } from '@/contexts/AuthContext'
import { FastAuthService } from '@/lib/auth'
import { DocumentType } from '@/types/technical-document'

// Mock dependencies
jest.mock('next/navigation')
jest.mock('@/contexts/AuthContext')
jest.mock('@/lib/auth')
jest.mock('@/hooks/useTechnicalDocument')
jest.mock('@/hooks/useChatGPT')

const mockUseSearchParams = useSearchParams as jest.MockedFunction<typeof useSearchParams>
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>
const mockFastAuthService = FastAuthService as jest.Mocked<typeof FastAuthService>

// Mock the hooks
const mockUseTechnicalDocument = require('@/hooks/useTechnicalDocument')
const mockUseChatGPT = require('@/hooks/useChatGPT')

describe('DocumentsTechniquesPage', () => {
  const mockUser = {
    id: 1,
    email: '<EMAIL>',
    first_name: 'Test',
    last_name: 'User'
  }

  const mockSearchParams = new URLSearchParams('project_id=7')

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock useSearchParams
    mockUseSearchParams.mockReturnValue(mockSearchParams)
    
    // Mock useAuth
    mockUseAuth.mockReturnValue({
      user: mockUser,
      signOut: jest.fn(),
      loading: false,
      error: null
    })

    // Mock FastAuthService
    mockFastAuthService.getToken.mockReturnValue('mock-token')

    // Mock useTechnicalDocument
    mockUseTechnicalDocument.useTechnicalDocument.mockReturnValue({
      document: null,
      loading: false,
      error: null,
      save: jest.fn(),
      refresh: jest.fn()
    })

    mockUseTechnicalDocument.useCreateTechnicalDocument.mockReturnValue({
      createDocument: jest.fn(),
      loading: false,
      error: null
    })

    // Mock useChatGPT
    mockUseChatGPT.useChatGPT.mockReturnValue({
      enhanceText: jest.fn(),
      loading: false,
      error: null
    })

    // Mock fetch
    global.fetch = jest.fn()
  })

  it('should render the page with correct title and description', () => {
    // Arrange & Act
    render(<DocumentsTechniquesPageContent />)

    // Assert
    expect(screen.getByText('Documents Techniques')).toBeInTheDocument()
    expect(screen.getByText('Gestion des documents CCTP et DPGF avec amélioration IA')).toBeInTheDocument()
  })

  it('should extract project_id from URL parameters', () => {
    // Arrange & Act
    render(<DocumentsTechniquesPageContent />)

    // Assert
    expect(mockUseSearchParams).toHaveBeenCalled()
    // Le composant devrait utiliser project_id=7 depuis les paramètres URL
  })

  it('should render document creation buttons', () => {
    // Arrange & Act
    render(<DocumentsTechniquesPageContent />)

    // Assert
    expect(screen.getByText('Créer un document')).toBeInTheDocument()
    expect(screen.getByText('Nouveau CCTP')).toBeInTheDocument()
    expect(screen.getByText('Nouveau DPGF')).toBeInTheDocument()
  })

  it('should show creation form when CCTP button is clicked', async () => {
    // Arrange
    render(<DocumentsTechniquesPageContent />)
    const cctpButton = screen.getByText('Nouveau CCTP')

    // Act
    fireEvent.click(cctpButton)

    // Assert
    await waitFor(() => {
      expect(screen.getByText('Nouveau document CCTP')).toBeInTheDocument()
    })
  })

  it('should show creation form when DPGF button is clicked', async () => {
    // Arrange
    render(<DocumentsTechniquesPageContent />)
    const dpgfButton = screen.getByText('Nouveau DPGF')

    // Act
    fireEvent.click(dpgfButton)

    // Assert
    await waitFor(() => {
      expect(screen.getByText('Nouveau document DPGF')).toBeInTheDocument()
    })
  })

  it('should handle document creation with valid name', async () => {
    // Arrange
    const mockCreateDocument = jest.fn().mockResolvedValue({
      id: 1,
      name: 'Test CCTP',
      type_document: DocumentType.CCTP,
      project_id: 7
    })

    mockUseTechnicalDocument.useCreateTechnicalDocument.mockReturnValue({
      createDocument: mockCreateDocument,
      loading: false,
      error: null
    })

    render(<DocumentsTechniquesPageContent />)
    
    // Click CCTP button to start creation
    fireEvent.click(screen.getByText('Nouveau CCTP'))

    // Wait for creation form
    await waitFor(() => {
      expect(screen.getByText('Nouveau document CCTP')).toBeInTheDocument()
    })

    // Act - Fill in document name and create
    const nameInput = screen.getByPlaceholderText('Nom du document')
    fireEvent.change(nameInput, { target: { value: 'Test CCTP Document' } })

    const createButton = screen.getByText('Créer')
    fireEvent.click(createButton)

    // Assert
    await waitFor(() => {
      expect(mockCreateDocument).toHaveBeenCalledWith({
        name: 'Test CCTP Document',
        type_document: DocumentType.CCTP,
        project_id: 7,
        content: '',
        company_ids: []
      })
    })
  })

  it('should not allow creation with empty document name', async () => {
    // Arrange
    render(<DocumentsTechniquesPageContent />)
    
    // Click CCTP button to start creation
    fireEvent.click(screen.getByText('Nouveau CCTP'))

    await waitFor(() => {
      expect(screen.getByText('Nouveau document CCTP')).toBeInTheDocument()
    })

    // Act - Try to create without name
    const createButton = screen.getByText('Créer')
    
    // Assert - Button should be disabled
    expect(createButton).toBeDisabled()
  })

  it('should show empty state when no document is selected', () => {
    // Arrange & Act
    render(<DocumentsTechniquesPageContent />)

    // Assert
    expect(screen.getByText('Aucun document sélectionné')).toBeInTheDocument()
    expect(screen.getByText('Sélectionnez un document existant ou créez-en un nouveau')).toBeInTheDocument()
  })

  it('should handle authentication requirement', () => {
    // Arrange - Mock unauthenticated user
    mockUseAuth.mockReturnValue({
      user: null,
      signOut: jest.fn(),
      loading: false,
      error: null
    })

    // Act & Assert
    // Le composant devrait être wrappé dans ProtectedRoute qui gère l'authentification
    render(<DocumentsTechniquesPageContent />)
    
    // Vérifier que le composant gère correctement l'état non authentifié
    expect(mockUseAuth).toHaveBeenCalled()
  })
})
