'use client'

import React, { useRef, useState, useEffect } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { Heading } from '@tiptap/extension-heading'
import { BulletList } from '@tiptap/extension-bullet-list'
import { OrderedList } from '@tiptap/extension-ordered-list'
import { ListItem } from '@tiptap/extension-list-item'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableCell } from '@tiptap/extension-table-cell'
import { TableHeader } from '@tiptap/extension-table-header'
import { Link } from '@tiptap/extension-link'
import { TextStyle } from '@tiptap/extension-text-style'
import { Color } from '@tiptap/extension-color'
import { Highlight } from '@tiptap/extension-highlight'
import { TextAlign } from '@tiptap/extension-text-align'
import { CharacterCount } from '@tiptap/extension-character-count'
import { Underline } from '@tiptap/extension-underline'
import { DocumentType } from '@/types/technical-document'
import AddArticleModal from './AddArticleModal'
import { FastAuthService } from '@/lib/auth'
import { AutoNumberedHeading } from './extensions/AutoNumberedHeading'
import './styles/numbered-headings.css'

interface TechnicalDocumentEditorProps {
  value: string
  onChange: (content: string) => void
  documentType: DocumentType
  onTextSelection?: (selectedText: string) => void
  readOnly?: boolean
  placeholder?: string
}

export default function TechnicalDocumentEditor({
  value,
  onChange,
  documentType,
  onTextSelection,
  readOnly = false,
  placeholder = ''
}: TechnicalDocumentEditorProps) {
  const [showAddArticleModal, setShowAddArticleModal] = useState(false)
  const [articlePosition, setArticlePosition] = useState<number>(0)
  const [isMounted, setIsMounted] = useState(false)
  const changeTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // S'assurer que nous sommes côté client
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Handler pour l'ajout d'article
  const handleAddArticle = (position: number) => {
    setArticlePosition(position)
    setShowAddArticleModal(true)
  }

  // Handler pour l'amélioration de texte
  const handleEnhanceText = (selectedText: string) => {
    if (onTextSelection) {
      onTextSelection(selectedText)
    }
  }

  // Extraire les headings du document avec numérotation automatique
  const getAvailableHeadings = () => {
    if (!editor) return []

    const headings: Array<{id: string, title: string, level: number, position: number, number: string}> = []

    editor.state.doc.descendants((node, pos) => {
      if (node.type.name === 'heading') {
        const level = node.attrs.level
        const title = node.textContent
        if (title.trim()) {
          headings.push({
            id: `heading-${pos}`,
            title: title.trim(),
            level,
            position: pos,
            number: '' // Sera calculé après
          })
        }
      }
    })

    // Trier par position pour maintenir l'ordre du document
    const sortedHeadings = headings.sort((a, b) => a.position - b.position)

    // Calculer la numérotation hiérarchique
    const counters: number[] = [0, 0, 0, 0, 0, 0] // Support jusqu'au niveau 6

    return sortedHeadings.map(heading => {
      const level = heading.level - 1 // Index 0-based

      // Incrémenter le compteur du niveau actuel
      counters[level]++

      // Remettre à zéro tous les compteurs des niveaux inférieurs
      for (let i = level + 1; i < counters.length; i++) {
        counters[i] = 0
      }

      // Construire le numéro hiérarchique
      const numberParts = counters.slice(0, level + 1).filter(n => n > 0)
      const number = numberParts.join('.')

      return {
        ...heading,
        number
      }
    })
  }

  // Calculer la position d'insertion optimale
  const calculateInsertionPosition = (parentId?: string) => {
    if (!editor || !parentId) {
      // Si pas de parent, insérer à la fin du document
      return editor?.state.doc.content.size || 0
    }

    const headings = getAvailableHeadings()
    const parentHeading = headings.find(h => h.id === parentId)

    if (!parentHeading) {
      return editor.state.doc.content.size
    }

    // Trouver la position après le parent et ses enfants
    const parentLevel = parentHeading.level
    let insertPosition = parentHeading.position

    // Chercher le prochain heading de même niveau ou supérieur
    for (let i = headings.indexOf(parentHeading) + 1; i < headings.length; i++) {
      const heading = headings[i]
      if (heading.level <= parentLevel) {
        insertPosition = heading.position
        break
      }
    }

    return insertPosition
  }

  // Calculer le numéro d'article basé sur la hiérarchie
  const calculateArticleNumber = (parentId?: string) => {
    if (!editor) return "1"

    const headings = getAvailableHeadings()

    if (!parentId) {
      // Compter les articles de niveau 1
      const level1Count = headings.filter(h => h.level === 1).length
      return `${level1Count + 1}`
    }

    const parentHeading = headings.find(h => h.id === parentId)
    if (!parentHeading) return "1"

    const parentLevel = parentHeading.level
    const parentIndex = headings.indexOf(parentHeading)

    // Compter les enfants directs du parent
    let childCount = 0
    for (let i = parentIndex + 1; i < headings.length; i++) {
      const heading = headings[i]
      if (heading.level <= parentLevel) break
      if (heading.level === parentLevel + 1) {
        childCount++
      }
    }

    // Extraire le numéro du parent et ajouter le nouveau numéro
    const parentTitle = parentHeading.title
    const parentNumber = extractNumberFromTitle(parentTitle)

    return `${parentNumber}.${childCount + 1}`
  }

  // Extraire le numéro d'un titre (ex: "1.2 Titre" -> "1.2")
  const extractNumberFromTitle = (title: string): string => {
    const match = title.match(/^(\d+(?:\.\d+)*)/);
    return match ? match[1] : "1"
  }

  // Handler pour la soumission d'article
  const handleArticleSubmit = async (articleData: any) => {
    if (!editor) return

    try {
      console.log('🚀 Génération d\'article - Données reçues:', articleData)
      console.log('🚀 Position d\'insertion:', articlePosition)

      // Appeler le nouvel endpoint pour générer l'article
      const token = FastAuthService.getToken()
      const requestBody = {
        parent_id: articleData.parentId,
        prestation: articleData.prestation,
        localisation: articleData.localisation,
        marque: articleData.marque,
        reference: articleData.reference,
        nature: articleData.nature,
        criteres_qualite: articleData.criteresQualite,
        dimensions: articleData.dimensions,
        couleur: articleData.couleur,
        particularite: articleData.particularite,
        description_pose: articleData.descriptionPose,
        type_pose: articleData.typePose,
        marque_pose: articleData.marquePose,
        reference_pose: articleData.referencePose,
        inclure_criteres: articleData.inclureCriteres,
        inclure_docs: articleData.inclureDocs,
        unite: articleData.unite,
        quantite: articleData.quantite
      }

      console.log('🚀 Corps de la requête API:', requestBody)

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/v1/technical-documents/generate-article`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      console.log('🚀 Statut de la réponse:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Erreur API:', errorText)
        throw new Error(`Erreur lors de la génération de l'article: ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ Réponse API:', result)

      // Créer le contenu de l'article avec le titre et le corps générés
      const articleContent = `
        <h3>Article ${result.article_number} - ${result.title}</h3>
        ${result.body_html}
      `

      console.log('📝 Contenu à insérer:', articleContent)

      // Insérer l'article à la position spécifiée
      editor.chain().focus().setTextSelection(articlePosition).insertContent(articleContent).run()

      console.log('✅ Article inséré avec succès!')

    } catch (error) {
      console.error('❌ Erreur lors de la génération de l\'article:', error)
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue'
      alert(`Erreur lors de la génération de l'article: ${errorMessage}`)
    }
  }



  // Configuration des extensions Tiptap
  const extensions = [
    StarterKit.configure({
      heading: false, // On utilise notre propre extension Heading
      bulletList: false, // On utilise notre propre extension
      orderedList: false, // On utilise notre propre extension
      listItem: false, // On utilise notre propre extension
    }),
    Heading.configure({
      levels: [1, 2, 3, 4, 5, 6],
    }),
    AutoNumberedHeading, // Extension pour la numérotation automatique
    BulletList.configure({
      HTMLAttributes: {
        class: 'tiptap-bullet-list',
      },
    }),
    OrderedList.configure({
      HTMLAttributes: {
        class: 'tiptap-ordered-list',
      },
    }),
    ListItem,
    Table.configure({
      resizable: true,
    }),
    TableRow,
    TableHeader,
    TableCell,
    Link.configure({
      openOnClick: false,
    }),
    TextStyle,
    Color,
    Highlight,
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    CharacterCount,
    Underline,
  ]

  // Initialiser l'éditeur Tiptap
  const editor = useEditor({
    extensions,
    content: value,
    editable: !readOnly,
    immediatelyRender: false, // Désactiver le rendu immédiat pour éviter les problèmes SSR
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()

      // Debounce pour éviter trop d'appels
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current)
      }

      changeTimeoutRef.current = setTimeout(() => {
        onChange(html)
      }, 300)
    },
    onSelectionUpdate: ({ editor }) => {
      const { from, to } = editor.state.selection
      const selectedText = editor.state.doc.textBetween(from, to, ' ')

      if (selectedText && selectedText.trim().length > 0 && onTextSelection) {
        onTextSelection(selectedText.trim())
      }
    },
    onCreate: () => {
      // Éditeur créé avec succès
    },
  })

  // Mettre à jour le contenu quand la valeur change
  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value)
    }
  }, [editor, value])

  // Fonction pour initialiser un document CCTP avec structure de base
  const initializeCCTPStructure = () => {
    if (!editor) return

    const cctpContent = `
      <h1>1 Objet du marché</h1>
      <p>Description générale des travaux...</p>

      <h1>2 Dispositions générales</h1>
      <h2>2.1 Réglementation</h2>
      <p>Textes réglementaires applicables...</p>

      <h2>2.2 Normes</h2>
      <p>Normes techniques de référence...</p>

      <h1>3 Description des ouvrages</h1>
      <h2>3.1 Gros œuvre</h2>
      <p>Spécifications techniques...</p>

      <h2>3.2 Second œuvre</h2>
      <p>Spécifications techniques...</p>
    `

    editor.commands.setContent(cctpContent)
  }

  // Gestionnaire de raccourcis clavier personnalisés
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!editor) return

      // Ctrl+K pour ajouter un lien
      if (event.ctrlKey && event.key === 'k') {
        event.preventDefault()
        const url = window.prompt('URL du lien:')
        if (url) {
          editor.chain().focus().setLink({ href: url }).run()
        }
      }

      // Ctrl+Shift+L pour supprimer un lien
      if (event.ctrlKey && event.shiftKey && event.key === 'L') {
        event.preventDefault()
        editor.chain().focus().unsetLink().run()
      }

      // Ctrl+Shift+T pour insérer un tableau
      if (event.ctrlKey && event.shiftKey && event.key === 'T') {
        event.preventDefault()
        editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
      }

      // Ctrl+Shift+H pour surligner
      if (event.ctrlKey && event.shiftKey && event.key === 'H') {
        event.preventDefault()
        editor.chain().focus().toggleHighlight().run()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [editor])

  // Cleanup
  useEffect(() => {
    return () => {
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current)
      }
      editor?.destroy()
    }
  }, [editor])

  if (!isMounted || !editor) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="text-gray-600">Chargement de l'éditeur...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="relative h-full flex flex-col">
      {/* Toolbar - Fixed at top */}
      <div className="sticky top-0 z-10 border-b border-gray-200 p-3 bg-white shadow-sm">
        <div className="flex flex-wrap gap-2">
          {/* Bouton Ajouter un article */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <button
              onClick={() => {
                const { from } = editor.state.selection
                setArticlePosition(from)
                setShowAddArticleModal(true)
              }}
              className="flex items-center space-x-1 px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              title="Ajouter un article CCTP"
            >
              <span>+</span>
              <span>Article</span>
            </button>
          </div>

          {/* Undo/Redo */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <button
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              className="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50"
              title="Annuler"
            >
              ↶
            </button>
            <button
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              className="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50"
              title="Refaire"
            >
              ↷
            </button>
          </div>

          {/* Headings */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <select
              onChange={(e) => {
                const level = parseInt(e.target.value)
                if (level === 0) {
                  editor.chain().focus().setParagraph().run()
                } else {
                  editor.chain().focus().toggleHeading({ level: level as 1 | 2 | 3 | 4 | 5 | 6 }).run()
                }
              }}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="0">Paragraphe</option>
              <option value="1">Titre 1</option>
              <option value="2">Titre 2</option>
              <option value="3">Titre 3</option>
              <option value="4">Titre 4</option>
              <option value="5">Titre 5</option>
              <option value="6">Titre 6</option>
            </select>
          </div>

          {/* Numérotation des headings */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <button
              onClick={() => {
                editor.chain().focus().renumberHeadings().run()
              }}
              className="numbering-button text-xs"
              title="Renuméroter tous les titres"
            >
              1.2.3
            </button>
            <button
              onClick={() => {
                const level = 1
                editor.chain().focus().addNumberedHeading(level, 'Nouveau titre').run()
              }}
              className="numbering-button text-xs ml-1"
              title="Ajouter un titre numéroté"
            >
              H#
            </button>
            {documentType === DocumentType.CCTP && (
              <button
                onClick={initializeCCTPStructure}
                className="numbering-button text-xs ml-1"
                title="Initialiser structure CCTP"
              >
                📋
              </button>
            )}
          </div>

          {/* Text formatting */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <button
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive('bold') ? 'bg-gray-200' : ''}`}
              title="Gras"
            >
              <strong>B</strong>
            </button>
            <button
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive('italic') ? 'bg-gray-200' : ''}`}
              title="Italique"
            >
              <em>I</em>
            </button>
            <button
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive('underline') ? 'bg-gray-200' : ''}`}
              title="Souligné"
            >
              <u>U</u>
            </button>
          </div>

          {/* Lists */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <button
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive('bulletList') ? 'bg-gray-200' : ''}`}
              title="Liste à puces"
            >
              •
            </button>
            <button
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive('orderedList') ? 'bg-gray-200' : ''}`}
              title="Liste numérotée"
            >
              1.
            </button>
          </div>

          {/* Alignment */}
          <div className="flex">
            <button
              onClick={() => editor.chain().focus().setTextAlign('left').run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive({ textAlign: 'left' }) ? 'bg-gray-200' : ''}`}
              title="Aligner à gauche"
            >
              ⬅
            </button>
            <button
              onClick={() => editor.chain().focus().setTextAlign('center').run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive({ textAlign: 'center' }) ? 'bg-gray-200' : ''}`}
              title="Centrer"
            >
              ↔
            </button>
            <button
              onClick={() => editor.chain().focus().setTextAlign('right').run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive({ textAlign: 'right' }) ? 'bg-gray-200' : ''}`}
              title="Aligner à droite"
            >
              ➡
            </button>
            <button
              onClick={() => editor.chain().focus().setTextAlign('justify').run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive({ textAlign: 'justify' }) ? 'bg-gray-200' : ''}`}
              title="Justifier"
            >
              ⬌
            </button>
          </div>

          {/* Colors and Highlighting */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <input
              type="color"
              value="#000000"
              onChange={(e) => editor.chain().focus().setColor(e.target.value).run()}
              className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
              title="Couleur du texte"
            />
            <button
              onClick={() => editor.chain().focus().toggleHighlight().run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ml-1 ${editor.isActive('highlight') ? 'bg-yellow-200' : ''}`}
              title="Surligner"
            >
              🖍️
            </button>
            <button
              onClick={() => editor.chain().focus().unsetColor().run()}
              className="p-2 text-gray-600 hover:text-gray-900 text-xs"
              title="Supprimer couleur"
            >
              ✕
            </button>
          </div>

          {/* Links */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <button
              onClick={() => {
                const url = window.prompt('URL du lien:')
                if (url) {
                  editor.chain().focus().setLink({ href: url }).run()
                }
              }}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive('link') ? 'bg-blue-200' : ''}`}
              title="Ajouter un lien"
            >
              🔗
            </button>
            <button
              onClick={() => editor.chain().focus().unsetLink().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Supprimer le lien"
            >
              🔗✕
            </button>
          </div>

          {/* Tables */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <button
              onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Insérer un tableau"
            >
              📊
            </button>
            <button
              onClick={() => editor.chain().focus().addColumnBefore().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Ajouter colonne avant"
            >
              ⬅️📋
            </button>
            <button
              onClick={() => editor.chain().focus().addColumnAfter().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Ajouter colonne après"
            >
              📋➡️
            </button>
            <button
              onClick={() => editor.chain().focus().deleteColumn().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Supprimer colonne"
            >
              📋✕
            </button>
            <button
              onClick={() => editor.chain().focus().addRowBefore().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Ajouter ligne avant"
            >
              ⬆️📋
            </button>
            <button
              onClick={() => editor.chain().focus().addRowAfter().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Ajouter ligne après"
            >
              📋⬇️
            </button>
            <button
              onClick={() => editor.chain().focus().deleteRow().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Supprimer ligne"
            >
              📋✕
            </button>
            <button
              onClick={() => editor.chain().focus().deleteTable().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Supprimer tableau"
            >
              📊✕
            </button>
          </div>

          {/* Advanced formatting */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <button
              onClick={() => editor.chain().focus().toggleStrike().run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive('strike') ? 'bg-gray-200' : ''}`}
              title="Barré"
            >
              <s>S</s>
            </button>
            <button
              onClick={() => editor.chain().focus().toggleCode().run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive('code') ? 'bg-gray-200' : ''}`}
              title="Code"
            >
              &lt;/&gt;
            </button>
            <button
              onClick={() => editor.chain().focus().toggleCodeBlock().run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive('codeBlock') ? 'bg-gray-200' : ''}`}
              title="Bloc de code"
            >
              📝
            </button>
            <button
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              className={`p-2 text-gray-600 hover:text-gray-900 ${editor.isActive('blockquote') ? 'bg-gray-200' : ''}`}
              title="Citation"
            >
              💬
            </button>
          </div>

          {/* Special actions */}
          <div className="flex border-r border-gray-300 pr-2 mr-2">
            <button
              onClick={() => editor.chain().focus().setHorizontalRule().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Ligne horizontale"
            >
              ➖
            </button>
            <button
              onClick={() => editor.chain().focus().setHardBreak().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Saut de ligne (Shift+Enter)"
            >
              ↵
            </button>
            <button
              onClick={() => editor.chain().focus().clearNodes().run()}
              className="p-2 text-gray-600 hover:text-gray-900"
              title="Effacer le formatage"
            >
              🧹
            </button>
          </div>

          {/* Raccourcis et aide */}
          <div className="flex">
            <button
              onClick={() => {
                alert(`Raccourcis clavier disponibles :

📝 Formatage :
• Ctrl+B : Gras
• Ctrl+I : Italique
• Ctrl+U : Souligné
• Ctrl+Shift+S : Barré

📋 Listes :
• Ctrl+Shift+8 : Liste à puces
• Ctrl+Shift+7 : Liste numérotée

🎯 Navigation :
• Ctrl+Z : Annuler
• Ctrl+Y : Refaire
• Ctrl+A : Tout sélectionner

🔗 Liens :
• Ctrl+K : Ajouter un lien
• Ctrl+Shift+L : Supprimer un lien

📊 Tableaux :
• Ctrl+Shift+T : Insérer un tableau

🎨 Mise en forme :
• Ctrl+Shift+H : Surligner

⚡ Spécial :
• Shift+Enter : Saut de ligne
• Enter dans un titre numéroté : Nouveau titre`)
              }}
              className="p-2 text-gray-600 hover:text-gray-900 text-sm"
              title="Aide et raccourcis"
            >
              ❓
            </button>
          </div>
        </div>
      </div>

      {/* Editor Content - Scrollable area */}
      <div className="flex-1 overflow-y-auto border-l border-r border-gray-200 bg-white">
        <EditorContent
          editor={editor}
          className="prose prose-sm max-w-none p-6 focus:outline-none min-h-full"
        />
      </div>

      {/* Status bar - Fixed at bottom */}
      <div className="sticky bottom-0 bg-white border-t border-gray-200 px-4 py-2 shadow-sm z-10">
        <div className="flex justify-between items-center text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            {/* Document type */}
            <span className="font-medium text-gray-700">
              {documentType === DocumentType.CCTP ? '📋 CCTP' :
               documentType === DocumentType.DPGF ? '📊 DPGF' : '📄 Document'}
            </span>

            {/* Current node info */}
            <span>
              {editor.isActive('heading', { level: 1 }) && '📍 Titre 1'}
              {editor.isActive('heading', { level: 2 }) && '📍 Titre 2'}
              {editor.isActive('heading', { level: 3 }) && '📍 Titre 3'}
              {editor.isActive('paragraph') && !editor.isActive('heading') && '📍 Paragraphe'}
              {editor.isActive('bulletList') && '📍 Liste à puces'}
              {editor.isActive('orderedList') && '📍 Liste numérotée'}
              {editor.isActive('table') && '📍 Tableau'}
            </span>
          </div>

          <div className="flex items-center space-x-4">
            {/* Selection info */}
            {editor.state.selection.from !== editor.state.selection.to && (
              <span className="text-blue-600">
                {editor.state.selection.to - editor.state.selection.from} caractères sélectionnés
              </span>
            )}

            {/* Word and character count */}
            {editor.extensionManager.extensions.find(ext => ext.name === 'characterCount') && (
              <div className="flex items-center space-x-2">
                <span>
                  📝 {editor.storage.characterCount.words()} mots
                </span>
                <span>
                  🔤 {editor.storage.characterCount.characters()} caractères
                </span>
              </div>
            )}

            {/* Cursor position */}
            <span>
              📍 Position {editor.state.selection.from}
            </span>

            {/* Read-only indicator */}
            {readOnly && (
              <span className="text-orange-600 font-medium">
                🔒 Lecture seule
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Modal d'ajout d'article */}
      <AddArticleModal
        isOpen={showAddArticleModal}
        onClose={() => setShowAddArticleModal(false)}
        onSubmit={handleArticleSubmit}
        documentType={documentType}
        availableHeadings={getAvailableHeadings()}
      />
    </div>
  )
}
