import React from 'react'
import { Card } from '@/components/ui/Card'
import { But<PERSON> } from '@/components/ui/Button'
import Link from 'next/link'

interface EmployeeCardProps {
  employee: {
    id: number
    firstName: string
    lastName: string
    email: string
    phone: string
    role: string
    hourlyRate: number
    totalHours: number
    activeProjects: number
    skills: string[]
    status: string
    joinDate: string
    avatar?: string
  }
}

export const EmployeeCard: React.FC<EmployeeCardProps> = ({ employee }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Actif': return 'bg-primary-100 text-primary-800'
      case 'En congé': return 'bg-yellow-100 text-yellow-800'
      case 'Inactif': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR')
  }

  return (
    <Card className="p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center mb-4">
        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
          <span className="text-lg font-bold text-blue-600">
            {employee.firstName[0]}{employee.lastName[0]}
          </span>
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">
            {employee.firstName} {employee.lastName}
          </h3>
          <p className="text-sm text-gray-600">{employee.role}</p>
        </div>
        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(employee.status)}`}>
          {employee.status}
        </span>
      </div>

      <div className="space-y-2 mb-4 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Email:</span>
          <span className="font-medium text-right">{employee.email}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Téléphone:</span>
          <span className="font-medium">{employee.phone}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Taux horaire:</span>
          <span className="font-medium text-green-600">{employee.hourlyRate}€/h</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Heures totales:</span>
          <span className="font-medium">{employee.totalHours}h</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Projets actifs:</span>
          <span className="font-medium">{employee.activeProjects}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Embauché le:</span>
          <span className="font-medium">{formatDate(employee.joinDate)}</span>
        </div>
      </div>

      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Compétences</h4>
        <div className="flex flex-wrap gap-1">
          {employee.skills.slice(0, 3).map((skill, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
            >
              {skill}
            </span>
          ))}
          {employee.skills.length > 3 && (
            <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
              +{employee.skills.length - 3}
            </span>
          )}
        </div>
      </div>

      <div className="flex gap-2">
        <Link href={`/employees/${employee.id}`} className="flex-1">
          <Button variant="outline" className="w-full">
            Voir profil
          </Button>
        </Link>
        <Button variant="outline" size="sm">
          Modifier
        </Button>
      </div>
    </Card>
  )
}