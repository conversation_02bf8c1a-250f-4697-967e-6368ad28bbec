#!/usr/bin/env python3
"""
Script pour réinitialiser tous les mots de passe des utilisateurs
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au PYTHONPATH
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.models.user import User
from app.core.security import get_password_hash

async def reset_all_passwords():
    """Réinitialise tous les mots de passe des utilisateurs"""
    
    new_password = "orbis123!"
    hashed_password = get_password_hash(new_password)
    
    print(f"🔄 Réinitialisation de tous les mots de passe...")
    print(f"📝 Nouveau mot de passe: {new_password}")
    print(f"🔐 Hash généré: {hashed_password[:20]}...")
    
    async for db in get_db():
        try:
            # Récupérer tous les utilisateurs
            result = await db.execute(select(User))
            users = result.scalars().all()
            
            print(f"👥 {len(users)} utilisateurs trouvés")
            
            if len(users) == 0:
                print("❌ Aucun utilisateur trouvé dans la base de données")
                return
            
            # Demander confirmation
            print("\n📋 Liste des utilisateurs qui seront mis à jour:")
            for user in users:
                print(f"  - {user.email} (ID: {user.id})")
            
            print(f"\n⚠️  ATTENTION: Cette action va changer le mot de passe de TOUS les utilisateurs vers '{new_password}'")
            confirmation = input("Êtes-vous sûr de vouloir continuer ? (oui/non): ").lower().strip()
            
            if confirmation not in ['oui', 'o', 'yes', 'y']:
                print("❌ Opération annulée")
                return
            
            # Mettre à jour tous les mots de passe
            updated_count = 0
            for user in users:
                try:
                    # Mettre à jour le mot de passe
                    await db.execute(
                        update(User)
                        .where(User.id == user.id)
                        .values(hashed_password=hashed_password)
                    )
                    print(f"✅ Mot de passe mis à jour pour: {user.email}")
                    updated_count += 1
                    
                except Exception as e:
                    print(f"❌ Erreur pour {user.email}: {e}")
            
            # Commit des changements
            await db.commit()
            
            print(f"\n🎉 Opération terminée!")
            print(f"✅ {updated_count}/{len(users)} mots de passe mis à jour avec succès")
            print(f"🔑 Nouveau mot de passe pour tous: {new_password}")
            
        except Exception as e:
            print(f"❌ Erreur générale: {e}")
            await db.rollback()
            raise
        
        break  # Sortir de la boucle async for

async def main():
    """Fonction principale"""
    print("🚀 Script de réinitialisation des mots de passe ORBIS")
    print("=" * 50)
    
    try:
        await reset_all_passwords()
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
