'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Modal } from '@/components/ui/Modal'
import { useLotPhoto } from '@/hooks/useLotPhoto'
import LotPhotoUpload from '@/components/lots/LotPhotoUpload'
import { lotsApiService } from '@/lib/api/lots'
import { Lot } from '@/types/lot'

export default function TestLotPhoto() {
  const { user, signOut } = useAuth()
  
  // États pour les lots
  const [lots, setLots] = useState<Lot[]>([])
  const [selectedLot, setSelectedLot] = useState<Lot | null>(null)
  const [loading, setLoading] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)
  
  // Hook pour la gestion de photo
  const lotPhotoHook = useLotPhoto()

  // Charger les lots
  useEffect(() => {
    const fetchLots = async () => {
      try {
        setLoading(true)
        const lotsData = await lotsApiService.getLots()
        setLots(lotsData)
        console.log('📋 Lots chargés:', lotsData)
      } catch (error) {
        console.error('❌ Erreur chargement lots:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchLots()
  }, [])

  // Fonctions pour gérer la photo
  const handlePhotoUpload = async (file: File) => {
    if (!selectedLot?.id) return
    
    try {
      console.log('📤 Upload photo pour lot:', selectedLot.id)
      const result = await lotPhotoHook.uploadPhoto(selectedLot.id, file)
      console.log('✅ Upload réussi:', result)
      
      // Mettre à jour le lot dans la liste
      setLots(prev => prev.map(lot => 
        lot.id === selectedLot.id 
          ? { ...lot, photo_url: result.photo_url, photo_filename: result.photo_filename }
          : lot
      ))
      
      // Mettre à jour le lot sélectionné
      setSelectedLot(prev => prev ? {
        ...prev,
        photo_url: result.photo_url,
        photo_filename: result.photo_filename
      } : null)
      
      alert('Photo uploadée avec succès !')
    } catch (error) {
      console.error('❌ Erreur upload photo:', error)
      alert('Erreur lors de l\'upload de la photo')
    }
  }

  const handlePhotoDelete = async () => {
    if (!selectedLot?.id) return
    
    try {
      console.log('🗑️ Suppression photo pour lot:', selectedLot.id)
      await lotPhotoHook.deletePhoto(selectedLot.id)
      console.log('✅ Suppression réussie')
      
      // Mettre à jour le lot dans la liste
      setLots(prev => prev.map(lot => 
        lot.id === selectedLot.id 
          ? { ...lot, photo_url: null, photo_filename: null }
          : lot
      ))
      
      // Mettre à jour le lot sélectionné
      setSelectedLot(prev => prev ? {
        ...prev,
        photo_url: null,
        photo_filename: null
      } : null)
      
      alert('Photo supprimée avec succès !')
    } catch (error) {
      console.error('❌ Erreur suppression photo:', error)
      alert('Erreur lors de la suppression de la photo')
    }
  }

  const openPhotoModal = (lot: Lot) => {
    setSelectedLot(lot)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setSelectedLot(null)
    setIsModalOpen(false)
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          {/* Sidebar */}
          <ModernSidebar user={user} />

          {/* Contenu principal */}
          <div className="flex-1 lg:ml-72">
            <ModernHeader
              title="Test Photo Lots"
              subtitle="Interface de test pour la gestion des photos de lots"
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={signOut}
            />

            <main className="p-6">
              <div className="space-y-6">
                {/* Instructions */}
                <Card className="p-6">
                  <h2 className="text-lg font-semibold mb-4">🧪 Test de gestion des photos de lots</h2>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>• Cliquez sur "Gérer photo" pour un lot</p>
                    <p>• Uploadez une photo par drag & drop ou clic</p>
                    <p>• Supprimez une photo avec le bouton X</p>
                    <p>• Les photos sont sauvegardées en base de données</p>
                  </div>
                </Card>

                {/* Liste des lots */}
                <Card className="p-6">
                  <h2 className="text-lg font-semibold mb-4">Lots disponibles ({lots.length})</h2>
                  
                  {lots.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">
                      Aucun lot trouvé. Créez d'abord des lots dans vos projets.
                    </p>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {lots.map((lot) => (
                        <div key={lot.id} className="border border-gray-200 rounded-lg p-4 space-y-3">
                          {/* Photo du lot */}
                          {lot.photo_url ? (
                            <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                              <img
                                src={`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${lot.photo_url}`}
                                alt={`Photo du lot ${lot.name}`}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  e.currentTarget.src = '/placeholder-image.png'
                                }}
                              />
                            </div>
                          ) : (
                            <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                              <span className="text-gray-400 text-sm">Pas de photo</span>
                            </div>
                          )}
                          
                          {/* Infos du lot */}
                          <div>
                            <h3 className="font-medium text-gray-900">{lot.name}</h3>
                            <p className="text-sm text-gray-500">Code: {lot.code || 'N/A'}</p>
                            <p className="text-sm text-gray-500">ID: {lot.id}</p>
                          </div>
                          
                          {/* Bouton de gestion */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openPhotoModal(lot)}
                            className="w-full"
                          >
                            📷 Gérer photo
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </Card>
              </div>
            </main>
          </div>
        </div>

        {/* Modal de gestion de photo */}
        <Modal isOpen={isModalOpen} onClose={closeModal}>
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">
              📷 Gestion photo - {selectedLot?.name}
            </h2>
            
            {selectedLot && (
              <div className="space-y-4">
                {/* Infos du lot */}
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p><strong>Nom:</strong> {selectedLot.name}</p>
                  <p><strong>Code:</strong> {selectedLot.code || 'N/A'}</p>
                  <p><strong>ID:</strong> {selectedLot.id}</p>
                </div>
                
                {/* Upload de photo */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Photo du lot
                  </label>
                  <LotPhotoUpload
                    currentPhotoUrl={selectedLot.photo_url}
                    onUpload={handlePhotoUpload}
                    onDelete={handlePhotoDelete}
                    loading={lotPhotoHook.uploading || lotPhotoHook.deleting}
                    width={400}
                    height={250}
                  />
                </div>
                
                {/* État des opérations */}
                {(lotPhotoHook.uploading || lotPhotoHook.deleting) && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <p className="text-blue-700 text-sm">
                      {lotPhotoHook.uploading ? '📤 Upload en cours...' : '🗑️ Suppression en cours...'}
                    </p>
                  </div>
                )}
                
                {/* Boutons */}
                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={closeModal}>
                    Fermer
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Modal>
      </div>
    </ProtectedRoute>
  )
}
