import { Editor } from '@tiptap/react'
import { useState, useRef, useEffect } from 'react'
import { extractHeadingNumbers, calculateSimpleIncrement } from '../utils/headingNumbering'
import { FastAuthService } from '@/lib/auth'

interface ArticleFormData {
  parentId?: string
  prestation: string
  localisation: string
  marque: string
  reference: string
  nature: string
  criteresQualite: string
  dimensions: string
  couleur: string
  particularite: string
  descriptionPose: string
  typePose: string
  marquePose: string
  referencePose: string
  inclureCriteres: boolean
  inclureDocs: boolean
  unite: string
  quantite: number
}

export interface HeadingOption {
  id: string
  title: string
  level: number
  position: number
  number: string
}

interface UseEditorActionsProps {
  editor: Editor | null
  onSave?: () => Promise<void>
  onClose?: () => void
  hasUnsavedChanges?: boolean
}

export function useEditorActions({
  editor,
  onSave,
  onClose,
  hasUnsavedChanges = false
}: UseEditorActionsProps) {
  const [isSaving, setIsSaving] = useState(false)
  const [isGeneratingArticle, setIsGeneratingArticle] = useState(false)
  const [showCloseConfirm, setShowCloseConfirm] = useState(false)
  const [showImportModal, setShowImportModal] = useState(false)
  const [showAddArticleModal, setShowAddArticleModal] = useState(false)
  const [availableHeadings, setAvailableHeadings] = useState<HeadingOption[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Fonction pour extraire les titres disponibles du document
  const extractAvailableHeadings = (): HeadingOption[] => {
    if (!editor) return []

    const headings: HeadingOption[] = []
    const doc = editor.state.doc

    doc.descendants((node, pos) => {
      if (node.type.name === 'heading') {
        const text = node.textContent
        const level = node.attrs.level

        // Extraire le numéro du titre s'il existe
        const match = text.match(/^(\d+(?:\.\d+)*(?:\.[a-z])?)\.?\s*(.*)/)
        const number = match ? match[1] : ''
        const title = match ? match[2] : text

        headings.push({
          id: `heading-${pos}`,
          title: title || 'Titre sans nom',
          level,
          position: pos,
          number
        })
      }
    })

    return headings
  }

  // Mettre à jour les titres disponibles quand le contenu change
  useEffect(() => {
    if (editor) {
      const updateHeadings = () => {
        setAvailableHeadings(extractAvailableHeadings())
      }

      // Mettre à jour immédiatement
      updateHeadings()

      // Écouter les changements
      editor.on('update', updateHeadings)

      return () => {
        editor.off('update', updateHeadings)
      }
    }
  }, [editor])

  // Fonction pour insérer un titre numéroté avec incrémentation automatique
  const insertNumberedHeading = (level: number) => {
    if (!editor) return

    console.log('🚀 Insertion titre niveau:', level)

    // Récupérer tous les titres existants dans le document
    const doc = editor.state.doc
    const headings: { level: number; text: string; pos: number }[] = []
    
    doc.descendants((node, pos) => {
      if (node.type.name === 'heading') {
        const text = node.textContent
        const nodeLevel = node.attrs.level
        headings.push({ level: nodeLevel, text, pos })
      }
    })

    console.log('📝 Tous les headings:', headings.map(h => ({ level: h.level, text: h.text })))

    // Extraire les numéros
    const numberedHeadings = extractHeadingNumbers(headings)
    
    // Calculer le prochain numéro (mode simple pour l'instant)
    const newNumber = calculateSimpleIncrement(numberedHeadings, level)
    
    // Insérer le titre avec le bon numéro
    const headingText = `${newNumber} Nouveau titre`
    editor.chain().focus().toggleHeading({ level: level as 1 | 2 | 3 | 4 | 5 | 6 }).run()
    editor.commands.insertContent(headingText)
    
    // Sélectionner le texte "Nouveau titre" pour permettre la modification
    const currentPos = editor.state.selection.from
    const startPos = currentPos - headingText.length + newNumber.length + 1
    const endPos = currentPos
    editor.commands.setTextSelection({ from: startPos, to: endPos })
  }

  // Initialiser structure CCTP
  const initializeCCTPStructure = () => {
    if (!editor) return
    
    const cctpContent = `
      <h1 style="text-align: center; color: #0F766E;">CAHIER DES CLAUSES TECHNIQUES PARTICULIÈRES</h1>
      <h2>1. Objet du marché</h2>
      <p>Description générale des travaux...</p>
      <h2>2. Dispositions générales</h2>
      <h3>2.1 Réglementation</h3>
      <p>Textes réglementaires applicables...</p>
      <h3>2.2 Normes</h3>
      <p>Normes techniques à respecter...</p>
      <h2>3. Description des ouvrages</h2>
      <h3>3.1 Matériaux</h3>
      <p>Spécifications des matériaux...</p>
      <h3>3.2 Mise en œuvre</h3>
      <p>Modalités de mise en œuvre...</p>
    `
    
    editor.commands.setContent(cctpContent)
  }

  // Gestion de la sauvegarde
  const handleSave = async () => {
    if (!onSave || isSaving) return
    
    setIsSaving(true)
    try {
      await onSave()
      console.log('✅ Document sauvegardé')
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde:', error)
      alert('Erreur lors de la sauvegarde')
    } finally {
      setIsSaving(false)
    }
  }

  // Gestion de la fermeture
  const handleClose = () => {
    if (hasUnsavedChanges) {
      setShowCloseConfirm(true)
    } else {
      onClose?.()
    }
  }

  const confirmClose = () => {
    setShowCloseConfirm(false)
    onClose?.()
  }

  const cancelClose = () => {
    setShowCloseConfirm(false)
  }

  // Gestion de l'import de fichier
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !editor) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      // Ici on pourrait ajouter une logique de conversion Word vers HTML
      editor.commands.insertContent(content)
    }
    reader.readAsText(file)

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Fonction pour formater les données JSON en tableau HTML
  const formatArticleDataToHTML = (articleData: any): string => {
    console.log('🎨 Formatage des données JSON en tableau HTML:', articleData)

    const {
      article_number = 'X.X',
      title = 'Article généré',
      description = '',
      fourniture = '',
      mise_en_oeuvre = '',
      criteres_essais = '',
      documents_techniques = '',
      unite = '',
      quantite = ''
    } = articleData

    let tableHTML = `
      <table style="
        border-collapse: collapse;
        width: 100%;
        font-family: Arial, sans-serif;
        font-size: 12px;
        border: 2px solid #000;
        margin: 10px 0;
      ">
        <!-- En-tête de l'article -->
        <tr>
          <td colspan="2" style="
            font-weight: bold;
            text-align: center;
            padding: 15px;
            border: 2px solid #000;
            font-size: 14px;
          ">
            Article ${article_number} - ${title}
          </td>
        </tr>

        <!-- Description -->
        <tr>
          <td style="
            font-weight: bold;
            padding: 12px;
            width: 25%;
            border: 1px solid #000;
            text-align: center;
          ">
            DESCRIPTION
          </td>
          <td style="
            padding: 12px;
            border: 1px solid #000;
            vertical-align: top;
            line-height: 1.5;
            text-align: justify;
          ">
            ${description}
          </td>
        </tr>

        <!-- Fourniture -->
        <tr>
          <td style="
            font-weight: bold;
            padding: 12px;
            width: 25%;
            border: 1px solid #000;
            text-align: center;
          ">
            FOURNITURE
          </td>
          <td style="
            padding: 12px;
            border: 1px solid #000;
            vertical-align: top;
            line-height: 1.5;
            text-align: justify;
          ">
            ${fourniture}
          </td>
        </tr>

        <!-- Mise en œuvre -->
        <tr>
          <td style="
            font-weight: bold;
            padding: 12px;
            width: 25%;
            border: 1px solid #000;
            text-align: center;
          ">
            MISE EN ŒUVRE
          </td>
          <td style="
            padding: 12px;
            border: 1px solid #000;
            vertical-align: top;
            line-height: 1.5;
            text-align: justify;
          ">
            ${mise_en_oeuvre}
          </td>
        </tr>`

    // Ajouter les sections optionnelles
    if (criteres_essais) {
      tableHTML += `
        <tr>
          <td style="
            font-weight: bold;
            padding: 12px;
            width: 25%;
            border: 1px solid #000;
            text-align: center;
          ">
            CRITÈRES D'ESSAIS
          </td>
          <td style="
            padding: 12px;
            border: 1px solid #000;
            vertical-align: top;
            line-height: 1.5;
            text-align: justify;
          ">
            ${criteres_essais}
          </td>
        </tr>`
    }

    if (documents_techniques) {
      tableHTML += `
        <tr>
          <td style="
            font-weight: bold;
            padding: 12px;
            width: 25%;
            border: 1px solid #000;
            text-align: center;
          ">
            DOCUMENTS TECHNIQUES
          </td>
          <td style="
            padding: 12px;
            border: 1px solid #000;
            vertical-align: top;
            line-height: 1.5;
            text-align: justify;
          ">
            ${documents_techniques}
          </td>
        </tr>`
    }

    // Quantification
    tableHTML += `
        <tr>
          <td colspan="2" style="
            font-weight: bold;
            text-align: center;
            padding: 10px;
            border: 2px solid #000;
          ">
            Unité : ${unite} - Quantité : ${quantite}
          </td>
        </tr>
      </table>`

    return tableHTML
  }

  // Fonction pour gérer l'ajout d'article avec appel ChatGPT (version JSON)
  const handleAddArticleJSON = async (articleData: ArticleFormData) => {
    if (!editor) return

    console.log('🚀 Début de la génération d\'article JSON avec ChatGPT')
    console.log('📋 Données de l\'article:', articleData)

    setIsGeneratingArticle(true)
    try {
      // Préparer les données pour l'API
      const token = FastAuthService.getToken()
      const requestBody = {
        parent_id: articleData.parentId || '',
        prestation: articleData.prestation,
        localisation: articleData.localisation,
        marque: articleData.marque,
        reference: articleData.reference,
        nature: articleData.nature,
        criteres_qualite: articleData.criteresQualite,
        dimensions: articleData.dimensions,
        couleur: articleData.couleur,
        particularite: articleData.particularite,
        description_pose: articleData.descriptionPose,
        type_pose: articleData.typePose,
        marque_pose: articleData.marquePose,
        reference_pose: articleData.referencePose,
        inclure_criteres: articleData.inclureCriteres,
        inclure_docs: articleData.inclureDocs,
        unite: articleData.unite,
        quantite: articleData.quantite
      }

      console.log('🚀 Corps de la requête API JSON:', requestBody)

      // Appel à l'API ChatGPT JSON
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/v1/technical-documents/generate-article-json`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      console.log('🚀 Statut de la réponse JSON:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Erreur API JSON:', errorText)
        throw new Error(`Erreur lors de la génération de l'article JSON: ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ Réponse API JSON:', result)

      if (!result.success) {
        throw new Error(result.error || 'Erreur lors de la génération')
      }

      // Formater les données JSON en tableau HTML
      const articleHTML = formatArticleDataToHTML(result.article_data)

      console.log('📝 Tableau HTML généré:', articleHTML)

      // Insérer l'article à la position actuelle du curseur
      editor.chain().focus().insertContent(articleHTML).run()

      console.log('✅ Article JSON inséré avec succès!')

    } catch (error) {
      console.error('❌ Erreur lors de la génération de l\'article JSON:', error)
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue'
      alert(`Erreur lors de la génération de l'article JSON: ${errorMessage}`)
    } finally {
      setIsGeneratingArticle(false)
    }
  }

  // Fonction pour gérer l'ajout d'article avec appel ChatGPT (version HTML originale)
  const handleAddArticle = async (articleData: ArticleFormData) => {
    if (!editor) return

    console.log('🚀 Début de la génération d\'article avec ChatGPT')
    console.log('📋 Données de l\'article:', articleData)

    setIsGeneratingArticle(true)
    try {
      // Préparer les données pour l'API
      const token = FastAuthService.getToken()
      const requestBody = {
        parent_id: articleData.parentId || '',
        prestation: articleData.prestation,
        localisation: articleData.localisation,
        marque: articleData.marque,
        reference: articleData.reference,
        nature: articleData.nature,
        criteres_qualite: articleData.criteresQualite,
        dimensions: articleData.dimensions,
        couleur: articleData.couleur,
        particularite: articleData.particularite,
        description_pose: articleData.descriptionPose,
        type_pose: articleData.typePose,
        marque_pose: articleData.marquePose,
        reference_pose: articleData.referencePose,
        inclure_criteres: articleData.inclureCriteres,
        inclure_docs: articleData.inclureDocs,
        unite: articleData.unite,
        quantite: articleData.quantite
      }

      console.log('🚀 Corps de la requête API:', requestBody)

      // Appel à l'API ChatGPT
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/v1/technical-documents/generate-article`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      console.log('🚀 Statut de la réponse:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Erreur API:', errorText)
        throw new Error(`Erreur lors de la génération de l'article: ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ Réponse API:', result)

      // Créer le contenu de l'article avec le titre et le corps générés
      const articleContent = `
        <h3>Article ${result.article_number || 'X'} - ${result.title || articleData.prestation}</h3>
        ${result.body_html || result.article_content || 'Contenu généré par ChatGPT'}
      `

      console.log('📝 Contenu à insérer:', articleContent)

      // Insérer l'article à la position actuelle du curseur
      editor.chain().focus().insertContent(articleContent).run()

      console.log('✅ Article inséré avec succès!')

    } catch (error) {
      console.error('❌ Erreur lors de la génération de l\'article:', error)
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue'
      alert(`Erreur lors de la génération de l'article: ${errorMessage}`)
    } finally {
      setIsGeneratingArticle(false)
    }
  }

  return {
    // États
    isSaving,
    isGeneratingArticle,
    showCloseConfirm,
    showImportModal,
    showAddArticleModal,
    availableHeadings,

    // Setters
    setShowImportModal,
    setShowAddArticleModal,

    // Actions
    insertNumberedHeading,
    initializeCCTPStructure,
    handleSave,
    handleClose,
    confirmClose,
    cancelClose,
    handleFileImport,
    handleAddArticle,
    handleAddArticleJSON,

    // Refs
    fileInputRef
  }
}
