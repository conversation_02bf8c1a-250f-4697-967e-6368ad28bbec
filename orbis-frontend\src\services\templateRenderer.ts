/**
 * Moteur de rendu pour les templates JSON
 */

export interface TemplateSection {
  id: string
  type: 'container' | 'table' | 'text' | 'dynamic-table'
  styles?: Record<string, string>
  content?: any
  children?: TemplateSection[]
  condition?: string
  dataSource?: string
  template?: any
}

export interface Template {
  name: string
  version: string
  description: string
  sections: TemplateSection[]
}

export class TemplateRenderer {
  private data: Record<string, any> = {}

  constructor(data: Record<string, any>) {
    this.data = data
  }

  /**
   * Rendre un template complet
   */
  render(template: Template): string {
    console.log('🎨 Rendu du template:', template.name)
    console.log('📊 Données:', this.data)

    return template.sections
      .map(section => this.renderSection(section))
      .join('')
  }

  /**
   * Rendre une section
   */
  private renderSection(section: TemplateSection): string {
    // Vérifier les conditions
    if (section.condition && !this.evaluateCondition(section.condition)) {
      return ''
    }

    switch (section.type) {
      case 'container':
        return this.renderContainer(section)
      case 'table':
        return this.renderTable(section)
      case 'text':
        return this.renderText(section)
      case 'dynamic-table':
        return this.renderDynamicTable(section)
      default:
        console.warn(`Type de section non supporté: ${section.type}`)
        return ''
    }
  }

  /**
   * Rendre un conteneur (div)
   */
  private renderContainer(section: TemplateSection): string {
    const styles = this.stylesToString(section.styles)
    const children = section.children 
      ? section.children.map(child => this.renderSection(child)).join('')
      : ''

    return `<div${styles}>${children}</div>`
  }

  /**
   * Rendre un tableau
   */
  private renderTable(section: TemplateSection): string {
    const styles = this.stylesToString(section.styles)
    const rows = section.content?.rows || []

    const rowsHtml = rows.map((row: any) => {
      const cellsHtml = row.cells.map((cell: any) => {
        const cellStyles = this.stylesToString(cell.styles)
        let cellContent = ''

        if (cell.type === 'company-with-logo') {
          // Rendu spécial pour les entreprises avec logo
          cellContent = this.renderCompanyWithLogo(cell)
        } else if (cell.content) {
          cellContent = this.replaceVariables(cell.content)
        } else if (cell.children) {
          cellContent = cell.children.map((child: any) => this.renderSection(child)).join('')
        }

        return `<td${cellStyles}>${cellContent}</td>`
      }).join('')

      return `<tr>${cellsHtml}</tr>`
    }).join('')

    return `<table${styles}>${rowsHtml}</table>`
  }

  /**
   * Rendre du texte
   */
  private renderText(section: TemplateSection): string {
    const styles = this.stylesToString(section.styles)
    const content = this.replaceVariables(section.content || '')
    
    return `<div${styles}>${content}</div>`
  }

  /**
   * Rendre un tableau dynamique (avec données)
   */
  private renderDynamicTable(section: TemplateSection): string {
    const dataSource = section.dataSource
    const data = dataSource ? this.data[dataSource] : []

    if (!data || !Array.isArray(data) || data.length === 0) {
      return ''
    }

    const styles = this.stylesToString(section.styles)
    let html = `<table${styles}>`

    // Header
    if (section.template?.header) {
      const headerStyles = this.stylesToString(section.template.header.styles)
      const headerContent = this.replaceVariables(section.template.header.content)
      html += `<tr><td${headerStyles}>${headerContent}</td></tr>`
    }

    // Rows
    if (section.template?.row) {
      data.forEach((item: any) => {
        const cellsHtml = section.template.row.cells.map((cell: any) => {
          const cellStyles = this.stylesToString(cell.styles)
          const cellContent = this.replaceVariables(cell.content, item)
          return `<td${cellStyles}>${cellContent}</td>`
        }).join('')

        html += `<tr>${cellsHtml}</tr>`
      })
    }

    html += '</table>'
    return html
  }

  /**
   * Convertir les styles en string
   */
  private stylesToString(styles?: Record<string, string>): string {
    if (!styles || Object.keys(styles).length === 0) {
      return ''
    }

    const styleString = Object.entries(styles)
      .map(([key, value]) => {
        // Convertir camelCase en kebab-case
        const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase()
        return `${cssKey}: ${value} !important`
      })
      .join('; ')

    return ` style="${styleString}"`
  }

  /**
   * Remplacer les variables dans le contenu
   */
  private replaceVariables(content: string, itemData?: any): string {
    const dataToUse = itemData || this.data

    return content.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      const value = dataToUse[key]
      return value !== undefined ? String(value) : match
    })
  }

  /**
   * Rendre une entreprise avec son logo
   */
  private renderCompanyWithLogo(cell: any): string {
    const companyName = this.replaceVariables(cell.content || '')
    const logoUrl = this.replaceVariables(cell.logoUrl || '')

    if (logoUrl && logoUrl !== cell.logoUrl) {
      // Il y a un logo à afficher
      return `
        <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
          <img
            src="${logoUrl}"
            alt="Logo ${companyName}"
            style="
              max-width: 80px;
              max-height: 60px;
              object-fit: contain;
              border: 1px solid #ddd;
              border-radius: 4px;
              padding: 4px;
              background-color: white;
            "
          />
          <div style="font-weight: bold; font-size: 16px;">
            ${companyName}
          </div>
        </div>
      `
    } else {
      // Pas de logo, affichage simple
      return `<div style="font-weight: bold; font-size: 16px;">${companyName}</div>`
    }
  }

  /**
   * Évaluer une condition
   */
  private evaluateCondition(condition: string): boolean {
    // Simple évaluation pour les conditions basiques
    const cleanCondition = condition.replace(/\{\{|\}\}/g, '')
    return !!this.data[cleanCondition]
  }
}

/**
 * Fonction utilitaire pour rendre un template
 */
export function renderTemplate(template: Template, data: Record<string, any>): string {
  try {
    // Créer le renderer et rendre
    const renderer = new TemplateRenderer(data)
    return renderer.render(template)
  } catch (error) {
    console.error('Erreur lors du rendu du template:', error)
    throw new Error(`Impossible de rendre le template: ${error}`)
  }
}
