"""
Script pour créer des lots pour chaque projet dans le workspace 8
"""

import asyncio
import aiohttp
import json
from datetime import datetime
from typing import Dict, Any, List

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"
WORKSPACE_ID = 8

class LotCreator:
    def __init__(self):
        self.session = None
        self.auth_token = None
        self.created_lots = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def get_headers(self) -> Dict[str, str]:
        """Retourne les headers avec authentification"""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    async def authenticate(self) -> bool:
        """Authentification avec l'utilisateur test"""
        try:
            auth_data = {
                "email": "<EMAIL>",
                "password": "orbis123!"
            }
            
            async with self.session.post(
                f"{API_BASE}/auth/login",
                json=auth_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.auth_token = data.get("access_token")
                    print(f"✅ Authentification réussie")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Échec authentification: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Erreur authentification: {e}")
            return False
    
    async def get_projects(self) -> List[Dict[str, Any]]:
        """Récupère tous les projets"""
        try:
            async with self.session.get(
                f"{API_BASE}/projects/",
                headers=self.get_headers()
            ) as response:
                if response.status == 200:
                    projects = await response.json()
                    print(f"✅ {len(projects)} projets trouvés")
                    return projects
                else:
                    error_text = await response.text()
                    print(f"❌ Erreur récupération projets: {response.status} - {error_text}")
                    return []
                    
        except Exception as e:
            print(f"❌ Erreur récupération projets: {e}")
            return []
    
    async def create_lot(self, project_id: int, lot_name: str, lot_code: str, description: str) -> Dict[str, Any]:
        """Crée un lot pour un projet donné"""
        lot_data = {
            "name": lot_name,
            "code": lot_code,
            "description": description,
            "project_id": project_id
        }
        
        try:
            async with self.session.post(
                f"{API_BASE}/lots/",
                json=lot_data,
                headers=self.get_headers()
            ) as response:
                if response.status == 201:
                    lot = await response.json()
                    self.created_lots.append(lot["id"])
                    print(f"✅ Lot créé: {lot['name']} (ID: {lot['id']})")
                    return lot
                else:
                    error_text = await response.text()
                    print(f"❌ Échec création lot {lot_name}: {response.status} - {error_text}")
                    return None
                    
        except Exception as e:
            print(f"❌ Erreur création lot {lot_name}: {e}")
            return None
    
    async def create_lots_for_project(self, project: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Crée 2 lots pour un projet donné"""
        project_id = project["id"]
        project_name = project["name"]
        
        print(f"\n📁 Création de lots pour le projet: {project_name} (ID: {project_id})")
        
        # Définir les types de lots standards
        lot_types = [
            {
                "name": f"Gros Œuvre - {project_name}",
                "code": f"GO-{project_id:03d}",
                "description": "Lot Gros Œuvre - Structure, fondations, maçonnerie"
            },
            {
                "name": f"Second Œuvre - {project_name}",
                "code": f"SO-{project_id:03d}",
                "description": "Lot Second Œuvre - Finitions, équipements, aménagements"
            }
        ]
        
        created_lots = []
        for lot_type in lot_types:
            lot = await self.create_lot(
                project_id=project_id,
                lot_name=lot_type["name"],
                lot_code=lot_type["code"],
                description=lot_type["description"]
            )
            if lot:
                created_lots.append(lot)
        
        return created_lots
    
    async def get_existing_lots(self) -> List[Dict[str, Any]]:
        """Récupère tous les lots existants"""
        try:
            async with self.session.get(
                f"{API_BASE}/lots/",
                headers=self.get_headers()
            ) as response:
                if response.status == 200:
                    lots = await response.json()
                    print(f"✅ {len(lots)} lots existants trouvés")
                    return lots
                else:
                    error_text = await response.text()
                    print(f"❌ Erreur récupération lots: {response.status} - {error_text}")
                    return []
                    
        except Exception as e:
            print(f"❌ Erreur récupération lots: {e}")
            return []
    
    async def run_creation(self):
        """Exécute la création de lots pour tous les projets"""
        print("🚀 Démarrage de la création de lots pour tous les projets")
        print("=" * 60)
        
        # Authentification
        if not await self.authenticate():
            print("❌ Impossible de s'authentifier, arrêt du script")
            return
        
        # Récupérer les projets
        projects = await self.get_projects()
        if not projects:
            print("❌ Aucun projet trouvé, arrêt du script")
            return
        
        # Récupérer les lots existants pour éviter les doublons
        existing_lots = await self.get_existing_lots()
        existing_project_ids = {lot.get("project_id") for lot in existing_lots if lot.get("project_id")}
        
        print(f"\n📊 Projets avec des lots existants: {len(existing_project_ids)}")
        print(f"📊 Projets sans lots: {len([p for p in projects if p['id'] not in existing_project_ids])}")
        
        # Créer des lots pour chaque projet
        total_created = 0
        for project in projects:
            project_id = project["id"]
            
            # Vérifier si le projet a déjà des lots
            project_lots = [lot for lot in existing_lots if lot.get("project_id") == project_id]
            
            if project_lots:
                print(f"\n⏭️  Projet {project['name']} a déjà {len(project_lots)} lot(s), ignoré")
                continue
            
            # Créer les lots pour ce projet
            created_lots = await self.create_lots_for_project(project)
            total_created += len(created_lots)
        
        # Résumé final
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ DE LA CRÉATION")
        print("=" * 60)
        print(f"Projets traités: {len(projects)}")
        print(f"Lots créés: {total_created}")
        print(f"IDs des lots créés: {self.created_lots}")
        
        if total_created > 0:
            print("🎉 Création de lots terminée avec succès!")
            
            # Afficher un échantillon des lots créés
            print("\n📋 Vérification des lots créés:")
            final_lots = await self.get_existing_lots()
            for lot in final_lots[-min(5, len(final_lots)):]:  # Afficher les 5 derniers
                print(f"   - {lot['name']} ({lot['code']}) - Phase: {lot['current_phase']}")
        else:
            print("ℹ️  Aucun nouveau lot créé (tous les projets ont déjà des lots)")


async def main():
    """Fonction principale"""
    async with LotCreator() as creator:
        await creator.run_creation()


if __name__ == "__main__":
    print("🏗️  Création de lots pour tous les projets")
    print("Assurez-vous que le serveur FastAPI est démarré sur http://localhost:8000")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Script interrompu par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur lors de l'exécution: {e}")
