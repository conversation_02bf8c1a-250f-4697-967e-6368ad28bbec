#!/usr/bin/env python3
"""
Script pour tester la création d'un document technique
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au PYTHONPATH
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import AsyncSessionLocal
from app.models.project import Project
from app.models.entreprise_tiers import EntrepriseTiers
from app.models.workspace import Workspace

async def test_technical_document_creation():
    """Tester les données nécessaires pour créer un document technique"""
    
    print("🔄 Test des données pour création de document technique...")
    
    async with AsyncSessionLocal() as session:
        try:
            # Récupérer le premier workspace disponible
            workspace_result = await session.execute(
                select(Workspace).where(Workspace.is_active == True).limit(1)
            )
            workspace = workspace_result.scalar_one_or_none()
            
            if not workspace:
                print("❌ Aucun workspace trouvé")
                return
            
            print(f"🔍 Workspace: {workspace.name} (ID: {workspace.id})")
            
            # Récupérer les projets du workspace
            projects_result = await session.execute(
                select(Project).where(Project.workspace_id == workspace.id).limit(5)
            )
            projects = projects_result.scalars().all()
            
            if not projects:
                print("❌ Aucun projet trouvé dans ce workspace")
                return
            
            print(f"📋 Projets disponibles ({len(projects)}):")
            for project in projects:
                print(f"  - {project.name} (ID: {project.id}) - {project.nature}")
            
            # Récupérer les entreprises tierces du workspace
            entreprises_result = await session.execute(
                select(EntrepriseTiers).where(EntrepriseTiers.workspace_id == workspace.id).limit(5)
            )
            entreprises = entreprises_result.scalars().all()
            
            if not entreprises:
                print("❌ Aucune entreprise tierce trouvée dans ce workspace")
                return
            
            print(f"🏢 Entreprises tierces disponibles ({len(entreprises)}):")
            for entreprise in entreprises:
                print(f"  - {entreprise.nom_entreprise} (ID: {entreprise.id}) - {entreprise.activite}")
            
            # Afficher un exemple de payload pour créer un document technique
            example_payload = {
                "name": "CCTP - Maçonnerie",
                "type_document": "CCTP",
                "content": "<h1>Cahier des Clauses Techniques Particulières</h1><p>Maçonnerie générale...</p>",
                "project_id": projects[0].id,
                "company_ids": [entreprises[0].id] if entreprises else []
            }
            
            print(f"\n📝 Exemple de payload pour créer un document technique:")
            print(f"POST /api/v1/technical-documents")
            print(f"Headers: Authorization: Bearer <token>")
            print(f"Body: {example_payload}")
            
            print(f"\n✅ Toutes les données nécessaires sont disponibles!")
            print(f"   - Workspace ID: {workspace.id}")
            print(f"   - Projet ID: {projects[0].id}")
            print(f"   - Entreprise tierce ID: {entreprises[0].id if entreprises else 'Aucune'}")
            
        except Exception as e:
            print(f"❌ Erreur lors du test: {e}")
            raise
        finally:
            await session.close()

if __name__ == "__main__":
    asyncio.run(test_technical_document_creation())
