# app/models/quote.py
from sqlalchemy import <PERSON>olean, Column, Integer, String, DateTime, ForeignKey, Text, Numeric, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.core.database import Base

class QuoteStatus(str, enum.Enum):
    DRAFT = "draft"
    SENT = "sent"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    EXPIRED = "expired"

class Quote(Base):
    __tablename__ = "quotes"

    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False)
    project_id = Column(Integer, ForeignKey("projects.id"))
    supplier_id = Column(Integer, ForeignKey("suppliers.id"))
    quote_number = Column(String, nullable=False, index=True)
    title = Column(String, nullable=False)
    description = Column(Text)
    quote_date = Column(DateTime, nullable=False)
    expiry_date = Column(DateTime)
    total_amount_ht = Column(Numeric(15, 2))
    vat_amount = Column(Numeric(15, 2))
    total_amount_ttc = Column(Numeric(15, 2))
    status = Column(Enum(QuoteStatus), default=QuoteStatus.DRAFT)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    workspace = relationship("Workspace", back_populates="quotes")
    project = relationship("Project", back_populates="quotes")
    supplier = relationship("Supplier", back_populates="quotes")
    lines = relationship("QuoteLine", back_populates="quote")

class QuoteLine(Base):
    __tablename__ = "quote_lines"

    id = Column(Integer, primary_key=True, index=True)
    quote_id = Column(Integer, ForeignKey("quotes.id"), nullable=False)
    material_id = Column(Integer, ForeignKey("materials.id"))
    description = Column(String, nullable=False)
    quantity = Column(Numeric(10, 2), nullable=False)
    unit = Column(String, nullable=False)
    unit_price = Column(Numeric(15, 4), nullable=False)
    total_amount = Column(Numeric(15, 2), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    quote = relationship("Quote", back_populates="lines")
    material = relationship("Material")

class QuoteTemplate(Base):
    __tablename__ = "quote_templates"

    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text)
    template_data = Column(Text)  # JSON data
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    workspace = relationship("Workspace")