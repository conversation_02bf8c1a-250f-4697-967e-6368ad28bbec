'use client'

import React, { useState } from 'react'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'

interface ProjectFormProps {
  onSubmit: (data: any) => void
  isSubmitting?: boolean
  submitButtonText?: string
  initialData?: any
}

export const ProjectForm: React.FC<ProjectFormProps> = ({
  onSubmit,
  isSubmitting = false,
  submitButtonText = 'Créer',
  initialData = {}
}) => {
  const [formData, setFormData] = useState({
    name: initialData.name || '',
    description: initialData.description || '',
    client: initialData.client || '',
    location: initialData.location || '',
    budget: initialData.budget || '',
    startDate: initialData.startDate || '',
    endDate: initialData.endDate || '',
    manager: initialData.manager || '',
    category: initialData.category || '',
    priority: initialData.priority || 'medium',
    notes: initialData.notes || ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <Input
            label="Nom du projet *"
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            placeholder="Ex: Rénovation Bureau Central"
            required
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleChange('description', e.target.value)}
            placeholder="Décrivez brièvement le projet..."
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            rows={3}
            required
          />
        </div>

        <Input
          label="Client *"
          value={formData.client}
          onChange={(e) => handleChange('client', e.target.value)}
          placeholder="Nom du client"
          required
        />

        <Input
          label="Localisation *"
          value={formData.location}
          onChange={(e) => handleChange('location', e.target.value)}
          placeholder="Adresse du projet"
          required
        />

        <Input
          label="Budget (€) *"
          type="number"
          value={formData.budget}
          onChange={(e) => handleChange('budget', e.target.value)}
          placeholder="150000"
          required
        />

        <Input
          label="Chef de projet *"
          value={formData.manager}
          onChange={(e) => handleChange('manager', e.target.value)}
          placeholder="Nom du chef de projet"
          required
        />

        <Input
          label="Date de début *"
          type="date"
          value={formData.startDate}
          onChange={(e) => handleChange('startDate', e.target.value)}
          required
        />

        <Input
          label="Date de fin prévue *"
          type="date"
          value={formData.endDate}
          onChange={(e) => handleChange('endDate', e.target.value)}
          required
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Catégorie
          </label>
          <select
            value={formData.category}
            onChange={(e) => handleChange('category', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">Sélectionner une catégorie</option>
            <option value="renovation">Rénovation</option>
            <option value="construction">Construction</option>
            <option value="reparation">Réparation</option>
            <option value="installation">Installation</option>
            <option value="maintenance">Maintenance</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Priorité
          </label>
          <select
            value={formData.priority}
            onChange={(e) => handleChange('priority', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="low">Basse</option>
            <option value="medium">Moyenne</option>
            <option value="high">Haute</option>
            <option value="urgent">Urgente</option>
          </select>
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Notes additionnelles
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => handleChange('notes', e.target.value)}
            placeholder="Informations complémentaires, contraintes, etc..."
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            rows={3}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={() => window.history.back()}
        >
          Annuler
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {isSubmitting ? 'Enregistrement...' : submitButtonText}
        </Button>
      </div>
    </form>
  )
}