#!/usr/bin/env python3
"""
Script pour vérifier les valeurs d'énumération dans la base de données
"""

import asyncio
import asyncpg

# Configuration directe
ASYNC_DATABASE_URL = "**************************************************************************************************/postgres"


async def check_enum_values():
    """Vérifier les valeurs d'énumération disponibles"""
    print("🔍 Vérification des valeurs d'énumération...")
    
    conn = await asyncpg.connect(ASYNC_DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier les types d'énumération
        result = await conn.fetch("""
            SELECT t.typname, e.enumlabel
            FROM pg_type t
            JOIN pg_enum e ON t.oid = e.enumtypid
            WHERE t.typname LIKE '%role%' OR t.typname LIKE '%status%'
            ORDER BY t.typname, e.enumsortorder
        """)
        
        if result:
            print("📋 Valeurs d'énumération trouvées:")
            current_type = None
            for row in result:
                if row['typname'] != current_type:
                    current_type = row['typname']
                    print(f"\n   🏷️  Type: {current_type}")
                print(f"      - {row['enumlabel']}")
        else:
            print("❌ Aucune énumération trouvée")
        
        # Vérifier la structure de la table users
        print("\n🔍 Structure de la table users:")
        result = await conn.fetch("""
            SELECT column_name, data_type, udt_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' 
            ORDER BY ordinal_position
        """)
        
        for row in result:
            print(f"   📝 {row['column_name']}: {row['data_type']} ({row['udt_name']})")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        await conn.close()


if __name__ == "__main__":
    asyncio.run(check_enum_values())
