#!/usr/bin/env python3
"""
Script pour vérifier la structure de la base de données
"""

import asyncio
import asyncpg
from app.core.config import settings

DATABASE_URL = settings.DATABASE_URL


async def check_database_structure():
    """Vérifier la structure de la base de données"""
    print("🔍 Vérification de la structure de la base de données...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Lister toutes les tables
        print("\n📋 Tables existantes:")
        tables = await conn.fetch("""
            SELECT table_name, table_type 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        for table in tables:
            print(f"   • {table['table_name']} ({table['table_type']})")
        
        # 2. Vérifier la structure de la table users
        print("\n👤 Structure de la table 'users':")
        users_columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'users' AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        for col in users_columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
            print(f"   • {col['column_name']}: {col['data_type']} {nullable}{default}")
        
        # 3. Vérifier si user_profiles existe
        print("\n👥 Vérification de la table 'user_profiles':")
        user_profiles_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'user_profiles' AND table_schema = 'public'
            )
        """)
        
        if user_profiles_exists:
            print("   ✅ Table user_profiles existe")
            
            profiles_columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'user_profiles' AND table_schema = 'public'
                ORDER BY ordinal_position
            """)
            
            for col in profiles_columns:
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                print(f"   • {col['column_name']}: {col['data_type']} {nullable}{default}")
                
            # Compter les enregistrements
            profile_count = await conn.fetchval("SELECT COUNT(*) FROM user_profiles")
            print(f"   📊 Nombre d'enregistrements: {profile_count}")
            
        else:
            print("   ❌ Table user_profiles n'existe pas")
        
        # 4. Vérifier les données dans users
        print("\n📊 Données dans la table 'users':")
        users_data = await conn.fetch("""
            SELECT id, email, first_name, last_name, role, supabase_user_id, is_active
            FROM users
            ORDER BY created_at
        """)
        
        for user in users_data:
            supabase_id = user['supabase_user_id'][:8] + "..." if user['supabase_user_id'] else "None"
            print(f"   • ID: {user['id']} | {user['email']} | {user['first_name']} {user['last_name']} | {user['role']} | Supabase: {supabase_id} | Actif: {user['is_active']}")
        
        # 5. Vérifier les relations avec user_companies
        print("\n🏢 Relations user_companies:")
        user_companies = await conn.fetch("""
            SELECT u.email, c.name as company_name, uc.role, uc.is_active
            FROM users u
            JOIN user_companies uc ON u.id = uc.user_id
            JOIN companies c ON uc.company_id = c.id
            ORDER BY u.email
        """)
        
        for rel in user_companies:
            print(f"   • {rel['email']} → {rel['company_name']} ({rel['role']}) - Actif: {rel['is_active']}")
        
        # 6. Recommandations
        print("\n💡 Recommandations:")
        
        if not user_profiles_exists:
            print("   🔧 Créer la table user_profiles pour séparer les données d'authentification des profils")
        
        if len(users_data) == 0:
            print("   👤 Aucun utilisateur trouvé - créer un utilisateur admin")
        
        admin_users = [u for u in users_data if u['role'] in ['ADMIN', 'admin']]
        if len(admin_users) == 0:
            print("   🔑 Aucun administrateur trouvé - promouvoir un utilisateur ou en créer un")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Vérification de la Structure de Base de Données")
    print("="*60)
    
    if await check_database_structure():
        print("\n✅ Vérification terminée avec succès!")
        return True
    else:
        print("\n❌ Échec de la vérification")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
