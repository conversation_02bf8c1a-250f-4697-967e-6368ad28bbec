#!/usr/bin/env python3
"""
Création du système RBAC (Role-Based Access Control) pour ORBIS
"""
import asyncio
import asyncpg
from app.core.config import settings

async def create_rbac_system():
    print("🔧 Création du système RBAC...")
    
    try:
        conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)
        
        # 1. Créer la table des rôles
        print("\n1. 📋 Création de la table roles...")
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS roles (
                id SERIAL PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                level INTEGER NOT NULL, -- Hiérarchie : 1=viewer, 2=user, 3=manager, 4=admin, 5=super_admin
                is_system_role BOOLEAN DEFAULT FALSE, -- R<PERSON><PERSON> système vs métier
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        """)
        
        # 2. Créer la table des permissions
        print("2. 🔐 Création de la table permissions...")
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS permissions (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) UNIQUE NOT NULL,
                resource VARCHAR(50) NOT NULL, -- 'companies', 'users', 'projects', 'documents'
                action VARCHAR(50) NOT NULL,   -- 'create', 'read', 'update', 'delete', 'manage'
                description TEXT,
                created_at TIMESTAMP DEFAULT NOW()
            )
        """)
        
        # 3. Créer la table de liaison role_permissions
        print("3. 🔗 Création de la table role_permissions...")
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS role_permissions (
                role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
                permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
                PRIMARY KEY (role_id, permission_id)
            )
        """)
        
        # 4. Modifier la table user_companies pour utiliser company_role
        print("4. 🏢 Modification de user_companies...")
        await conn.execute("""
            ALTER TABLE user_companies 
            DROP COLUMN IF EXISTS role CASCADE
        """)
        
        await conn.execute("""
            ALTER TABLE user_companies 
            ADD COLUMN IF NOT EXISTS company_role VARCHAR(50) DEFAULT 'USER'
        """)
        
        # 5. Insérer les rôles système
        print("5. 👤 Insertion des rôles système...")
        system_roles = [
            ('SUPER_ADMIN', 'Super administrateur système', 5, True),
            ('ADMIN', 'Administrateur', 4, True),
            ('MANAGER', 'Manager', 3, True),
            ('USER', 'Utilisateur', 2, True),
            ('VIEWER', 'Lecteur', 1, True)
        ]
        
        for name, desc, level, is_system in system_roles:
            await conn.execute("""
                INSERT INTO roles (name, description, level, is_system_role)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (name) DO UPDATE SET
                    description = EXCLUDED.description,
                    level = EXCLUDED.level,
                    is_system_role = EXCLUDED.is_system_role
            """, name, desc, level, is_system)
        
        # 6. Insérer les rôles métier BTP
        print("6. 🏗️ Insertion des rôles métier BTP...")
        business_roles = [
            ('MOA', 'Maître d\'Ouvrage', 4, False),
            ('MOADEL', 'Maître d\'Ouvrage Délégué', 4, False),
            ('ARCHI', 'Architecte', 3, False),
            ('BE', 'Bureau d\'Études', 3, False),
            ('BC', 'Bureau de Contrôle', 3, False),
            ('OPC', 'Ordonnancement Pilotage Coordination', 3, False),
            ('ENT', 'Entreprise', 2, False),
            ('FO', 'Fournisseur', 1, False)
        ]
        
        for name, desc, level, is_system in business_roles:
            await conn.execute("""
                INSERT INTO roles (name, description, level, is_system_role)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (name) DO UPDATE SET
                    description = EXCLUDED.description,
                    level = EXCLUDED.level,
                    is_system_role = EXCLUDED.is_system_role
            """, name, desc, level, is_system)
        
        # 7. Insérer les permissions de base
        print("7. 🔑 Insertion des permissions...")
        permissions = [
            # Permissions système
            ('manage_system', 'system', 'manage', 'Gérer le système'),
            ('manage_companies', 'companies', 'manage', 'Gérer les entreprises'),
            ('view_all_companies', 'companies', 'read', 'Voir toutes les entreprises'),
            
            # Permissions entreprise
            ('manage_company_users', 'users', 'manage', 'Gérer les utilisateurs de l\'entreprise'),
            ('view_company_users', 'users', 'read', 'Voir les utilisateurs de l\'entreprise'),
            ('create_users', 'users', 'create', 'Créer des utilisateurs'),
            ('update_users', 'users', 'update', 'Modifier des utilisateurs'),
            ('delete_users', 'users', 'delete', 'Supprimer des utilisateurs'),
            
            # Permissions projets
            ('manage_projects', 'projects', 'manage', 'Gérer les projets'),
            ('view_projects', 'projects', 'read', 'Voir les projets'),
            ('create_projects', 'projects', 'create', 'Créer des projets'),
            ('update_projects', 'projects', 'update', 'Modifier des projets'),
            ('delete_projects', 'projects', 'delete', 'Supprimer des projets'),
            
            # Permissions documents
            ('manage_documents', 'documents', 'manage', 'Gérer les documents'),
            ('view_documents', 'documents', 'read', 'Voir les documents'),
            ('upload_documents', 'documents', 'create', 'Uploader des documents'),
            ('download_documents', 'documents', 'read', 'Télécharger des documents'),
        ]
        
        for name, resource, action, desc in permissions:
            await conn.execute("""
                INSERT INTO permissions (name, resource, action, description)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (name) DO NOTHING
            """, name, resource, action, desc)
        
        # 8. Assigner les permissions aux rôles système
        print("8. 🔗 Attribution des permissions aux rôles...")
        
        # SUPER_ADMIN : toutes les permissions
        super_admin_id = await conn.fetchval("SELECT id FROM roles WHERE name = 'SUPER_ADMIN'")
        all_permissions = await conn.fetch("SELECT id FROM permissions")
        
        for perm in all_permissions:
            await conn.execute("""
                INSERT INTO role_permissions (role_id, permission_id)
                VALUES ($1, $2)
                ON CONFLICT DO NOTHING
            """, super_admin_id, perm['id'])
        
        # ADMIN : permissions d'entreprise
        admin_id = await conn.fetchval("SELECT id FROM roles WHERE name = 'ADMIN'")
        admin_perms = await conn.fetch("""
            SELECT id FROM permissions 
            WHERE name IN ('manage_company_users', 'view_company_users', 'create_users', 
                          'update_users', 'manage_projects', 'view_projects', 'create_projects',
                          'update_projects', 'manage_documents', 'view_documents', 'upload_documents')
        """)
        
        for perm in admin_perms:
            await conn.execute("""
                INSERT INTO role_permissions (role_id, permission_id)
                VALUES ($1, $2)
                ON CONFLICT DO NOTHING
            """, admin_id, perm['id'])
        
        # 9. Vérification finale
        print("\n9. ✅ Vérification du système RBAC...")
        
        roles_count = await conn.fetchval("SELECT COUNT(*) FROM roles")
        permissions_count = await conn.fetchval("SELECT COUNT(*) FROM permissions")
        role_perms_count = await conn.fetchval("SELECT COUNT(*) FROM role_permissions")
        
        print(f"  - {roles_count} rôles créés")
        print(f"  - {permissions_count} permissions créées")
        print(f"  - {role_perms_count} associations rôle-permission")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(create_rbac_system())
    if success:
        print('\n🎉 Système RBAC créé avec succès!')
    else:
        print('\n❌ Échec de la création du système RBAC')
