#!/usr/bin/env python3
"""
Script pour ajouter la colonne nature à la table projects
"""

import asyncio
import asyncpg

DATABASE_URL = "**************************************************************************************************/postgres"

async def add_nature_column():
    """Ajouter la colonne nature à la table projects"""
    print("🚀 Ajout de la colonne nature à la table projects...")
    
    try:
        # Connexion à la base de données
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Connexion à la base de données réussie")
        
        # 1. Créer l'enum ProjectNature s'il n'existe pas
        try:
            await conn.execute("""
                CREATE TYPE projectnature AS ENUM ('Devis', 'AO', 'Affaire')
            """)
            print("✅ Enum ProjectNature créé")
        except Exception as e:
            if "already exists" in str(e):
                print("ℹ️ Enum ProjectNature existe déjà")
            else:
                print(f"⚠️ Erreur lors de la création de l'enum: {e}")
        
        # 2. Vérifier si la colonne nature existe déjà
        column_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'projects' AND column_name = 'nature'
            )
        """)
        
        if column_exists:
            print("ℹ️ La colonne nature existe déjà")
        else:
            # 3. Ajouter la colonne nature
            await conn.execute("""
                ALTER TABLE projects 
                ADD COLUMN nature projectnature DEFAULT 'Devis'
            """)
            print("✅ Colonne nature ajoutée")
            
            # 4. Mettre à jour les projets existants avec une valeur par défaut
            updated_count = await conn.fetchval("""
                UPDATE projects 
                SET nature = 'Devis' 
                WHERE nature IS NULL
                RETURNING (SELECT COUNT(*) FROM projects WHERE nature = 'Devis')
            """)
            print(f"✅ {updated_count or 0} projets mis à jour avec nature='Devis'")
        
        # 5. Vérifier le résultat
        projects_with_nature = await conn.fetch("""
            SELECT name, code, nature 
            FROM projects 
            LIMIT 5
        """)
        
        print(f"\n📊 Exemples de projets avec nature:")
        for project in projects_with_nature:
            print(f"   • {project['name']} ({project['code']}) → {project['nature']}")
        
        # 6. Compter par nature
        nature_counts = await conn.fetch("""
            SELECT nature, COUNT(*) as count
            FROM projects 
            GROUP BY nature
            ORDER BY nature
        """)
        
        print(f"\n📈 Répartition par nature:")
        for row in nature_counts:
            print(f"   • {row['nature']}: {row['count']} projet(s)")
        
        await conn.close()
        print("\n🎉 Colonne nature ajoutée avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'ajout de la colonne: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_nature_column():
    """Tester la colonne nature"""
    print("\n🧪 Test de la colonne nature...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Test: récupérer les projets avec leur nature
        projects = await conn.fetch("""
            SELECT name, code, nature
            FROM projects
            WHERE nature IS NOT NULL
            LIMIT 3
        """)
        
        print("✅ Test de lecture réussi:")
        for project in projects:
            print(f"   • {project['name']} ({project['code']}) → {project['nature']}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Ajout de la colonne nature à la table projects")
    print("=" * 50)
    
    async def main():
        # Ajouter la colonne
        success = await add_nature_column()
        
        if success:
            # Tester la colonne
            await test_nature_column()
        
        return success
    
    result = asyncio.run(main())
    
    if result:
        print("\n✅ Colonne nature ajoutée avec succès!")
        print("🚀 Vous pouvez maintenant utiliser le filtrage par nature")
    else:
        print("\n❌ Échec de l'ajout de la colonne nature")
