'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function TestSupabasePage() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testConnection = async () => {
    setLoading(true)
    setResult('Testing Supabase connection...\n')
    
    try {
      // Test 1: Vérifier la configuration
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      
      setResult(prev => prev + `Supabase URL: ${supabaseUrl}\n`)
      setResult(prev => prev + `Supabase Key: ${supabaseKey ? 'Configured' : 'Missing'}\n\n`)
      
      // Test 2: Test de connexion basique
      setResult(prev => prev + 'Testing basic connection...\n')
      const { data, error } = await supabase.auth.getSession()
      
      if (error) {
        setResult(prev => prev + `Session error: ${error.message}\n`)
      } else {
        setResult(prev => prev + `Session check: OK\n`)
        setResult(prev => prev + `Current user: ${data.session?.user?.email || 'None'}\n\n`)
      }
      
      // Test 3: Test de connexion avec les identifiants admin
      setResult(prev => prev + 'Testing admin login...\n')
      const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'orbis123!'
      })
      
      if (loginError) {
        setResult(prev => prev + `Login error: ${loginError.message}\n`)
        setResult(prev => prev + `Error code: ${loginError.status}\n`)
      } else {
        setResult(prev => prev + `Login success!\n`)
        setResult(prev => prev + `User ID: ${loginData.user?.id}\n`)
        setResult(prev => prev + `Email: ${loginData.user?.email}\n`)
        setResult(prev => prev + `Metadata: ${JSON.stringify(loginData.user?.user_metadata, null, 2)}\n`)
        
        // Déconnexion après le test
        await supabase.auth.signOut()
        setResult(prev => prev + `Signed out after test\n`)
      }
      
    } catch (error: any) {
      setResult(prev => prev + `Unexpected error: ${error.message}\n`)
    } finally {
      setLoading(false)
    }
  }

  const clearResult = () => {
    setResult('')
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Test Supabase Connection</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex space-x-4 mb-4">
            <button
              onClick={testConnection}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg"
            >
              {loading ? 'Testing...' : 'Test Connection'}
            </button>
            
            <button
              onClick={clearResult}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg"
            >
              Clear
            </button>
          </div>
          
          <div className="bg-gray-100 rounded-lg p-4 min-h-[400px]">
            <pre className="text-sm text-gray-800 whitespace-pre-wrap font-mono">
              {result || 'Click "Test Connection" to start testing...'}
            </pre>
          </div>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-900 mb-2">Environment Variables</h3>
          <div className="text-sm text-blue-800 space-y-1">
            <div>NEXT_PUBLIC_SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set'}</div>
            <div>NEXT_PUBLIC_SUPABASE_ANON_KEY: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set'}</div>
          </div>
        </div>
      </div>
    </div>
  )
}
