'use client'

import { useState, useEffect } from 'react'
import { EmployeeCard } from '@/components/employees/EmployeeCard'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import { Modal } from '@/components/ui/Modal'
import Link from 'next/link'
import { api } from '@/lib/api'

export default function Employees() {
  const [employees, setEmployees] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState('all')
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [newEmployee, setNewEmployee] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: '',
    hourlyRate: '',
    skills: ''
  })

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setLoading(true)
        const data = await api.getEmployees()
        setEmployees(data)
        setError(null)
      } catch (err) {
        console.error('Error fetching employees:', err)
        setError('Erreur lors du chargement des employés')
      } finally {
        setLoading(false)
      }
    }

    fetchEmployees()
  }, [])

  const filteredEmployees = employees.filter((employee: any) => {
    const matchesSearch = 
      employee.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.role.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterRole === 'all' || employee.role === filterRole

    return matchesSearch && matchesFilter
  })

  const roles = [...new Set(employees.map((emp: any) => emp.role))]

  const handleAddEmployee = async () => {
    try {
      const employeeData = {
        first_name: newEmployee.firstName,
        last_name: newEmployee.lastName,
        email: newEmployee.email,
        phone: newEmployee.phone,
        role: newEmployee.role,
        hourly_rate: parseFloat(newEmployee.hourlyRate),
        skills: newEmployee.skills.split(',').map(s => s.trim()),
        status: 'Actif'
      }
      
      await api.createEmployee(employeeData)
      
      // Refresh employees list
      const data = await api.getEmployees()
      setEmployees(data)
      
      setIsAddModalOpen(false)
      setNewEmployee({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        role: '',
        hourlyRate: '',
        skills: ''
      })
    } catch (err) {
      console.error('Error adding employee:', err)
      alert('Erreur lors de l\'ajout de l\'employé')
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-lg text-gray-600">Chargement des employés...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-4">{error}</div>
          <Button onClick={() => window.location.reload()}>
            Réessayer
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Employés</h1>
          <p className="text-gray-600 mt-1">Gérez votre équipe et suivez les performances</p>
        </div>
        <Button 
          className="bg-blue-600 hover:bg-blue-700"
          onClick={() => setIsAddModalOpen(true)}
        >
          Nouvel Employé
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">👥</div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{employees.length}</div>
              <div className="text-sm text-gray-600">Total Employés</div>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">✅</div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {employees.filter((emp: any) => emp.status === 'Actif').length}
              </div>
              <div className="text-sm text-gray-600">Actifs</div>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">⏰</div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {employees.reduce((sum: number, emp: any) => sum + emp.totalHours, 0)}
              </div>
              <div className="text-sm text-gray-600">Heures Totales</div>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">💰</div>
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(employees.reduce((sum: number, emp: any) => sum + emp.hourlyRate, 0) / employees.length)}€
              </div>
              <div className="text-sm text-gray-600">Taux Moyen/h</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Rechercher un employé..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="flex gap-2 flex-wrap">
            <Button
              variant={filterRole === 'all' ? 'primary' : 'outline'}
              onClick={() => setFilterRole('all')}
            >
              Tous
            </Button>
            {roles.map((role) => (
              <Button
                key={role}
                variant={filterRole === role ? 'primary' : 'outline'}
                onClick={() => setFilterRole(role)}
              >
                {role}
              </Button>
            ))}
          </div>
        </div>
      </Card>

      {/* Employees Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredEmployees.map((employee: any) => (
          <EmployeeCard key={employee.id} employee={employee} />
        ))}
      </div>

      {/* Empty State */}
      {filteredEmployees.length === 0 && (
        <Card className="p-12 text-center">
          <div className="text-gray-400 text-6xl mb-4">👥</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Aucun employé trouvé</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filterRole !== 'all' 
              ? 'Aucun employé ne correspond à vos critères de recherche.'
              : 'Vous n\'avez pas encore d\'employés. Ajoutez votre premier employé pour commencer.'
            }
          </p>
          <Button 
            className="bg-blue-600 hover:bg-blue-700"
            onClick={() => setIsAddModalOpen(true)}
          >
            Ajouter un Employé
          </Button>
        </Card>
      )}

      {/* Add Employee Modal */}
      <Modal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)}>
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Ajouter un nouvel employé</h2>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Prénom"
                value={newEmployee.firstName}
                onChange={(e) => setNewEmployee({...newEmployee, firstName: e.target.value})}
              />
              <Input
                label="Nom"
                value={newEmployee.lastName}
                onChange={(e) => setNewEmployee({...newEmployee, lastName: e.target.value})}
              />
            </div>
            <Input
              label="Email"
              type="email"
              value={newEmployee.email}
              onChange={(e) => setNewEmployee({...newEmployee, email: e.target.value})}
            />
            <Input
              label="Téléphone"
              value={newEmployee.phone}
              onChange={(e) => setNewEmployee({...newEmployee, phone: e.target.value})}
            />
            <Input
              label="Poste"
              value={newEmployee.role}
              onChange={(e) => setNewEmployee({...newEmployee, role: e.target.value})}
            />
            <Input
              label="Taux horaire (€)"
              type="number"
              value={newEmployee.hourlyRate}
              onChange={(e) => setNewEmployee({...newEmployee, hourlyRate: e.target.value})}
            />
            <Input
              label="Compétences (séparées par des virgules)"
              value={newEmployee.skills}
              onChange={(e) => setNewEmployee({...newEmployee, skills: e.target.value})}
            />
            <div className="flex justify-end gap-2 mt-6">
              <Button 
                variant="outline" 
                onClick={() => setIsAddModalOpen(false)}
              >
                Annuler
              </Button>
              <Button onClick={handleAddEmployee}>
                Ajouter
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  )
}