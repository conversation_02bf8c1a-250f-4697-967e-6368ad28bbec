/**
 * Utilitaires pour la numérotation automatique des titres
 */

export interface HeadingInfo {
  number: string
  level: number
  pos: number
  text: string
}

/**
 * Extrait les numéros des titres existants dans le document
 */
export function extractHeadingNumbers(headings: { level: number; text: string; pos: number }[]): HeadingInfo[] {
  const numberedHeadings: HeadingInfo[] = []
  
  headings.forEach(heading => {
    // Regex qui accepte le point final : "1." ou "1.1." ou "1.1.1." etc.
    const match = heading.text.match(/^(\d+(?:\.\d+)*(?:\.[a-z])?)\.?\s/)
    if (match) {
      numberedHeadings.push({
        number: match[1],
        level: heading.level,
        pos: heading.pos,
        text: heading.text
      })
      console.log('✅ Numéro trouvé:', match[1], 'niveau:', heading.level, 'pos:', heading.pos)
    } else {
      console.log('❌ Pas de numéro dans:', heading.text)
    }
  })
  
  return numberedHeadings
}

/**
 * Trouve le contexte hiérarchique au-dessus du curseur
 */
export function findParentContext(numberedHeadings: HeadingInfo[], currentPos: number, targetLevel: number): string {
  const headingsAbove = numberedHeadings.filter(h => h.pos < currentPos)
  console.log('⬆️ Titres au-dessus du curseur:', headingsAbove)
  
  const parentLevel = targetLevel - 1
  let parentNumber = ''
  
  if (parentLevel > 0) {
    // Chercher le dernier titre de niveau parent au-dessus du curseur
    const parentHeadings = headingsAbove.filter(h => h.level === parentLevel)
    if (parentHeadings.length > 0) {
      const lastParent = parentHeadings[parentHeadings.length - 1]
      parentNumber = lastParent.number
      console.log('👆 Dernier parent (niveau', parentLevel, '):', parentNumber)
    }
  }
  
  return parentNumber
}

/**
 * Calcule le prochain numéro de titre selon le contexte hiérarchique
 */
export function calculateNextHeadingNumber(
  numberedHeadings: HeadingInfo[], 
  currentPos: number, 
  targetLevel: number
): string {
  console.log('🔍 calculateNextHeadingNumber - targetLevel:', targetLevel)
  console.log('📍 Position curseur:', currentPos)
  console.log('📊 Titres numérotés:', numberedHeadings)
  
  // Trouver le contexte parent
  const parentNumber = findParentContext(numberedHeadings, currentPos, targetLevel)
  
  // Chercher les enfants de ce parent
  let childNumbers: number[] = []
  
  if (parentNumber) {
    // Chercher tous les enfants de ce parent
    const regex = new RegExp(`^${parentNumber.replace(/\./g, '\\.')}\\.(\\d+)$`)
    childNumbers = numberedHeadings
      .filter(h => h.level === targetLevel)
      .map(h => {
        const match = h.number.match(regex)
        return match ? parseInt(match[1]) : null
      })
      .filter(num => num !== null) as number[]
    
    console.log('👶 Enfants de', parentNumber + ':', childNumbers)
  } else {
    // Pas de parent, chercher au niveau racine
    if (targetLevel === 1) {
      childNumbers = numberedHeadings
        .filter(h => h.level === 1)
        .map(h => parseInt(h.number))
        .filter(num => !isNaN(num))
    }
  }
  
  // Calculer le prochain numéro
  const maxChild = childNumbers.length > 0 ? Math.max(...childNumbers) : 0
  const nextNumber = maxChild + 1
  
  let result = ''
  if (parentNumber) {
    if (targetLevel === 6) {
      // Niveau 6: utiliser des lettres
      const nextLetter = String.fromCharCode(97 + maxChild) // a, b, c...
      result = `${parentNumber}.${nextLetter}.`
    } else {
      result = `${parentNumber}.${nextNumber}.`
    }
  } else {
    result = `${nextNumber}.`
  }
  
  console.log('🎯 Résultat final:', result)
  return result
}

/**
 * Logique simplifiée pour les tests - cherche le dernier du même niveau et incrémente
 */
export function calculateSimpleIncrement(numberedHeadings: HeadingInfo[], targetLevel: number): string {
  console.log('🔍 Mode simple - targetLevel:', targetLevel)
  
  // Créer le pattern pour le niveau cible
  let levelPattern: RegExp
  if (targetLevel === 1) {
    levelPattern = /^\d+$/
  } else if (targetLevel === 2) {
    levelPattern = /^\d+\.\d+$/
  } else if (targetLevel === 3) {
    levelPattern = /^\d+\.\d+\.\d+$/
  } else if (targetLevel === 4) {
    levelPattern = /^\d+\.\d+\.\d+\.\d+$/
  } else if (targetLevel === 5) {
    levelPattern = /^\d+\.\d+\.\d+\.\d+\.\d+$/
  } else { // targetLevel === 6
    levelPattern = /^\d+\.\d+\.\d+\.\d+\.\d+\.[a-z]$/
  }

  // Filtrer les numéros du niveau cible
  const currentLevelNumbers = numberedHeadings
    .filter(h => levelPattern.test(h.number))
    .map(h => h.number)
  
  console.log('📋 Numéros du niveau', targetLevel, ':', currentLevelNumbers)
  
  if (currentLevelNumbers.length === 0) {
    // Aucun numéro de ce niveau, créer le premier
    if (targetLevel === 1) {
      return '1.'
    } else if (targetLevel === 6) {
      return '1.1.1.1.1.a.'
    } else {
      return '1.' + Array(targetLevel - 1).fill('1').join('.') + '.'
    }
  }

  // Prendre le dernier numéro du niveau et incrémenter la dernière partie
  const lastNumber = currentLevelNumbers[currentLevelNumbers.length - 1]
  const parts = lastNumber.split('.')
  const lastPart = parts[parts.length - 1]
  
  if (targetLevel === 6 && /^[a-z]$/.test(lastPart)) {
    // Niveau 6 avec lettres : a -> b -> c...
    const nextLetter = String.fromCharCode(lastPart.charCodeAt(0) + 1)
    parts[parts.length - 1] = nextLetter
  } else {
    // Tous les autres niveaux : incrémenter le nombre
    const currentNum = parseInt(lastPart)
    parts[parts.length - 1] = (currentNum + 1).toString()
  }
  
  const result = parts.join('.') + '.'
  console.log('🎯 Résultat simple:', result)
  return result
}
