'use client'

import { useState } from 'react';
import { Lot, LotPhase, PHASE_LABELS, PHASE_ORDER, PHASE_COLORS, canValidatePhase } from '@/types/lot';
import { Button } from '@/components/ui/Button';
import { lotsApiService } from '@/lib/api/lots';

interface LotPhaseProgressProps {
  lot: Lot;
  onPhaseUpdate?: (updatedLot: Lot) => void;
  readOnly?: boolean;
}

export default function LotPhaseProgress({ lot, onPhaseUpdate, readOnly = false }: LotPhaseProgressProps) {
  const [loading, setLoading] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handlePhaseValidation = async (phase: LotPhase, validated: boolean) => {
    if (readOnly) return;
    
    setLoading(phase);
    setError(null);

    try {
      const updatedLot = await lotsApiService.validateLotPhase(lot.id, {
        phase,
        validated
      });
      
      onPhaseUpdate?.(updatedLot);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la validation');
    } finally {
      setLoading(null);
    }
  };
  const getPhaseStatus = (phase: LotPhase) => {
    switch (phase) {
      case LotPhase.ESQ:
        return lot.esq_validated;
      case LotPhase.APD:
        return lot.apd_validated;
      case LotPhase.PRODCE:
        return lot.prodce_validated;
      case LotPhase.EXE:
        return lot.exe_validated;
      default:
        return false;
    }
  };

  const getPhaseStyle = (phase: LotPhase, index: number) => {
    const isValidated = getPhaseStatus(phase);
    const isCurrent = lot.current_phase === phase;
    
    if (isValidated) {
      return 'bg-green-500 text-white border-green-500';
    } else if (isCurrent) {
      // Utiliser les mêmes couleurs que les badges dans PHASE_COLORS
      const badgeColors = PHASE_COLORS[phase];
      if (badgeColors.includes('bg-blue-100')) {
        return 'bg-blue-500 text-white border-blue-500';
      } else if (badgeColors.includes('bg-yellow-100')) {
        return 'bg-yellow-500 text-white border-yellow-500';
      } else if (badgeColors.includes('bg-orange-100')) {
        return 'bg-orange-500 text-white border-orange-500';
      } else if (badgeColors.includes('bg-green-100')) {
        return 'bg-green-500 text-white border-green-500';
      } else {
        return 'bg-blue-500 text-white border-blue-500';
      }
    } else {
      return 'bg-gray-200 text-gray-500 border-gray-200';
    }
  };

  return (
    <div className="w-full">
      <div className="grid grid-cols-4 gap-2">
        {PHASE_ORDER.map((phase, index) => {
          const isValidated = getPhaseStatus(phase);
          const isCurrent = lot.current_phase === phase;
          
          return (
            <div
              key={phase}
              className={`
                relative h-16 rounded-lg border-2 flex flex-col items-center justify-center
                transition-all duration-300 ${getPhaseStyle(phase, index)}
              `}
            >
              {/* Icône de validation ou numéro */}
              <div className="flex items-center justify-center mb-1">
                {isValidated ? (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : isCurrent ? (
                  <div className="w-3 h-3 rounded-full bg-white animate-pulse" />
                ) : (
                  <span className="text-xs font-medium">{index + 1}</span>
                )}
              </div>
              
              {/* Label de la phase */}
              <div className="text-xs font-semibold text-center">
                {PHASE_LABELS[phase]}
              </div>
              
              {/* Indicateur de statut */}
              {isCurrent && !isValidated && (
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
                  <div className="text-xs bg-blue-600 text-white px-2 py-0.5 rounded-full whitespace-nowrap">
                    En cours
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
      
      {/* Ligne de progression */}
      <div className="relative mt-4">
        <div className="absolute top-0 left-0 w-full h-1 bg-gray-200 rounded-full" />
        <div 
          className="absolute top-0 left-0 h-1 bg-green-500 rounded-full transition-all duration-500"
          style={{ 
            width: `${(PHASE_ORDER.findIndex(p => p === lot.current_phase) + (getPhaseStatus(lot.current_phase) ? 1 : 0)) / PHASE_ORDER.length * 100}%` 
          }}
        />
      </div>
    </div>
  );
}
