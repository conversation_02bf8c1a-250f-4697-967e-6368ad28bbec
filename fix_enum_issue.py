#!/usr/bin/env python3
"""
Fix the enum issue by updating the user role to match SQLAlchemy expectations
"""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def fix_enum_issue():
    """Fix the enum issue by updating the user's role"""
    
    DATABASE_URL = os.getenv("DATABASE_URL")
    if not DATABASE_URL:
        print("❌ DATABASE_URL non trouvé dans .env")
        return False
    
    print("🔧 Correction du problème d'enum")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        print("✅ Connexion à la base de données réussie")
        
        # 1. Vérifier l'utilisateur actuel
        print("\n1. 🔍 Vérification de l'utilisateur actuel:")
        user = await conn.fetchrow("SELECT id, email, role FROM users WHERE email = '<EMAIL>'")
        
        if user:
            print(f"  - ID: {user['id']}")
            print(f"  - Email: {user['email']}")
            print(f"  - Role actuel: {user['role']}")
        else:
            print("  ❌ Utilisateur non trouvé")
            return False
        
        # 2. Supprimer l'utilisateur et le recréer avec le bon enum
        print("\n2. 🔄 Suppression et recréation de l'utilisateur...")
        
        # Supprimer l'utilisateur
        await conn.execute("DELETE FROM users WHERE email = '<EMAIL>'")
        print("  ✅ Utilisateur supprimé")
        
        # Recréer l'utilisateur avec le bon enum
        user_id = await conn.fetchval("""
            INSERT INTO users (
                email, first_name, last_name, role, is_active, is_superuser, is_verified,
                created_at, updated_at
            ) VALUES (
                '<EMAIL>', 'Jeremy', 'Giaime', 'super_admin'::userrole, 
                true, true, true, NOW(), NOW()
            ) RETURNING id
        """)
        
        print(f"  ✅ Utilisateur recréé avec ID: {user_id}")
        
        # 3. Vérifier la création
        print("\n3. ✅ Vérification finale:")
        updated_user = await conn.fetchrow("SELECT id, email, role, is_superuser FROM users WHERE email = '<EMAIL>'")
        
        if updated_user:
            print(f"  - ID: {updated_user['id']}")
            print(f"  - Email: {updated_user['email']}")
            print(f"  - Role: {updated_user['role']}")
            print(f"  - Is Superuser: {updated_user['is_superuser']}")
        
        await conn.close()
        print("\n🎉 Correction terminée avec succès!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_enum_issue())
    if success:
        print("\n✅ Redémarrez le serveur et testez la connexion.")
    else:
        print("\n❌ Correction échouée.")
