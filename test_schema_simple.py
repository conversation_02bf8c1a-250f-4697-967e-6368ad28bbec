#!/usr/bin/env python3
"""
Test isolé du schéma TCompanyResponse sans dépendances
"""

from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
from datetime import datetime
import re

# Copie simplifiée du schéma pour test
class TCompanyCreate:
    @staticmethod
    def _validate_siret_luhn(siret: str) -> bool:
        """Validation de l'algorithme de Luhn pour le SIRET"""
        try:
            total = 0
            for i, digit in enumerate(siret):
                n = int(digit)
                if i % 2 == 1:
                    n *= 2
                    if n > 9:
                        n = n // 10 + n % 10
                total += n
            return total % 10 == 0
        except (ValueError, TypeError):
            return False

class TCompanyResponse(BaseModel):
    """Schéma pour la réponse d'une entreprise tierce - Version test"""
    id: int
    company_name: Optional[str] = None
    activity: Optional[str] = None
    address: Optional[str] = None
    postal_code: Optional[str] = None
    city: Optional[str] = None
    country: Optional[str] = "France"
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    siret: Optional[str] = None
    workspace_id: int
    created_at: datetime
    updated_at: datetime
    created_by: Optional[int] = None
    is_active: Optional[bool] = True
    
    # Champs calculés
    full_address: Optional[str] = None
    is_siret_valid: Optional[bool] = None
    
    class Config:
        from_attributes = True
    
    @validator('full_address', always=True)
    def set_full_address(cls, v, values):
        """Calcule l'adresse complète"""
        if v is not None:
            return v
        
        parts = []
        if values.get('address'):
            parts.append(values['address'])
        if values.get('postal_code') and values.get('city'):
            parts.append(f"{values['postal_code']} {values['city']}")
        elif values.get('city'):
            parts.append(values['city'])
        if values.get('country') and values.get('country') != "France":
            parts.append(values['country'])
        
        return ", ".join(parts) if parts else None
    
    @validator('is_siret_valid', always=True, pre=True)
    def set_siret_valid(cls, v, values):
        """Vérifie la validité du SIRET"""
        # Si c'est une méthode (venant du modèle SQLAlchemy), on l'appelle
        if callable(v):
            return v()
        
        # Si c'est déjà une valeur, on la retourne
        if v is not None:
            return v
        
        # Sinon, on calcule la validité à partir du SIRET
        siret = values.get('siret')
        if not siret:
            return None
        
        return len(siret) == 14 and siret.isdigit() and TCompanyCreate._validate_siret_luhn(siret)

def test_schema_with_callable():
    """Test avec une méthode callable (comme SQLAlchemy)"""
    
    print("🔍 Test du schéma avec méthode callable")
    
    # Simuler une méthode SQLAlchemy
    def mock_is_siret_valid():
        return True
    
    test_data = {
        "id": 1,
        "company_name": "Test Company",
        "workspace_id": 1,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "siret": "12345678901234",
        "is_siret_valid": mock_is_siret_valid  # Méthode callable
    }
    
    try:
        company = TCompanyResponse(**test_data)
        print("✅ Schéma créé avec succès")
        print(f"📊 SIRET valid: {company.is_siret_valid}")
        print(f"📊 Type: {type(company.is_siret_valid)}")
        
        # Test de sérialisation
        data_dict = company.dict()
        print("✅ Sérialisation réussie")
        print(f"📊 SIRET valid dans dict: {data_dict['is_siret_valid']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_schema_with_value():
    """Test avec une valeur directe"""
    
    print("\n🔍 Test du schéma avec valeur directe")
    
    test_data = {
        "id": 2,
        "company_name": "Test Company 2",
        "workspace_id": 2,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "siret": "98765432109876",
        "is_siret_valid": True  # Valeur directe
    }
    
    try:
        company = TCompanyResponse(**test_data)
        print("✅ Schéma créé avec succès")
        print(f"📊 SIRET valid: {company.is_siret_valid}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_schema_without_siret():
    """Test sans SIRET (calcul automatique)"""
    
    print("\n🔍 Test du schéma sans SIRET (calcul automatique)")
    
    test_data = {
        "id": 3,
        "company_name": "Test Company 3",
        "workspace_id": 3,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "siret": "12345678901234"  # SIRET pour calcul automatique
    }
    
    try:
        company = TCompanyResponse(**test_data)
        print("✅ Schéma créé avec succès")
        print(f"📊 SIRET valid (calculé): {company.is_siret_valid}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Test isolé du schéma TCompanyResponse\n")
    
    success1 = test_schema_with_callable()
    success2 = test_schema_with_value()
    success3 = test_schema_without_siret()
    
    if success1 and success2 and success3:
        print("\n✅ Tous les tests réussis - La correction fonctionne !")
        print("🔧 Le problème DetachedInstanceError devrait être résolu")
    else:
        print("\n❌ Certains tests ont échoué")
