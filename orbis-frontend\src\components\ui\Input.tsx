import React from 'react'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  className = '',
  ...props
}) => {
  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-ton2">
          {label}
        </label>
      )}
      <input
        className={`block w-full px-3 py-2 border border-ton3 rounded-md shadow-sm placeholder-ton2-light focus:outline-none focus:ring-ton1 focus:border-ton1 sm:text-sm ${
          error ? 'border-ton2 focus:ring-ton2 focus:border-ton2' : ''
        } ${className}`}
        {...props}
      />
      {error && (
        <p className="text-sm text-ton2">{error}</p>
      )}
    </div>
  )
}