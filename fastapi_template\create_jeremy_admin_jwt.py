#!/usr/bin/env python3
"""
Script pour créer l'utilisateur admin Jeremy pour le système JWT
"""

import asyncio
import asyncpg
from datetime import datetime
from app.core.config import settings

async def create_jeremy_admin():
    """Créer l'utilisateur admin Jeremy dans la base locale"""
    print("👤 Création de l'admin Jeremy pour le système JWT...")
    
    conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)
    
    try:
        admin_email = "<EMAIL>"
        
        # Vérifier si l'utilisateur existe déjà
        existing_user = await conn.fetchval(
            "SELECT id FROM users WHERE email = $1", 
            admin_email
        )
        
        if existing_user:
            print(f"   ⚠️ Utilisateur {admin_email} existe déjà (ID: {existing_user})")
            
            # Mettre à jour pour s'assurer qu'il est super admin
            await conn.execute("""
                UPDATE users 
                SET role = 'SUPER_ADMIN', is_active = true, is_verified = true, updated_at = $1
                WHERE email = $2
            """, datetime.utcnow(), admin_email)
            
            print(f"   ✅ Utilisateur mis à jour en SUPER_ADMIN")
            user_id = existing_user
        else:
            # Créer l'utilisateur admin
            user_id = await conn.fetchval("""
                INSERT INTO users (
                    email, first_name, last_name, role, 
                    is_active, is_verified, is_superuser,
                    created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING id
            """,
            admin_email,
            "Jeremy",
            "Giaime", 
            "SUPER_ADMIN",
            True,
            True,
            True,
            datetime.utcnow(),
            datetime.utcnow()
            )
            
            print(f"   ✅ Utilisateur admin créé (ID: {user_id})")
        
        # Vérifier les données
        user_data = await conn.fetchrow("""
            SELECT id, email, first_name, last_name, role, is_active, is_verified
            FROM users 
            WHERE email = $1
        """, admin_email)
        
        print(f"\n📋 Données utilisateur:")
        print(f"   ID: {user_data['id']}")
        print(f"   Email: {user_data['email']}")
        print(f"   Nom: {user_data['first_name']} {user_data['last_name']}")
        print(f"   Rôle: {user_data['role']}")
        print(f"   Actif: {user_data['is_active']}")
        print(f"   Vérifié: {user_data['is_verified']}")
        
        return user_data
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        await conn.close()

async def test_jwt_creation():
    """Tester la création d'un JWT pour Jeremy"""
    print("\n🔑 Test de création JWT...")
    
    try:
        from app.core.jwt_auth import JWTManager
        
        # Créer un token pour Jeremy
        token = JWTManager.create_user_token(
            user_id=1,  # ID fictif pour le test
            email="<EMAIL>",
            role="SUPER_ADMIN",
            is_superuser=True
        )
        
        print(f"   ✅ JWT créé: {token[:50]}...")
        
        # Vérifier le token
        user_data = JWTManager.extract_user_from_token(token)
        print(f"   ✅ JWT vérifié:")
        print(f"      Email: {user_data['email']}")
        print(f"      Rôle: {user_data['role']}")
        print(f"      Super admin: {user_data['is_superuser']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur JWT: {e}")
        return False

async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Configuration admin Jeremy pour JWT")
    print("="*60)
    
    # Créer l'utilisateur
    user_data = await create_jeremy_admin()
    
    if user_data:
        # Tester le JWT
        jwt_ok = await test_jwt_creation()
        
        print(f"\n📋 Résumé:")
        print(f"   Utilisateur admin: {'✅' if user_data else '❌'}")
        print(f"   Système JWT: {'✅' if jwt_ok else '❌'}")
        
        if user_data and jwt_ok:
            print(f"\n🎯 Prochaines étapes:")
            print(f"   1. Démarrer le backend: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000")
            print(f"   2. Tester la connexion avec:")
            print(f"      Email: <EMAIL>")
            print(f"      Mot de passe: [votre mot de passe Supabase]")
            print(f"   3. L'authentification sera hybride:")
            print(f"      - Supabase pour vérifier le mot de passe")
            print(f"      - JWT pour les API rapides")
            
            print(f"\n💡 Avantages:")
            print(f"   ✅ Authentification sécurisée via Supabase")
            print(f"   ✅ API ultra-rapides via JWT")
            print(f"   ✅ Pas de latence réseau pour chaque requête")
            
            return True
    
    print(f"\n❌ Échec de la configuration")
    return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
