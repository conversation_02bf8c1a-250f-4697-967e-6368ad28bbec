# app/schemas/financial.py
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from decimal import Decimal
from app.models.financial import BudgetStatus, InvoiceStatus

class BudgetBase(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    total_amount: Optional[Decimal] = None
    status: Optional[BudgetStatus] = BudgetStatus.DRAFT
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

class BudgetCreate(BudgetBase):
    name: str
    total_amount: Decimal
    workspace_id: int
    project_id: int

class BudgetUpdate(BudgetBase):
    pass

class BudgetInDBBase(BudgetBase):
    id: Optional[int] = None
    workspace_id: Optional[int] = None
    project_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Budget(BudgetInDBBase):
    pass

class BudgetLine(BaseModel):
    id: Optional[int] = None
    budget_id: int
    description: str
    category: Optional[str] = None
    quantity: Optional[Decimal] = None
    unit_price: Optional[Decimal] = None
    total_amount: Optional[Decimal] = None

    class Config:
        from_attributes = True

class InvoiceBase(BaseModel):
    invoice_number: Optional[str] = None
    invoice_date: Optional[datetime] = None
    due_date: Optional[datetime] = None
    total_amount_ht: Optional[Decimal] = None
    vat_amount: Optional[Decimal] = None
    total_amount_ttc: Optional[Decimal] = None
    status: Optional[InvoiceStatus] = InvoiceStatus.DRAFT
    description: Optional[str] = None
    file_path: Optional[str] = None

class InvoiceCreate(InvoiceBase):
    invoice_number: str
    invoice_date: datetime
    workspace_id: int

class Invoice(InvoiceBase):
    id: Optional[int] = None
    workspace_id: Optional[int] = None
    supplier_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Payment(BaseModel):
    id: Optional[int] = None
    invoice_id: int
    amount: Decimal
    payment_date: datetime
    payment_method: Optional[str] = None
    reference: Optional[str] = None
    notes: Optional[str] = None

    class Config:
        from_attributes = True

class FinancialReport(BaseModel):
    id: Optional[int] = None
    workspace_id: int
    name: str
    report_type: str
    period_start: Optional[datetime] = None
    period_end: Optional[datetime] = None
    data: Optional[str] = None
    file_path: Optional[str] = None

    class Config:
        from_attributes = True