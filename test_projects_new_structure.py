#!/usr/bin/env python3
"""
Test du CRUD des projets avec la nouvelle structure project_company
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_projects_crud():
    """Test complet du CRUD des projets avec la nouvelle structure"""
    print("🧪 Test du CRUD des projets avec project_company\n")
    
    # 1. Connexion pour obtenir le token JWT
    print("🔐 1. Connexion...")
    login_response = requests.post(f"{API_BASE}/api/v1/fast-auth/login", json={
        "email": "<EMAIL>",
        "password": "orbis123!"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Erreur de connexion: {login_response.text}")
        return False
    
    login_data = login_response.json()
    token = login_data['access_token']
    print(f"✅ Connexion réussie")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. Test de lecture de tous les projets
    print("\n📋 2. Lecture de tous les projets...")
    list_response = requests.get(f"{API_BASE}/api/v1/projects/", headers=headers)
    
    if list_response.status_code != 200:
        print(f"❌ Erreur lecture: {list_response.text}")
        return False
    
    projects = list_response.json()
    print(f"✅ {len(projects)} projet(s) trouvé(s)")
    for project in projects:
        print(f"   • {project['name']} ({project['code']})")
    
    # 3. Test de création d'un projet
    print("\n📝 3. Création d'un projet...")
    project_data = {
        "name": "Test Projet Nouvelle Structure",
        "description": "Projet de test pour la nouvelle structure project_company",
        "nature": "Devis",
        "status": "En cours",
        "client_name": "Client Test Structure",
        "client_contact": "<EMAIL>",
        "address": "456 Rue de la Structure, 75002 Paris",
        "budget_total": 75000.00,
        "start_date": "2025-02-01T00:00:00",
        "end_date": "2025-07-01T00:00:00"
    }
    
    create_response = requests.post(f"{API_BASE}/api/v1/projects/", 
                                  headers=headers, 
                                  json=project_data)
    
    if create_response.status_code != 200:
        print(f"❌ Erreur création: {create_response.text}")
        return False
    
    created_project = create_response.json()
    project_id = created_project['id']
    print(f"✅ Projet créé avec ID: {project_id}")
    print(f"   Code: {created_project['code']}")
    print(f"   Nature: {created_project['nature']}")
    
    # 4. Test de lecture d'un projet spécifique
    print(f"\n🔍 4. Lecture du projet {project_id}...")
    get_response = requests.get(f"{API_BASE}/api/v1/projects/{project_id}", headers=headers)
    
    if get_response.status_code != 200:
        print(f"❌ Erreur lecture projet: {get_response.text}")
        return False
    
    project = get_response.json()
    print(f"✅ Projet récupéré: {project['name']}")
    
    # 5. Test de mise à jour
    print(f"\n✏️ 5. Mise à jour du projet {project_id}...")
    update_data = {
        "name": "Test Projet Nouvelle Structure - Modifié",
        "nature": "Affaire",
        "status": "En attente",
        "budget_total": 90000.00
    }
    
    update_response = requests.put(f"{API_BASE}/api/v1/projects/{project_id}", 
                                 headers=headers, 
                                 json=update_data)
    
    if update_response.status_code != 200:
        print(f"❌ Erreur mise à jour: {update_response.text}")
        return False
    
    updated_project = update_response.json()
    print(f"✅ Projet mis à jour:")
    print(f"   Nom: {updated_project['name']}")
    print(f"   Nature: {updated_project['nature']}")
    print(f"   Budget: {updated_project['budget_total']}")
    
    # 6. Test des filtres
    print("\n🔍 6. Test des filtres...")
    
    # Filtre par nature
    filter_response = requests.get(f"{API_BASE}/api/v1/projects/?nature=Affaire", headers=headers)
    if filter_response.status_code == 200:
        filtered_projects = filter_response.json()
        print(f"✅ Filtrage par nature 'Affaire': {len(filtered_projects)} projet(s)")
    
    # Recherche textuelle
    search_response = requests.get(f"{API_BASE}/api/v1/projects/?search=Structure", headers=headers)
    if search_response.status_code == 200:
        search_projects = search_response.json()
        print(f"✅ Recherche 'Structure': {len(search_projects)} projet(s)")
    
    # 7. Test des statistiques
    print("\n📊 7. Test des statistiques...")
    stats_response = requests.get(f"{API_BASE}/api/v1/projects/stats/summary", headers=headers)
    
    if stats_response.status_code != 200:
        print(f"❌ Erreur statistiques: {stats_response.text}")
        return False
    
    stats = stats_response.json()
    print(f"✅ Statistiques récupérées:")
    print(f"   Total actif: {stats['total_active']}")
    print(f"   Par nature: {stats['by_nature']}")
    print(f"   Par statut: {stats['by_status']}")
    
    # 8. Test de suppression (archivage)
    print(f"\n🗑️ 8. Suppression (archivage) du projet {project_id}...")
    delete_response = requests.delete(f"{API_BASE}/api/v1/projects/{project_id}", headers=headers)
    
    if delete_response.status_code != 200:
        print(f"❌ Erreur suppression: {delete_response.text}")
        return False
    
    delete_result = delete_response.json()
    print(f"✅ {delete_result['message']}")
    
    # 9. Test de restauration
    print(f"\n🔄 9. Restauration du projet {project_id}...")
    restore_response = requests.post(f"{API_BASE}/api/v1/projects/{project_id}/restore", headers=headers)
    
    if restore_response.status_code != 200:
        print(f"❌ Erreur restauration: {restore_response.text}")
        return False
    
    restore_result = restore_response.json()
    print(f"✅ {restore_result['message']}")
    
    # 10. Nettoyage final
    print(f"\n🧹 10. Nettoyage final...")
    requests.delete(f"{API_BASE}/api/v1/projects/{project_id}", headers=headers)
    print("✅ Nettoyage terminé")
    
    print("\n🎉 Tous les tests CRUD sont passés avec succès!")
    return True

if __name__ == "__main__":
    print("🚀 Test du CRUD des projets avec la nouvelle structure")
    print("=" * 60)
    
    try:
        success = test_projects_crud()
        
        if success:
            print("\n✅ Tous les tests sont passés!")
            print("🎯 La nouvelle structure project_company fonctionne parfaitement!")
        else:
            print("\n❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"\n💥 Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
