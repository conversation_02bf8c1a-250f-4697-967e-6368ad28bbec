# app/services/supabase_service.py
from typing import Optional, Dict, Any
import httpx
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)


class SupabaseService:
    """
    Service pour interagir avec Supabase Auth
    """
    
    def __init__(self):
        self.supabase_url = settings.SUPABASE_URL
        self.service_role_key = settings.SUPABASE_SERVICE_ROLE_KEY
        self.anon_key = settings.SUPABASE_ANON_KEY
        
        if not all([self.supabase_url, self.service_role_key]):
            logger.warning("Supabase configuration incomplete")
    
    async def create_user(
        self, 
        email: str, 
        password: str, 
        user_metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Créer un utilisateur dans Supabase Auth
        """
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    "Authorization": f"Bearer {self.service_role_key}",
                    "Content-Type": "application/json",
                    "apikey": self.service_role_key
                }
                
                payload = {
                    "email": email,
                    "password": password,
                    "email_confirm": True,  # Auto-confirmer l'email
                    "user_metadata": user_metadata or {}
                }
                
                response = await client.post(
                    f"{self.supabase_url}/auth/v1/admin/users",
                    headers=headers,
                    json=payload
                )
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"User created in Supabase: {email}")
                    return data
                else:
                    error_data = response.json() if response.content else {}
                    logger.error(f"Failed to create user in Supabase: {response.status_code} - {error_data}")

                    # Extract the actual error message from Supabase response
                    error_msg = error_data.get('msg', error_data.get('message', 'Unknown error'))
                    error_code = error_data.get('error_code', '')

                    # Create a more descriptive error message
                    if error_code == 'email_exists':
                        raise Exception(f"email_exists: A user with this email address has already been registered")
                    else:
                        raise Exception(f"Supabase error ({error_code}): {error_msg}")
                    
        except Exception as e:
            logger.error(f"Error creating user in Supabase: {e}")
            raise
    
    async def update_user(
        self, 
        user_id: str, 
        email: Optional[str] = None,
        password: Optional[str] = None,
        user_metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Mettre à jour un utilisateur dans Supabase Auth
        """
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    "Authorization": f"Bearer {self.service_role_key}",
                    "Content-Type": "application/json",
                    "apikey": self.service_role_key
                }
                
                payload = {}
                if email:
                    payload["email"] = email
                if password:
                    payload["password"] = password
                if user_metadata:
                    payload["user_metadata"] = user_metadata
                
                response = await client.put(
                    f"{self.supabase_url}/auth/v1/admin/users/{user_id}",
                    headers=headers,
                    json=payload
                )
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"User updated in Supabase: {user_id}")
                    return data
                else:
                    error_data = response.json() if response.content else {}
                    logger.error(f"Failed to update user in Supabase: {response.status_code} - {error_data}")
                    raise Exception(f"Supabase error: {error_data.get('message', 'Unknown error')}")
                    
        except Exception as e:
            logger.error(f"Error updating user in Supabase: {e}")
            raise
    
    async def delete_user(self, user_id: str) -> bool:
        """
        Supprimer un utilisateur dans Supabase Auth
        """
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    "Authorization": f"Bearer {self.service_role_key}",
                    "apikey": self.service_role_key
                }
                
                response = await client.delete(
                    f"{self.supabase_url}/auth/v1/admin/users/{user_id}",
                    headers=headers
                )
                
                if response.status_code == 200:
                    logger.info(f"User deleted from Supabase: {user_id}")
                    return True
                else:
                    error_data = response.json() if response.content else {}
                    logger.error(f"Failed to delete user from Supabase: {response.status_code} - {error_data}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error deleting user from Supabase: {e}")
            return False
    
    async def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Récupérer un utilisateur depuis Supabase Auth
        """
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    "Authorization": f"Bearer {self.service_role_key}",
                    "apikey": self.service_role_key
                }
                
                response = await client.get(
                    f"{self.supabase_url}/auth/v1/admin/users/{user_id}",
                    headers=headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data
                else:
                    logger.error(f"Failed to get user from Supabase: {response.status_code}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting user from Supabase: {e}")
            return None
    
    async def list_users(self, page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """
        Lister les utilisateurs depuis Supabase Auth
        """
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    "Authorization": f"Bearer {self.service_role_key}",
                    "apikey": self.service_role_key
                }
                
                params = {
                    "page": page,
                    "per_page": per_page
                }
                
                response = await client.get(
                    f"{self.supabase_url}/auth/v1/admin/users",
                    headers=headers,
                    params=params
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data
                else:
                    error_data = response.json() if response.content else {}
                    logger.error(f"Failed to list users from Supabase: {response.status_code} - {error_data}")
                    return {"users": [], "total": 0}
                    
        except Exception as e:
            logger.error(f"Error listing users from Supabase: {e}")
            return {"users": [], "total": 0}
    
    async def send_invitation_email(
        self, 
        email: str, 
        invitation_url: str,
        company_name: str
    ) -> bool:
        """
        Envoyer un email d'invitation (placeholder pour l'intégration future)
        """
        try:
            # TODO: Intégrer avec un service d'email (SendGrid, Mailgun, etc.)
            logger.info(f"Invitation email would be sent to {email} for company {company_name}")
            logger.info(f"Invitation URL: {invitation_url}")
            
            # Pour l'instant, on simule l'envoi
            return True
            
        except Exception as e:
            logger.error(f"Error sending invitation email: {e}")
            return False
    
    async def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Vérifier un token JWT Supabase
        """
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    "Authorization": f"Bearer {token}",
                    "apikey": self.anon_key
                }
                
                response = await client.get(
                    f"{self.supabase_url}/auth/v1/user",
                    headers=headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data
                else:
                    return None
                    
        except Exception as e:
            logger.error(f"Error verifying JWT token: {e}")
            return None


# Instance globale du service
supabase_service = SupabaseService()
