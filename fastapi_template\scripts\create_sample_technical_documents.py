#!/usr/bin/env python3
"""
Script pour créer des documents techniques d'exemple
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.core.database import engine

async def create_sample_documents():
    """Créer des documents techniques d'exemple"""
    
    print("🔄 Création de documents techniques d'exemple...")
    
    try:
        async with engine.begin() as conn:
            print("  ✅ Connexion à la base de données établie")
            
            # Vérifier les projets existants
            print("  🔍 Vérification des projets existants...")
            projects_result = await conn.execute(text("SELECT id, name FROM projects ORDER BY id LIMIT 5"))
            projects = projects_result.fetchall()
            
            if not projects:
                print("  ❌ Aucun projet trouvé. Créons un projet d'exemple...")
                # Créer un projet d'exemple
                await conn.execute(text("""
                    INSERT INTO projects (name, code, description, nature, status, created_at)
                    VALUES ('Projet Test CCTP', 'TEST-2024', 'Projet de test pour les documents techniques', 'AFFAIRE', 'EN_COURS', CURRENT_TIMESTAMP)
                """))
                
                # Récupérer l'ID du projet créé
                project_result = await conn.execute(text("SELECT id FROM projects WHERE code = 'TEST-2024'"))
                project_id = project_result.scalar()
                print(f"  ✅ Projet créé avec ID: {project_id}")
            else:
                project_id = projects[0][0]
                project_name = projects[0][1]
                print(f"  ✅ Utilisation du projet existant: {project_name} (ID: {project_id})")
            
            # Vérifier les utilisateurs existants
            print("  🔍 Vérification des utilisateurs existants...")
            users_result = await conn.execute(text("SELECT id, email FROM users ORDER BY id LIMIT 5"))
            users = users_result.fetchall()
            
            if not users:
                print("  ❌ Aucun utilisateur trouvé")
                return
            
            user_id = users[0][0]
            user_email = users[0][1]
            print(f"  ✅ Utilisation de l'utilisateur: {user_email} (ID: {user_id})")
            
            # Vérifier les entreprises existantes
            print("  🔍 Vérification des entreprises existantes...")
            companies_result = await conn.execute(text("SELECT id, name FROM companies ORDER BY id LIMIT 5"))
            companies = companies_result.fetchall()
            
            if not companies:
                print("  ❌ Aucune entreprise trouvée")
                return
            
            company_id = companies[0][0]
            company_name = companies[0][1]
            print(f"  ✅ Utilisation de l'entreprise: {company_name} (ID: {company_id})")
            
            # Créer des documents techniques d'exemple
            documents_data = [
                {
                    'name': 'CCTP - Gros Œuvre',
                    'type': 'CCTP',
                    'content': '''# CCTP - Gros Œuvre

## 1. OBJET DU MARCHÉ

Le présent Cahier des Clauses Techniques Particulières (CCTP) a pour objet de définir les conditions techniques d'exécution des travaux de gros œuvre.

## 2. DESCRIPTION DES TRAVAUX

### 2.1 Terrassements
- Décapage de la terre végétale sur 30 cm d'épaisseur
- Fouilles en rigole pour fondations
- Remblaiement et compactage

### 2.2 Fondations
- Béton de propreté dosé à 150 kg/m³
- Semelles filantes en béton armé B25
- Chaînages horizontaux et verticaux

### 2.3 Élévation des murs
- Murs en blocs béton 20 cm d'épaisseur
- Chaînages à tous les niveaux
- Linteaux préfabriqués

## 3. MATÉRIAUX

### 3.1 Béton
- Béton prêt à l'emploi certifié NF
- Résistance caractéristique : 25 MPa
- Consistance S3

### 3.2 Aciers
- Aciers haute adhérence FeE500
- Conformes à la norme NF A 35-080

## 4. MISE EN ŒUVRE

Tous les travaux devront être réalisés selon les règles de l'art et les DTU en vigueur.
'''
                },
                {
                    'name': 'CCTP - Second Œuvre',
                    'type': 'CCTP',
                    'content': '''# CCTP - Second Œuvre

## 1. OBJET DU MARCHÉ

Le présent CCTP définit les conditions techniques d'exécution des travaux de second œuvre.

## 2. CLOISONS ET DOUBLAGES

### 2.1 Cloisons de distribution
- Cloisons en carreaux de plâtre 7 cm
- Cloisons en plaques de plâtre sur ossature métallique

### 2.2 Doublages
- Doublage thermique par l'intérieur
- Isolant laine de verre 100 mm
- Parement plaque de plâtre BA13

## 3. REVÊTEMENTS DE SOL

### 3.1 Carrelage
- Carrelage grès cérame 30x30 cm
- Pose collée sur chape
- Joints de 2 mm

### 3.2 Parquet
- Parquet contrecollé chêne 14 mm
- Pose flottante sur sous-couche

## 4. PEINTURES

### 4.1 Préparation des supports
- Rebouchage et ponçage
- Application d'une sous-couche

### 4.2 Finition
- Peinture acrylique satinée
- Application en 2 couches
'''
                },
                {
                    'name': 'DPGF - Estimation Générale',
                    'type': 'DPGF',
                    'content': '''# DPGF - Décomposition du Prix Global et Forfaitaire

## 1. GROS ŒUVRE

| Désignation | Unité | Quantité | Prix Unitaire | Prix Total |
|-------------|-------|----------|---------------|------------|
| Terrassement général | m³ | 150 | 25,00 € | 3 750,00 € |
| Béton de propreté | m³ | 8 | 120,00 € | 960,00 € |
| Fondations béton armé | m³ | 25 | 180,00 € | 4 500,00 € |
| Murs en blocs béton | m² | 200 | 45,00 € | 9 000,00 € |
| Dalle béton armé | m² | 120 | 65,00 € | 7 800,00 € |

**SOUS-TOTAL GROS ŒUVRE : 26 010,00 €**

## 2. SECOND ŒUVRE

| Désignation | Unité | Quantité | Prix Unitaire | Prix Total |
|-------------|-------|----------|---------------|------------|
| Cloisons placo | m² | 80 | 35,00 € | 2 800,00 € |
| Doublage isolant | m² | 150 | 28,00 € | 4 200,00 € |
| Carrelage sol | m² | 60 | 45,00 € | 2 700,00 € |
| Parquet | m² | 40 | 55,00 € | 2 200,00 € |
| Peinture | m² | 300 | 12,00 € | 3 600,00 € |

**SOUS-TOTAL SECOND ŒUVRE : 15 500,00 €**

## 3. RÉCAPITULATIF

- Gros œuvre : 26 010,00 €
- Second œuvre : 15 500,00 €
- **TOTAL HT : 41 510,00 €**
- TVA 20% : 8 302,00 €
- **TOTAL TTC : 49 812,00 €**
'''
                }
            ]
            
            print(f"  🔄 Création de {len(documents_data)} documents techniques...")
            
            for i, doc_data in enumerate(documents_data):
                # Insérer le document
                result = await conn.execute(text("""
                    INSERT INTO technical_documents (name, type_document, content, project_id, created_by, created_at, updated_at)
                    VALUES (:name, :type_document, :content, :project_id, :created_by, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    RETURNING id
                """), {
                    'name': doc_data['name'],
                    'type_document': doc_data['type'],
                    'content': doc_data['content'],
                    'project_id': project_id,
                    'created_by': user_id
                })
                
                doc_id = result.scalar()
                
                # Associer le document à l'entreprise
                await conn.execute(text("""
                    INSERT INTO technical_document_companies (technical_document_id, company_id, created_at)
                    VALUES (:doc_id, :company_id, CURRENT_TIMESTAMP)
                """), {
                    'doc_id': doc_id,
                    'company_id': company_id
                })
                
                print(f"    ✅ Document créé: {doc_data['name']} (ID: {doc_id})")
            
            print("✅ Documents techniques créés avec succès!")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création des documents: {e}")
        raise

async def verify_documents():
    """Vérifier que les documents ont été créés"""
    
    print("\n🔍 Vérification des documents créés...")
    
    try:
        async with engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT td.id, td.name, td.type_document, p.name as project_name, u.email as creator_email
                FROM technical_documents td
                JOIN projects p ON td.project_id = p.id
                JOIN users u ON td.created_by = u.id
                ORDER BY td.created_at DESC
                LIMIT 10
            """))
            
            documents = result.fetchall()
            
            print(f"  📋 {len(documents)} documents trouvés:")
            for doc in documents:
                print(f"    - {doc[1]} ({doc[2]}) - Projet: {doc[3]} - Créé par: {doc[4]}")
            
            return len(documents) > 0
            
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

async def main():
    """Fonction principale"""
    print("🚀 Création de documents techniques d'exemple")
    print("=" * 50)
    
    try:
        await create_sample_documents()
        success = await verify_documents()
        
        if success:
            print("\n🎉 Documents créés avec succès!")
            print("\nProchaines étapes:")
            print("1. Aller sur http://localhost:3000/projects")
            print("2. Cliquer sur un projet")
            print("3. Aller dans l'onglet 'Documents'")
            print("4. Tester les documents techniques")
        else:
            print("\n⚠️  Création incomplète")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Échec de la création: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
