#!/usr/bin/env python3
"""
Script minimal de peuplement de la base de données ORBIS
Utilise SQLAlchemy comme dans l'application
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Ajouter le répertoire app au path Python
sys.path.insert(0, str(Path(__file__).parent))

from sqlalchemy import text
from app.core.database import AsyncSessionLocal


async def create_minimal_data():
    """Créer des données minimales"""
    print("🌱 Création de données minimales...")
    
    async with AsyncSessionLocal() as session:
        try:
            # 1. Vérifier/Créer une entreprise
            print("   📊 Vérification/Création d'une entreprise...")

            # Vérifier si l'entreprise existe déjà
            result = await session.execute(text("""
                SELECT id FROM companies WHERE code = :code
            """), {'code': 'EITP001'})

            existing_company = result.fetchone()

            if existing_company:
                company_id = existing_company[0]
                print(f"   ✅ Entreprise existante trouvée avec l'ID: {company_id}")
            else:
                result = await session.execute(text("""
                    INSERT INTO companies (name, code, siret, address, phone, email, created_at, updated_at)
                    VALUES (:name, :code, :siret, :address, :phone, :email, :created_at, :updated_at)
                    RETURNING id
                """), {
                    'name': 'EITP Construction',
                    'code': 'EITP001',
                    'siret': '12345678901234',
                    'address': '123 Rue de la Construction, 75001 Paris',
                    'phone': '***********.89',
                    'email': '<EMAIL>',
                    'created_at': datetime.utcnow(),
                    'updated_at': datetime.utcnow()
                })
                company_id = result.fetchone()[0]
                print(f"   ✅ Nouvelle entreprise créée avec l'ID: {company_id}")
            
            # 2. Créer des utilisateurs
            print("   👥 Création des utilisateurs...")
            users_data = [
                {
                    'email': '<EMAIL>',
                    'first_name': 'Admin',
                    'last_name': 'EITP',
                    'hashed_password': 'hashed_admin123',
                    'role': 'ADMIN'
                },
                {
                    'email': '<EMAIL>',
                    'first_name': 'Chef',
                    'last_name': 'Projet',
                    'hashed_password': 'hashed_chef123',
                    'role': 'CHEF_PROJET'
                }
            ]
            
            user_ids = []
            for user_data in users_data:
                user_data.update({
                    'is_active': True,
                    'created_at': datetime.utcnow(),
                    'updated_at': datetime.utcnow()
                })
                
                result = await session.execute(text("""
                    INSERT INTO users (email, first_name, last_name, hashed_password, role, is_active, created_at, updated_at)
                    VALUES (:email, :first_name, :last_name, :hashed_password, :role, :is_active, :created_at, :updated_at)
                    RETURNING id
                """), user_data)
                
                user_id = result.fetchone()[0]
                user_ids.append(user_id)
                print(f"   ✅ Utilisateur créé: {user_data['first_name']} {user_data['last_name']}")
            
            # 3. Associer les utilisateurs à l'entreprise
            print("   🔗 Association des utilisateurs à l'entreprise...")
            for user_id in user_ids:
                await session.execute(text("""
                    INSERT INTO user_companies (user_id, company_id, is_default, created_at)
                    VALUES (:user_id, :company_id, :is_default, :created_at)
                """), {
                    'user_id': user_id,
                    'company_id': company_id,
                    'is_default': True,
                    'created_at': datetime.utcnow()
                })
            
            # 4. Créer un projet
            print("   🏗️ Création d'un projet...")
            result = await session.execute(text("""
                INSERT INTO projects (name, code, description, status, budget_total, company_id, start_date, created_at, updated_at)
                VALUES (:name, :code, :description, :status, :budget_total, :company_id, :start_date, :created_at, :updated_at)
                RETURNING id
            """), {
                'name': 'Rénovation Immeuble Test',
                'code': 'PROJ001',
                'description': 'Projet de test pour la démonstration',
                'status': 'dao',
                'budget_total': 500000.00,
                'company_id': company_id,
                'start_date': datetime.utcnow() - timedelta(days=30),
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
            project_id = result.fetchone()[0]
            print(f"   ✅ Projet créé avec l'ID: {project_id}")
            
            # 5. Créer un employé
            print("   👷 Création d'un employé...")
            result = await session.execute(text("""
                INSERT INTO employees (employee_number, first_name, last_name, position, hourly_rate, company_id, created_at, updated_at)
                VALUES (:employee_number, :first_name, :last_name, :position, :hourly_rate, :company_id, :created_at, :updated_at)
                RETURNING id
            """), {
                'employee_number': 'EMP001',
                'first_name': 'Jean',
                'last_name': 'Dupont',
                'position': 'Maçon',
                'hourly_rate': 25.50,
                'company_id': company_id,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
            employee_id = result.fetchone()[0]
            print(f"   ✅ Employé créé avec l'ID: {employee_id}")
            
            # Commit des changements
            await session.commit()
            
            print("\n🎉 Données minimales créées avec succès!")
            print("\n📋 Résumé:")
            print(f"   - 1 entreprise: EITP Construction")
            print(f"   - {len(users_data)} utilisateurs")
            print(f"   - 1 projet")
            print(f"   - 1 employé")
            
            print("\n🔑 Identifiants de test:")
            print("   - <EMAIL> / admin123 (ADMIN)")
            print("   - <EMAIL> / chef123 (CHEF_PROJET)")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la création des données: {e}")
            await session.rollback()
            return False


async def check_data():
    """Vérifier les données créées"""
    print("\n🔍 Vérification des données créées...")
    
    async with AsyncSessionLocal() as session:
        try:
            # Compter les enregistrements
            tables = ['companies', 'users', 'projects', 'employees']
            
            for table in tables:
                result = await session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.fetchone()[0]
                print(f"   📊 {table}: {count} enregistrements")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la vérification: {e}")
            return False


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Peuplement Minimal de la Base de Données")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # Vérifier les données existantes
    await check_data()
    
    # Créer les données minimales
    success = await create_minimal_data()
    
    if success:
        # Vérification finale
        await check_data()
        print("\n✅ Peuplement terminé avec succès!")
    else:
        print("\n❌ Échec du peuplement")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
