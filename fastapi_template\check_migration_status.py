#!/usr/bin/env python3
"""
Script pour vérifier l'état des migrations et des tables
"""
import asyncio
from app.core.database import engine
from sqlalchemy import text

async def check_migration_status():
    """Vérifier l'état des migrations et des tables"""
    
    async with engine.begin() as conn:
        try:
            # 1. Vérifier la version Alembic actuelle
            print("🔍 Vérification de la version Alembic...")
            result = await conn.execute(text("SELECT version_num FROM alembic_version"))
            version = result.fetchone()
            if version:
                print(f"✅ Version Alembic actuelle: {version[0]}")
            else:
                print("❌ Aucune version Alembic trouvée")
            
            # 2. Vérifier l'existence des tables
            print("\n🔍 Vérification des tables...")
            
            # Vérifier entreprises_tiers
            result = await conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'entreprises_tiers'
                )
            """))
            entreprises_exists = result.fetchone()[0]
            print(f"{'✅' if entreprises_exists else '❌'} Table entreprises_tiers: {'existe' if entreprises_exists else 'n\'existe pas'}")
            
            # Vérifier tcompanies
            result = await conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'tcompanies'
                )
            """))
            tcompanies_exists = result.fetchone()[0]
            print(f"{'✅' if tcompanies_exists else '❌'} Table tcompanies: {'existe' if tcompanies_exists else 'n\'existe pas'}")
            
            # 3. Compter les enregistrements
            if entreprises_exists:
                result = await conn.execute(text("SELECT COUNT(*) FROM entreprises_tiers"))
                count_entreprises = result.fetchone()[0]
                print(f"📊 Nombre d'enregistrements dans entreprises_tiers: {count_entreprises}")
            
            if tcompanies_exists:
                result = await conn.execute(text("SELECT COUNT(*) FROM tcompanies"))
                count_tcompanies = result.fetchone()[0]
                print(f"📊 Nombre d'enregistrements dans tcompanies: {count_tcompanies}")
            
            # 4. Vérifier les foreign keys dans employees
            print("\n🔍 Vérification des colonnes employees...")
            result = await conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'employees' 
                AND column_name IN ('entreprise_tiers_id', 'tcompany_id')
            """))
            employee_columns = [row[0] for row in result.fetchall()]
            print(f"📋 Colonnes dans employees: {employee_columns}")
            
        except Exception as e:
            print(f"❌ Erreur lors de la vérification: {e}")

if __name__ == "__main__":
    asyncio.run(check_migration_status())
