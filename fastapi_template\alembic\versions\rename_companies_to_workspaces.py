# alembic/versions/rename_companies_to_workspaces.py
"""Rename companies to workspaces

Revision ID: rename_companies_to_workspaces
Revises: add_logo_to_entreprise_tiers
Create Date: 2025-01-07 10:00:00.000000

This migration renames all company-related tables to workspace-related tables
to better reflect the SaaS nature of the application.

IMPORTANT: This migration does NOT touch:
- technical_document_companies (belongs to entreprises_tiers)
- project_company (belongs to entreprises_tiers)

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'rename_companies_to_workspaces'
down_revision: Union[str, None] = 'add_logo_to_entreprise_tiers'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Rename companies tables to workspaces tables"""

    print("🔄 Starting companies → workspaces migration...")

    # 1. Rename main companies table to workspaces
    print("   📋 Renaming companies → workspaces")
    op.rename_table('companies', 'workspaces')

    # 2. Rename company_settings to workspace_settings
    print("   ⚙️ Renaming company_settings → workspace_settings")
    op.rename_table('company_settings', 'workspace_settings')

    # 3. Rename user_companies to user_workspaces
    print("   👥 Renaming user_companies → user_workspaces")
    op.rename_table('user_companies', 'user_workspaces')

    # 4. Rename company_role_permissions to workspace_role_permissions
    print("   🔐 Renaming company_role_permissions → workspace_role_permissions")
    try:
        op.rename_table('company_role_permissions', 'workspace_role_permissions')
    except Exception as e:
        print(f"   ⚠️ Warning: company_role_permissions table not found: {e}")

    # 5. Rename company_invitations to workspace_invitations
    print("   📧 Renaming company_invitations → workspace_invitations")
    try:
        op.rename_table('company_invitations', 'workspace_invitations')
    except Exception as e:
        print(f"   ⚠️ Warning: company_invitations table not found: {e}")

    # 6. Update column names from company_id to workspace_id in workspace-related tables
    print("   🔗 Updating column names company_id → workspace_id")

    # Tables that belong to workspaces (SaaS tenants)
    workspace_tables = [
        'workspace_settings',
        'user_workspaces',
        'workspace_role_permissions',
        'workspace_invitations',
        'audit_logs',
        'budgets',
        'documents',
        'employees',
        'financial_reports',
        'invoices',
        'materials',
        'purchase_orders',
        'quote_templates',
        'quotes',
        'suppliers'
    ]

    for table_name in workspace_tables:
        try:
            print(f"     - Updating {table_name}.company_id → workspace_id")
            op.alter_column(table_name, 'company_id', new_column_name='workspace_id')
        except Exception as e:
            print(f"   ⚠️ Warning: Could not update {table_name}.company_id: {e}")

    # IMPORTANT: Do NOT update these tables (they belong to entreprises_tiers):
    # - technical_document_companies.company_id (stays company_id)
    # - project_company.company_id (stays company_id)
    # - entreprises_tiers.company_id (this references workspaces, should be updated)

    # Special case: entreprises_tiers.company_id should become workspace_id
    try:
        print("     - Updating entreprises_tiers.company_id → workspace_id")
        op.alter_column('entreprises_tiers', 'company_id', new_column_name='workspace_id')
    except Exception as e:
        print(f"   ⚠️ Warning: Could not update entreprises_tiers.company_id: {e}")
    
    # 7. Update foreign key constraints
    print("   🔗 Updating foreign key constraints")

    # List of tables with FK constraints to update
    fk_tables = [
        ('workspace_settings', 'workspace_settings_workspace_id_fkey', 'company_settings_company_id_fkey'),
        ('user_workspaces', 'user_workspaces_workspace_id_fkey', 'user_companies_company_id_fkey'),
        ('workspace_role_permissions', 'workspace_role_permissions_workspace_id_fkey', 'company_role_permissions_company_id_fkey'),
        ('workspace_invitations', 'workspace_invitations_workspace_id_fkey', 'company_invitations_company_id_fkey'),
        ('audit_logs', 'audit_logs_workspace_id_fkey', 'audit_logs_company_id_fkey'),
        ('budgets', 'budgets_workspace_id_fkey', 'budgets_company_id_fkey'),
        ('documents', 'documents_workspace_id_fkey', 'documents_company_id_fkey'),
        ('employees', 'employees_workspace_id_fkey', 'employees_company_id_fkey'),
        ('financial_reports', 'financial_reports_workspace_id_fkey', 'financial_reports_company_id_fkey'),
        ('invoices', 'invoices_workspace_id_fkey', 'invoices_company_id_fkey'),
        ('materials', 'materials_workspace_id_fkey', 'materials_company_id_fkey'),
        ('purchase_orders', 'purchase_orders_workspace_id_fkey', 'purchase_orders_company_id_fkey'),
        ('quote_templates', 'quote_templates_workspace_id_fkey', 'quote_templates_company_id_fkey'),
        ('quotes', 'quotes_workspace_id_fkey', 'quotes_company_id_fkey'),
        ('suppliers', 'suppliers_workspace_id_fkey', 'suppliers_company_id_fkey'),
        ('entreprises_tiers', 'entreprises_tiers_workspace_id_fkey', 'entreprises_tiers_company_id_fkey')
    ]

    for table_name, new_fk_name, old_fk_name in fk_tables:
        try:
            # Drop old constraint
            try:
                op.drop_constraint(old_fk_name, table_name, type_='foreignkey')
            except Exception:
                print(f"     - Old FK {old_fk_name} not found in {table_name}")

            # Create new constraint
            op.create_foreign_key(
                new_fk_name,
                table_name,
                'workspaces',
                ['workspace_id'],
                ['id'],
                ondelete='CASCADE'
            )
            print(f"     ✅ Updated FK constraint for {table_name}")

        except Exception as e:
            print(f"   ⚠️ Warning: Could not update FK for {table_name}: {e}")
    
    # 8. Update indexes
    print("   📊 Updating indexes")

    # Most indexes should be automatically renamed with the tables
    # But we need to update some specific ones that reference column names

    index_updates = [
        ('user_workspaces', 'ix_user_companies_user_id', 'ix_user_workspaces_user_id', ['user_id']),
        ('user_workspaces', 'ix_user_companies_company_id', 'ix_user_workspaces_workspace_id', ['workspace_id']),
        ('workspace_settings', 'ix_company_settings_id', 'ix_workspace_settings_id', ['id'])
    ]

    for table_name, old_index, new_index, columns in index_updates:
        try:
            op.drop_index(old_index, table_name=table_name)
            op.create_index(new_index, table_name, columns)
            print(f"     ✅ Updated index {old_index} → {new_index}")
        except Exception as e:
            print(f"   ⚠️ Warning: Could not update index {old_index}: {e}")

    print("\n✅ Companies → Workspaces migration completed!")
    print("📝 Note: technical_document_companies and project_company tables were preserved")
    print("📝 These tables still reference 'companies' and belong to entreprises_tiers")


def downgrade() -> None:
    """Revert workspaces tables back to companies tables"""
    
    print("🔄 Starting workspaces → companies rollback...")
    
    # Reverse all the operations
    
    # 1. Update indexes back
    print("   📊 Reverting indexes")
    try:
        op.drop_index('ix_user_workspaces_workspace_id', table_name='user_workspaces')
        op.create_index('ix_user_companies_company_id', 'user_workspaces', ['workspace_id'])
    except Exception as e:
        print(f"   ⚠️ Warning: Could not revert workspace_id indexes: {e}")
    
    try:
        op.drop_index('ix_user_workspaces_user_id', table_name='user_workspaces')
        op.create_index('ix_user_companies_user_id', 'user_workspaces', ['user_id'])
    except Exception as e:
        print(f"   ⚠️ Warning: Could not revert user_workspaces indexes: {e}")
    
    # 2. Drop new foreign key constraints
    print("   🔗 Reverting foreign key constraints")
    
    try:
        op.drop_constraint('workspace_role_permissions_workspace_id_fkey', 'workspace_role_permissions', type_='foreignkey')
    except Exception as e:
        print(f"   ⚠️ Warning: Could not drop workspace_role_permissions FK: {e}")
    
    op.drop_constraint('user_workspaces_workspace_id_fkey', 'user_workspaces', type_='foreignkey')
    op.drop_constraint('workspace_settings_workspace_id_fkey', 'workspace_settings', type_='foreignkey')
    
    # 3. Revert column names workspace_id → company_id
    print("   🔗 Reverting column names workspace_id → company_id")
    
    try:
        op.alter_column('workspace_role_permissions', 'workspace_id', new_column_name='company_id')
    except Exception as e:
        print(f"   ⚠️ Warning: workspace_role_permissions.workspace_id not found: {e}")
    
    op.alter_column('user_workspaces', 'workspace_id', new_column_name='company_id')
    op.alter_column('workspace_settings', 'workspace_id', new_column_name='company_id')
    
    # 4. Rename tables back
    print("   📋 Reverting table names")
    
    try:
        op.rename_table('workspace_role_permissions', 'company_role_permissions')
    except Exception as e:
        print(f"   ⚠️ Warning: workspace_role_permissions table not found: {e}")
    
    op.rename_table('user_workspaces', 'user_companies')
    op.rename_table('workspace_settings', 'company_settings')
    op.rename_table('workspaces', 'companies')
    
    # 5. Recreate original foreign key constraints
    print("   🔗 Recreating original foreign key constraints")
    
    op.create_foreign_key(
        'company_settings_company_id_fkey', 
        'company_settings', 
        'companies', 
        ['company_id'], 
        ['id'],
        ondelete='CASCADE'
    )
    
    op.create_foreign_key(
        'user_companies_company_id_fkey', 
        'user_companies', 
        'companies', 
        ['company_id'], 
        ['id'],
        ondelete='CASCADE'
    )
    
    try:
        op.create_foreign_key(
            'company_role_permissions_company_id_fkey', 
            'company_role_permissions', 
            'companies', 
            ['company_id'], 
            ['id'],
            ondelete='CASCADE'
        )
    except Exception as e:
        print(f"   ⚠️ Warning: Could not recreate company_role_permissions FK: {e}")
    
    print("✅ Workspaces → Companies rollback completed!")
