import { useState } from 'react'
import { FastAuthService } from '@/lib/auth'

interface UploadResponse {
  message: string
  photo_filename: string
  photo_url: string
}

interface DeleteResponse {
  message: string
}

export function useLotPhoto() {
  const [uploading, setUploading] = useState(false)
  const [deleting, setDeleting] = useState(false)

  const uploadPhoto = async (lotId: number, file: File): Promise<UploadResponse> => {
    setUploading(true)

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = FastAuthService.getToken()

      if (!token) {
        throw new Error('Token d\'authentification manquant')
      }

      const formData = new FormData()
      formData.append('file', file)

      const url = `${API_BASE_URL}/api/v1/lots/${lotId}/upload-photo`
      console.log('📤 Upload URL:', url)

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      console.log('📡 Upload réponse:', { status: response.status, statusText: response.statusText })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `Erreur HTTP: ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ Upload réussi:', result)

      return result
    } catch (error) {
      console.error('❌ Erreur upload photo lot:', error)
      throw error
    } finally {
      setUploading(false)
    }
  }

  const deletePhoto = async (lotId: number): Promise<DeleteResponse> => {
    setDeleting(true)

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = FastAuthService.getToken()

      if (!token) {
        throw new Error('Token d\'authentification manquant')
      }

      const url = `${API_BASE_URL}/api/v1/lots/${lotId}/photo`
      console.log('🗑️ Delete URL:', url)

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      console.log('📡 Delete réponse:', { status: response.status, statusText: response.statusText })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `Erreur HTTP: ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ Suppression réussie:', result)

      return result
    } catch (error) {
      console.error('❌ Erreur suppression photo lot:', error)
      throw error
    } finally {
      setDeleting(false)
    }
  }

  return {
    uploadPhoto,
    deletePhoto,
    uploading,
    deleting
  }
}
