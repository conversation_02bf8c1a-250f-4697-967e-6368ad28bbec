#!/usr/bin/env python3
"""
Script pour tester la création d'utilisateur avec un token JWT valide
"""

import requests
import json
import jwt
from datetime import datetime, timedelta

def create_test_token():
    """Créer un token JWT pour le super admin"""
    # Utiliser la même clé secrète que dans l'application
    SECRET_KEY = "orbis-jwt-secret-key-change-in-production"
    ALGORITHM = "HS256"

    user_data = {
        "user_id": 1,  # ID du super admin créé
        "email": "<EMAIL>",
        "role": "SUPER_ADMIN",
        "is_superuser": True,
        "token_type": "access"
    }

    to_encode = user_data.copy()
    expire = datetime.utcnow() + timedelta(hours=24)
    to_encode.update({"exp": expire, "iat": datetime.utcnow()})

    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def test_companies_list(token):
    """Tester la liste des entreprises"""
    print("🔍 Test de la liste des entreprises...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get("http://localhost:8000/api/v1/admin/companies", headers=headers)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        companies = response.json()
        print(f"✅ {len(companies)} entreprises trouvées")
        for company in companies:
            print(f"  - {company['name']} (ID: {company['id']})")
        return companies
    else:
        print(f"❌ Erreur: {response.text}")
        return []

def test_user_creation(token, company_id):
    """Tester la création d'un utilisateur"""
    print(f"\n👤 Test de création d'utilisateur pour l'entreprise {company_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    user_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "User",
        "role": "admin",
        "company_id": company_id
    }
    
    response = requests.post(
        "http://localhost:8000/api/v1/admin/users",
        headers=headers,
        json=user_data
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 201:
        user = response.json()
        print(f"✅ Utilisateur créé avec succès!")
        print(f"  - ID: {user['id']}")
        print(f"  - Email: {user['email']}")
        print(f"  - Nom: {user['first_name']} {user['last_name']}")
        print(f"  - Rôle: {user['role']}")
        return user
    else:
        print(f"❌ Erreur: {response.text}")
        return None

if __name__ == "__main__":
    print("🚀 Test de création d'utilisateur\n")
    
    # 1. Créer un token JWT
    print("🔑 Création du token JWT...")
    token = create_test_token()
    print(f"✅ Token créé: {token[:50]}...\n")
    
    # 2. Tester la liste des entreprises
    companies = test_companies_list(token)
    
    if companies:
        # 3. Tester la création d'utilisateur avec la première entreprise
        company_id = companies[0]['id']
        user = test_user_creation(token, company_id)
        
        if user:
            print("\n🎉 Test réussi! L'utilisateur a été créé avec succès.")
        else:
            print("\n❌ Test échoué lors de la création d'utilisateur.")
    else:
        print("\n❌ Impossible de tester la création d'utilisateur sans entreprises.")
    
    print("\n✅ Tests terminés")
