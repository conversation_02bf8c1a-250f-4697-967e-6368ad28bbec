#!/usr/bin/env python3
"""
Script de test pour la génération d'articles CCTP
"""

import asyncio
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.chatgpt_service import chatgpt_service

async def test_article_generation():
    """Test de la génération d'article CCTP"""
    
    print("🧪 Test de génération d'article CCTP")
    print("=" * 50)
    
    # Vérifier la configuration du service
    config_status = chatgpt_service.get_configuration_status()
    print(f"📋 Statut de configuration:")
    for key, value in config_status.items():
        print(f"   {key}: {value}")
    
    if not chatgpt_service.is_configured():
        print("❌ Service ChatGPT non configuré - Veuillez définir OPENAI_API_KEY")
        return False
    
    # Données de test pour un article CCTP
    article_data = {
        "prestation": "Pose de carrelage grès cérame",
        "localisation": "Cuisine et salle de bain",
        "marque": "Porcelanosa",
        "reference": "PAR-KER CHELSEA COGNAC",
        "nature": "Grès cérame émaillé",
        "criteresQualite": "Classement UPEC U4 P3 E3 C2",
        "dimensions": "20x120 cm",
        "couleur": "Cognac",
        "particularite": "Pose en chevron",
        "descriptionPose": "Pose collée sur chape béton",
        "typePose": "Pose droite avec joints de 2mm",
        "marquePose": "Weber",
        "referencePose": "Weber.col flex",
        "inclureCriteres": True,
        "inclureDocs": True,
        "unite": "m²",
        "quantite": "45.5"
    }
    
    print(f"\n📝 Données de test:")
    for key, value in article_data.items():
        print(f"   {key}: {value}")
    
    print(f"\n🚀 Génération de l'article...")
    
    try:
        result = await chatgpt_service.ajout_article_cctp(article_data)
        
        if result.get("success", False):
            print(f"✅ Article généré avec succès!")
            print(f"⏱️  Temps de traitement: {result.get('processing_time', 0):.2f}s")
            print(f"🎯 Modèle utilisé: {result.get('model_used', 'N/A')}")
            print(f"\n📄 Article généré:")
            print("-" * 50)
            print(result.get("article_content", ""))
            print("-" * 50)
            return True
        else:
            print(f"❌ Erreur lors de la génération: {result.get('error', 'Erreur inconnue')}")
            return False
            
    except Exception as e:
        print(f"❌ Exception lors du test: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_article_generation())
    if success:
        print("\n🎉 Test réussi!")
    else:
        print("\n💥 Test échoué!")
    
    sys.exit(0 if success else 1)
