#!/usr/bin/env python3
import asyncio
import asyncpg
from app.core.config import settings

async def check_enums():
    conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)
    try:
        result = await conn.fetch("""
            SELECT enumlabel 
            FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'projectstatus')
        """)
        print('Project status enum values:')
        for row in result:
            print(f'  {row[0]}')
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await conn.close()

asyncio.run(check_enums())
