'use client'

import React, { useState, useEffect } from 'react'
import * as Dialog from '@radix-ui/react-dialog'
import * as Select from '@radix-ui/react-select'
import * as Label from '@radix-ui/react-label'
import * as Switch from '@radix-ui/react-switch'
import { DocumentType } from '@/types/technical-document'

interface AddArticleModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (articleData: ArticleFormData) => void
  documentType: DocumentType
  availableHeadings: HeadingOption[]
}

interface HeadingOption {
  id: string
  title: string
  level: number
  position: number
  number: string
}

interface ArticleFormData {
  parentId?: string
  prestation: string
  localisation: string
  marque: string
  reference: string
  nature: string
  criteresQualite: string
  dimensions: string
  couleur: string
  particularite: string
  descriptionPose: string
  typePose: string
  marquePose: string
  referencePose: string
  inclureCriteres: boolean
  inclureDocs: boolean
  unite: string
  quantite: number
}

const UNITE_OPTIONS = [
  { value: 'ml', label: 'ml (mètre linéaire)' },
  { value: 'm²', label: 'm² (mètre carré)' },
  { value: 'm³', label: 'm³ (mètre cube)' },
  { value: 'u', label: 'u (unité)' },
  { value: 'kg', label: 'kg (kilogramme)' },
  { value: 't', label: 't (tonne)' },
  { value: 'l', label: 'l (litre)' },
  { value: 'forfait', label: 'forfait' },
]

export default function AddArticleModal({
  isOpen,
  onClose,
  onSubmit,
  documentType,
  availableHeadings
}: AddArticleModalProps) {
  console.log('🔍 AddArticleModal render - isOpen:', isOpen)

  const [formData, setFormData] = useState<ArticleFormData>({
    parentId: '',
    prestation: 'Installation de luminaires LED',
    localisation: 'Hall d\'entrée principal',
    marque: 'Philips',
    reference: 'CoreLine LED WT120C',
    nature: 'Éclairage LED étanche',
    criteresQualite: 'Conformité NF EN 60598, IP65 minimum, durée de vie > 50 000h',
    dimensions: '1200 x 300 x 100 mm',
    couleur: 'Blanc RAL 9003',
    particularite: 'Détecteur de mouvement intégré, gradation 1-10V',
    descriptionPose: 'Fixation par suspentes réglables, raccordement électrique étanche',
    typePose: 'Suspension',
    marquePose: 'Legrand',
    referencePose: 'Kit suspension KS120',
    inclureCriteres: true,
    inclureDocs: true,
    unite: 'u',
    quantite: 12
  })

  console.log('📝 FormData actuel:', formData)

  const [filteredHeadings, setFilteredHeadings] = useState<HeadingOption[]>([])
  const [headingFilter, setHeadingFilter] = useState('')



  // Filtrer les headings disponibles
  useEffect(() => {
    console.log('🔍 Available headings:', availableHeadings)
    console.log('🔍 Heading filter:', headingFilter)

    if (headingFilter.trim() === '') {
      setFilteredHeadings(availableHeadings)
    } else {
      setFilteredHeadings(
        availableHeadings.filter(heading =>
          heading.title.toLowerCase().includes(headingFilter.toLowerCase())
        )
      )
    }

    console.log('🔍 Filtered headings:', filteredHeadings)
  }, [headingFilter, availableHeadings])

  // Réinitialiser le formulaire quand la modal s'ouvre avec les données de test
  useEffect(() => {
    if (isOpen) {
      console.log('🔄 Pré-remplissage du formulaire avec les données de test')
      setFormData({
        parentId: '',
        prestation: 'Installation de luminaires LED',
        localisation: 'Hall d\'entrée principal',
        marque: 'Philips',
        reference: 'CoreLine LED WT120C',
        nature: 'Éclairage LED étanche',
        criteresQualite: 'Conformité NF EN 60598, IP65 minimum, durée de vie > 50 000h',
        dimensions: '1200 x 300 x 100 mm',
        couleur: 'Blanc RAL 9003',
        particularite: 'Détecteur de mouvement intégré, gradation 1-10V',
        descriptionPose: 'Fixation par suspentes réglables, raccordement électrique étanche',
        typePose: 'Suspension',
        marquePose: 'Legrand',
        referencePose: 'Kit suspension KS120',
        inclureCriteres: true,
        inclureDocs: true,
        unite: 'u',
        quantite: 12
      })
      setHeadingFilter('')
    }
  }, [isOpen])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    console.log('🚀 Soumission du formulaire - FormData:', formData)
    console.log('🚀 Prestation value:', `"${formData.prestation}"`)
    console.log('🚀 Prestation trimmed:', `"${formData.prestation.trim()}"`)
    console.log('🚀 Prestation length:', formData.prestation.length)

    // Validation
    if (!formData.prestation.trim()) {
      alert('La description de la prestation est obligatoire')
      return
    }

    console.log('✅ Validation passée, envoi des données...')
    onSubmit(formData)
    onClose()
  }

  const handleInputChange = (field: keyof ArticleFormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black bg-opacity-50 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl z-50 w-full max-w-5xl max-h-[95vh] overflow-y-auto">
          <div className="p-6">
            <Dialog.Title className="text-xl font-semibold text-gray-900 mb-4">
              Ajouter un article CCTP
            </Dialog.Title>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Sélecteur de heading parent */}
              <div>
                <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                  Heading parent (optionnel)
                </Label.Root>
                <div className="space-y-2">
                  <input
                    type="text"
                    placeholder="Filtrer les headings..."
                    value={headingFilter}
                    onChange={(e) => setHeadingFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <Select.Root
                    value={formData.parentId}
                    onValueChange={(value) => handleInputChange('parentId', value)}
                  >
                    <Select.Trigger className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left">
                      <Select.Value placeholder="Sélectionner un heading parent..." />
                    </Select.Trigger>
                    <Select.Content
                      className="bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-y-auto min-w-[400px]"
                      position="popper"
                      sideOffset={4}
                    >
                      <Select.Viewport>
                        {filteredHeadings.length === 0 ? (
                          <div className="px-3 py-3 text-gray-500 text-sm">
                            Aucun heading disponible dans le document
                          </div>
                        ) : (
                          filteredHeadings.map((heading) => (
                            <Select.Item
                              key={heading.id}
                              value={heading.id}
                              className="px-3 py-3 cursor-pointer hover:bg-blue-50 focus:bg-blue-50 outline-none flex items-center border-l-2 border-transparent hover:border-blue-300"
                            >
                              <Select.ItemText className="flex items-center w-full">
                                <span className="text-blue-600 mr-3 font-mono text-sm min-w-[3rem] font-medium">
                                  {heading.number}
                                </span>
                                <span
                                  className="text-gray-900 flex-1"
                                  style={{ marginLeft: `${(heading.level - 1) * 16}px` }}
                                >
                                  {heading.title}
                                </span>
                              </Select.ItemText>
                            </Select.Item>
                          ))
                        )}
                      </Select.Viewport>
                    </Select.Content>
                  </Select.Root>
                </div>
              </div>

              {/* Champs du formulaire en grille */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Description de la prestation - OBLIGATOIRE */}
                <div className="md:col-span-2">
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Description de la prestation *
                  </Label.Root>
                  <textarea
                    value={formData.prestation}
                    onChange={(e) => handleInputChange('prestation', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                    rows={3}
                    required
                  />
                </div>

                {/* Localisation */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Localisation de l'ouvrage
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.localisation}
                    onChange={(e) => handleInputChange('localisation', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Marque */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Marque ou équivalent
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.marque}
                    onChange={(e) => handleInputChange('marque', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Référence */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Référence technique
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.reference}
                    onChange={(e) => handleInputChange('reference', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Nature */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Nature des matériaux
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.nature}
                    onChange={(e) => handleInputChange('nature', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Critères qualité */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Critères qualité
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.criteresQualite}
                    onChange={(e) => handleInputChange('criteresQualite', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Dimensions */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Dimensions
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.dimensions}
                    onChange={(e) => handleInputChange('dimensions', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Couleur */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Couleur
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.couleur}
                    onChange={(e) => handleInputChange('couleur', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Particularité */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Particularité de la prestation
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.particularite}
                    onChange={(e) => handleInputChange('particularite', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Description mise en œuvre */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Description mise en œuvre
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.descriptionPose}
                    onChange={(e) => handleInputChange('descriptionPose', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Type de pose */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Type de pose
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.typePose}
                    onChange={(e) => handleInputChange('typePose', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Marque matériels pose */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Marque matériels pose
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.marquePose}
                    onChange={(e) => handleInputChange('marquePose', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Référence technique pose */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Référence technique pose
                  </Label.Root>
                  <input
                    type="text"
                    value={formData.referencePose}
                    onChange={(e) => handleInputChange('referencePose', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>

                {/* Unité */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Unité de mesure
                  </Label.Root>
                  <Select.Root
                    value={formData.unite}
                    onValueChange={(value) => handleInputChange('unite', value)}
                  >
                    <Select.Trigger className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left bg-yellow-50">
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Portal>
                      <Select.Content className="bg-white border border-gray-300 rounded-md shadow-lg z-50">
                        <Select.Viewport>
                          {UNITE_OPTIONS.map((option) => (
                            <Select.Item
                              key={option.value}
                              value={option.value}
                              className="px-3 py-2 cursor-pointer hover:bg-gray-100 focus:bg-gray-100 outline-none"
                            >
                              <Select.ItemText>{option.label}</Select.ItemText>
                            </Select.Item>
                          ))}
                        </Select.Viewport>
                      </Select.Content>
                    </Select.Portal>
                  </Select.Root>
                </div>

                {/* Quantité */}
                <div>
                  <Label.Root className="block text-sm font-medium text-gray-700 mb-2">
                    Quantité
                  </Label.Root>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.quantite || 12}
                    onChange={(e) => handleInputChange('quantite', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-yellow-50"
                  />
                </div>
              </div>

              {/* Switches pour inclure critères et docs */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Switch.Root
                    checked={formData.inclureCriteres}
                    onCheckedChange={(checked) => handleInputChange('inclureCriteres', checked)}
                    className="w-11 h-6 bg-gray-200 rounded-full relative focus:outline-none focus:ring-2 focus:ring-blue-500 data-[state=checked]:bg-blue-600"
                  >
                    <Switch.Thumb className="block w-5 h-5 bg-white rounded-full transition-transform duration-100 translate-x-0.5 will-change-transform data-[state=checked]:translate-x-[22px]" />
                  </Switch.Root>
                  <Label.Root className="text-sm font-medium text-gray-700">
                    Inclure § 4 (essais)
                  </Label.Root>
                </div>

                <div className="flex items-center space-x-3">
                  <Switch.Root
                    checked={formData.inclureDocs}
                    onCheckedChange={(checked) => handleInputChange('inclureDocs', checked)}
                    className="w-11 h-6 bg-gray-200 rounded-full relative focus:outline-none focus:ring-2 focus:ring-blue-500 data-[state=checked]:bg-blue-600"
                  >
                    <Switch.Thumb className="block w-5 h-5 bg-white rounded-full transition-transform duration-100 translate-x-0.5 will-change-transform data-[state=checked]:translate-x-[22px]" />
                  </Switch.Root>
                  <Label.Root className="text-sm font-medium text-gray-700">
                    Inclure § 5 (docs)
                  </Label.Root>
                </div>
              </div>

              {/* Boutons d'action */}
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Générer l'article
                </button>
              </div>
            </form>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}
