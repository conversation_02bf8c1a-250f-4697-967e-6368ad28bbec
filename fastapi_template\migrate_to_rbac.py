#!/usr/bin/env python3
"""
Migration complète vers le système RBAC flexible pour ORBIS
- Supprime les données existantes (sauf super admin)
- Recrée les ENUMs proprement
- Crée les nouvelles tables RBAC
- Insère les rôles et permissions de base
- Crée 5 entreprises de test avec utilisateurs
"""
import asyncio
import asyncpg
import uuid
from datetime import datetime
from app.core.config import settings

async def migrate_to_rbac():
    print("🚀 MIGRATION VERS SYSTÈME RBAC FLEXIBLE")
    print("=" * 50)

    try:
        conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)

        # 1. SAUVEGARDER LE SUPER ADMIN
        print("\n1. 💾 Sauvegarde du super admin...")
        super_admin = await conn.fetchrow("""
            SELECT * FROM users WHERE role = 'SUPER_ADMIN' LIMIT 1
        """)

        if super_admin:
            print(f"   ✅ Super admin trouvé: {super_admin['email']}")
        else:
            print("   ⚠️  Aucun super admin trouvé")

        # 2. SUPPRESSION DES DONNÉES (sauf super admin)
        print("\n2. 🗑️  Suppression des données existantes...")

        # Supprimer les liaisons user_companies
        await conn.execute("DELETE FROM user_companies")
        print("   ✅ user_companies vidée")

        # Supprimer les utilisateurs (sauf super admin)
        if super_admin:
            await conn.execute("DELETE FROM users WHERE id != $1", super_admin['id'])
            print(f"   ✅ Utilisateurs supprimés (sauf super admin)")
        else:
            await conn.execute("DELETE FROM users")
            print("   ✅ Tous les utilisateurs supprimés")

        # Supprimer les données liées aux entreprises dans le bon ordre
        await conn.execute("DELETE FROM company_settings")
        print("   ✅ company_settings supprimées")

        # Supprimer toutes les autres tables liées aux entreprises
        tables_to_clean = [
            'audit_logs', 'budget_lines', 'budgets', 'deliveries', 'delivery_lines',
            'document_folders', 'document_versions', 'documents', 'employee_assignments',
            'employees', 'financial_reports', 'invoices', 'materials', 'payments',
            'price_history', 'project_documents', 'project_employees', 'projects',
            'purchase_order_lines', 'purchase_orders', 'quote_lines', 'quote_templates',
            'quotes', 'supplier_contacts', 'suppliers', 'technical_sheets', 'time_entries',
            'company_invitations'
        ]

        for table in tables_to_clean:
            try:
                await conn.execute(f"DELETE FROM {table}")
                print(f"   ✅ {table} vidée")
            except Exception as e:
                print(f"   ⚠️  {table}: {e}")

        # Supprimer les entreprises
        await conn.execute("DELETE FROM companies")
        print("   ✅ Entreprises supprimées")

        # 3. SUPPRESSION DES ANCIENNES TABLES RBAC (si elles existent)
        print("\n3. 🔧 Nettoyage des anciennes tables RBAC...")
        await conn.execute("DROP TABLE IF EXISTS company_role_permissions CASCADE")
        await conn.execute("DROP TABLE IF EXISTS role_permissions CASCADE")
        await conn.execute("DROP TABLE IF EXISTS permissions CASCADE")
        await conn.execute("DROP TABLE IF EXISTS roles CASCADE")
        print("   ✅ Anciennes tables RBAC supprimées")

        # 4. MODIFICATION DE L'ENUM COMPANYROLE
        print("\n4. 🔄 Modification de l'enum companyrole...")

        # Supprimer l'ancien enum et le recréer
        await conn.execute("DROP TYPE IF EXISTS companyrole CASCADE")
        await conn.execute("""
            CREATE TYPE companyrole AS ENUM (
                'ADMIN', 'MANAGER', 'USER', 'VIEWER',
                'MOA', 'MOADEL', 'ARCHI', 'BE', 'BC', 'OPC', 'ENT', 'FO'
            )
        """)
        print("   ✅ Enum companyrole recréé avec tous les rôles")

        await conn.close()

    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

    return True

async def create_rbac_tables():
    print("\n5. 🏗️  Création des nouvelles tables RBAC...")

    try:
        conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)

        # Table roles
        await conn.execute("""
            CREATE TABLE roles (
                id SERIAL PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                is_system_role BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT NOW()
            )
        """)
        print("   ✅ Table roles créée")

        # Table permissions
        await conn.execute("""
            CREATE TABLE permissions (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) UNIQUE NOT NULL,
                resource VARCHAR(50) NOT NULL,
                action VARCHAR(50) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT NOW()
            )
        """)
        print("   ✅ Table permissions créée")

        # Table company_role_permissions
        await conn.execute("""
            CREATE TABLE company_role_permissions (
                id SERIAL PRIMARY KEY,
                company_id INTEGER REFERENCES companies(id) ON DELETE CASCADE,
                role_name VARCHAR(50) NOT NULL,
                permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
                created_at TIMESTAMP DEFAULT NOW(),
                UNIQUE(company_id, role_name, permission_id)
            )
        """)
        print("   ✅ Table company_role_permissions créée")

        # Modifier user_companies pour utiliser role_name
        await conn.execute("ALTER TABLE user_companies DROP COLUMN IF EXISTS role CASCADE")
        await conn.execute("ALTER TABLE user_companies ADD COLUMN role_name VARCHAR(50) DEFAULT 'USER'")
        print("   ✅ Table user_companies modifiée")

        await conn.close()
        return True

    except Exception as e:
        print(f"❌ Erreur création tables: {e}")
        return False

async def insert_roles_and_permissions():
    print("\n6. 📝 Insertion des rôles et permissions...")

    try:
        conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)

        # Insérer les rôles
        roles = [
            ('SUPER_ADMIN', 'Super administrateur système', True),
            ('ADMIN', 'Administrateur d\'entreprise', False),
            ('MANAGER', 'Gestionnaire', False),
            ('USER', 'Utilisateur standard', False),
            ('VIEWER', 'Lecture seule', False),
            ('MOA', 'Maître d\'Ouvrage', False),
            ('MOADEL', 'Maître d\'Ouvrage Délégué', False),
            ('ARCHI', 'Architecte', False),
            ('BE', 'Bureau d\'Études', False),
            ('BC', 'Bureau de Contrôle', False),
            ('OPC', 'Ordonnancement Pilotage Coordination', False),
            ('ENT', 'Entreprise', False),
            ('FO', 'Fournisseur', False)
        ]

        for name, desc, is_system in roles:
            await conn.execute("""
                INSERT INTO roles (name, description, is_system_role)
                VALUES ($1, $2, $3)
            """, name, desc, is_system)

        print(f"   ✅ {len(roles)} rôles insérés")

        # Insérer les permissions
        permissions = [
            # Projects
            ('projects.create', 'projects', 'create', 'Créer des projets'),
            ('projects.read', 'projects', 'read', 'Voir les projets'),
            ('projects.update', 'projects', 'update', 'Modifier les projets'),
            ('projects.delete', 'projects', 'delete', 'Supprimer les projets'),
            ('projects.manage_team', 'projects', 'manage_team', 'Gérer l\'équipe projet'),
            ('projects.view_financial', 'projects', 'view_financial', 'Voir les données financières'),

            # Users
            ('users.create', 'users', 'create', 'Créer des utilisateurs'),
            ('users.read', 'users', 'read', 'Voir les utilisateurs'),
            ('users.update', 'users', 'update', 'Modifier les utilisateurs'),
            ('users.delete', 'users', 'delete', 'Supprimer les utilisateurs'),
            ('users.invite', 'users', 'invite', 'Inviter des utilisateurs'),
            ('users.manage_roles', 'users', 'manage_roles', 'Gérer les rôles'),

            # Documents
            ('documents.create', 'documents', 'create', 'Créer des documents'),
            ('documents.read', 'documents', 'read', 'Voir les documents'),
            ('documents.update', 'documents', 'update', 'Modifier les documents'),
            ('documents.delete', 'documents', 'delete', 'Supprimer les documents'),
            ('documents.download', 'documents', 'download', 'Télécharger les documents'),
            ('documents.upload', 'documents', 'upload', 'Uploader des documents'),

            # Budgets
            ('budgets.create', 'budgets', 'create', 'Créer des budgets'),
            ('budgets.read', 'budgets', 'read', 'Voir les budgets'),
            ('budgets.update', 'budgets', 'update', 'Modifier les budgets'),
            ('budgets.delete', 'budgets', 'delete', 'Supprimer les budgets'),
            ('budgets.approve', 'budgets', 'approve', 'Approuver les budgets'),

            # Quotes
            ('quotes.create', 'quotes', 'create', 'Créer des devis'),
            ('quotes.read', 'quotes', 'read', 'Voir les devis'),
            ('quotes.update', 'quotes', 'update', 'Modifier les devis'),
            ('quotes.delete', 'quotes', 'delete', 'Supprimer les devis'),
            ('quotes.send', 'quotes', 'send', 'Envoyer les devis'),
            ('quotes.approve', 'quotes', 'approve', 'Approuver les devis'),

            # Purchase Orders
            ('purchase_orders.create', 'purchase_orders', 'create', 'Créer des commandes'),
            ('purchase_orders.read', 'purchase_orders', 'read', 'Voir les commandes'),
            ('purchase_orders.update', 'purchase_orders', 'update', 'Modifier les commandes'),
            ('purchase_orders.delete', 'purchase_orders', 'delete', 'Supprimer les commandes'),
            ('purchase_orders.validate', 'purchase_orders', 'validate', 'Valider les commandes'),

            # Invoices
            ('invoices.create', 'invoices', 'create', 'Créer des factures'),
            ('invoices.read', 'invoices', 'read', 'Voir les factures'),
            ('invoices.update', 'invoices', 'update', 'Modifier les factures'),
            ('invoices.delete', 'invoices', 'delete', 'Supprimer les factures'),
            ('invoices.send', 'invoices', 'send', 'Envoyer les factures'),
            ('invoices.pay', 'invoices', 'pay', 'Marquer comme payé'),

            # Companies
            ('companies.read', 'companies', 'read', 'Voir les informations entreprise'),
            ('companies.update', 'companies', 'update', 'Modifier l\'entreprise'),
            ('companies.manage_settings', 'companies', 'manage_settings', 'Gérer les paramètres'),

            # Reports
            ('reports.read', 'reports', 'read', 'Voir les rapports'),
            ('reports.create', 'reports', 'create', 'Créer des rapports'),
            ('reports.export', 'reports', 'export', 'Exporter les rapports')
        ]

        for name, resource, action, desc in permissions:
            await conn.execute("""
                INSERT INTO permissions (name, resource, action, description)
                VALUES ($1, $2, $3, $4)
            """, name, resource, action, desc)

        print(f"   ✅ {len(permissions)} permissions insérées")

        await conn.close()
        return True

    except Exception as e:
        print(f"❌ Erreur insertion rôles/permissions: {e}")
        return False

async def create_test_companies():
    print("\n7. 🏢 Création des 5 entreprises de test...")

    try:
        conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)

        companies = [
            {
                'name': 'ORBIS Construction',
                'code': 'ORBIS',
                'description': 'Entreprise principale ORBIS',
                'address': '123 Rue de la Construction, 75001 Paris',
                'phone': '***********.89',
                'email': '<EMAIL>',
                'website': 'https://orbis.fr',
                'siret': '12345678901234'
            },
            {
                'name': 'MOA Développement',
                'code': 'MOA_DEV',
                'description': 'Maître d\'ouvrage spécialisé en développement',
                'address': '456 Avenue des Projets, 69000 Lyon',
                'phone': '***********.12',
                'email': '<EMAIL>',
                'website': 'https://moa-dev.fr',
                'siret': '23456789012345'
            },
            {
                'name': 'Architectes Associés',
                'code': 'ARCHI_ASSO',
                'description': 'Cabinet d\'architectes',
                'address': '789 Boulevard de l\'Architecture, 13000 Marseille',
                'phone': '***********.67',
                'email': '<EMAIL>',
                'website': 'https://archi-asso.fr',
                'siret': '34567890123456'
            },
            {
                'name': 'Bureau Études Techniques',
                'code': 'BET',
                'description': 'Bureau d\'études techniques spécialisé',
                'address': '321 Rue de l\'Ingénierie, 31000 Toulouse',
                'phone': '***********.23',
                'email': '<EMAIL>',
                'website': 'https://bet.fr',
                'siret': '45678901234567'
            },
            {
                'name': 'Entreprise Générale BTP',
                'code': 'EG_BTP',
                'description': 'Entreprise générale du bâtiment',
                'address': '654 Avenue du BTP, 44000 Nantes',
                'phone': '***********.90',
                'email': '<EMAIL>',
                'website': 'https://eg-btp.fr',
                'siret': '56789012345678'
            }
        ]

        company_ids = []
        for company in companies:
            company_id = await conn.fetchval("""
                INSERT INTO companies (
                    name, code, description, address, phone, email, website, siret,
                    is_active, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                RETURNING id
            """,
            company['name'], company['code'], company['description'],
            company['address'], company['phone'], company['email'],
            company['website'], company['siret'], True,
            datetime.utcnow(), datetime.utcnow())

            company_ids.append(company_id)
            print(f"   ✅ {company['name']} créée (ID: {company_id})")

        await conn.close()
        return company_ids

    except Exception as e:
        print(f"❌ Erreur création entreprises: {e}")
        return []

async def create_test_users(company_ids):
    print("\n8. 👥 Création des utilisateurs de test...")

    try:
        conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)

        # Utilisateurs de test pour chaque entreprise
        test_users = [
            # ORBIS Construction (company_ids[0])
            {
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'ORBIS',
                'role': 'ADMIN',
                'company_id': company_ids[0],
                'company_role': 'ADMIN'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Manager',
                'last_name': 'ORBIS',
                'role': 'EMPLOYE',
                'company_id': company_ids[0],
                'company_role': 'MANAGER'
            },

            # MOA Développement (company_ids[1])
            {
                'email': '<EMAIL>',
                'first_name': 'Jean',
                'last_name': 'Dupont',
                'role': 'EMPLOYE',
                'company_id': company_ids[1],
                'company_role': 'MOA'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Marie',
                'last_name': 'Martin',
                'role': 'EMPLOYE',
                'company_id': company_ids[1],
                'company_role': 'MOADEL'
            },

            # Architectes Associés (company_ids[2])
            {
                'email': '<EMAIL>',
                'first_name': 'Pierre',
                'last_name': 'Architecte',
                'role': 'EMPLOYE',
                'company_id': company_ids[2],
                'company_role': 'ARCHI'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Sophie',
                'last_name': 'Dessin',
                'role': 'EMPLOYE',
                'company_id': company_ids[2],
                'company_role': 'USER'
            },

            # Bureau Études Techniques (company_ids[3])
            {
                'email': '<EMAIL>',
                'first_name': 'Paul',
                'last_name': 'Ingénieur',
                'role': 'EMPLOYE',
                'company_id': company_ids[3],
                'company_role': 'BE'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Anne',
                'last_name': 'Contrôle',
                'role': 'EMPLOYE',
                'company_id': company_ids[3],
                'company_role': 'BC'
            },

            # Entreprise Générale BTP (company_ids[4])
            {
                'email': '<EMAIL>',
                'first_name': 'Marc',
                'last_name': 'Entrepreneur',
                'role': 'EMPLOYE',
                'company_id': company_ids[4],
                'company_role': 'ENT'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Julie',
                'last_name': 'Fourniture',
                'role': 'EMPLOYE',
                'company_id': company_ids[4],
                'company_role': 'FO'
            }
        ]

        for user in test_users:
            # Créer l'utilisateur
            user_id = await conn.fetchval("""
                INSERT INTO users (
                    supabase_user_id, email, first_name, last_name, role,
                    is_active, is_verified, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING id
            """,
            str(uuid.uuid4()), user['email'], user['first_name'],
            user['last_name'], user['role'], True, True,
            datetime.utcnow(), datetime.utcnow())

            # Associer à l'entreprise
            await conn.execute("""
                INSERT INTO user_companies (
                    user_id, company_id, role_name, is_default, is_active,
                    joined_at, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """,
            user_id, user['company_id'], user['company_role'], True, True,
            datetime.utcnow(), datetime.utcnow(), datetime.utcnow())

            print(f"   ✅ {user['email']} créé avec rôle {user['company_role']}")

        await conn.close()
        return True

    except Exception as e:
        print(f"❌ Erreur création utilisateurs: {e}")
        return False

async def setup_default_permissions(company_ids):
    print("\n9. 🔐 Configuration des permissions par défaut...")

    try:
        conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)

        # Permissions par rôle (configuration par défaut)
        role_permissions = {
            'ADMIN': [
                'projects.create', 'projects.read', 'projects.update', 'projects.delete', 'projects.manage_team', 'projects.view_financial',
                'users.create', 'users.read', 'users.update', 'users.delete', 'users.invite', 'users.manage_roles',
                'documents.create', 'documents.read', 'documents.update', 'documents.delete', 'documents.download', 'documents.upload',
                'budgets.create', 'budgets.read', 'budgets.update', 'budgets.delete', 'budgets.approve',
                'quotes.create', 'quotes.read', 'quotes.update', 'quotes.delete', 'quotes.send', 'quotes.approve',
                'purchase_orders.create', 'purchase_orders.read', 'purchase_orders.update', 'purchase_orders.delete', 'purchase_orders.validate',
                'invoices.create', 'invoices.read', 'invoices.update', 'invoices.delete', 'invoices.send', 'invoices.pay',
                'companies.read', 'companies.update', 'companies.manage_settings',
                'reports.read', 'reports.create', 'reports.export'
            ],
            'MANAGER': [
                'projects.create', 'projects.read', 'projects.update', 'projects.manage_team', 'projects.view_financial',
                'users.read', 'users.invite',
                'documents.create', 'documents.read', 'documents.update', 'documents.download', 'documents.upload',
                'budgets.read', 'budgets.update', 'budgets.approve',
                'quotes.create', 'quotes.read', 'quotes.update', 'quotes.send',
                'purchase_orders.create', 'purchase_orders.read', 'purchase_orders.update',
                'invoices.read', 'invoices.update',
                'companies.read',
                'reports.read', 'reports.create'
            ],
            'MOA': [
                'projects.create', 'projects.read', 'projects.update', 'projects.view_financial',
                'documents.read', 'documents.download', 'documents.upload',
                'budgets.read', 'budgets.approve',
                'quotes.read', 'quotes.approve',
                'reports.read'
            ],
            'MOADEL': [
                'projects.read', 'projects.update', 'projects.view_financial',
                'documents.read', 'documents.download', 'documents.upload',
                'budgets.read', 'budgets.update',
                'quotes.read', 'quotes.update',
                'reports.read'
            ],
            'ARCHI': [
                'projects.read', 'projects.update',
                'documents.create', 'documents.read', 'documents.update', 'documents.download', 'documents.upload',
                'quotes.create', 'quotes.read', 'quotes.update',
                'reports.read'
            ],
            'BE': [
                'projects.read', 'projects.update',
                'documents.create', 'documents.read', 'documents.update', 'documents.download', 'documents.upload',
                'quotes.read', 'quotes.update',
                'reports.read'
            ],
            'BC': [
                'projects.read',
                'documents.read', 'documents.download', 'documents.upload',
                'reports.read'
            ],
            'OPC': [
                'projects.read', 'projects.update', 'projects.manage_team',
                'documents.read', 'documents.download', 'documents.upload',
                'reports.read'
            ],
            'ENT': [
                'projects.read',
                'documents.read', 'documents.download', 'documents.upload',
                'quotes.read',
                'purchase_orders.read',
                'invoices.read'
            ],
            'USER': [
                'projects.read',
                'documents.read', 'documents.download', 'documents.upload',
                'reports.read'
            ],
            'VIEWER': [
                'projects.read',
                'documents.read', 'documents.download',
                'reports.read'
            ],
            'FO': [
                'documents.read', 'documents.download',
                'quotes.read',
                'purchase_orders.read'
            ]
        }

        # Appliquer les permissions à toutes les entreprises
        for company_id in company_ids:
            for role_name, permissions in role_permissions.items():
                for permission_name in permissions:
                    # Récupérer l'ID de la permission
                    permission_id = await conn.fetchval("""
                        SELECT id FROM permissions WHERE name = $1
                    """, permission_name)

                    if permission_id:
                        await conn.execute("""
                            INSERT INTO company_role_permissions (company_id, role_name, permission_id)
                            VALUES ($1, $2, $3)
                            ON CONFLICT (company_id, role_name, permission_id) DO NOTHING
                        """, company_id, role_name, permission_id)

        print(f"   ✅ Permissions configurées pour {len(company_ids)} entreprises")

        await conn.close()
        return True

    except Exception as e:
        print(f"❌ Erreur configuration permissions: {e}")
        return False

async def main():
    print("🚀 MIGRATION COMPLÈTE VERS SYSTÈME RBAC")
    print("=" * 60)

    # Étape 1: Migration de base
    if not await migrate_to_rbac():
        print("❌ Échec de la migration de base")
        return

    # Étape 2: Création des tables RBAC
    if not await create_rbac_tables():
        print("❌ Échec de la création des tables RBAC")
        return

    # Étape 3: Insertion des rôles et permissions
    if not await insert_roles_and_permissions():
        print("❌ Échec de l'insertion des rôles/permissions")
        return

    # Étape 4: Création des entreprises de test
    company_ids = await create_test_companies()
    if not company_ids:
        print("❌ Échec de la création des entreprises")
        return

    # Étape 5: Création des utilisateurs de test
    if not await create_test_users(company_ids):
        print("❌ Échec de la création des utilisateurs")
        return

    # Étape 6: Configuration des permissions par défaut
    if not await setup_default_permissions(company_ids):
        print("❌ Échec de la configuration des permissions")
        return

    print("\n" + "=" * 60)
    print("✅ MIGRATION RBAC TERMINÉE AVEC SUCCÈS!")
    print("=" * 60)
    print("\n📊 Résumé:")
    print(f"   - {len(company_ids)} entreprises créées")
    print("   - 10 utilisateurs de test créés")
    print("   - 13 rôles configurés")
    print("   - 47 permissions définies")
    print("   - Permissions par défaut appliquées")
    print("\n🔗 Prochaines étapes:")
    print("   1. Mettre à jour les modèles SQLAlchemy")
    print("   2. Adapter les schémas Pydantic")
    print("   3. Modifier les endpoints FastAPI")
    print("   4. Tester le système")

if __name__ == "__main__":
    asyncio.run(main())
