# app/core/supabase_client.py
from supabase import create_client, Client
from app.core.config import settings
import asyncio
from typing import Optional, Dict, Any
import httpx
import jwt
import time

class SupabaseClient:
    def __init__(self):
        self.url = settings.SUPABASE_URL
        self.key = settings.SUPABASE_ANON_KEY
        self.service_key = settings.SUPABASE_SERVICE_ROLE_KEY
        self.client: Optional[Client] = None
        self.admin_client: Optional[Client] = None

    def get_client(self) -> Client:
        """Get Supabase client with anon key"""
        if not self.client:
            self.client = create_client(self.url, self.key)
        return self.client

    def get_admin_client(self) -> Client:
        """Get Supabase client with service role key for admin operations"""
        if not self.admin_client:
            self.admin_client = create_client(self.url, self.service_key)
        return self.admin_client

    # user_profiles table removed - using users table directly

    async def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token with <PERSON>pa<PERSON>"""
        try:
            # Use httpx for async HTTP requests
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {token}',
                    'apikey': self.key
                }
                
                response = await client.get(f'{self.url}/auth/v1/user', headers=headers)
                
                if response.status_code == 200:
                    return response.json()
                else:
                    return None
                    
        except Exception as e:
            print(f"Error verifying token: {str(e)}")
            return None

    async def get_projects(self, user_id: str) -> list:
        """Get all projects"""
        try:
            client = self.get_client()
            result = client.table('projects').select('*').execute()
            return result.data if result.data else []
        except Exception as e:
            raise Exception(f"Error getting projects: {str(e)}")

    async def create_project(self, project_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Create a new project"""
        try:
            client = self.get_client()
            project_data['created_by'] = user_id
            
            result = client.table('projects').insert(project_data).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise Exception("Failed to create project")
                
        except Exception as e:
            raise Exception(f"Error creating project: {str(e)}")

    async def get_employees(self) -> list:
        """Get all employees"""
        try:
            client = self.get_client()
            result = client.table('employees').select('*, user_profiles(*)').execute()
            return result.data if result.data else []
        except Exception as e:
            raise Exception(f"Error getting employees: {str(e)}")

    async def create_employee(self, employee_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new employee"""
        try:
            client = self.get_client()
            
            result = client.table('employees').insert(employee_data).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise Exception("Failed to create employee")
                
        except Exception as e:
            raise Exception(f"Error creating employee: {str(e)}")

    async def get_time_entries(self, employee_id: Optional[int] = None, project_id: Optional[int] = None) -> list:
        """Get time entries with optional filters"""
        try:
            client = self.get_client()
            query = client.table('time_entries').select('*, employees(*), projects(*)')
            
            if employee_id:
                query = query.eq('employee_id', employee_id)
            if project_id:
                query = query.eq('project_id', project_id)
                
            result = query.execute()
            return result.data if result.data else []
        except Exception as e:
            raise Exception(f"Error getting time entries: {str(e)}")

    async def create_time_entry(self, time_entry_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new time entry"""
        try:
            client = self.get_client()
            
            result = client.table('time_entries').insert(time_entry_data).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise Exception("Failed to create time entry")
                
        except Exception as e:
            raise Exception(f"Error creating time entry: {str(e)}")

    async def get_documents(self) -> list:
        """Get all documents"""
        try:
            client = self.get_client()
            result = client.table('documents').select('*, projects(*)').execute()
            return result.data if result.data else []
        except Exception as e:
            raise Exception(f"Error getting documents: {str(e)}")



    async def get_user_by_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Obtenir les données utilisateur par token"""
        try:
            # Utiliser le token pour obtenir les données utilisateur
            client = self.get_client()
            client.auth.set_session(token, "")
            user = client.auth.get_user()
            return user.user if user else None
        except Exception as e:
            print(f"Erreur récupération utilisateur: {e}")
            return None

# Create global instance
supabase_client = SupabaseClient()