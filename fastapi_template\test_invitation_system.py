#!/usr/bin/env python3
"""
Script de test pour le système d'invitation
"""

import asyncio
import asyncpg
from datetime import datetime, timedelta
import secrets
import json
from app.core.config import settings

DATABASE_URL = settings.DATABASE_URL


async def test_invitation_system():
    """Tester le système d'invitation"""
    print("🧪 Test du système d'invitation...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Vérifier que les tables existent
        print("   📊 Vérification des tables...")
        
        tables_to_check = ['audit_logs', 'company_invitations']
        for table in tables_to_check:
            exists = await conn.fetchval(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = '{table}'
                )
            """)
            if exists:
                print(f"   ✅ Table {table} existe")
            else:
                print(f"   ❌ Table {table} manquante")
                return False
        
        # 2. Créer une invitation de test
        print("   📧 Création d'une invitation de test...")
        
        # Récupérer l'entreprise ORBIS et un utilisateur admin
        company = await conn.fetchrow("SELECT id, name FROM companies WHERE name = 'ORBIS'")
        if not company:
            print("   ❌ Entreprise ORBIS non trouvée")
            return False
        
        admin_user = await conn.fetchrow("""
            SELECT u.id, u.email FROM users u
            JOIN user_companies uc ON u.id = uc.user_id
            WHERE uc.company_id = $1 AND uc.role = 'admin'
            LIMIT 1
        """, company['id'])
        
        if not admin_user:
            print("   ❌ Utilisateur admin non trouvé")
            return False
        
        # Créer une invitation de test
        test_email = "<EMAIL>"
        invitation_token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(days=7)
        
        invitation_id = await conn.fetchval("""
            INSERT INTO company_invitations (
                company_id, email, role, invited_by, invitation_token, 
                expires_at, is_active, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
        """, 
        company['id'], test_email, 'user', admin_user['id'], invitation_token,
        expires_at, True, datetime.utcnow(), datetime.utcnow()
        )
        
        print(f"   ✅ Invitation créée (ID: {invitation_id})")
        print(f"   📧 Email: {test_email}")
        print(f"   🔗 Token: {invitation_token}")
        
        # 3. Tester la récupération de l'invitation
        print("   🔍 Test de récupération de l'invitation...")
        
        invitation = await conn.fetchrow("""
            SELECT ci.*, c.name as company_name, u.first_name, u.last_name
            FROM company_invitations ci
            JOIN companies c ON ci.company_id = c.id
            JOIN users u ON ci.invited_by = u.id
            WHERE ci.invitation_token = $1
        """, invitation_token)
        
        if invitation:
            print(f"   ✅ Invitation récupérée")
            print(f"   📊 Entreprise: {invitation['company_name']}")
            print(f"   👤 Invité par: {invitation['first_name']} {invitation['last_name']}")
            print(f"   📅 Expire le: {invitation['expires_at']}")
            print(f"   ⏰ En attente: {invitation['accepted_at'] is None and invitation['rejected_at'] is None}")
        else:
            print("   ❌ Impossible de récupérer l'invitation")
            return False
        
        # 4. Tester l'audit log
        print("   📋 Test de l'audit log...")
        
        audit_id = await conn.fetchval("""
            INSERT INTO audit_logs (
                company_id, user_id, action, resource_type, resource_id,
                details, created_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id
        """,
        company['id'], admin_user['id'], 'invite', 'user_invitation', str(invitation_id),
        json.dumps({'invited_email': test_email, 'role': 'user'}), datetime.utcnow()
        )
        
        print(f"   ✅ Audit log créé (ID: {audit_id})")
        
        # 5. Lister les invitations pour l'entreprise
        print("   📋 Liste des invitations pour ORBIS...")
        
        invitations = await conn.fetch("""
            SELECT ci.email, ci.role, ci.is_active, ci.created_at,
                   u.first_name, u.last_name,
                   ci.accepted_at IS NOT NULL as is_accepted,
                   ci.rejected_at IS NOT NULL as is_rejected,
                   ci.expires_at < NOW() as is_expired
            FROM company_invitations ci
            JOIN users u ON ci.invited_by = u.id
            WHERE ci.company_id = $1
            ORDER BY ci.created_at DESC
        """, company['id'])
        
        print(f"   📊 {len(invitations)} invitation(s) trouvée(s):")
        for inv in invitations:
            status = "Acceptée" if inv['is_accepted'] else "Refusée" if inv['is_rejected'] else "Expirée" if inv['is_expired'] else "En attente"
            print(f"     • {inv['email']} ({inv['role']}) - {status} - Invité par {inv['first_name']} {inv['last_name']}")
        
        # 6. Nettoyer les données de test
        print("   🧹 Nettoyage des données de test...")
        
        await conn.execute("DELETE FROM audit_logs WHERE id = $1", audit_id)
        await conn.execute("DELETE FROM company_invitations WHERE id = $1", invitation_id)
        
        print("   ✅ Données de test supprimées")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Test du Système d'Invitation")
    print("="*50)
    
    if await test_invitation_system():
        print("\n✅ Système d'invitation testé avec succès!")
        print("\n📋 Fonctionnalités testées:")
        print("   - Création d'invitations")
        print("   - Récupération par token")
        print("   - Audit trail")
        print("   - Listing des invitations")
        
        print("\n🔧 Endpoints disponibles:")
        print("   POST /api/v1/invitations/ - Créer une invitation")
        print("   GET /api/v1/invitations/ - Lister les invitations")
        print("   POST /api/v1/invitations/{id}/resend - Renvoyer une invitation")
        print("   DELETE /api/v1/invitations/{id} - Annuler une invitation")
        print("   POST /api/v1/invitations/accept/{token} - Voir une invitation")
        print("   POST /api/v1/invitations/confirm/{token} - Confirmer une invitation")
        
        print("\n🎯 Prochaines étapes:")
        print("   1. Intégrer avec Supabase Auth pour la création d'utilisateurs")
        print("   2. Ajouter l'envoi d'emails d'invitation")
        print("   3. Tester l'interface admin")
        
        return True
    else:
        print("\n❌ Échec du test du système d'invitation")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
