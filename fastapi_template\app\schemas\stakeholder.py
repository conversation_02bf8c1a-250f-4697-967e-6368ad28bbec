# app/schemas/stakeholder.py
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from app.schemas.tcompany import TCompanyResponse, TCompanyCreate

class StakeholderBase(BaseModel):
    """
    Schéma de base pour les Stakeholders (Intervenants)
    Label français: Intervenants
    """
    role: Optional[str] = Field(None, max_length=100, description="Rôle de l'intervenant (Architecte, Bureau d'études, etc.)")
    is_active: Optional[bool] = Field(True, description="Statut actif de l'intervenant")

class StakeholderCreate(StakeholderBase):
    """
    Schéma pour créer un nouvel intervenant
    Permet soit de choisir une entreprise existante soit d'en créer une nouvelle
    """
    lot_id: int = Field(..., gt=0, description="ID du lot")
    company_id: Optional[int] = Field(None, gt=0, description="ID de l'entreprise existante (optionnel)")
    company_data: Optional[TCompanyCreate] = Field(None, description="Données pour créer une nouvelle entreprise")
    role: Optional[str] = Field(None, max_length=100, description="Rôle de l'intervenant")

    class Config:
        json_schema_extra = {
            "example": {
                "lot_id": 1,
                "company_id": 5,  # OU company_data pour créer une nouvelle entreprise
                "role": "Architecte",
                "is_active": True
            }
        }

class StakeholderUpdate(StakeholderBase):
    """Schéma pour mettre à jour un intervenant"""
    company_id: Optional[int] = Field(None, gt=0, description="Nouvel ID d'entreprise")

class StakeholderResponse(StakeholderBase):
    """Schéma de réponse pour un intervenant"""
    id: int
    lot_id: int
    company_id: int
    company: Optional[TCompanyResponse] = None
    created_at: datetime
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "lot_id": 1,
                "company_id": 5,
                "role": "Architecte",
                "is_active": True,
                "created_at": "2025-01-15T08:22:00Z",
                "company": {
                    "id": 5,
                    "company_name": "Cabinet d'Architecture Moderne",
                    "activity": "Architecture",
                    "email": "<EMAIL>"
                }
            }
        }

class StakeholderInDB(StakeholderResponse):
    """Schéma complet pour la base de données"""
    created_by: Optional[int] = None

# Schémas pour les opérations en lot
class BulkStakeholderCreate(BaseModel):
    """Créer plusieurs intervenants en une fois"""
    lot_id: int = Field(..., gt=0)
    stakeholders: list[StakeholderCreate] = Field(..., min_items=1)

class BulkStakeholderResponse(BaseModel):
    """Réponse pour les opérations en lot"""
    success_count: int
    error_count: int
    errors: Optional[list[dict]] = None
    created_stakeholders: Optional[list[StakeholderResponse]] = None

# Schémas pour les statistiques
class StakeholderStats(BaseModel):
    """Statistiques des intervenants"""
    total_active: int
    total_inactive: int
    by_role: dict  # {"Architecte": 5, "Bureau d'études": 3, ...}
    by_lot: dict   # {lot_id: count, ...}

# Alias pour compatibilité (à supprimer après migration complète)
LotIntervenantBase = StakeholderBase
LotIntervenantCreate = StakeholderCreate
LotIntervenantUpdate = StakeholderUpdate
LotIntervenantResponse = StakeholderResponse
