"""
Script pour tester la création de lots dans les projets
Utilisateur: <EMAIL>
Mot de passe: orbis123!
"""

import requests
import json
import time
from typing import List, Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "orbis123!"

def authenticate() -> str:
    """Authentification et récupération du token"""
    print("🔐 Authentification...")
    
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/auth/login",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        token_data = response.json()
        access_token = token_data.get("access_token")
        print(f"✅ Authentification réussie!")
        return access_token
    else:
        print(f"❌ Erreur d'authentification: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def get_workspaces(token: str) -> List[Dict[str, Any]]:
    """Récupérer les workspaces de l'utilisateur"""
    print("\n📁 Récupération des workspaces...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/workspaces/",
        headers=headers
    )
    
    if response.status_code == 200:
        workspaces = response.json()
        print(f"✅ {len(workspaces)} workspace(s) trouvé(s)")
        for ws in workspaces:
            print(f"   - {ws.get('name', 'N/A')} (ID: {ws.get('id', 'N/A')})")
        return workspaces
    else:
        print(f"❌ Erreur récupération workspaces: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def get_projects(token: str, workspace_id: int = None) -> List[Dict[str, Any]]:
    """Récupérer les projets"""
    print(f"\n🏗️ Récupération des projets...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    params = {}
    if workspace_id:
        params["workspace_id"] = workspace_id
    
    response = requests.get(
        f"{BASE_URL}/api/v1/projects/",
        headers=headers,
        params=params
    )
    
    if response.status_code == 200:
        projects = response.json()
        print(f"✅ {len(projects)} projet(s) trouvé(s)")
        for project in projects:
            print(f"   - {project.get('name', 'N/A')} (ID: {project.get('id', 'N/A')}, Code: {project.get('code', 'N/A')})")
        return projects
    else:
        print(f"❌ Erreur récupération projets: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def create_lot(token: str, project_id: int, lot_name: str, lot_code: str = None) -> Dict[str, Any]:
    """Créer un lot dans un projet"""
    print(f"🔨 Création du lot '{lot_name}' dans le projet {project_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    lot_data = {
        "name": lot_name,
        "project_id": project_id,
        "description": f"Lot de test créé automatiquement pour le projet {project_id}",
        "current_phase": "ESQ"
    }
    
    if lot_code:
        lot_data["code"] = lot_code
    
    response = requests.post(
        f"{BASE_URL}/api/v1/lots/",
        json=lot_data,
        headers=headers
    )
    
    if response.status_code == 201:
        lot = response.json()
        print(f"✅ Lot créé: {lot.get('name')} (ID: {lot.get('id')}, Code: {lot.get('code', 'N/A')})")
        return lot
    else:
        print(f"❌ Erreur création lot: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def get_lots_by_project(token: str, project_id: int) -> List[Dict[str, Any]]:
    """Récupérer les lots d'un projet"""
    print(f"📋 Récupération des lots du projet {project_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/lots/project/{project_id}",
        headers=headers
    )
    
    if response.status_code == 200:
        lots = response.json()
        print(f"✅ {len(lots)} lot(s) trouvé(s) dans le projet {project_id}")
        for lot in lots:
            print(f"   - {lot.get('name', 'N/A')} (ID: {lot.get('id', 'N/A')}, Phase: {lot.get('current_phase', 'N/A')})")
        return lots
    else:
        print(f"❌ Erreur récupération lots: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def test_lots_creation():
    """Test complet de création de lots"""
    print("🚀 TEST DE CRÉATION DE LOTS")
    print("=" * 50)
    
    # 1. Authentification
    token = authenticate()
    if not token:
        return
    
    # 2. Récupération des workspaces
    workspaces = get_workspaces(token)
    if not workspaces:
        print("❌ Aucun workspace trouvé")
        return
    
    # 3. Récupération des projets
    projects = get_projects(token)
    if not projects:
        print("❌ Aucun projet trouvé")
        return
    
    # 4. Création de 2 lots par projet
    print(f"\n🔨 CRÉATION DE LOTS")
    print("=" * 30)
    
    created_lots = []
    
    for project in projects:
        project_id = project.get('id')
        project_name = project.get('name', 'Projet sans nom')
        project_code = project.get('code', 'NO_CODE')
        
        print(f"\n📁 Projet: {project_name} (ID: {project_id})")
        
        # Créer 2 lots pour ce projet avec timestamp pour éviter les doublons
        timestamp = int(time.time())
        for i in range(1, 3):
            lot_name = f"Lot {i} - {project_name} ({timestamp})"
            lot_code = f"{project_code}_LOT_{i:02d}_{timestamp}"
            
            lot = create_lot(token, project_id, lot_name, lot_code)
            if lot:
                created_lots.append(lot)
    
    # 5. Vérification des lots créés
    print(f"\n📊 VÉRIFICATION DES LOTS CRÉÉS")
    print("=" * 40)
    
    for project in projects:
        project_id = project.get('id')
        project_name = project.get('name', 'Projet sans nom')
        
        print(f"\n📁 Lots du projet: {project_name}")
        lots = get_lots_by_project(token, project_id)
    
    # 6. Résumé
    print(f"\n📈 RÉSUMÉ")
    print("=" * 20)
    print(f"✅ Lots créés avec succès: {len(created_lots)}")
    print(f"📁 Projets traités: {len(projects)}")
    
    if created_lots:
        print(f"\n📋 Liste des lots créés:")
        for lot in created_lots:
            print(f"   - {lot.get('name')} (ID: {lot.get('id')}, Code: {lot.get('code', 'N/A')})")

def save_test_results():
    """Sauvegarder les résultats du test"""
    test_info = {
        "test_type": "lots_creation",
        "email": TEST_EMAIL,
        "date": "2025-07-11",
        "description": "Test de création de lots dans les projets ORBIS"
    }
    
    with open("test_lots_results.json", "w") as f:
        json.dump(test_info, f, indent=2)
    
    print(f"💾 Résultats sauvegardés dans test_lots_results.json")

if __name__ == "__main__":
    print("🚀 Test de création de lots ORBIS")
    
    # Sauvegarder les informations du test
    save_test_results()
    
    # Tester la création de lots
    try:
        test_lots_creation()
        print("\n✅ Test terminé avec succès!")
    except requests.exceptions.ConnectionError:
        print("❌ Serveur non accessible sur http://localhost:8000")
        print("💡 Assurez-vous que le serveur FastAPI est démarré")
    except Exception as e:
        print(f"❌ Erreur durant le test: {e}")
        import traceback
        traceback.print_exc()
