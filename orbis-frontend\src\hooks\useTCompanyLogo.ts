import { useState, useEffect } from 'react'
import { FastAuthService } from '@/lib/auth'

interface UseTCompanyLogoProps {
  tcompanyId: number
  onSuccess?: () => void
  initialLogoUrl?: string | null
}

export function useTCompanyLogo({ tcompanyId, onSuccess, initialLogoUrl }: UseTCompanyLogoProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [logoUrl, setLogoUrl] = useState<string | null>(initialLogoUrl || null)
  const [logoDeleted, setLogoDeleted] = useState(false)

  // Mettre à jour logoUrl quand initialLogoUrl change
  useEffect(() => {
    setLogoUrl(initialLogoUrl || null)
    setLogoDeleted(false) // Reset l'état de suppression
  }, [initialLogoUrl])

  const uploadLogo = async (file: File) => {
    console.log('📤 Upload logo - Début:', { tcompanyId, fileName: file.name, fileSize: file.size })
    setLoading(true)
    setError(null)

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = FastAuthService.getToken()

      if (!token) {
        throw new Error('Token d\'authentification manquant')
      }

      const formData = new FormData()
      formData.append('file', file)

      const url = `${API_BASE_URL}/api/v1/tcompanies/${tcompanyId}/upload-logo`
      console.log('📤 Upload URL:', url)

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      console.log('📡 Upload réponse:', { status: response.status, statusText: response.statusText })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `Erreur HTTP: ${response.status}`)
      }

      const result = await response.json()
      setLogoUrl(result.logo_url)
      setLogoDeleted(false) // Reset l'état de suppression

      console.log('✅ Upload réussi:', result)

      if (onSuccess) {
        onSuccess()
      }

      return result

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de l\'upload'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const deleteLogo = async () => {
    console.log('🗑️ Suppression logo - Début:', { tcompanyId })
    setLoading(true)
    setError(null)

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = FastAuthService.getToken()

      if (!token) {
        throw new Error('Token d\'authentification manquant')
      }

      const url = `${API_BASE_URL}/api/v1/tcompanies/${tcompanyId}/logo`
      console.log('🗑️ Delete URL:', url)

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      console.log('📡 Delete réponse:', { status: response.status, statusText: response.statusText })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `Erreur HTTP: ${response.status}`)
      }

      const result = await response.json()
      setLogoUrl(null)
      setLogoDeleted(true) // Marquer comme supprimé

      console.log('✅ Suppression réussie:', result)

      if (onSuccess) {
        onSuccess()
      }

      return result

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la suppression'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return {
    uploadLogo,
    deleteLogo,
    loading,
    error,
    logoUrl,
    logoDeleted
  }
}
