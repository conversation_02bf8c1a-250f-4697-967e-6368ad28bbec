#!/usr/bin/env python3
"""
Script pour vérifier que la migration companies → workspaces s'est bien passée
"""

import asyncio
import asyncpg

DATABASE_URL = "**************************************************************************************************/postgres"

async def verify_migration():
    """Vérifier que la migration s'est bien passée"""
    print("🔍 Vérification post-migration companies → workspaces...")
    print("="*60)
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Vérifier les nouvelles tables
        print("\n✅ 1. Nouvelles tables créées:")
        new_tables = ['workspaces', 'workspace_settings', 'user_workspaces', 'workspace_role_permissions', 'workspace_invitations']
        
        for table_name in new_tables:
            try:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
                print(f"  - {table_name}: {count} enregistrements")
            except Exception as e:
                print(f"  - {table_name}: ❌ Erreur - {e}")
        
        # 2. Vérifier que les anciennes tables n'existent plus
        print("\n🗑️ 2. Anciennes tables supprimées:")
        old_tables = ['companies', 'company_settings', 'user_companies', 'company_role_permissions', 'company_invitations']
        
        for table_name in old_tables:
            try:
                await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
                print(f"  - {table_name}: ❌ ENCORE PRÉSENTE!")
            except Exception:
                print(f"  - {table_name}: ✅ Supprimée")
        
        # 3. Vérifier les tables préservées
        print("\n🛡️ 3. Tables préservées (entreprises tiers):")
        preserved_tables = ['technical_document_companies', 'project_company']
        
        for table_name in preserved_tables:
            try:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
                print(f"  - {table_name}: ✅ {count} enregistrements")
            except Exception as e:
                print(f"  - {table_name}: ❌ Erreur - {e}")
        
        # 4. Vérifier les colonnes workspace_id
        print("\n🔗 4. Colonnes workspace_id créées:")
        workspace_id_tables = [
            'workspace_settings', 'user_workspaces', 'audit_logs', 'budgets', 
            'documents', 'employees', 'entreprises_tiers', 'financial_reports',
            'invoices', 'materials', 'purchase_orders', 'quote_templates', 
            'quotes', 'suppliers'
        ]
        
        for table_name in workspace_id_tables:
            try:
                result = await conn.fetchval(f"""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = '{table_name}' AND column_name = 'workspace_id'
                    )
                """)
                if result:
                    print(f"  - {table_name}.workspace_id: ✅")
                else:
                    print(f"  - {table_name}.workspace_id: ❌ Manquante")
            except Exception as e:
                print(f"  - {table_name}: ⚠️ Table non trouvée")
        
        # 5. Vérifier que company_id n'existe plus dans les tables workspace
        print("\n🚫 5. Colonnes company_id supprimées:")
        for table_name in workspace_id_tables:
            try:
                result = await conn.fetchval(f"""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = '{table_name}' AND column_name = 'company_id'
                    )
                """)
                if result:
                    print(f"  - {table_name}.company_id: ❌ ENCORE PRÉSENTE!")
                else:
                    print(f"  - {table_name}.company_id: ✅ Supprimée")
            except Exception:
                pass
        
        # 6. Vérifier que company_id existe toujours dans les tables préservées
        print("\n🛡️ 6. Colonnes company_id préservées:")
        for table_name in preserved_tables:
            try:
                result = await conn.fetchval(f"""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = '{table_name}' AND column_name = 'company_id'
                    )
                """)
                if result:
                    print(f"  - {table_name}.company_id: ✅ Préservée")
                else:
                    print(f"  - {table_name}.company_id: ❌ SUPPRIMÉE PAR ERREUR!")
            except Exception as e:
                print(f"  - {table_name}: ⚠️ Table non trouvée")
        
        # 7. Test de requête simple
        print("\n🧪 7. Test de requête:")
        try:
            workspaces = await conn.fetch("SELECT id, name, code FROM workspaces LIMIT 3")
            print("  ✅ Requête sur workspaces réussie:")
            for ws in workspaces:
                print(f"    • ID: {ws['id']}, Nom: {ws['name']}, Code: {ws['code']}")
        except Exception as e:
            print(f"  ❌ Erreur requête workspaces: {e}")
        
        print("\n🎉 Vérification terminée!")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(verify_migration())
