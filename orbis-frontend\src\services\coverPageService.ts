import { TCompany } from '@/types/tcompany'
import { Lot } from '@/types/lot'

// Étendre TCompany pour inclure le rôle du stakeholder
interface TCompanyWithRole extends TCompany {
  role?: string;
}

export interface CoverPageData {
  lot: Lot
  companies: TCompanyWithRole[]
  projectTitle: string
  documentType: string
  documentIndice: string
  date: string
}

export interface CoverPageTemplate {
  title: string
  subtitle: string
  maitreDOuvrage: {
    name: string
    logo?: string
    address: string
  }
  maitriseOeuvre: {
    name: string
    logo?: string
    address: string
    contact: string
  }
  otherStakeholders: Array<{
    role: string
    name: string
    address?: string
  }>
  lotInfo: {
    title: string
    description: string
  }
  documentInfo: {
    type: string
    indice: string
    date: string
  }
}

export const generateCoverPageHTML = (data: CoverPageData): string => {
  console.log('🔍 Données reçues pour la page de garde:', data)

  const template: CoverPageTemplate = {
    title: data.lot.project?.name || data.projectTitle || `Projet ${data.lot.project?.id || 'N/A'}`,
    subtitle: ``, // TODO: récupérer le nom du workspace
    maitreDOuvrage: {
      name: data.companies.find(c => c.role?.includes('MOA'))?.company_name || 'Non renseigné',
      address: data.companies.find(c => c.role?.includes('MOA'))?.address || ''
    },
    maitriseOeuvre: {
      name: data.companies.find(c => c.role?.includes('ARCHI') || c.role?.includes('BE'))?.company_name || 'Non renseigné',
      logo: data.companies.find(c => c.role?.includes('ARCHI') || c.role?.includes('BE'))?.logo_url,
      address: data.companies.find(c => c.role?.includes('ARCHI') || c.role?.includes('BE'))?.address || '',
      contact: data.companies.find(c => c.role?.includes('ARCHI') || c.role?.includes('BE'))?.email || ''
    },
    otherStakeholders: data.companies
      .filter(c => !c.role?.includes('MOA') && !c.role?.includes('ARCHI') && !c.role?.includes('BE'))
      .map(c => ({
        role: c.role || 'Intervenant',
        name: c.company_name,
        address: c.address
      })),
    lotInfo: {
      title: data.lot.name || `LOT ${data.lot.id}`,
      description: data.lot.description || 'Description du lot'
    },
    documentInfo: {
      type: data.documentType,
      indice: data.documentIndice,
      date: data.date
    }
  }

  console.log('📋 Template généré:', template)
  console.log('🎨 NOUVELLES MODIFICATIONS - Police 30px/25px, centrage activé')
  console.log('⏰ Timestamp:', new Date().toISOString())

  return `

<    <!-- DEBUT PAGE DE GARDE - VERSION MODIFIEE 2025 -->
    <div style="page-break-after: always; font-family: Arial, sans-serif !important; padding: 15px; margin: 0; background-color: white; text-align: center !important;">
      <div style="display: inline-block; text-align: center; max-width: 1200px;">

        <!-- CADRE 1 : Titre du projet -->
        <table style="width: 100% !important; border-collapse: collapse !important; border: 2px solid #000 !important; margin-bottom: 10px !important; font-family: Arial, sans-serif !important;">
          <tr>
            <td style="padding: 30px; text-align: center !important; background-color: white; border: none;">
              <div style="font-size: 30px !important; font-weight: bold; color: #333; margin-bottom: 8px; text-align: center !important;">
                ${template.title}
              </div>
              <div style="font-size: 25px !important; color: #333; text-align: center !important;">
                ${data.lot.project?.name || 'Centre du projet'}
              </div>
            </td>
          </tr>
        </table>

        <!-- CADRE 2 : Maîtrise d'ouvrage -->
        <table style="width: 100%; border-collapse: collapse; border: 2px solid #000; margin-bottom: 10px;">
          <tr>
            <td style="padding: 8px; text-align: center; background-color: #f0f0f0; font-weight: bold; font-size: 14px; border: none;">
              MAITRE D'OUVRAGE
            </td>
          </tr>
          <tr>
            <td style="padding: 20px; text-align: center; background-color: white; border: none;">
              <table style="width: 100%; border: none; border-collapse: collapse;">
                <tr>
                  <td style="width: 30%; text-align: center; vertical-align: middle; padding-right: 20px; border: none;">
                    ${template.maitreDOuvrage.logo ?
                      `<img src="${template.maitreDOuvrage.logo}" alt="Logo MOA" style="max-height: 60px;" />` :
                      `<div style="width: 60px; height: 45px; background-color: #e0e0e0; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #999;">Logo</div>`
                    }
                  </td>
                  <td style="width: 70%; text-align: left; vertical-align: middle; border: none;">
                    <div style="font-weight: bold; font-size: 16px; margin-bottom: 8px; color: #333;">
                      ${template.maitreDOuvrage.name}
                    </div>
                    ${template.maitreDOuvrage.address ?
                      `<div style="font-size: 12px; color: #666; line-height: 1.3;">
                        ${template.maitreDOuvrage.address}
                      </div>` : ''
                    }
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>

        <!-- CADRE 3 : Maîtrise d'œuvre -->
        <table style="width: 100%; border-collapse: collapse; border: 2px solid #000; margin-bottom: 10px;">
          <tr>
            <td style="padding: 8px; text-align: center; background-color: #f0f0f0; font-weight: bold; font-size: 14px; border: none;">
              MAITRISE D'ŒUVRE
            </td>
          </tr>
          ${data.companies
            .filter(c => !c.role?.includes('MOA'))
            .map((stakeholder, index) => {
              return `
                <tr>
                  <td style="padding: 0; border: none;">
                    <table style="width: 100%; border: none; border-collapse: collapse;">
                      <tr>
                        <td style="width: 25%; text-align: center; vertical-align: middle; padding: 15px; border: none;">
                          <div style="font-weight: bold; font-size: 12px; margin-bottom: 8px;">
                            ${stakeholder.role || 'Intervenant'}
                          </div>
                        </td>
                        <td style="width: 35%; text-align: center; vertical-align: middle; padding: 15px; border: none;">
                          ${stakeholder.logo_url ?
                            `<img src="${stakeholder.logo_url}" alt="Logo ${stakeholder.company_name}" style="max-height: 50px;" />` :
                            `<div style="width: 60px; height: 40px; background-color: #e0e0e0; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #999;">Logo</div>`
                          }
                        </td>
                        <td style="width: 40%; text-align: left; vertical-align: middle; padding: 15px; border: none;">
                          <div style="font-weight: bold; font-size: 14px; margin-bottom: 6px; color: #333;">
                            ${stakeholder.company_name}
                          </div>
                          ${stakeholder.address ?
                            `<div style="font-size: 11px; color: #666; line-height: 1.2; margin-bottom: 4px;">
                              ${stakeholder.address}
                            </div>` : ''
                          }
                          ${stakeholder.email ?
                            `<div style="font-size: 11px; color: #666;">
                              📧 ${stakeholder.email}
                            </div>` : ''
                          }
                          ${stakeholder.phone ?
                            `<div style="font-size: 11px; color: #666;">
                              📞 ${stakeholder.phone}
                            </div>` : ''
                          }
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              `;
            }).join('')}
        </table>

        <!-- CADRE 4 : LOT -->
        <table style="width: 100%; border-collapse: collapse; border: 2px solid #000; margin-bottom: 10px;">
          <tr>
            <td style="padding: 8px; text-align: center; background-color: #f0f0f0; font-weight: bold; font-size: 14px; border: none;">
              LOT
            </td>
          </tr>
          <tr>
            <td style="padding: 20px; text-align: center; background-color: white; border: none;">
              <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #333;">
                ${template.lotInfo.title}
              </div>
              <div style="font-size: 14px; font-weight: bold; color: #333;">
                ${template.documentInfo.type || 'Cahier des Clauses Techniques Particulières'}
              </div>
            </td>
          </tr>
        </table>

        <!-- CADRE 5 : Révisions -->
        <table style="width: 100%; border-collapse: collapse; border: 2px solid #000; margin-bottom: 10px;">
          <tr>
            <td style="padding: 6px; text-align: center; background-color: white; font-weight: bold; font-size: 12px; border: none; width: 15%;">Indice</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-weight: bold; font-size: 12px; border: none; width: 20%;">Date</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-weight: bold; font-size: 12px; border: none; width: 65%;">Modification</td>
          </tr>
          <tr>
            <td style="padding: 6px; text-align: center; background-color: white; font-size: 11px; border: none;">01</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-size: 11px; border: none;">${template.documentInfo.date}</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-size: 11px; border: none;">Édition originale</td>
          </tr>
          <tr>
            <td style="padding: 6px; text-align: center; background-color: white; font-size: 11px; border: none;">00</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-size: 11px; border: none;">${template.documentInfo.date}</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-size: 11px; border: none;">Édition originale</td>
          </tr>
          <tr>
            <td style="padding: 12px; background-color: white; border: none;" colspan="3"></td>
          </tr>
          <tr>
            <td style="padding: 12px; background-color: white; border: none;" colspan="3"></td>
          </tr>
          <tr>
            <td style="padding: 12px; background-color: white; border: none;" colspan="3"></td>
          </tr>
        </table>

        <!-- CADRE 6 : Informations document -->
        <table style="width: 100%; border-collapse: collapse; border: 2px solid #000; margin-bottom: 10px;">
          <tr>
            <td style="padding: 6px; text-align: center; background-color: white; font-weight: bold; font-size: 12px; border: none; width: 15%;">Affaire</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-weight: bold; font-size: 12px; border: none; width: 15%;">Type</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-weight: bold; font-size: 12px; border: none; width: 15%;">Phase</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-weight: bold; font-size: 12px; border: none; width: 15%;">N° lot</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-weight: bold; font-size: 12px; border: none; width: 15%;">Indice</td>
            <td style="padding: 6px; text-align: center; background-color: white; font-weight: bold; font-size: 12px; border: none; width: 25%;">Date</td>
          </tr>
          <tr>
            <td style="padding: 8px; text-align: center; background-color: white; font-size: 12px; font-weight: bold; border: none;">${data.lot.project?.name?.substring(0, 10) || '21-022'}</td>
            <td style="padding: 8px; text-align: center; background-color: white; font-size: 12px; font-weight: bold; border: none;">CCTP</td>
            <td style="padding: 8px; text-align: center; background-color: white; font-size: 12px; font-weight: bold; border: none;">DCE</td>
            <td style="padding: 8px; text-align: center; background-color: white; font-size: 12px; font-weight: bold; border: none;">${template.lotInfo.title.split(' ')[0] || 'CVP-FS'}</td>
            <td style="padding: 8px; text-align: center; background-color: white; font-size: 12px; font-weight: bold; border: none;">${template.documentInfo.indice}</td>
            <td style="padding: 8px; text-align: center; background-color: white; font-size: 12px; font-weight: bold; border: none;">${template.documentInfo.date}</td>
          </tr>
        </table>

      </div>
    </div>
    <!-- FIN PAGE DE GARDE -->

  `
}
