/**
 * Composants pour protéger l'accès basé sur les permissions
 */

import React from 'react'
import { usePermissions, useHasPermission, useHasAnyPermission } from '@/hooks/usePermissions'
import { PERMISSION_MESSAGES } from '@/lib/permissions'
import { ShieldX, Lock, AlertTriangle } from 'lucide-react'

interface PermissionGuardProps {
  permission?: string
  permissions?: string[]
  requireAll?: boolean
  fallback?: React.ReactNode
  children: React.ReactNode
}

/**
 * Composant pour protéger l'accès basé sur une ou plusieurs permissions
 */
export function PermissionGuard({ 
  permission, 
  permissions, 
  requireAll = false, 
  fallback, 
  children 
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermissions()

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Vérification des droits...</span>
      </div>
    )
  }

  let hasAccess = false

  if (permission) {
    hasAccess = hasPermission(permission)
  } else if (permissions) {
    hasAccess = requireAll ? hasAllPermissions(permissions) : hasAnyPermission(permissions)
  }

  if (!hasAccess) {
    return fallback || <InsufficientRightsMessage />
  }

  return <>{children}</>
}

/**
 * Composant pour afficher le message "Droits insuffisants"
 */
export function InsufficientRightsMessage({ 
  message = PERMISSION_MESSAGES.INSUFFICIENT_RIGHTS,
  showIcon = true,
  className = ""
}: {
  message?: string
  showIcon?: boolean
  className?: string
}) {
  return (
    <div className={`flex flex-col items-center justify-center p-8 text-center bg-gray-50 rounded-lg border border-gray-200 ${className}`}>
      {showIcon && (
        <div className="mb-4">
          <ShieldX className="h-16 w-16 text-gray-400" />
        </div>
      )}
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Accès restreint
      </h3>
      <p className="text-gray-600 mb-4 max-w-md">
        {message}
      </p>
      <p className="text-sm text-gray-500">
        {PERMISSION_MESSAGES.CONTACT_ADMIN}
      </p>
    </div>
  )
}

/**
 * Composant pour afficher un message d'erreur de permission compact
 */
export function PermissionError({ 
  message = PERMISSION_MESSAGES.INSUFFICIENT_RIGHTS,
  variant = "default"
}: {
  message?: string
  variant?: "default" | "compact" | "inline"
}) {
  if (variant === "inline") {
    return (
      <span className="inline-flex items-center text-sm text-red-600">
        <Lock className="h-4 w-4 mr-1" />
        {message}
      </span>
    )
  }

  if (variant === "compact") {
    return (
      <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-md">
        <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
        <span className="text-sm text-red-700">{message}</span>
      </div>
    )
  }

  return <InsufficientRightsMessage message={message} />
}

/**
 * Hook pour créer un composant protégé par permissions
 */
export function withPermission<T extends object>(
  Component: React.ComponentType<T>,
  permission: string,
  fallback?: React.ReactNode
) {
  return function ProtectedComponent(props: T) {
    return (
      <PermissionGuard permission={permission} fallback={fallback}>
        <Component {...props} />
      </PermissionGuard>
    )
  }
}

/**
 * Composant pour protéger les boutons d'action
 */
interface ProtectedButtonProps {
  permission: string
  children: React.ReactNode
  fallback?: React.ReactNode
  showTooltip?: boolean
  className?: string
  onClick?: () => void
  disabled?: boolean
}

export function ProtectedButton({ 
  permission, 
  children, 
  fallback,
  showTooltip = true,
  className = "",
  onClick,
  disabled = false,
  ...props
}: ProtectedButtonProps) {
  const { hasPermission } = usePermissions()
  
  if (!hasPermission(permission)) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    if (showTooltip) {
      return (
        <button
          className={`${className} opacity-50 cursor-not-allowed`}
          disabled={true}
          title={PERMISSION_MESSAGES.INSUFFICIENT_RIGHTS}
          {...props}
        >
          {children}
        </button>
      )
    }
    
    return null
  }

  return (
    <button
      className={className}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  )
}

/**
 * Composant pour protéger les liens
 */
interface ProtectedLinkProps {
  permission: string
  children: React.ReactNode
  href?: string
  className?: string
  fallback?: React.ReactNode
}

export function ProtectedLink({ 
  permission, 
  children, 
  href,
  className = "",
  fallback,
  ...props
}: ProtectedLinkProps) {
  const { hasPermission } = usePermissions()
  
  if (!hasPermission(permission)) {
    return fallback || (
      <span className={`${className} opacity-50 cursor-not-allowed`} title={PERMISSION_MESSAGES.INSUFFICIENT_RIGHTS}>
        {children}
      </span>
    )
  }

  return (
    <a href={href} className={className} {...props}>
      {children}
    </a>
  )
}
