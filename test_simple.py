#!/usr/bin/env python3
"""
Test simple de l'API
"""

import requests

API_BASE = "http://localhost:8000"

def test_simple():
    """Test simple de l'API"""
    print("🧪 Test simple de l'API")
    print("="*30)
    
    try:
        # Test de santé
        print("1. 🏥 Test de santé...")
        health_response = requests.get(f"{API_BASE}/health", timeout=5)
        print(f"   Status: {health_response.status_code}")
        
        if health_response.status_code == 200:
            print("✅ API en ligne")
            
            # Test de connexion
            print("\n2. 🔐 Test de connexion...")
            login_response = requests.post(f"{API_BASE}/api/v1/fast-auth/login", json={
                "email": "<EMAIL>",
                "password": "orbis123!"
            }, timeout=5)
            
            print(f"   Status: {login_response.status_code}")
            
            if login_response.status_code == 200:
                print("✅ Connexion réussie")
                return True
            else:
                print(f"❌ Erreur de connexion: {login_response.text}")
                return False
        else:
            print(f"❌ API non disponible: {health_response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

if __name__ == "__main__":
    success = test_simple()
    print(f"\n{'='*30}")
    if success:
        print("✅ API fonctionnelle")
    else:
        print("❌ Problème avec l'API")
