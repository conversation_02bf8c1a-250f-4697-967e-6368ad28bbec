# Analyse Technique - Base de Données ORBIS

## Vue d'ensemble de l'Architecture

### Structure Multi-tenant
- **Workspace** : Entité principale pour l'isolation des données
- **EntrepriseTiers** : Carnet d'adresses des entreprises tierces par workspace
- **Project** : Projets liés aux workspaces avec associations aux entreprises tierces
- **User** : Utilisateurs avec rôles système et permissions par workspace

## Analyse des Modèles SQLAlchemy

### ✅ Points Forts

1. **Cohérence des noms de colonnes**
   - Convention snake_case respectée
   - Noms explicites et cohérents
   - Index appropriés sur les clés étrangères

2. **Relations bien définies**
   - Relations bidirectionnelles correctement configurées
   - Cascade appropriées pour la suppression
   - Foreign keys avec contraintes

3. **Métadonnées complètes**
   - `created_at` et `updated_at` sur toutes les entités principales
   - Champs `is_active` pour la suppression logique
   - Audit trail avec `created_by`

### ⚠️ Points d'Amélioration

1. **Incohérences dans les noms de colonnes**
   ```python
   # EntrepriseTiers utilise des noms français
   nom_entreprise = Column(String(255))
   
   # Alors que d'autres modèles utilisent l'anglais
   name = Column(String)
   ```

2. **Types de données inconsistants**
   ```python
   # Longueurs de String non spécifiées dans certains modèles
   name = Column(String)  # Devrait être String(255)
   
   # Précision des Decimal non uniforme
   budget_total = Column(Numeric(15, 2))
   hourly_rate = Column(Numeric(10, 2))
   ```

3. **Contraintes manquantes**
   ```python
   # Manque de contraintes unique composites
   # Par exemple : (workspace_id, code) pour les projets
   ```

## Analyse des Schémas Pydantic

### ✅ Points Forts

1. **Validation robuste**
   - Validators personnalisés pour les mots de passe
   - Validation des emails avec EmailStr
   - Contraintes de longueur appropriées

2. **Hiérarchie claire**
   - Base, Create, Update, Response patterns
   - Config from_attributes correctement configuré

3. **Types optionnels appropriés**
   - Distinction claire entre champs requis et optionnels

### ⚠️ Points d'Amélioration

1. **Incohérence avec les modèles SQLAlchemy**
   ```python
   # Schéma Project utilise 'entreprise_tiers_id'
   # Mais le modèle Project n'a pas ce champ direct
   ```

2. **Validators manquants**
   ```python
   # Pas de validation pour SIRET, téléphone, etc.
   # Pas de validation des codes postaux français
   ```

3. **Propriétés calculées**
   ```python
   # primary_company_id devrait être une propriété calculée
   # plutôt qu'un champ dans le schéma
   ```

## Recommandations d'Amélioration

### 1. Standardisation des noms
```python
# Remplacer dans EntrepriseTiers
company_name = Column(String(255), nullable=False, index=True)  # au lieu de nom_entreprise
activity = Column(String(255), nullable=True)  # au lieu de activite
```

### 2. Contraintes de base de données
```python
# Ajouter dans Project
__table_args__ = (
    UniqueConstraint('workspace_id', 'code', name='unique_workspace_project_code'),
)
```

### 3. Validation Pydantic améliorée
```python
@validator('siret')
def validate_siret(cls, v):
    if v and not re.match(r'^\d{14}$', v):
        raise ValueError('SIRET must be 14 digits')
    return v
```

### 4. Types de données standardisés
```python
# Standardiser les longueurs de String
name = Column(String(255), nullable=False)
code = Column(String(50), nullable=False)
email = Column(String(320), nullable=True)  # RFC 5321 max length
```

## Structure des Tests CRUD Recommandée

### 1. Tests par entité
- `test_user_crud.py`
- `test_workspace_crud.py`
- `test_project_crud.py`
- `test_entreprise_tiers_crud.py`

### 2. Tests d'intégration
- `test_multi_tenant_isolation.py`
- `test_permissions_rbac.py`
- `test_project_company_associations.py`

### 3. Tests de validation
- `test_pydantic_validation.py`
- `test_database_constraints.py`

## Authentification Supabase

### Architecture actuelle
1. **Supabase Auth** : Gestion de l'authentification
2. **JWT Token** : Transmission des permissions
3. **Local User Model** : Stockage des données utilisateur étendues
4. **RBAC System** : Gestion des permissions par workspace

### Points de vigilance
- Synchronisation entre Supabase et base locale
- Gestion des tokens expirés
- Isolation des données par workspace dans les JWT

## Prochaines Étapes

1. **Correction des incohérences** identifiées
2. **Implémentation des tests CRUD** complets
3. **Validation de l'isolation multi-tenant**
4. **Tests de performance** sur les requêtes complexes
5. **Documentation API** mise à jour
