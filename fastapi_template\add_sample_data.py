#!/usr/bin/env python3
"""
Script simple pour ajouter des données d'exemple à ORBIS
"""

import asyncio
import asyncpg
from datetime import datetime
import sys
from app.core.config import settings

DATABASE_URL = settings.DATABASE_URL


async def add_sample_data():
    """Ajouter des données d'exemple"""
    print("📊 Ajout de données d'exemple pour ORBIS...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Récupérer l'ID de la société ORBIS
        company_id = await conn.fetchval("SELECT id FROM companies WHERE name = 'ORBIS'")
        if not company_id:
            print("❌ Société ORBIS non trouvée")
            return False
        
        print(f"   ✅ Société ORBIS trouvée (ID: {company_id})")
        
        # Vérifier et créer les paramètres de la société (si la table existe)
        try:
            settings_exists = await conn.fetchval("SELECT id FROM company_settings WHERE company_id = $1", company_id)
            if not settings_exists:
                await conn.execute("""
                    INSERT INTO company_settings (company_id, created_at, updated_at)
                    VALUES ($1, $2, $3)
                """,
                company_id, datetime.utcnow(), datetime.utcnow()
                )
                print("   ✅ Paramètres de société créés")
        except:
            print("   ⚠️ Table company_settings non trouvée, ignorée")
        
        # Vérifier si des projets existent déjà
        existing_projects = await conn.fetchval("SELECT COUNT(*) FROM projects WHERE company_id = $1", company_id)
        if existing_projects > 0:
            print(f"   ✅ {existing_projects} projets existent déjà")
        else:
            # Créer des projets d'exemple
            projects_data = [
                ("Rénovation Bureau Central", "ORBIS-2024-001", "Rénovation complète des bureaux centraux", "EXE", 150000.00, "Entreprise ABC"),
                ("Construction Entrepôt", "ORBIS-2024-002", "Construction d'un nouvel entrepôt logistique", "DAO", 300000.00, "Logistique XYZ"),
                ("Aménagement Showroom", "ORBIS-2024-003", "Aménagement d'un showroom moderne", "COMPLETED", 80000.00, "Retail Pro")
            ]
            
            for name, code, description, status, budget, client in projects_data:
                project_id = await conn.fetchval("""
                    INSERT INTO projects (
                        name, code, description, status, budget_total, company_id, 
                        start_date, created_at, updated_at, client_name
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    RETURNING id
                """,
                name, code, description, status, budget, company_id,
                datetime.utcnow(), datetime.utcnow(), datetime.utcnow(), client
                )
                print(f"   ✅ Projet créé: {name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Ajout de données d'exemple")
    print("="*50)
    
    if await add_sample_data():
        print("\n✅ Données d'exemple ajoutées avec succès!")
        print("\n📋 Configuration actuelle:")
        print("   - Société: ORBIS")
        print("   - Projets d'exemple: 3")
        print("   - Paramètres: Configurés")
        
        print("\n🔑 Prochaines étapes pour l'authentification:")
        print("   1. Ouvrir Supabase Dashboard: https://supabase.com/dashboard")
        print("   2. Aller dans Authentication > Users")
        print("   3. Cliquer 'Add user' > 'Create new user'")
        print("   4. Email: <EMAIL>")
        print("   5. Mot de passe: orbis123!")
        print("   6. Confirmer l'email automatiquement")
        
        print("\n💡 Pourquoi Supabase Auth est parfait:")
        print("   ✅ Gestion complète des utilisateurs")
        print("   ✅ Sécurité intégrée (JWT, hashing, etc.)")
        print("   ✅ Scalable jusqu'à 50,000 utilisateurs gratuits")
        print("   ✅ Fonctionnalités avancées (MFA, social login)")
        print("   ✅ Pas de code d'auth à maintenir")
        
        return True
    else:
        print("\n❌ Échec de l'ajout des données")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
