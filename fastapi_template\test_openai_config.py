#!/usr/bin/env python3
"""
Script pour tester la configuration OpenAI
"""

import asyncio
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.chatgpt_service import ChatGPTService
from app.models.document import DocumentType

async def test_openai_configuration():
    """Tester la configuration OpenAI"""
    
    print("🔧 Test de la configuration OpenAI...")
    
    # Initialiser le service
    chatgpt_service = ChatGPTService()
    
    # Vérifier le statut de configuration
    config_status = chatgpt_service.get_configuration_status()
    print(f"📊 Statut de configuration:")
    print(f"   ✅ Configuré: {config_status['configured']}")
    print(f"   🔑 Clé API présente: {config_status['api_key_present']}")
    print(f"   🤖 Modèle: {config_status['model']}")
    print(f"   🔌 Client initialisé: {config_status['client_initialized']}")
    
    if not config_status['configured']:
        print("❌ Service ChatGPT non configuré")
        print("💡 Pour configurer:")
        print("   1. Obtenez une clé API sur https://platform.openai.com/api-keys")
        print("   2. Ajoutez OPENAI_API_KEY=votre-clé dans le fichier .env")
        print("   3. Redémarrez le serveur")
        return False
    
    # Test simple d'amélioration de texte
    print("\n🧪 Test d'amélioration de texte...")
    test_text = "Pose de carrelage dans la cuisine"
    
    try:
        result = await chatgpt_service.enhance_text(
            text=test_text,
            prompt_type="Ameliore",
            document_type=DocumentType.CCTP,
            context="Rénovation d'appartement"
        )
        
        print(f"✅ Test réussi!")
        print(f"   📝 Texte original: {test_text}")
        print(f"   ✨ Texte amélioré: {result['enhanced_text'][:100]}...")
        print(f"   ⏱️  Temps de traitement: {result['processing_time']:.2f}s")
        print(f"   🎯 Modèle utilisé: {result['model_used']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        return False

if __name__ == "__main__":
    print("🤖 Test de configuration OpenAI ChatGPT...")
    success = asyncio.run(test_openai_configuration())
    
    if success:
        print("\n✅ Configuration OpenAI fonctionnelle!")
    else:
        print("\n❌ Problème de configuration OpenAI")
    
    print("🏁 Test terminé!")
