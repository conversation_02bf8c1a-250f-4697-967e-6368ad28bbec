#!/usr/bin/env python3
"""
Script simple de peuplement de la base de données ORBIS
Utilise asyncpg directement pour éviter les problèmes de cache SQLAlchemy
"""

import asyncio
import asyncpg
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Configuration directe
ASYNC_DATABASE_URL = "**************************************************************************************************/postgres"


async def create_sample_data():
    """Créer des données d'exemple"""
    print("🌱 Création des données d'exemple...")
    
    # Connexion avec statement_cache_size=0 pour éviter les problèmes pgbouncer
    conn = await asyncpg.connect(ASYNC_DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Créer une entreprise
        print("   📊 Création d'une entreprise...")
        company_id = await conn.fetchval("""
            INSERT INTO companies (name, code, siret, address, phone, email, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id
        """,
        "EITP Construction",
        "EITP001",
        "12345678901234",
        "123 Rue de la Construction, 75001 Paris",
        "***********.89",
        "<EMAIL>",
        datetime.utcnow(),
        datetime.utcnow()
        )
        
        print(f"   ✅ Entreprise créée avec l'ID: {company_id}")
        
        # 2. Créer des utilisateurs
        print("   👥 Création des utilisateurs...")

        # Hash simple pour les mots de passe (en production, utiliser bcrypt)
        users_data = [
            ("<EMAIL>", "Admin", "EITP", "admin123", "ADMIN"),
            ("<EMAIL>", "Chef", "Projet", "chef123", "CHEF_PROJET"),
            ("<EMAIL>", "Conducteur", "Travaux", "conducteur123", "EMPLOYE"),
            ("<EMAIL>", "Ouvrier", "Qualifié", "ouvrier123", "EMPLOYE")
        ]

        user_ids = []
        for email, first_name, last_name, password, role in users_data:
            user_id = await conn.fetchval("""
                INSERT INTO users (email, first_name, last_name, hashed_password, role, is_active, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING id
            """,
            email, first_name, last_name, f"hashed_{password}", role, True, datetime.utcnow(), datetime.utcnow()
            )
            user_ids.append(user_id)
            print(f"   ✅ Utilisateur créé: {first_name} {last_name} ({email})")
        
        # 3. Associer les utilisateurs à l'entreprise
        print("   🔗 Association des utilisateurs à l'entreprise...")
        for user_id in user_ids:
            await conn.execute("""
                INSERT INTO user_companies (user_id, company_id, is_default, created_at)
                VALUES ($1, $2, $3, $4)
            """, user_id, company_id, True, datetime.utcnow())
        
        # 4. Créer des projets
        print("   🏗️ Création des projets...")

        projects_data = [
            ("Rénovation Immeuble Haussmann", "PROJ001", "Rénovation complète d'un immeuble haussmannien", "en_cours", 500000.00),
            ("Construction Villa Moderne", "PROJ002", "Construction d'une villa moderne avec piscine", "dao", 750000.00),
            ("Réhabilitation Bureaux", "PROJ003", "Réhabilitation d'un bâtiment de bureaux", "termine", 300000.00)
        ]

        project_ids = []
        for name, code, description, status, budget in projects_data:
            project_id = await conn.fetchval("""
                INSERT INTO projects (name, code, description, status, budget_total, company_id, start_date, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING id
            """,
            name, code, description, status, budget, company_id,
            datetime.utcnow() - timedelta(days=30), datetime.utcnow(), datetime.utcnow()
            )
            project_ids.append(project_id)
            print(f"   ✅ Projet créé: {name}")
        
        # 5. Créer des employés
        print("   👷 Création des employés...")

        employees_data = [
            ("EMP001", "Jean", "Dupont", "Maçon", 25.50),
            ("EMP002", "Marie", "Martin", "Électricienne", 28.00),
            ("EMP003", "Pierre", "Durand", "Plombier", 26.75),
            ("EMP004", "Sophie", "Leroy", "Peintre", 24.00)
        ]

        for employee_number, first_name, last_name, position, hourly_rate in employees_data:
            employee_id = await conn.fetchval("""
                INSERT INTO employees (employee_number, first_name, last_name, position, hourly_rate, company_id, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING id
            """,
            employee_number, first_name, last_name, position, hourly_rate, company_id, datetime.utcnow(), datetime.utcnow()
            )
            print(f"   ✅ Employé créé: {first_name} {last_name} ({position})")
        
        # 6. Créer des fournisseurs
        print("   🏪 Création des fournisseurs...")

        suppliers_data = [
            ("Matériaux Pro", "SUPP001", "Fournisseur de matériaux de construction", "***********.44", "<EMAIL>"),
            ("Électro Services", "SUPP002", "Matériel électrique professionnel", "***********.88", "<EMAIL>"),
            ("Plomberie Expert", "SUPP003", "Équipements de plomberie", "***********.66", "<EMAIL>")
        ]

        for name, code, type_supplier, phone, email in suppliers_data:
            supplier_id = await conn.fetchval("""
                INSERT INTO suppliers (name, code, type, phone, email, company_id, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING id
            """,
            name, code, "supplier", phone, email, company_id, datetime.utcnow(), datetime.utcnow()
            )
            print(f"   ✅ Fournisseur créé: {name}")
        
        print("\n🎉 Données d'exemple créées avec succès!")
        print("\n📋 Résumé:")
        print(f"   - 1 entreprise: EITP Construction")
        print(f"   - {len(users_data)} utilisateurs")
        print(f"   - {len(projects_data)} projets")
        print(f"   - {len(employees_data)} employés")
        print(f"   - {len(suppliers_data)} fournisseurs")
        
        print("\n🔑 Identifiants de test:")
        for email, first_name, last_name, password, role in users_data:
            print(f"   - {email} / {password} ({role})")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des données: {e}")
        raise
    finally:
        await conn.close()


async def check_existing_data():
    """Vérifier les données existantes"""
    print("🔍 Vérification des données existantes...")
    
    conn = await asyncpg.connect(ASYNC_DATABASE_URL, statement_cache_size=0)
    
    try:
        # Compter les enregistrements dans les tables principales
        tables = ['companies', 'users', 'projects', 'employees', 'suppliers']
        
        for table in tables:
            count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
            print(f"   📊 {table}: {count} enregistrements")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Peuplement Simple de la Base de Données")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # Vérifier les données existantes
    if not await check_existing_data():
        return False
    
    # Demander confirmation si des données existent déjà
    try:
        conn = await asyncpg.connect(ASYNC_DATABASE_URL, statement_cache_size=0)
        company_count = await conn.fetchval("SELECT COUNT(*) FROM companies")
        await conn.close()
        
        if company_count > 0:
            print(f"\n⚠️  La base de données contient déjà {company_count} entreprise(s).")
            print("   Les nouvelles données seront ajoutées aux données existantes.")
    except:
        pass
    
    # Créer les données d'exemple
    try:
        await create_sample_data()
        
        print("\n" + "="*60)
        print("✅ Peuplement terminé avec succès!")
        
        # Vérification finale
        await check_existing_data()
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur durant le peuplement: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
