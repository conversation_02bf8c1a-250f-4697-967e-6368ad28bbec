#!/usr/bin/env python3
"""
Script pour appliquer la migration des stakeholders en mode synchrone
Utilise SQLAlchemy synchrone pour éviter les problèmes d'async
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

def create_sync_engine():
    """Crée un engine SQLAlchemy synchrone"""
    # Convertir l'URL async en URL sync
    sync_url = settings.ASYNC_DATABASE_URL.replace("+asyncpg", "")
    
    engine = create_engine(
        sync_url,
        echo=False,
        pool_pre_ping=True,
        pool_recycle=60,
        pool_size=1,
        max_overflow=0
    )
    return engine

def check_table_exists(session, table_name):
    """Vérifie si une table existe"""
    try:
        result = session.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = :table_name
            );
        """), {"table_name": table_name})
        return result.fetchone()[0]
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de la table {table_name}: {e}")
        return False

def apply_stakeholders_migration():
    """Applique la migration des stakeholders"""
    print("🚀 Application de la migration stakeholders en mode synchrone...")
    
    engine = create_sync_engine()
    SessionLocal = sessionmaker(bind=engine)
    
    try:
        with SessionLocal() as session:
            # Vérifier si la table lot_intervenants existe
            if not check_table_exists(session, 'lot_intervenants'):
                print("⚠️  Table lot_intervenants n'existe pas, rien à migrer")
                return True
            
            # Vérifier si la table stakeholders existe déjà
            if check_table_exists(session, 'stakeholders'):
                print("✅ Table stakeholders existe déjà")
                return True
            
            print("📝 Renommage de la table lot_intervenants en stakeholders...")
            session.execute(text('ALTER TABLE lot_intervenants RENAME TO stakeholders'))
            
            print("📝 Renommage des index...")
            # Renommer les index (ignorer les erreurs si ils n'existent pas)
            try:
                session.execute(text('ALTER INDEX ix_lot_intervenants_id RENAME TO ix_stakeholders_id'))
                print("✅ Index ix_stakeholders_id renommé")
            except Exception as e:
                print(f"⚠️  Index ix_lot_intervenants_id: {e}")
            
            try:
                session.execute(text('ALTER INDEX ix_lot_intervenants_lot_id RENAME TO ix_stakeholders_lot_id'))
                print("✅ Index ix_stakeholders_lot_id renommé")
            except Exception as e:
                print(f"⚠️  Index ix_lot_intervenants_lot_id: {e}")
            
            try:
                session.execute(text('ALTER INDEX ix_lot_intervenants_company_id RENAME TO ix_stakeholders_company_id'))
                print("✅ Index ix_stakeholders_company_id renommé")
            except Exception as e:
                print(f"⚠️  Index ix_lot_intervenants_company_id: {e}")
            
            print("📝 Mise à jour des contraintes de clés étrangères...")
            # Supprimer les anciennes contraintes (ignorer les erreurs si elles n'existent pas)
            try:
                session.execute(text('ALTER TABLE stakeholders DROP CONSTRAINT IF EXISTS lot_intervenants_lot_id_fkey'))
                session.execute(text('ALTER TABLE stakeholders DROP CONSTRAINT IF EXISTS lot_intervenants_company_id_fkey'))
                session.execute(text('ALTER TABLE stakeholders DROP CONSTRAINT IF EXISTS lot_intervenants_created_by_fkey'))
                print("✅ Anciennes contraintes supprimées")
            except Exception as e:
                print(f"⚠️  Suppression contraintes: {e}")
            
            # Recréer les contraintes avec les nouveaux noms
            try:
                session.execute(text('''
                    ALTER TABLE stakeholders 
                    ADD CONSTRAINT stakeholders_lot_id_fkey 
                    FOREIGN KEY (lot_id) REFERENCES lots(id)
                '''))
                print("✅ Contrainte stakeholders_lot_id_fkey créée")
            except Exception as e:
                print(f"⚠️  Contrainte lot_id: {e}")
            
            try:
                session.execute(text('''
                    ALTER TABLE stakeholders 
                    ADD CONSTRAINT stakeholders_company_id_fkey 
                    FOREIGN KEY (company_id) REFERENCES tcompanies(id)
                '''))
                print("✅ Contrainte stakeholders_company_id_fkey créée")
            except Exception as e:
                print(f"⚠️  Contrainte company_id: {e}")
            
            try:
                session.execute(text('''
                    ALTER TABLE stakeholders 
                    ADD CONSTRAINT stakeholders_created_by_fkey 
                    FOREIGN KEY (created_by) REFERENCES users(id)
                '''))
                print("✅ Contrainte stakeholders_created_by_fkey créée")
            except Exception as e:
                print(f"⚠️  Contrainte created_by: {e}")
            
            # Valider les changements
            session.commit()
            print("✅ Migration appliquée avec succès")
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de la migration: {e}")
        return False

def verify_migration():
    """Vérifie que la migration a été appliquée correctement"""
    print("\n🔍 Vérification de la migration...")
    
    engine = create_sync_engine()
    SessionLocal = sessionmaker(bind=engine)
    
    try:
        with SessionLocal() as session:
            # Vérifier que la table stakeholders existe
            if not check_table_exists(session, 'stakeholders'):
                print("❌ Table 'stakeholders' non trouvée")
                return False
            
            print("✅ Table 'stakeholders' trouvée")
            
            # Vérifier que l'ancienne table lot_intervenants n'existe plus
            if check_table_exists(session, 'lot_intervenants'):
                print("⚠️  Ancienne table 'lot_intervenants' existe encore")
            else:
                print("✅ Ancienne table 'lot_intervenants' supprimée")
            
            # Compter les enregistrements
            result = session.execute(text("SELECT COUNT(*) FROM stakeholders"))
            count = result.fetchone()[0]
            print(f"✅ {count} enregistrements dans la table stakeholders")
            
            # Vérifier les colonnes
            result = session.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'stakeholders' 
                ORDER BY ordinal_position
            """))
            
            columns = [row[0] for row in result.fetchall()]
            expected_columns = ['id', 'lot_id', 'company_id', 'role', 'is_active', 'created_at', 'created_by']
            
            for expected in expected_columns:
                if expected in columns:
                    print(f"✅ Colonne '{expected}' trouvée")
                else:
                    print(f"❌ Colonne '{expected}' manquante")
            
            # Vérifier les contraintes
            result = session.execute(text("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_name = 'stakeholders' 
                AND constraint_type = 'FOREIGN KEY'
            """))
            
            constraints = [row[0] for row in result.fetchall()]
            expected_constraints = ['stakeholders_lot_id_fkey', 'stakeholders_company_id_fkey', 'stakeholders_created_by_fkey']
            
            for expected in expected_constraints:
                if expected in constraints:
                    print(f"✅ Contrainte '{expected}' trouvée")
                else:
                    print(f"⚠️  Contrainte '{expected}' manquante")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔄 Migration Synchrone des Stakeholders (lot_intervenants → stakeholders)")
    print("=" * 70)
    
    # Étape 1: Appliquer la migration
    if not apply_stakeholders_migration():
        print("❌ Échec de l'application de la migration")
        return False
    
    # Étape 2: Vérifier la migration
    if not verify_migration():
        print("❌ Échec de la vérification de la migration")
        return False
    
    print("\n" + "=" * 70)
    print("🎉 Migration des stakeholders terminée avec succès !")
    print("\nProchaines étapes:")
    print("1. Tester l'implémentation avec: python test_stakeholders_implementation.py")
    print("2. Mettre à jour le frontend pour utiliser les nouveaux endpoints")
    print("3. Supprimer les alias de compatibilité après validation complète")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
