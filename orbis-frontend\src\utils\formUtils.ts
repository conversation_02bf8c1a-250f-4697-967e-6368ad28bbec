/**
 * Utilitaires pour le traitement des formulaires
 */

/**
 * Nettoie les données de formulaire en remplaçant les chaînes vides par null
 * Cela évite les problèmes de contraintes uniques en base de données
 */
export function cleanFormData<T extends Record<string, any>>(formData: T): T {
  return Object.fromEntries(
    Object.entries(formData).map(([key, value]) => [
      key,
      typeof value === 'string' && value.trim() === '' ? null : value
    ])
  ) as T
}

/**
 * Valide les champs obligatoires d'un formulaire
 */
export function validateRequiredFields(
  data: Record<string, any>, 
  requiredFields: string[]
): { isValid: boolean; missingFields: string[] } {
  const missingFields = requiredFields.filter(field => {
    const value = data[field]
    return !value || (typeof value === 'string' && value.trim() === '')
  })

  return {
    isValid: missingFields.length === 0,
    missingFields
  }
}

/**
 * Formate les données pour l'affichage (remplace null par chaîne vide)
 */
export function formatForDisplay<T extends Record<string, any>>(data: T): T {
  return Object.fromEntries(
    Object.entries(data).map(([key, value]) => [
      key,
      value === null || value === undefined ? '' : value
    ])
  ) as T
}
