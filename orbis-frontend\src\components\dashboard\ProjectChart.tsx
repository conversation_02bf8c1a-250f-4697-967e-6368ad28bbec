'use client'

import React from 'react'

export const ProjectChart: React.FC = () => {
  const data = [
    { month: 'Jan', active: 8, completed: 2 },
    { month: 'Fév', active: 10, completed: 4 },
    { month: 'Mar', active: 12, completed: 6 },
    { month: 'Avr', active: 15, completed: 8 },
    { month: 'Mai', active: 12, completed: 10 },
    { month: 'Jun', active: 14, completed: 12 }
  ]

  const maxValue = Math.max(...data.map(d => Math.max(d.active, d.completed)))

  return (
    <div className="h-64 flex items-end justify-between space-x-2">
      {data.map((item, index) => (
        <div key={index} className="flex-1 flex flex-col items-center">
          <div className="flex flex-col items-center space-y-1 mb-2">
            <div
              className="w-8 bg-blue-500 rounded-t"
              style={{
                height: `${(item.active / maxValue) * 120}px`,
                minHeight: '4px'
              }}
            />
            <div
              className="w-8 bg-green-500 rounded-t"
              style={{
                height: `${(item.completed / maxValue) * 120}px`,
                minHeight: '4px'
              }}
            />
          </div>
          <div className="text-xs text-gray-600 text-center">
            {item.month}
          </div>
        </div>
      ))}
      <div className="ml-4 space-y-2">
        <div className="flex items-center text-xs">
          <div className="w-3 h-3 bg-blue-500 rounded mr-2" />
          <span>Actifs</span>
        </div>
        <div className="flex items-center text-xs">
          <div className="w-3 h-3 bg-green-500 rounded mr-2" />
          <span>Terminés</span>
        </div>
      </div>
    </div>
  )
}