'use client'

import React from 'react'
import { Editor } from '@tiptap/react'

interface FloatingMenuProps {
  editor: Editor
  onAddArticle: () => void
  onEnhanceText: () => void
  selectedText: string
}

export default function FloatingMenu({ 
  editor, 
  onAddArticle, 
  onEnhanceText, 
  selectedText 
}: FloatingMenuProps) {
  const [position, setPosition] = React.useState({ x: 0, y: 0 })
  const [isVisible, setIsVisible] = React.useState(false)

  React.useEffect(() => {
    const updatePosition = () => {
      const { selection } = editor.state
      const { from, to } = selection
      
      if (from === to) {
        // Pas de sélection, cacher le menu
        setIsVisible(false)
        return
      }

      // Calculer la position du menu basée sur la sélection
      const start = editor.view.coordsAtPos(from)
      const end = editor.view.coordsAtPos(to)
      
      const x = (start.left + end.left) / 2
      const y = start.top - 60 // Au-dessus de la sélection
      
      setPosition({ x, y })
      setIsVisible(true)
    }

    // Écouter les changements de sélection
    const handleSelectionUpdate = () => {
      setTimeout(updatePosition, 10) // Petit délai pour laisser le DOM se mettre à jour
    }

    editor.on('selectionUpdate', handleSelectionUpdate)
    editor.on('update', handleSelectionUpdate)

    return () => {
      editor.off('selectionUpdate', handleSelectionUpdate)
      editor.off('update', handleSelectionUpdate)
    }
  }, [editor])

  // Cacher le menu si on clique ailleurs
  React.useEffect(() => {
    const handleClickOutside = () => {
      setIsVisible(false)
    }

    if (isVisible) {
      document.addEventListener('click', handleClickOutside)
      return () => document.removeEventListener('click', handleClickOutside)
    }
  }, [isVisible])

  if (!isVisible) return null

  return (
    <div
      className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-2 flex space-x-2"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translateX(-50%)'
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {/* Bouton Ajouter un article */}
      <button
        onClick={() => {
          onAddArticle()
          setIsVisible(false)
        }}
        className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        title="Ajouter un article CCTP"
      >
        📝 Article
      </button>

      {/* Bouton Améliorer le texte (si du texte est sélectionné) */}
      {selectedText && selectedText.trim().length > 0 && (
        <button
          onClick={() => {
            onEnhanceText()
            setIsVisible(false)
          }}
          className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
          title="Améliorer le texte avec l'IA"
        >
          ✨ Améliorer
        </button>
      )}
    </div>
  )
}
