'use client'

import { useState } from 'react';
import { Lot, LotPhase, PHASE_LABELS, PHASE_ORDER, canValidatePhase, getNextPhase } from '@/types/lot';
import { Button } from '@/components/ui/Button';
import { lotsApiService } from '@/lib/api/lots';

interface LotPhaseTrackerProps {
  lot: Lot;
  onPhaseUpdate?: (updatedLot: Lot) => void;
  readOnly?: boolean;
}

export default function LotPhaseTracker({ lot, onPhaseUpdate, readOnly = false }: LotPhaseTrackerProps) {
  const [loading, setLoading] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handlePhaseValidation = async (phase: LotPhase, validated: boolean) => {
    if (readOnly) return;
    
    setLoading(phase);
    setError(null);

    try {
      const updatedLot = await lotsApiService.validateLotPhase(lot.id, {
        phase,
        validated
      });
      
      onPhaseUpdate?.(updatedLot);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la validation');
    } finally {
      setLoading(null);
    }
  };

  const getPhaseStatus = (phase: LotPhase) => {
    switch (phase) {
      case LotPhase.ESQ:
        return lot.esq_validated;
      case LotPhase.APD:
        return lot.apd_validated;
      case LotPhase.PRODCE:
        return lot.prodce_validated;
      case LotPhase.EXE:
        return lot.exe_validated;
      default:
        return false;
    }
  };

  const getPhaseValidationDate = (phase: LotPhase) => {
    switch (phase) {
      case LotPhase.ESQ:
        return lot.esq_validated_at;
      case LotPhase.APD:
        return lot.apd_validated_at;
      case LotPhase.PRODCE:
        return lot.prodce_validated_at;
      case LotPhase.EXE:
        return lot.exe_validated_at;
      default:
        return null;
    }
  };

  const nextPhase = getNextPhase(lot);

  return (
    <div className="space-y-4">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Indicateur de phase actuelle */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-blue-900">Phase actuelle</h3>
            <p className="text-blue-700">{PHASE_LABELS[lot.current_phase]}</p>
          </div>
          {nextPhase && !readOnly && (
            <div className="text-right">
              <p className="text-sm text-blue-600 mb-2">Prochaine phase à valider:</p>
              <Button
                onClick={() => handlePhaseValidation(nextPhase, true)}
                disabled={!canValidatePhase(lot, nextPhase) || loading === nextPhase}
                className="bg-blue-600 hover:bg-blue-700"
                size="sm"
              >
                {loading === nextPhase ? 'Validation...' : `Valider ${PHASE_LABELS[nextPhase]}`}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Timeline des phases */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Progression des phases</h4>
        
        <div className="relative">
          {PHASE_ORDER.map((phase, index) => {
            const isValidated = getPhaseStatus(phase);
            const validationDate = getPhaseValidationDate(phase);
            const isCurrent = lot.current_phase === phase;
            const canValidate = canValidatePhase(lot, phase);
            const isLoading = loading === phase;

            return (
              <div key={phase} className="relative flex items-center">
                {/* Ligne de connexion */}
                {index < PHASE_ORDER.length - 1 && (
                  <div 
                    className={`absolute left-4 top-8 w-0.5 h-12 ${
                      isValidated ? 'bg-green-500' : 'bg-gray-300'
                    }`}
                  />
                )}

                {/* Cercle de phase */}
                <div className={`relative z-10 w-8 h-8 rounded-full flex items-center justify-center ${
                  isValidated 
                    ? 'bg-green-500 text-white' 
                    : isCurrent 
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-300 text-gray-600'
                }`}>
                  {isValidated ? (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <span className="text-xs font-medium">{index + 1}</span>
                  )}
                </div>

                {/* Contenu de la phase */}
                <div className="ml-4 flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className={`font-medium ${
                        isValidated ? 'text-green-700' : isCurrent ? 'text-blue-700' : 'text-gray-700'
                      }`}>
                        {PHASE_LABELS[phase]}
                      </h5>
                      {validationDate && (
                        <p className="text-sm text-gray-500">
                          Validée le {new Date(validationDate).toLocaleDateString('fr-FR')}
                        </p>
                      )}
                      {isCurrent && !isValidated && (
                        <p className="text-sm text-blue-600">Phase en cours</p>
                      )}
                    </div>

                    {/* Actions */}
                    {!readOnly && (
                      <div className="flex gap-2">
                        {isValidated ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePhaseValidation(phase, false)}
                            disabled={isLoading}
                            className="text-red-600 border-red-300 hover:bg-red-50"
                          >
                            {isLoading ? 'Annulation...' : 'Annuler'}
                          </Button>
                        ) : canValidate ? (
                          <Button
                            size="sm"
                            onClick={() => handlePhaseValidation(phase, true)}
                            disabled={isLoading}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            {isLoading ? 'Validation...' : 'Valider'}
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            disabled
                            variant="outline"
                            className="text-gray-400"
                          >
                            En attente
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Barre de progression globale */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Progression globale</span>
          <span className="text-sm font-medium text-gray-700">
            {PHASE_ORDER.filter(phase => getPhaseStatus(phase)).length} / {PHASE_ORDER.length} phases
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-green-500 h-2 rounded-full transition-all duration-300"
            style={{ 
              width: `${(PHASE_ORDER.filter(phase => getPhaseStatus(phase)).length / PHASE_ORDER.length) * 100}%` 
            }}
          />
        </div>
      </div>
    </div>
  );
}
