import '@testing-library/jest-dom'
import React from 'react'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return ''
  },
}))

// Mock environment variables
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:8000'
process.env.NEXT_PUBLIC_TINYMCE_API_KEY = 'test-api-key'

// Mock fetch globally
global.fetch = jest.fn()

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock

// Mock TinyMCE
jest.mock('@tinymce/tinymce-react', () => ({
  Editor: ({ onInit, onEditorChange, initialValue }) => {
    // Simulate editor initialization
    React.useEffect(() => {
      if (onInit) {
        const mockEditor = {
          getContent: () => initialValue || '',
          setContent: jest.fn(),
          focus: jest.fn(),
        }
        onInit({}, mockEditor)
      }
    }, [onInit, initialValue])

    return React.createElement('div', {
      'data-testid': 'tinymce-editor',
      onChange: (e) => onEditorChange && onEditorChange(e.target.value)
    })
  }
}))

// Suppress console warnings in tests
const originalError = console.error
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
})
