#!/usr/bin/env python3
"""
Script de validation rapide de la migration companies → workspaces
"""

import sys
import os
from pathlib import Path

# Ajouter les répertoires au path Python
sys.path.insert(0, str(Path(__file__).parent / "fastapi_template"))

def test_backend_imports():
    """Test rapide des imports backend"""
    print("🔧 Test des imports backend...")
    
    try:
        # Nouveaux modèles
        from app.models.workspace import Workspace, UserWorkspace, WorkspaceSettings
        print("   ✅ Modèles Workspace importés")
        
        # Nouveaux schémas
        from app.schemas.workspace import WorkspaceCreate, WorkspaceUpdate
        print("   ✅ Schémas Workspace importés")
        
        # Nouveaux endpoints
        from app.api.api_v1.endpoints import workspaces, admin_workspaces
        print("   ✅ Endpoints Workspace importés")
        
        # Alias de compatibilité
        from app.models.workspace import Company, UserCompany
        from app.schemas.workspace import CompanyCreate
        print("   ✅ Alias de compatibilité importés")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_frontend_files():
    """Test rapide des fichiers frontend"""
    print("\n🎨 Test des fichiers frontend...")
    
    required_files = [
        "orbis-admin/src/app/workspaces/page.tsx",
        "orbis-admin/src/app/workspaces/new/page.tsx", 
        "orbis-admin/src/components/WorkspaceForm.tsx",
        "orbis-admin/src/lib/fast-auth.ts"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {os.path.basename(file_path)}")
        else:
            print(f"   ❌ {file_path}: MANQUANT")
            all_exist = False
    
    return all_exist

def test_service_content():
    """Test du contenu du service"""
    print("\n🔧 Test du service FastWorkspaceService...")
    
    try:
        service_file = "orbis-admin/src/lib/fast-auth.ts"
        if not os.path.exists(service_file):
            print("   ❌ fast-auth.ts: MANQUANT")
            return False
        
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_methods = [
            'FastWorkspaceService',
            'getWorkspaces',
            'createWorkspace',
            'updateWorkspace',
            'deleteWorkspace'
        ]
        
        for method in required_methods:
            if method in content:
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method}: MANQUANT")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_navigation():
    """Test de la navigation"""
    print("\n🧭 Test de la navigation...")
    
    try:
        sidebar_file = "orbis-admin/src/components/layout/Sidebar.tsx"
        if not os.path.exists(sidebar_file):
            print("   ❌ Sidebar.tsx: MANQUANT")
            return False
        
        with open(sidebar_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'Espaces de travail' in content and '/workspaces' in content:
            print("   ✅ Navigation mise à jour")
            return True
        else:
            print("   ❌ Navigation non mise à jour")
            return False
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_api_routes():
    """Test des routes API"""
    print("\n🌐 Test des routes API...")
    
    try:
        from app.api.api_v1.api import api_router
        
        all_routes = [route.path for route in api_router.routes if hasattr(route, 'path')]
        
        workspace_routes = [route for route in all_routes if '/workspaces' in route]
        admin_workspace_routes = [route for route in all_routes if '/admin/workspaces' in route]
        
        print(f"   ✅ Routes /workspaces: {len(workspace_routes)}")
        print(f"   ✅ Routes /admin/workspaces: {len(admin_workspace_routes)}")
        
        return len(workspace_routes) > 0 and len(admin_workspace_routes) > 0
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_table_names():
    """Test des noms de tables"""
    print("\n📋 Test des noms de tables...")
    
    try:
        from app.models.workspace import Workspace, UserWorkspace, WorkspaceSettings
        from app.models.rbac import WorkspaceRolePermission
        
        expected_tables = {
            Workspace: "workspaces",
            UserWorkspace: "user_workspaces",
            WorkspaceSettings: "workspace_settings",
            WorkspaceRolePermission: "workspace_role_permissions"
        }
        
        for model, expected_table in expected_tables.items():
            actual_table = model.__tablename__
            if actual_table == expected_table:
                print(f"   ✅ {model.__name__}: {actual_table}")
            else:
                print(f"   ❌ {model.__name__}: attendu '{expected_table}', trouvé '{actual_table}'")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_compatibility_files():
    """Test des fichiers de compatibilité"""
    print("\n🔄 Test de compatibilité...")
    
    compatibility_files = [
        "orbis-admin/src/app/companies/page.tsx",
        "orbis-admin/src/components/CompanyForm.tsx"
    ]
    
    all_exist = True
    for file_path in compatibility_files:
        if os.path.exists(file_path):
            print(f"   ✅ {os.path.basename(file_path)} (compatibilité)")
        else:
            print(f"   ❌ {file_path}: MANQUANT")
            all_exist = False
    
    return all_exist

def main():
    """Fonction principale de validation"""
    print("🧪 VALIDATION RAPIDE DE LA MIGRATION COMPANIES → WORKSPACES")
    print("="*60)
    
    tests = [
        ("Imports backend", test_backend_imports),
        ("Fichiers frontend", test_frontend_files),
        ("Service FastWorkspaceService", test_service_content),
        ("Navigation", test_navigation),
        ("Routes API", test_api_routes),
        ("Noms de tables", test_table_names),
        ("Compatibilité", test_compatibility_files)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
            if result:
                print(f"✅ {test_name}: RÉUSSI")
            else:
                print(f"❌ {test_name}: ÉCHOUÉ")
        except Exception as e:
            print(f"💥 {test_name}: ERREUR - {e}")
            results.append(False)
    
    # Résumé
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 RÉSUMÉ: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 MIGRATION VALIDÉE AVEC SUCCÈS !")
        print("✅ Base de données: Tables workspaces créées")
        print("✅ Backend: Modèles, schémas et endpoints mis à jour")
        print("✅ Frontend: Composants et services créés")
        print("✅ Navigation: Mise à jour vers 'Espaces de travail'")
        print("✅ Compatibilité: Alias et anciens fichiers préservés")
        print("\n🚀 La migration companies → workspaces est TERMINÉE !")
        return True
    else:
        print(f"\n❌ VALIDATION INCOMPLÈTE")
        print(f"❌ {total - passed} test(s) ont échoué")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n📋 PROCHAINES ÉTAPES RECOMMANDÉES:")
        print("1. Tester l'application en démarrant le backend et le frontend")
        print("2. Vérifier que les nouveaux endpoints /workspaces fonctionnent")
        print("3. Tester la création/modification d'espaces de travail")
        print("4. Supprimer progressivement les alias de compatibilité")
    
    sys.exit(0 if success else 1)
