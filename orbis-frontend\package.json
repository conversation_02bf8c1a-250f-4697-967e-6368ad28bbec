{"name": "orbis-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@supabase/supabase-js": "^2.50.3", "@tinymce/tinymce-react": "^6.2.1", "@tiptap/extension-bullet-list": "^3.0.6", "@tiptap/extension-character-count": "^3.0.6", "@tiptap/extension-color": "^3.0.6", "@tiptap/extension-floating-menu": "^3.0.6", "@tiptap/extension-heading": "^3.0.6", "@tiptap/extension-highlight": "^3.0.6", "@tiptap/extension-link": "^3.0.6", "@tiptap/extension-list-item": "^3.0.6", "@tiptap/extension-ordered-list": "^3.0.6", "@tiptap/extension-table": "^3.0.6", "@tiptap/extension-table-cell": "^3.0.6", "@tiptap/extension-table-header": "^3.0.6", "@tiptap/extension-table-row": "^3.0.6", "@tiptap/extension-text-align": "^3.0.6", "@tiptap/extension-text-style": "^3.0.6", "@tiptap/extension-underline": "^3.0.6", "@tiptap/pm": "^3.0.6", "@tiptap/react": "^3.0.6", "@tiptap/starter-kit": "^3.0.6", "framer-motion": "^12.18.1", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "tinymce": "^7.9.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.3", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.5.5", "tailwindcss": "^3.4.0", "typescript": "^5"}}