'use client'

import React from 'react'
import { usePermissions, useProjectPermissions, useDocumentPermissions } from '@/hooks/usePermissions'
import { PermissionGuard, InsufficientRightsMessage, ProtectedButton, PermissionError } from '@/components/ui/PermissionGuard'
import { PERMISSIONS, PERMISSION_MESSAGES } from '@/lib/permissions'
import { Plus, Edit, Trash2, Eye, Download, Upload } from 'lucide-react'

export default function TestPermissionsPage() {
  const { 
    permissions, 
    loading, 
    error, 
    hasPermission, 
    isSuperAdmin, 
    isWorkspaceAdmin,
    refreshPermissions 
  } = usePermissions()

  const projectPerms = useProjectPermissions()
  const documentPerms = useDocumentPermissions()

  if (loading) {
    return (
      <div className="p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Chargement des permissions...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Erreur</h2>
          <p className="text-red-600">{error}</p>
          <button 
            onClick={refreshPermissions}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Réessayer
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Test des Permissions</h1>
        <p className="text-gray-600">
          Cette page permet de tester le système de permissions en temps réel.
        </p>
      </div>

      {/* Informations utilisateur */}
      <div className="bg-white rounded-lg shadow p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Informations utilisateur</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <span className="font-medium">Rôle:</span> 
            <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
              {permissions?.role || 'Non défini'}
            </span>
          </div>
          <div>
            <span className="font-medium">Super Admin:</span> 
            <span className={`ml-2 px-2 py-1 rounded text-sm ${isSuperAdmin ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
              {isSuperAdmin ? 'Oui' : 'Non'}
            </span>
          </div>
          <div>
            <span className="font-medium">Admin Workspace:</span> 
            <span className={`ml-2 px-2 py-1 rounded text-sm ${isWorkspaceAdmin ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
              {isWorkspaceAdmin ? 'Oui' : 'Non'}
            </span>
          </div>
        </div>
        <div className="mt-4">
          <span className="font-medium">Permissions ({permissions?.permissions.length || 0}):</span>
          <div className="mt-2 flex flex-wrap gap-1">
            {permissions?.permissions.map(permission => (
              <span key={permission} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                {permission}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Test des boutons protégés */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        {/* Projets */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Projets</h3>
          <div className="space-y-3">
            <ProtectedButton
              permission={PERMISSIONS.PROJECTS.CREATE}
              className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Créer un projet
            </ProtectedButton>

            <ProtectedButton
              permission={PERMISSIONS.PROJECTS.UPDATE}
              className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              <Edit className="h-4 w-4 mr-2" />
              Modifier un projet
            </ProtectedButton>

            <ProtectedButton
              permission={PERMISSIONS.PROJECTS.DELETE}
              className="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Supprimer un projet
            </ProtectedButton>

            <ProtectedButton
              permission={PERMISSIONS.PROJECTS.VIEW_FINANCIAL}
              className="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
            >
              <Eye className="h-4 w-4 mr-2" />
              Voir les finances
            </ProtectedButton>
          </div>

          <div className="mt-4 p-3 bg-gray-50 rounded">
            <h4 className="font-medium text-sm mb-2">Statut des permissions:</h4>
            <div className="text-xs space-y-1">
              <div>Créer: <span className={projectPerms.canCreate ? 'text-green-600' : 'text-red-600'}>{projectPerms.canCreate ? '✓' : '✗'}</span></div>
              <div>Lire: <span className={projectPerms.canRead ? 'text-green-600' : 'text-red-600'}>{projectPerms.canRead ? '✓' : '✗'}</span></div>
              <div>Modifier: <span className={projectPerms.canUpdate ? 'text-green-600' : 'text-red-600'}>{projectPerms.canUpdate ? '✓' : '✗'}</span></div>
              <div>Supprimer: <span className={projectPerms.canDelete ? 'text-green-600' : 'text-red-600'}>{projectPerms.canDelete ? '✓' : '✗'}</span></div>
            </div>
          </div>
        </div>

        {/* Documents */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Documents</h3>
          <div className="space-y-3">
            <ProtectedButton
              permission={PERMISSIONS.DOCUMENTS.CREATE}
              className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Créer un document
            </ProtectedButton>

            <ProtectedButton
              permission={PERMISSIONS.DOCUMENTS.UPLOAD}
              className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              <Upload className="h-4 w-4 mr-2" />
              Uploader un document
            </ProtectedButton>

            <ProtectedButton
              permission={PERMISSIONS.DOCUMENTS.DOWNLOAD}
              className="w-full flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 disabled:opacity-50"
            >
              <Download className="h-4 w-4 mr-2" />
              Télécharger un document
            </ProtectedButton>

            <ProtectedButton
              permission={PERMISSIONS.DOCUMENTS.DELETE}
              className="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Supprimer un document
            </ProtectedButton>
          </div>

          <div className="mt-4 p-3 bg-gray-50 rounded">
            <h4 className="font-medium text-sm mb-2">Statut des permissions:</h4>
            <div className="text-xs space-y-1">
              <div>Créer: <span className={documentPerms.canCreate ? 'text-green-600' : 'text-red-600'}>{documentPerms.canCreate ? '✓' : '✗'}</span></div>
              <div>Lire: <span className={documentPerms.canRead ? 'text-green-600' : 'text-red-600'}>{documentPerms.canRead ? '✓' : '✗'}</span></div>
              <div>Modifier: <span className={documentPerms.canUpdate ? 'text-green-600' : 'text-red-600'}>{documentPerms.canUpdate ? '✓' : '✗'}</span></div>
              <div>Supprimer: <span className={documentPerms.canDelete ? 'text-green-600' : 'text-red-600'}>{documentPerms.canDelete ? '✓' : '✗'}</span></div>
            </div>
          </div>
        </div>
      </div>

      {/* Test des composants de protection */}
      <div className="mt-8 space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Test des composants de protection</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Section protégée (permission: users.create)</h4>
              <PermissionGuard permission={PERMISSIONS.USERS.CREATE}>
                <div className="p-4 bg-green-50 border border-green-200 rounded">
                  <p className="text-green-800">✓ Vous avez accès à cette section protégée !</p>
                </div>
              </PermissionGuard>
            </div>

            <div>
              <h4 className="font-medium mb-2">Section protégée (permission: users.delete)</h4>
              <PermissionGuard permission={PERMISSIONS.USERS.DELETE}>
                <div className="p-4 bg-green-50 border border-green-200 rounded">
                  <p className="text-green-800">✓ Vous pouvez supprimer des utilisateurs !</p>
                </div>
              </PermissionGuard>
            </div>

            <div>
              <h4 className="font-medium mb-2">Message d'erreur compact</h4>
              <PermissionError variant="compact" message="Vous ne pouvez pas accéder à cette fonctionnalité." />
            </div>

            <div>
              <h4 className="font-medium mb-2">Message d'erreur inline</h4>
              <p>Cette action est restreinte: <PermissionError variant="inline" /></p>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="mt-8 flex justify-center">
        <button 
          onClick={refreshPermissions}
          className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Rafraîchir les permissions
        </button>
      </div>
    </div>
  )
}
