# 📋 Guide des Documents Techniques CCTP/DPGF

## 🎯 Vue d'ensemble

Ce module permet la création, l'édition et la gestion de documents techniques (CCTP et DPGF) avec amélioration par intelligence artificielle.

### ✨ Fonctionnalités principales

- **Éditeur WYSIWYG** : Interface d'édition riche avec TinyMCE
- **Types de documents** : Support CCTP et DPGF avec templates spécialisés
- **Amélioration IA** : 4 types d'amélioration avec ChatGPT (améliorer, reformuler, développer, simplifier)
- **Multi-entreprises** : Association de plusieurs entreprises par document
- **Sauvegarde automatique** : Sauvegarde toutes les 3 secondes
- **Menu contextuel** : Amélioration IA via clic droit sur texte sélectionné

## 🚀 Démarrage rapide

### 1. Installation des dépendances

```bash
# Backend
cd fastapi_template
pip install -r requirements.txt

# Frontend (TinyMCE déjà installé)
cd orbis-frontend
npm install
```

### 2. Configuration OpenAI

Modifiez le fichier `.env` à la racine du projet :

```bash
# OpenAI Configuration for Technical Documents Enhancement
OPENAI_API_KEY=sk-votre-cle-api-openai-ici
OPENAI_MODEL=gpt-3.5-turbo
```

> 🔑 **Important** : Obtenez votre clé API sur https://platform.openai.com/api-keys

### 3. Migration de la base de données

```bash
cd fastapi_template
python scripts/migrate_technical_documents.py
```

### 4. Configuration de l'utilisateur de test

```bash
python scripts/setup_test_user.py
```

Cela créera :
- Utilisateur : `<EMAIL>`
- Entreprise : ORBIS Construction
- 3 projets de test
- 3 entreprises tierces

### 5. Démarrage des services

```bash
# Terminal 1 - Backend
cd fastapi_template
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

# Terminal 2 - Frontend
cd orbis-frontend
npm run dev
```

### 6. Test du module

1. **Accédez au frontend** : http://localhost:3000
2. **Connectez-vous** avec `<EMAIL>` (mot de passe selon votre config)
3. **Accédez aux documents** : http://localhost:3000/documents-techniques
4. **Ou via le dashboard** : Cliquez sur "Documents CCTP/DPGF"

## 📖 Utilisation

### Création d'un document

1. Cliquez sur **"CCTP"** ou **"DPGF"** dans le gestionnaire de documents
2. Saisissez le nom du document
3. Cliquez sur **"Créer"**

### Édition avec l'IA

1. **Sélectionnez du texte** dans l'éditeur
2. **Clic droit** pour ouvrir le menu contextuel
3. **Choisissez une action** :
   - ✨ **Améliorer** : Améliore la qualité technique
   - 🔄 **Reformuler** : Reformule pour plus de clarté
   - 📝 **Développer** : Ajoute des détails techniques
   - 🎯 **Simplifier** : Simplifie en gardant l'essentiel

### Gestion des entreprises

1. Utilisez le **sélecteur d'entreprises** sous le nom du document
2. **Recherchez** et **sélectionnez** les entreprises à associer
3. Les modifications sont **sauvegardées automatiquement**

### Raccourcis clavier

- `Ctrl+Shift+A` : Améliorer le texte sélectionné
- `Ctrl+Shift+R` : Reformuler le texte sélectionné
- `Ctrl+Shift+E` : Développer le texte sélectionné
- `Ctrl+Shift+S` : Simplifier le texte sélectionné

## 🔧 Tests et validation

### Test complet du module

```bash
cd fastapi_template
python scripts/test_technical_documents.py
```

### Tests unitaires

```bash
pytest tests/test_technical_documents.py -v
```

### Vérification de l'API

Accédez à la documentation Swagger : http://localhost:8000/docs

Endpoints disponibles :
- `GET /api/v1/technical-documents/` - Liste des documents
- `POST /api/v1/technical-documents/` - Créer un document
- `GET /api/v1/technical-documents/{id}` - Récupérer un document
- `PUT /api/v1/technical-documents/{id}` - Modifier un document
- `DELETE /api/v1/technical-documents/{id}` - Supprimer un document
- `POST /api/v1/technical-documents/enhance-text` - Améliorer du texte

## 🏗️ Architecture technique

### Backend (FastAPI)

```
fastapi_template/
├── app/
│   ├── models/
│   │   └── document.py              # TechnicalDocument, DocumentType
│   ├── schemas/
│   │   └── technical_document.py    # Schémas Pydantic
│   ├── api/api_v1/endpoints/
│   │   └── technical_documents.py   # Endpoints API
│   └── services/
│       └── chatgpt_service.py       # Service OpenAI
└── scripts/
    ├── migrate_technical_documents.py
    ├── test_technical_documents.py
    └── setup_test_user.py
```

### Frontend (Next.js)

```
orbis-frontend/src/
├── app/
│   └── documents-techniques/
│       └── page.tsx                 # Page principale
├── components/
│   ├── editor/
│   │   └── TechnicalDocumentEditor.tsx
│   └── technical-documents/
│       ├── DocumentManager.tsx
│       ├── CompanySelector.tsx
│       └── ContextMenu.tsx
├── hooks/
│   ├── useTechnicalDocument.ts
│   └── useChatGPT.ts
└── types/
    └── technical-document.ts
```

## 🎨 Templates disponibles

### CCTP
- **Article standard** : Structure d'article avec spécifications techniques
- **Styles prédéfinis** : Spécification technique, Note importante, Avertissement

### DPGF
- **Poste standard** : Structure avec tableau de quantités et prix
- **Tableaux** : Formatage automatique pour les métrés

## 🔍 Dépannage

### Problèmes courants

1. **Erreur OpenAI** : Vérifiez que `OPENAI_API_KEY` est configurée
2. **Erreur de base de données** : Exécutez la migration
3. **Erreur d'authentification** : Vérifiez que l'utilisateur test existe
4. **Éditeur ne se charge pas** : Vérifiez que TinyMCE est installé

### Logs utiles

```bash
# Logs backend
tail -f fastapi_template/logs/app.log

# Logs frontend
# Consultez la console du navigateur (F12)
```

### Réinitialisation

Pour repartir de zéro :

```bash
# Supprimer les données de test
cd fastapi_template
python -c "
import asyncio
from app.core.database import get_async_engine
from sqlalchemy import text

async def cleanup():
    engine = get_async_engine()
    async with engine.begin() as conn:
        await conn.execute(text('DELETE FROM technical_document_companies'))
        await conn.execute(text('DELETE FROM technical_documents'))
        print('Données supprimées')
    await engine.dispose()

asyncio.run(cleanup())
"

# Recréer les données de test
python scripts/setup_test_user.py
```

## 📞 Support

Pour toute question ou problème :

1. Vérifiez les logs d'erreur
2. Consultez la documentation API : http://localhost:8000/docs
3. Exécutez les tests de validation
4. Vérifiez la configuration OpenAI

## 🎉 Prêt à utiliser !

Le module est maintenant configuré et prêt à être utilisé. Connectez-vous avec `<EMAIL>` et commencez à créer vos premiers documents techniques avec l'aide de l'IA !
