{"system_design": {"metadata": {"project_name": "ORBIS Suivi Travaux SAAS", "company": "ORBIS", "version": "1.0", "created_date": "2025-06-16", "architect": "<PERSON>", "document_type": "System Architecture Design"}, "project_overview": {"description": "Modern SAAS platform for construction project management designed for ORBIS company", "type": "Multi-tenant web application", "target_users": "Construction companies, project managers, site supervisors, employees", "core_purpose": "Digitize and streamline construction project tracking, financial management, and team collaboration"}, "implementation_approach": {"architecture_pattern": "Microservices with API Gateway", "development_methodology": "Agile with 4-phase delivery", "difficult_points": ["Multi-tenant data isolation for up to 10 companies", "Large dataset handling (999,999+ records per entity)", "Complex Excel DPGF import with flexible column mapping", "Real-time subcontractor validation with budget simulation", "Hierarchical document management with auto-folder creation", "Performance optimization for concurrent users"], "selected_frameworks": {"backend": {"primary": "FastAPI (Python 3.11+)", "orm": "SQLAlchemy 2.0 with async support", "authentication": "FastAPI-Users with JWT", "task_queue": "Celery with Redis broker", "data_processing": "Pandas for Excel handling", "pdf_generation": "ReportLab", "file_storage": "boto3 for S3 compatibility", "database_driver": "asyncpg for PostgreSQL", "migrations": "Alembic", "validation": "Pydantic v2"}, "frontend": {"framework": "Next.js 14 with App Router", "language": "TypeScript", "styling": "Tailwind CSS", "ui_components": "shadcn/ui", "forms": "React Hook Form + Zod validation", "state_management": "TanStack Query + Zustand", "data_tables": "TanStack Table v8", "charts": "Recharts", "file_upload": "react-dropzone"}, "database": {"primary": "PostgreSQL 15+", "cache": "Redis 7+", "extensions": ["pgvector for future AI features", "pg_stat_statements for monitoring"]}, "infrastructure": {"containerization": "Docker + <PERSON>er Compose", "reverse_proxy": "<PERSON><PERSON><PERSON>", "cloud_provider": "AWS (RDS, S3, CloudFront, EKS)", "ci_cd": "GitHub Actions", "monitoring": "Sentry + Prometheus + Grafana"}}}, "system_architecture": {"architecture_type": "3-tier with microservices", "layers": {"presentation_layer": {"technology": "Next.js 14 with TypeScript", "components": ["React Server Components for SEO", "Client Components for interactivity", "Responsive design with Tailwind CSS", "Progressive Web App capabilities"], "routing": "App Router with nested layouts", "state_management": "Server state (TanStack Query) + Client state (Zustand)"}, "application_layer": {"api_gateway": "FastAPI with automatic OpenAPI documentation", "services": ["AuthService - User authentication and authorization", "CompanyService - Multi-tenant company management", "ProjectService - Project lifecycle management", "EmployeeService - Employee and time tracking", "SupplierService - Supplier and subcontractor management", "MaterialService - Material catalog and technical sheets", "QuoteService - Quote creation and management", "PurchaseOrderService - Purchase order processing", "FinancialService - Budget and invoice management", "DocumentService - File storage and organization", "ExcelService - Import/export Excel functionality", "ReportService - Report generation and analytics"], "middleware": ["Authentication middleware", "Multi-tenant context middleware", "Request logging middleware", "Rate limiting middleware", "CORS middleware"]}, "data_layer": {"primary_database": "PostgreSQL with row-level security", "cache_layer": "Redis for sessions and query caching", "file_storage": "AWS S3 or compatible object storage", "backup_strategy": "Automated daily backups with point-in-time recovery"}}, "multi_tenant_strategy": {"approach": "Shared database with Row-Level Security (RLS)", "isolation_method": "company_id column with RLS policies", "scalability": "Option to migrate large tenants to dedicated schemas", "data_segregation": "Automatic filtering at database level", "security": "JWT tokens include company context"}}, "database_design": {"estimated_tables": 42, "key_entities": {"core_entities": ["users - User accounts and authentication", "companies - Internal company management (max 10)", "user_companies - Many-to-many user-company relationships", "company_settings - Company-specific configurations"], "project_management": ["projects - Construction projects/sites", "project_employees - Project team assignments", "project_documents - Document organization", "project_status_history - Status change tracking"], "employee_management": ["employees - Employee profiles and information", "time_entries - Time tracking and attendance", "employee_assignments - Project assignments"], "supplier_management": ["suppliers - Supplier and subcontractor registry", "supplier_contacts - Contact information", "supplier_ratings - Performance tracking"], "material_management": ["materials - Material catalog", "material_categories - Hierarchical categorization", "technical_sheets - Technical specifications (DAF)", "price_history - Historical pricing data"], "purchase_management": ["purchase_orders - Purchase order headers", "purchase_order_lines - Order line items", "deliveries - Delivery tracking", "delivery_lines - Delivered quantities"], "quote_management": ["quotes - Project quotes", "quote_lines - Quote line items", "quote_templates - Reusable templates"], "financial_management": ["budgets - Project budgets", "budget_lines - Budget line items", "invoices - Supplier invoices", "payments - Payment tracking", "financial_reports - Generated reports"], "document_management": ["documents - File metadata and organization", "document_versions - Version control", "document_folders - Folder structure"], "system_management": ["excel_imports - Import job tracking", "audit_logs - System audit trail", "notifications - User notifications", "reports - Generated reports metadata"]}, "indexing_strategy": {"multi_tenant_indexes": ["CREATE INDEX idx_projects_company_status ON projects(company_id, status, created_at)", "CREATE INDEX idx_employees_company_active ON employees(company_id, is_active, last_name)", "CREATE INDEX idx_time_entries_company_date ON time_entries(company_id, date, employee_id)"], "performance_indexes": ["CREATE INDEX idx_documents_project_category ON documents(project_id, category, upload_date)", "CREATE INDEX idx_purchase_orders_supplier_status ON purchase_orders(supplier_id, status, order_date)"]}, "partitioning_strategy": {"time_based_partitioning": ["time_entries - Monthly partitions", "audit_logs - Monthly partitions", "notifications - Monthly partitions"], "benefits": "Improved query performance and easier maintenance"}}, "api_design": {"rest_principles": "RESTful API following OpenAPI 3.0 specification", "base_url": "https://api.orbis-suivi-travaux.com/api/v1", "authentication": "Bearer JWT tokens with refresh mechanism", "rate_limiting": "100 requests per minute per user", "endpoint_categories": {"authentication": {"endpoints": ["POST /auth/login - User authentication", "POST /auth/refresh - Token refresh", "POST /auth/logout - User logout", "GET /auth/me - Current user info"]}, "company_management": {"endpoints": ["GET /companies - List user companies", "POST /companies - Create new company", "GET /companies/{id} - Get company details", "PUT /companies/{id} - Update company", "GET /companies/{id}/settings - Get company settings", "PUT /companies/{id}/settings - Update settings"]}, "project_management": {"endpoints": ["GET /projects - List projects with filtering", "POST /projects - Create new project", "GET /projects/{id} - Get project details", "PUT /projects/{id} - Update project", "GET /projects/{id}/dashboard - Project dashboard data", "POST /projects/{id}/employees - Assign employee", "DELETE /projects/{id}/employees/{employee_id} - Remove assignment"]}, "employee_management": {"endpoints": ["GET /employees - List employees", "POST /employees - Create employee", "GET /employees/{id} - Get employee details", "PUT /employees/{id} - Update employee", "POST /time-entries - Log time entry", "GET /employees/{id}/timesheet - Get employee timesheet"]}, "material_management": {"endpoints": ["GET /materials - List materials with search", "POST /materials - Create material", "GET /materials/{id} - Get material details", "POST /materials/{id}/technical-sheet - Add technical sheet", "GET /materials/categories - Get material categories"]}, "excel_integration": {"endpoints": ["POST /excel/import/dpgf - Import DPGF Excel file", "GET /excel/import/{id}/status - Check import status", "POST /excel/export/report - Export data to Excel", "GET /excel/templates/{type} - Download import templates"]}, "financial_management": {"endpoints": ["GET /budgets - List project budgets", "POST /budgets - Create budget", "GET /invoices - List invoices", "POST /invoices - Create invoice", "PUT /invoices/{id}/pay - Mark invoice as paid"]}, "document_management": {"endpoints": ["POST /documents/upload - Upload document", "GET /documents - List documents with filtering", "GET /documents/{id}/download - Download document", "DELETE /documents/{id} - Delete document", "POST /documents/{id}/version - Create new version"]}, "reporting": {"endpoints": ["POST /reports/generate - Generate report", "GET /reports - List generated reports", "GET /reports/{id}/download - Download report", "GET /analytics/dashboard - Dashboard analytics"]}}, "response_format": {"success_response": {"structure": "{ data: T, meta?: { pagination, total } }", "content_type": "application/json"}, "error_response": {"structure": "{ error: string, code: number, details?: any }", "http_status_codes": [400, 401, 403, 404, 422, 500]}}}, "frontend_architecture": {"framework_details": {"nextjs_version": "Next.js 14 with App Router", "typescript_version": "TypeScript 5.0+", "rendering_strategy": "Hybrid SSR/CSR with Server Components"}, "project_structure": {"app_directory": {"layout_structure": "Nested layouts with authentication boundaries", "route_groups": "(auth), (dashboard), (public)", "loading_ui": "Suspense boundaries with skeleton loaders", "error_handling": "Error boundaries with fallback UI"}, "components_organization": {"ui_components": "Reusable UI components with shadcn/ui", "layout_components": "Header, sidebar, navigation components", "form_components": "Form wrappers with validation", "chart_components": "Data visualization components", "table_components": "Advanced data tables with sorting/filtering"}, "state_management": {"server_state": "TanStack Query for API data caching", "client_state": "Zustand for UI state and user preferences", "form_state": "React Hook Form with <PERSON>od validation"}}, "key_features": {"responsive_design": "Mobile-first approach with Tailwind CSS", "accessibility": "WCAG 2.1 AA compliance with shadcn/ui", "internationalization": "French language with potential for expansion", "offline_capability": "Service worker for basic offline functionality", "performance_optimization": "Code splitting, lazy loading, image optimization"}, "user_interface_design": {"navigation_structure": {"sidebar_navigation": "Collapsible sidebar with role-based menu items", "breadcrumb_navigation": "Contextual breadcrumbs for deep navigation", "company_selector": "Dropdown in header for multi-company users"}, "dashboard_design": {"layout": "Grid-based layout with customizable widgets", "widgets": ["Project overview cards", "Recent activity feed", "Financial summary charts", "Employee status grid"], "responsiveness": "Adaptive layout for desktop, tablet, mobile"}, "data_tables": {"features": ["Column sorting", "Multi-column filtering", "Pagination", "Row selection", "Bulk actions", "Export functionality"], "performance": "Virtual scrolling for large datasets"}, "forms": {"validation": "Real-time validation with Zod schemas", "user_experience": "Auto-save, field dependencies, progressive disclosure", "file_uploads": "Drag-and-drop with progress indicators"}}}, "security_architecture": {"authentication": {"method": "JWT-based authentication with refresh tokens", "token_storage": "Secure HTTP-only cookies for refresh tokens", "session_management": "Redis-based session storage", "password_policy": "Strong password requirements with bcrypt hashing"}, "authorization": {"model": "Role-Based Access Control (RBAC)", "roles": ["Admin", "Project Manager", "Employee", "Viewer"], "permissions": "Granular permissions per resource and action", "multi_tenant_isolation": "Row-level security with company_id filtering"}, "data_protection": {"encryption_at_rest": "AES-256 encryption for sensitive data", "encryption_in_transit": "TLS 1.3 for all communications", "database_security": "PostgreSQL RLS policies for data isolation", "file_security": "Presigned URLs for S3 file access"}, "compliance": {"gdpr_compliance": "Data anonymization, right to be forgotten", "audit_logging": "Comprehensive audit trail for all operations", "data_retention": "Configurable data retention policies"}, "security_monitoring": {"intrusion_detection": "Failed login attempt monitoring", "rate_limiting": "API rate limiting per user and endpoint", "vulnerability_scanning": "Automated security scanning in CI/CD"}}, "performance_optimization": {"database_optimization": {"indexing_strategy": "Composite indexes for multi-tenant queries", "query_optimization": "Query analysis and optimization", "connection_pooling": "pgbouncer for connection management", "caching_strategy": "Redis caching for frequently accessed data"}, "application_performance": {"async_processing": "Celery for background tasks (Excel import, report generation)", "pagination": "Cursor-based pagination for large datasets", "lazy_loading": "On-demand loading of heavy components", "compression": "gzip compression for API responses"}, "frontend_performance": {"code_splitting": "Route-based and component-based code splitting", "image_optimization": "Next.js Image component with automatic optimization", "caching": "Browser caching with proper cache headers", "bundling": "Webpack optimization for production builds"}, "monitoring_metrics": {"response_times": "Target <2 seconds for standard operations", "throughput": "Support for 100+ concurrent users", "availability": "99.5% uptime target", "error_rates": "<1% error rate for API endpoints"}}, "deployment_architecture": {"cloud_infrastructure": {"provider": "AWS (Amazon Web Services)", "regions": "EU-West-1 (Ireland) for GDPR compliance", "availability_zones": "Multi-AZ deployment for high availability"}, "containerization": {"technology": "Docker with multi-stage builds", "orchestration": "Amazon EKS (Kubernetes)", "service_mesh": "Istio for advanced traffic management (optional)"}, "services_deployment": {"api_gateway": "Application Load Balancer with SSL termination", "backend_services": "FastAPI pods with horizontal auto-scaling", "frontend_application": "Next.js pods with CDN distribution", "worker_services": "Celery workers for background processing", "database": "Amazon RDS PostgreSQL with Multi-AZ", "cache": "Amazon ElastiCache Redis cluster", "file_storage": "Amazon S3 with CloudFront CDN"}, "ci_cd_pipeline": {"version_control": "Git with GitFlow branching strategy", "ci_platform": "GitHub Actions", "pipeline_stages": ["Code quality checks (linting, formatting)", "Unit and integration tests", "Security vulnerability scanning", "Docker image building and scanning", "Deployment to staging environment", "Automated testing in staging", "Production deployment with blue-green strategy"], "deployment_strategy": "Blue-green deployment for zero downtime"}, "monitoring_and_logging": {"application_monitoring": "Prometheus + Grafana for metrics", "error_tracking": "Sentry for error monitoring and alerting", "log_aggregation": "ELK stack (Elasticsearch, Logstash, Kibana)", "uptime_monitoring": "External uptime monitoring service", "alerts": "PagerDuty integration for critical alerts"}, "backup_and_disaster_recovery": {"database_backups": "Automated daily backups with 30-day retention", "point_in_time_recovery": "PostgreSQL PITR capability", "file_backups": "S3 cross-region replication", "disaster_recovery": "Multi-region failover capability", "rto_rpo_targets": "RTO: 4 hours, RPO: 1 hour"}}, "integration_points": {"excel_integration": {"import_capabilities": {"dpgf_import": "Advanced DPGF (Price Schedule) import with flexible column mapping", "validation": "Data validation and error reporting", "async_processing": "Background processing for large files", "progress_tracking": "Real-time import progress updates"}, "export_capabilities": {"report_export": "Export reports in Excel format", "data_export": "Bulk data export for analysis", "template_download": "Import template generation"}}, "document_management": {"file_handling": "Support for PDF, Excel, Word, images", "folder_structure": "Automatic project folder creation", "version_control": "Document versioning with history", "search_capability": "Full-text search across documents"}, "future_integrations": {"accounting_software": "Sage, QuickBooks, EBP integration", "bim_software": "Autodesk, Bentley Systems connectivity", "payment_gateways": "Stripe, PayPal for online payments", "mobile_apps": "iOS and Android companion applications"}}, "data_migration_strategy": {"current_system_analysis": {"source_system": "Windows-based ORBIS application", "data_formats": "Proprietary database formats, Excel files", "migration_approach": "Phased migration with validation"}, "migration_phases": {"phase_1_preparation": {"data_mapping": "Map existing data structures to new schema", "cleanup": "Data cleansing and validation rules", "migration_tools": "Custom ETL scripts using Python/Pandas"}, "phase_2_pilot_migration": {"scope": "Migrate one company's data for testing", "validation": "Comprehensive data validation and user testing", "rollback_plan": "Ability to rollback to original system"}, "phase_3_production_migration": {"approach": "Company-by-company migration", "timing": "Off-hours migration to minimize disruption", "support": "Dedicated support team during migration"}}}, "testing_strategy": {"testing_levels": {"unit_testing": {"backend": "pytest for Python code with >90% coverage", "frontend": "Jest and React Testing Library for components"}, "integration_testing": {"api_testing": "FastAPI TestClient for endpoint testing", "database_testing": "Test database with realistic data"}, "end_to_end_testing": {"framework": "Playwright for browser automation", "scenarios": "Critical user journeys and workflows"}, "performance_testing": {"load_testing": "k6 for API load testing", "stress_testing": "Database stress testing with large datasets"}}}, "maintenance_and_support": {"development_lifecycle": {"agile_methodology": "2-week sprints with continuous delivery", "code_review": "Mandatory peer review for all changes", "documentation": "Automated API documentation with OpenAPI"}, "operational_support": {"monitoring": "24/7 system monitoring with automated alerts", "support_tiers": "L1 (user support), L2 (technical issues), L3 (development)", "response_times": "Critical: 1 hour, High: 4 hours, Medium: 24 hours"}, "update_strategy": {"regular_updates": "Monthly feature releases", "security_patches": "Immediate deployment of security fixes", "maintenance_windows": "Scheduled maintenance during off-peak hours"}}, "unclear_points_for_clarification": {"data_migration": ["What is the exact format of data in the current Windows application?", "How much historical data needs to be migrated?", "What is the acceptable downtime window for migration?", "Are there any data format compatibility issues we should be aware of?"], "integration_requirements": ["Which accounting software systems need priority integration?", "Are there existing APIs available for payroll system integration?", "What level of BIM software integration is required?", "Are there any banking/payment system integration requirements?"], "customization_needs": ["How much UI customization is required per company?", "Are there industry-specific workflows that need accommodation?", "What level of white-labeling is expected?", "Are there regional compliance requirements beyond GDPR?"], "performance_requirements": ["What is the expected number of concurrent users per company?", "Are there seasonal usage patterns in the construction industry?", "What are the specific SLA requirements for uptime?", "What is the expected data growth rate per year?"], "training_and_support": ["What is the technical skill level of typical end users?", "Is multilingual support required beyond French?", "What type of training delivery is preferred (online, on-site)?", "What are the expected support response time requirements?"], "compliance_and_security": ["Are there specific French construction industry regulations?", "What level of audit logging is required for compliance?", "Are there any security certifications needed (ISO 27001, SOC 2)?", "What is the required data retention period for different types of data?"]}, "success_metrics": {"technical_metrics": {"performance": "API response times <2 seconds, 99.5% uptime", "scalability": "Support 1000+ concurrent users, 999,999+ records per entity", "reliability": "<1% error rate, automated failover capability"}, "user_adoption_metrics": {"onboarding": "80% user activation within 30 days", "engagement": "60% of core features used regularly", "satisfaction": "NPS >50, CSAT >4.5/5"}, "business_metrics": {"efficiency_gains": "50% reduction in project setup time", "cost_savings": "30% reduction in administrative overhead", "data_accuracy": "90% improvement in financial tracking accuracy"}}}}