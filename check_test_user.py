"""
Script pour vérifier l'utilisateur <EMAIL> dans la base de données
"""

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi_template.app.db.session import AsyncSessionLocal
from fastapi_template.app.models.user import User
from fastapi_template.app.models.workspace import UserWorkspace

async def check_test_user():
    """Vérifier l'utilisateur <EMAIL>"""
    async with AsyncSessionLocal() as session:
        # Chercher l'utilisateur par email
        result = await session.execute(
            select(User).where(User.email == "<EMAIL>")
        )
        user = result.scalar_one_or_none()
        
        if user:
            print(f"✅ Utilisateur trouvé:")
            print(f"   ID: {user.id}")
            print(f"   Email: {user.email}")
            print(f"   Supabase ID: {user.supabase_user_id}")
            print(f"   Nom: {user.first_name} {user.last_name}")
            print(f"   Rôle: {user.role}")
            print(f"   Actif: {user.is_active}")
            
            # Chercher ses workspaces
            workspace_result = await session.execute(
                select(UserWorkspace).where(
                    UserWorkspace.user_id == user.id
                )
            )
            workspaces = workspace_result.scalars().all()
            
            print(f"\n📁 Workspaces associés: {len(workspaces)}")
            for ws in workspaces:
                print(f"   - Workspace ID: {ws.workspace_id}, Rôle: {ws.role_name}, Actif: {ws.is_active}")
        else:
            print("❌ Utilisateur <EMAIL> non trouvé dans la base de données locale")
            
            # Chercher tous les utilisateurs pour debug
            all_users_result = await session.execute(select(User))
            all_users = all_users_result.scalars().all()
            print(f"\n📊 Utilisateurs dans la base: {len(all_users)}")
            for u in all_users[:5]:  # Afficher les 5 premiers
                print(f"   - {u.email} (ID: {u.id}, Supabase: {u.supabase_user_id})")

if __name__ == "__main__":
    asyncio.run(check_test_user())
