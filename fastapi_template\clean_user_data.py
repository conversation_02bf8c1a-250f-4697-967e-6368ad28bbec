#!/usr/bin/env python3
"""
Script pour nettoyer et clarifier les données utilisateurs
"""

import asyncio
import asyncpg
from datetime import datetime
from app.core.config import settings

DATABASE_URL = settings.DATABASE_URL


async def clean_user_data():
    """Nettoyer et clarifier les données utilisateurs"""
    print("🧹 Nettoyage des données utilisateurs...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Afficher l'état actuel
        print("\n📊 État actuel des données:")
        
        # Table users
        users = await conn.fetch("""
            SELECT id, email, first_name, last_name, role, supabase_user_id, is_active
            FROM users ORDER BY id
        """)
        
        print(f"\n👤 Table 'users' ({len(users)} enregistrement(s)):")
        for user in users:
            supabase_id = user['supabase_user_id'][:8] + "..." if user['supabase_user_id'] else "None"
            print(f"   • ID: {user['id']} | {user['email']} | {user['first_name']} {user['last_name']} | {user['role']} | Supabase: {supabase_id}")
        
        # Table user_profiles
        profiles = await conn.fetch("""
            SELECT id, first_name, last_name, company, role, is_active
            FROM user_profiles ORDER BY created_at
        """)
        
        print(f"\n👥 Table 'user_profiles' ({len(profiles)} enregistrement(s)):")
        for profile in profiles:
            profile_id = str(profile['id'])[:8] + "..."
            print(f"   • ID: {profile_id} | {profile['first_name']} {profile['last_name']} | {profile['company']} | {profile['role']}")
        
        # 2. Identifier les problèmes
        print("\n🔍 Analyse des problèmes:")
        
        # Profils orphelins (dans user_profiles mais pas dans users)
        orphan_profiles = await conn.fetch("""
            SELECT up.id, up.first_name, up.last_name, up.company
            FROM user_profiles up
            LEFT JOIN users u ON u.supabase_user_id = up.id::text
            WHERE u.id IS NULL
        """)
        
        if orphan_profiles:
            print(f"   ⚠️ {len(orphan_profiles)} profil(s) orphelin(s) trouvé(s):")
            for profile in orphan_profiles:
                profile_id = str(profile['id'])[:8] + "..."
                print(f"     • {profile_id} | {profile['first_name']} {profile['last_name']} ({profile['company']})")
        
        # Utilisateurs sans profil Supabase
        users_without_profiles = await conn.fetch("""
            SELECT u.id, u.email, u.first_name, u.last_name
            FROM users u
            LEFT JOIN user_profiles up ON u.supabase_user_id = up.id::text
            WHERE u.supabase_user_id IS NOT NULL AND up.id IS NULL
        """)
        
        if users_without_profiles:
            print(f"   ⚠️ {len(users_without_profiles)} utilisateur(s) sans profil Supabase:")
            for user in users_without_profiles:
                print(f"     • {user['email']} | {user['first_name']} {user['last_name']}")
        
        # 3. Proposer des actions de nettoyage
        print("\n🔧 Actions de nettoyage proposées:")
        
        if orphan_profiles:
            print("   1. Supprimer les profils orphelins")
            response = input("   Voulez-vous supprimer les profils orphelins ? (y/N): ")
            
            if response.lower() == 'y':
                for profile in orphan_profiles:
                    await conn.execute("DELETE FROM user_profiles WHERE id = $1", profile['id'])
                    print(f"     ✅ Profil supprimé: {profile['first_name']} {profile['last_name']}")
        
        # 4. Vérifier l'utilisateur admin principal
        print("\n👑 Vérification de l'utilisateur admin principal:")
        
        admin_user = await conn.fetchrow("""
            SELECT u.*, up.first_name as profile_first_name, up.last_name as profile_last_name
            FROM users u
            LEFT JOIN user_profiles up ON u.supabase_user_id = up.id::text
            WHERE u.email = '<EMAIL>'
        """)
        
        if admin_user:
            print(f"   ✅ Utilisateur admin trouvé:")
            print(f"     • Email: {admin_user['email']}")
            print(f"     • Nom (users): {admin_user['first_name']} {admin_user['last_name']}")
            print(f"     • Nom (profile): {admin_user['profile_first_name']} {admin_user['profile_last_name']}")
            print(f"     • Rôle: {admin_user['role']}")
            print(f"     • Supabase ID: {admin_user['supabase_user_id']}")
            print(f"     • Actif: {admin_user['is_active']}")
        else:
            print("   ❌ Aucun utilisateur admin trouvé")
        
        # 5. Résumé final
        print("\n📋 Résumé final:")
        
        final_users = await conn.fetchval("SELECT COUNT(*) FROM users")
        final_profiles = await conn.fetchval("SELECT COUNT(*) FROM user_profiles")
        linked_users = await conn.fetchval("""
            SELECT COUNT(*) FROM users u
            JOIN user_profiles up ON u.supabase_user_id = up.id::text
        """)
        
        print(f"   • Utilisateurs (users): {final_users}")
        print(f"   • Profils (user_profiles): {final_profiles}")
        print(f"   • Utilisateurs liés: {linked_users}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Nettoyage des Données Utilisateurs")
    print("="*60)
    
    if await clean_user_data():
        print("\n✅ Nettoyage terminé!")
        
        print("\n📝 Architecture clarifiée:")
        print("   🎯 Table 'users' = Utilisateurs de l'application ORBIS")
        print("   🔐 Table 'user_profiles' = Profils d'authentification Supabase")
        print("   🔗 Lien = users.supabase_user_id → user_profiles.id")
        
        print("\n💡 Recommandations:")
        print("   • Utiliser 'users' pour la logique métier")
        print("   • Utiliser 'user_profiles' pour l'authentification")
        print("   • Maintenir la synchronisation entre les deux")
        
        return True
    else:
        print("\n❌ Échec du nettoyage")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
