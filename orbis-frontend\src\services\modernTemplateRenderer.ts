/**
 * Moteur de rendu moderne pour les templates JSON avec support avancé
 * Support: images, layouts flexbox, conteneurs dynamiques
 */

export interface ModernTemplateSection {
  id?: string
  type: 'container' | 'table' | 'text' | 'image' | 'dynamic-container' | 'dynamic-table'
  styles?: Record<string, string>
  content?: any
  src?: string // Pour les images
  children?: ModernTemplateSection[]
  condition?: string
  dataSource?: string
  template?: ModernTemplateSection
}

export interface ModernTemplate {
  name: string
  version: string
  description: string
  sections: ModernTemplateSection[]
}

export class ModernTemplateRenderer {
  private data: Record<string, any> = {}

  constructor(data: Record<string, any>) {
    this.data = data
  }

  /**
   * Rendre un template complet
   */
  render(template: ModernTemplate): string {
    console.log('🎨 Rendu du template moderne:', template.name)
    console.log('📊 Données:', this.data)

    const html = template.sections
      .map(section => this.renderSection(section))
      .join('')

    return this.wrapInDocument(html)
  }

  /**
   * Rendre une section
   */
  private renderSection(section: ModernTemplateSection): string {
    // Vérifier les conditions
    if (section.condition && !this.evaluateCondition(section.condition)) {
      return ''
    }

    switch (section.type) {
      case 'container':
        return this.renderContainer(section)
      case 'text':
        return this.renderText(section)
      case 'image':
        return this.renderImage(section)
      case 'table':
        return this.renderTable(section)
      case 'dynamic-container':
        return this.renderDynamicContainer(section)
      case 'dynamic-table':
        return this.renderDynamicTable(section)
      default:
        console.warn('Type de section non supporté:', section.type)
        return ''
    }
  }

  /**
   * Rendre un conteneur
   */
  private renderContainer(section: ModernTemplateSection): string {
    let className = ''
    let styles = section.styles || {}

    // Utiliser des classes CSS pour les layouts complexes
    if (section.id === 'main-container') {
      className = ' class="main-container"'
      styles = {} // Supprimer les styles inline pour utiliser la classe CSS
    } else if (section.id === 'content-wrapper') {
      className = ' class="flex-container"'
      styles = { marginTop: '80px', minHeight: '600px' }
    } else if (section.id === 'left-column') {
      className = ' class="flex-left"'
      styles = {}
    } else if (section.id === 'right-column') {
      className = ' class="flex-right"'
      styles = {}
    }

    const styleAttr = this.renderStyles(styles)
    const children = section.children?.map(child => this.renderSection(child)).join('') || ''

    return `<div${section.id ? ` id="${section.id}"` : ''}${className}${styleAttr}>${children}</div>`
  }

  /**
   * Rendre du texte
   */
  private renderText(section: ModernTemplateSection): string {
    const styles = this.renderStyles(section.styles)
    const content = this.interpolateVariables(section.content || '')
    
    return `<div${styles}>${content}</div>`
  }

  /**
   * Rendre une image
   */
  private renderImage(section: ModernTemplateSection): string {
    const styles = this.renderStyles(section.styles)
    const src = this.interpolateVariables(section.src || '')
    
    if (!src) return ''
    
    return `<img src="${src}"${styles} alt="" />`
  }

  /**
   * Rendre un tableau
   */
  private renderTable(section: ModernTemplateSection): string {
    const styles = this.renderStyles(section.styles)
    
    if (!section.content?.rows) return ''
    
    const rows = section.content.rows.map((row: any) => {
      const cells = row.cells.map((cell: any) => {
        const cellStyles = this.renderStyles(cell.styles)
        const content = this.interpolateVariables(cell.content || '')
        return `<td${cellStyles}>${content}</td>`
      }).join('')
      
      return `<tr>${cells}</tr>`
    }).join('')
    
    return `<table${styles}>${rows}</table>`
  }

  /**
   * Rendre un conteneur dynamique (répétition basée sur dataSource)
   */
  private renderDynamicContainer(section: ModernTemplateSection): string {
    if (!section.dataSource || !section.template) return ''

    const dataArray = this.data[section.dataSource]
    if (!Array.isArray(dataArray)) return ''

    return dataArray.map((item, index) => {
      // Créer un nouveau renderer avec les données de l'item
      const itemRenderer = new ModernTemplateRenderer({ ...this.data, ...item })

      // Ajouter une classe CSS pour les éléments d'entreprise
      let template = { ...section.template! }
      if (section.id === 'companies-list') {
        template.styles = { ...template.styles }
        // Utiliser la classe CSS au lieu des styles inline
      }

      return itemRenderer.renderSection(template)
    }).join('')
  }

  /**
   * Rendre un tableau dynamique
   */
  private renderDynamicTable(section: ModernTemplateSection): string {
    const styles = this.renderStyles(section.styles)
    
    if (!section.dataSource || !section.template) return ''
    
    const dataArray = this.data[section.dataSource]
    if (!Array.isArray(dataArray)) return ''
    
    // En-tête si défini
    let headerHtml = ''
    if (section.template.header) {
      const headerStyles = this.renderStyles(section.template.header.styles)
      const headerContent = this.interpolateVariables(section.template.header.content || '')
      headerHtml = `<tr><th${headerStyles}>${headerContent}</th></tr>`
    }
    
    // Lignes de données
    const rowsHtml = dataArray.map(item => {
      const itemRenderer = new ModernTemplateRenderer({ ...this.data, ...item })
      
      if (section.template?.row?.cells) {
        const cells = section.template.row.cells.map((cell: any) => {
          const cellStyles = itemRenderer.renderStyles(cell.styles)
          const content = itemRenderer.interpolateVariables(cell.content || '')
          return `<td${cellStyles}>${content}</td>`
        }).join('')
        
        return `<tr>${cells}</tr>`
      }
      
      return ''
    }).join('')
    
    return `<table${styles}>${headerHtml}${rowsHtml}</table>`
  }

  /**
   * Rendre les styles CSS
   */
  private renderStyles(styles?: Record<string, string>): string {
    if (!styles || Object.keys(styles).length === 0) return ''

    const cssRules = Object.entries(styles)
      .filter(([key, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${this.camelToKebab(key)}: ${value}`)
      .join('; ')

    return cssRules ? ` style="${cssRules}"` : ''
  }

  /**
   * Convertir camelCase en kebab-case pour CSS
   */
  private camelToKebab(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
  }

  /**
   * Interpoler les variables dans le contenu
   */
  private interpolateVariables(content: string): string {
    return content.replace(/\{\{(\w+)\}\}/g, (match, varName) => {
      const value = this.data[varName]
      return value !== undefined ? String(value) : match
    })
  }

  /**
   * Évaluer une condition
   */
  private evaluateCondition(condition: string): boolean {
    // Extraire le nom de variable de la condition {{varName}}
    const match = condition.match(/\{\{(\w+)\}\}/)
    if (match) {
      const varName = match[1]
      const value = this.data[varName]
      return Boolean(value)
    }
    return false
  }

  /**
   * Envelopper le HTML dans un document complet
   */
  private wrapInDocument(html: string): string {
    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      line-height: 1.4;
      background-color: #f5f5f5;
    }
    * {
      box-sizing: border-box;
    }
    img {
      max-width: 100%;
      height: auto;
      display: block;
    }
    table {
      border-collapse: collapse;
      width: 100%;
    }

    /* Classes CSS pour le layout */
    .flex-container {
      display: flex !important;
      gap: 30px;
    }
    .flex-left {
      flex: 0 0 45% !important;
      padding-right: 15px;
      border-right: 1px solid #ddd;
    }
    .flex-right {
      flex: 0 0 55% !important;
      padding-left: 15px;
    }
    .company-item {
      margin-bottom: 25px;
      border-bottom: 1px solid #ccc;
      padding-bottom: 18px;
    }
    .company-header {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 10px;
    }
    .company-logo {
      height: 35px;
      width: auto;
      flex-shrink: 0;
      border: 1px solid #eee;
    }
    .company-info {
      flex: 1;
    }
    .main-container {
      margin: 0 auto;
      background-color: white;
      border: 3px solid #000;
      min-height: 270mm;
      width: 190mm;
      position: relative;
      padding: 25px;
      box-sizing: border-box;
    }
  </style>
</head>
<body>
${html}
</body>
</html>`
  }
}
