import { useState, useEffect } from 'react'
import { FastAuthService } from '@/lib/auth'

interface UseProjectPhotoProps {
  projectId: number
  onSuccess?: () => void
  initialPhotoUrl?: string | null
}

export function useProjectPhoto({ projectId, onSuccess, initialPhotoUrl }: UseProjectPhotoProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [photoUrl, setPhotoUrl] = useState<string | null>(initialPhotoUrl || null)
  const [photoDeleted, setPhotoDeleted] = useState(false)

  // Mettre à jour photoUrl quand initialPhotoUrl change
  useEffect(() => {
    setPhotoUrl(initialPhotoUrl || null)
    setPhotoDeleted(false) // Reset l'état de suppression
  }, [initialPhotoUrl])

  const uploadPhoto = async (file: File) => {
    // Vérifier que projectId est valide
    if (!projectId || projectId === 0) {
      throw new Error('ID de projet invalide')
    }

    console.log('📤 Upload photo - Début:', { projectId, fileName: file.name, fileSize: file.size })
    setLoading(true)
    setError(null)

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = FastAuthService.getToken()

      if (!token) {
        throw new Error('Token d\'authentification manquant')
      }

      const formData = new FormData()
      formData.append('file', file)

      const url = `${API_BASE_URL}/api/v1/projects/${projectId}/upload-photo`
      console.log('📤 Upload URL:', url)

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      console.log('📡 Upload réponse:', { status: response.status, statusText: response.statusText })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `Erreur HTTP: ${response.status}`)
      }

      const result = await response.json()
      setPhotoUrl(result.photo_url)
      setPhotoDeleted(false) // Reset l'état de suppression
      
      console.log('✅ Upload réussi:', result)
      
      if (onSuccess) {
        onSuccess()
      }

      return result

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de l\'upload'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const deletePhoto = async () => {
    // Vérifier que projectId est valide
    if (!projectId || projectId === 0) {
      throw new Error('ID de projet invalide')
    }

    console.log('🗑️ Suppression photo - Début:', { projectId })
    setLoading(true)
    setError(null)

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = FastAuthService.getToken()

      if (!token) {
        throw new Error('Token d\'authentification manquant')
      }

      const url = `${API_BASE_URL}/api/v1/projects/${projectId}/photo`
      console.log('🗑️ Delete URL:', url)

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      console.log('📡 Delete réponse:', { status: response.status, statusText: response.statusText })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `Erreur HTTP: ${response.status}`)
      }

      const result = await response.json()
      setPhotoUrl(null)
      setPhotoDeleted(true) // Marquer comme supprimé
      
      console.log('✅ Suppression réussie:', result)
      
      if (onSuccess) {
        onSuccess()
      }

      return result

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la suppression'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return {
    uploadPhoto,
    deletePhoto,
    loading,
    error,
    photoUrl,
    photoDeleted
  }
}
