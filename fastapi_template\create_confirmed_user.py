#!/usr/bin/env python3
"""
Créer un utilisateur avec email confirmé pour les tests
"""

import requests
import json
from datetime import datetime

API_BASE_URL = "http://localhost:8000"
API_V1_URL = f"{API_BASE_URL}/api/v1"


def test_with_simple_credentials():
    """Tester avec des identifiants simples"""
    print("🔐 Test avec identifiants simples")
    
    # Essayer de se connecter avec un utilisateur existant
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(f"{API_V1_URL}/auth/login", json=login_data, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            return response.json()
        
    except Exception as e:
        print(f"Erreur: {e}")
    
    return None


def test_registration_and_manual_confirm():
    """Tester l'inscription et donner les instructions pour confirmer"""
    print("📧 Test d'inscription avec instructions de confirmation")
    
    user_data = {
        "email": "<EMAIL>",  # Domaine différent
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "User",
        "company": "Test Company",
        "phone": "0123456789"
    }
    
    try:
        response = requests.post(f"{API_V1_URL}/auth/register", json=user_data, timeout=10)
        print(f"Status inscription: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n✅ Utilisateur créé: {user_data['email']}")
            print(f"📧 Email de confirmation envoyé")
            print(f"\n💡 INSTRUCTIONS:")
            print(f"1. Vérifiez votre boîte email: {user_data['email']}")
            print(f"2. Cliquez sur le lien de confirmation")
            print(f"3. Puis testez la connexion avec:")
            print(f"   Email: {user_data['email']}")
            print(f"   Password: {user_data['password']}")
            
            return user_data
        
    except Exception as e:
        print(f"Erreur: {e}")
    
    return None


def main():
    """Test principal"""
    print("🚀 ORBIS - Création d'Utilisateur Confirmé")
    print("="*50)
    
    # Test 1: Identifiants simples
    token_data = test_with_simple_credentials()
    
    if token_data:
        print("🎉 Connexion réussie avec identifiants existants!")
        return True
    
    # Test 2: Nouvelle inscription
    user_data = test_registration_and_manual_confirm()
    
    if user_data:
        print("\n🎯 PROCHAINES ÉTAPES:")
        print("1. Confirmez l'email dans votre boîte")
        print("2. Testez la connexion sur http://localhost:8000/docs")
        print("3. Utilisez le middleware pour les endpoints protégés")
        return True
    
    print("\n❌ Échec des tests")
    return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
