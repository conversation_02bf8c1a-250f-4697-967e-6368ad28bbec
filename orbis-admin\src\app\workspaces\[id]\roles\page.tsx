'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import {
  Building2,
  Shield,
  ArrowLeft,
  Settings,
  Users,
  CheckCircle,
  XCircle,
  Save,
  RotateCcw
} from 'lucide-react'
import { useToast } from '@/components/ui/Toast'
import { FastWorkspaceService, RBACService, Permission } from '@/lib/fast-auth'
import { useAuth } from '@/contexts/AuthContext'

interface Workspace {
  id: number
  name: string
  code: string
}

interface RolePermissions {
  [roleName: string]: string[]
}

interface PermissionsByResource {
  [resource: string]: Permission[]
}

// Labels pour les ressources
const RESOURCE_LABELS = {
  'projects': 'Projets (Dossiers)',
  'users': 'Utilisateurs',
  'documents': 'Documents',
  'companies': 'Entreprises tierces',
  'budgets': 'Budgets',
  'quotes': 'Devis',
  'purchase_orders': 'Bons de commande',
  'invoices': 'Factures',
  'reports': 'Rapports',
  'settings': 'Paramètres',
  'admin': 'Administration'
}

// Labels pour les actions
const ACTION_LABELS = {
  'create': 'Créer',
  'read': 'Voir',
  'update': 'Modifier',
  'delete': 'Supprimer',
  'approve': 'Approuver',
  'send': 'Envoyer',
  'validate': 'Valider',
  'download': 'Télécharger',
  'upload': 'Uploader',
  'manage': 'Gérer',
  'assign': 'Assigner'
}

export default function WorkspaceRolesPage() {
  const params = useParams()
  const router = useRouter()
  const workspaceId = parseInt(params.id as string)
  const { user } = useAuth()
  const { success, error: showError } = useToast()

  const [workspace, setWorkspace] = useState<Workspace | null>(null)
  const [loading, setLoading] = useState(true)
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [roles, setRoles] = useState<string[]>([])
  const [selectedRole, setSelectedRole] = useState<string | null>(null)
  const [rolePermissions, setRolePermissions] = useState<RolePermissions>({})
  const [originalPermissions, setOriginalPermissions] = useState<RolePermissions>({})
  const [hasChanges, setHasChanges] = useState(false)
  const [saving, setSaving] = useState(false)

  // Grouper les permissions par ressource
  const permissionsByResource: PermissionsByResource = permissions.reduce((acc, permission) => {
    if (!acc[permission.resource]) {
      acc[permission.resource] = []
    }
    acc[permission.resource].push(permission)
    return acc
  }, {} as PermissionsByResource)

  useEffect(() => {
    loadData()
  }, [workspaceId])

  const loadData = async () => {
    try {
      setLoading(true)

      // Charger les informations du workspace
      const workspaceData = await FastWorkspaceService.getWorkspace(workspaceId)
      setWorkspace(workspaceData)

      // Charger les rôles disponibles
      const rolesData = await RBACService.getRoles()
      setRoles(rolesData.map((role: any) => role.name))

      // Sélectionner le premier rôle par défaut
      if (rolesData.length > 0) {
        setSelectedRole(rolesData[0].name)
      }

      // Charger toutes les permissions
      const permissionsData = await RBACService.getPermissions()
      setPermissions(permissionsData)

      // Charger la matrice des permissions pour ce workspace
      const matrixData = await RBACService.getCompanyPermissionMatrix(workspaceId)
      setRolePermissions(matrixData.matrix || {})
      setOriginalPermissions(matrixData.matrix || {})

    } catch (error) {
      console.error('❌ Erreur chargement données:', error)
      showError('Erreur', 'Impossible de charger les données')
    } finally {
      setLoading(false)
    }
  }

  const handlePermissionToggle = (roleName: string, permissionName: string) => {
    setRolePermissions(prev => {
      const rolePerms = prev[roleName] || []
      const newPerms = rolePerms.includes(permissionName)
        ? rolePerms.filter(p => p !== permissionName)
        : [...rolePerms, permissionName]
      
      const newRolePermissions = {
        ...prev,
        [roleName]: newPerms
      }
      
      // Vérifier s'il y a des changements
      const hasChanges = JSON.stringify(newRolePermissions) !== JSON.stringify(originalPermissions)
      setHasChanges(hasChanges)
      
      return newRolePermissions
    })
  }

  const savePermissions = async () => {
    if (!selectedRole) return

    try {
      setSaving(true)

      await RBACService.setRolePermissions(workspaceId, selectedRole, rolePermissions[selectedRole] || [])

      success('Succès', `Permissions du rôle ${selectedRole} mises à jour`)
      setOriginalPermissions({ ...rolePermissions })
      setHasChanges(false)

    } catch (error) {
      console.error('❌ Erreur sauvegarde:', error)
      showError('Erreur', error instanceof Error ? error.message : 'Impossible de sauvegarder les permissions')
    } finally {
      setSaving(false)
    }
  }

  const resetPermissions = () => {
    setRolePermissions({ ...originalPermissions })
    setHasChanges(false)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des permissions...</p>
        </div>
      </div>
    )
  }

  if (!workspace) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Workspace non trouvé</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push(`/workspaces/${workspaceId}`)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              title="Retour aux détails"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                <Shield className="w-8 h-8 text-blue-600" />
                Gestion des rôles - {workspace.name}
              </h1>
              <p className="text-gray-600 mt-1">
                Configurez les permissions pour chaque rôle dans ce workspace
              </p>
            </div>
          </div>

          {/* Actions */}
          {hasChanges && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Settings className="w-5 h-5 text-yellow-600" />
                  <span className="text-yellow-800 font-medium">
                    Vous avez des modifications non sauvegardées
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={resetPermissions}
                    className="px-3 py-1 text-sm text-yellow-700 hover:text-yellow-900 flex items-center gap-1"
                  >
                    <RotateCcw className="w-4 h-4" />
                    Annuler
                  </button>
                  <button
                    onClick={savePermissions}
                    disabled={saving}
                    className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:opacity-50 flex items-center gap-2"
                  >
                    {saving ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Save className="w-4 h-4" />
                    )}
                    Sauvegarder
                  </button>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Liste des rôles */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow">
                <div className="p-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <Users className="w-5 h-5 text-blue-600" />
                    Rôles ({roles.length})
                  </h2>
                </div>
                <div className="p-2">
                  {roles.map((role) => (
                    <button
                      key={role}
                      onClick={() => setSelectedRole(role)}
                      className={`w-full text-left p-3 rounded-lg transition-colors mb-1 ${
                        selectedRole === role
                          ? 'bg-blue-50 border border-blue-200 text-blue-900'
                          : 'hover:bg-gray-50 text-gray-700'
                      }`}
                    >
                      <div className="font-medium">{role}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {(rolePermissions[role] || []).length} permissions
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Permissions */}
            <div className="lg:col-span-3">
              {selectedRole ? (
                <div className="bg-white rounded-lg shadow">
                  <div className="p-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">
                      Permissions pour le rôle : {selectedRole}
                    </h2>
                    <p className="text-sm text-gray-600 mt-1">
                      {(rolePermissions[selectedRole] || []).length} permissions accordées sur {permissions.length} disponibles
                    </p>
                  </div>
                  
                  <div className="p-6 space-y-6">
                    {Object.entries(permissionsByResource).map(([resource, resourcePermissions]) => (
                      <div key={resource} className="border border-gray-200 rounded-lg">
                        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                          <h3 className="font-medium text-gray-900">
                            {RESOURCE_LABELS[resource as keyof typeof RESOURCE_LABELS] || resource}
                          </h3>
                          <p className="text-sm text-gray-600 mt-1">
                            {resourcePermissions.length} actions disponibles
                          </p>
                        </div>
                        <div className="p-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {resourcePermissions.map((permission) => {
                              const isGranted = (rolePermissions[selectedRole] || []).includes(permission.name)
                              return (
                                <label
                                  key={permission.name}
                                  className={`flex items-start gap-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                                    isGranted
                                      ? 'bg-blue-50 border-blue-200 text-blue-900'
                                      : 'bg-white border-gray-200 hover:bg-gray-50'
                                  }`}
                                >
                                  <input
                                    type="checkbox"
                                    checked={isGranted}
                                    onChange={() => handlePermissionToggle(selectedRole, permission.name)}
                                    className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                  />
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center gap-2">
                                      {isGranted ? (
                                        <CheckCircle className="w-4 h-4 text-blue-600" />
                                      ) : (
                                        <XCircle className="w-4 h-4 text-gray-400" />
                                      )}
                                      <span className="font-medium text-sm">
                                        {ACTION_LABELS[permission.action as keyof typeof ACTION_LABELS] || permission.action}
                                      </span>
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1 truncate">
                                      {permission.description}
                                    </p>
                                  </div>
                                </label>
                              )
                            })}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow p-8 text-center">
                  <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Sélectionnez un rôle</h3>
                  <p className="text-gray-600">
                    Choisissez un rôle dans la liste de gauche pour voir et modifier ses permissions.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
