#!/usr/bin/env python3
"""
Script de test pour l'authentification Supabase
"""

import requests
import json
from datetime import datetime


# Configuration
API_BASE_URL = "http://localhost:8000"
API_V1_URL = f"{API_BASE_URL}/api/v1"


def print_header(title: str):
    """Affiche un en-tête formaté"""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print(f"{'='*60}")


def print_test_result(test_name: str, success: bool, details: str = ""):
    """Affiche le résultat d'un test"""
    status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
    print(f"{status} - {test_name}")
    if details:
        print(f"   📝 {details}")


def test_register_user():
    """Test d'inscription d'un nouvel utilisateur"""
    print_header("Test d'Inscription")
    
    user_data = {
        "email": "<EMAIL>",
        "password": "Password123!",
        "first_name": "Test",
        "last_name": "User",
        "company": "Test Company",
        "phone": "0123456789"
    }
    
    try:
        response = requests.post(
            f"{API_V1_URL}/auth/register",
            json=user_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print_test_result("Inscription", True, f"Utilisateur créé: {user_data['email']}")
            return user_data
        else:
            print_test_result("Inscription", False, f"Status: {response.status_code}, Response: {response.text}")
            return None
            
    except Exception as e:
        print_test_result("Inscription", False, str(e))
        return None


def test_login_user(email: str, password: str):
    """Test de connexion"""
    print_header("Test de Connexion")
    
    login_data = {
        "email": email,
        "password": password
    }
    
    try:
        response = requests.post(
            f"{API_V1_URL}/auth/login",
            json=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            access_token = result.get("access_token")
            user_info = result.get("user", {})
            
            print_test_result("Connexion", True, f"Token reçu pour {user_info.get('email', 'N/A')}")
            return result
        else:
            print_test_result("Connexion", False, f"Status: {response.status_code}, Response: {response.text}")
            return None
            
    except Exception as e:
        print_test_result("Connexion", False, str(e))
        return None


def test_protected_endpoint(token_data):
    """Test d'un endpoint protégé"""
    if not token_data:
        return False
    
    print_header("Test d'Endpoint Protégé")
    
    access_token = token_data.get("access_token")
    token_type = token_data.get("token_type", "Bearer")
    
    try:
        headers = {
            "Authorization": f"{token_type} {access_token}",
            "Content-Type": "application/json"
        }
        
        # Test de l'endpoint /me
        response = requests.get(f"{API_V1_URL}/auth/me", headers=headers, timeout=10)
        
        if response.status_code == 200:
            user_info = response.json()
            print_test_result("Endpoint /me", True, f"Utilisateur: {user_info.get('email', 'N/A')}")
            return True
        else:
            print_test_result("Endpoint /me", False, f"Status: {response.status_code}, Response: {response.text}")
            return False
            
    except Exception as e:
        print_test_result("Endpoint protégé", False, str(e))
        return False


def test_companies_endpoint(token_data):
    """Test de l'endpoint des entreprises"""
    if not token_data:
        return False
    
    print_header("Test Endpoint Entreprises")
    
    access_token = token_data.get("access_token")
    token_type = token_data.get("token_type", "Bearer")
    
    try:
        headers = {
            "Authorization": f"{token_type} {access_token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{API_V1_URL}/companies", headers=headers, timeout=10)
        
        if response.status_code == 200:
            companies = response.json()
            print_test_result("Endpoint /companies", True, f"{len(companies)} entreprise(s) trouvée(s)")
            
            if companies:
                print("   📋 Entreprises:")
                for company in companies[:3]:
                    print(f"      - {company.get('name', 'N/A')} (ID: {company.get('id', 'N/A')})")
            
            return True
        else:
            print_test_result("Endpoint /companies", False, f"Status: {response.status_code}, Response: {response.text}")
            return False
            
    except Exception as e:
        print_test_result("Endpoint entreprises", False, str(e))
        return False


def main():
    """Fonction principale"""
    print("🚀 ORBIS - Test d'Authentification Supabase")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 URL de l'API: {API_BASE_URL}")
    
    # Test d'inscription
    user_data = test_register_user()
    
    if user_data:
        # Test de connexion
        token_data = test_login_user(user_data["email"], user_data["password"])
        
        if token_data:
            # Test des endpoints protégés
            test_protected_endpoint(token_data)
            test_companies_endpoint(token_data)
            
            print_header("Résumé des Tests")
            print("🎉 TOUS LES TESTS RÉUSSIS!")
            print("✅ L'authentification Supabase fonctionne parfaitement.")
            
            print(f"\n🔑 Identifiants créés:")
            print(f"   - Email: {user_data['email']}")
            print(f"   - Mot de passe: {user_data['password']}")
            
            print(f"\n💡 Vous pouvez maintenant tester manuellement sur:")
            print(f"   - Documentation: {API_BASE_URL}/docs")
            print(f"   - Redoc: {API_BASE_URL}/redoc")
            
            return True
    
    print_header("Résumé des Tests")
    print("❌ ÉCHEC DES TESTS")
    print("🚨 Problème avec l'authentification Supabase.")
    
    return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
