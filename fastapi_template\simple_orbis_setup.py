#!/usr/bin/env python3
"""
Script simple pour créer la configuration ORBIS de base
"""

import asyncio
import asyncpg
from datetime import datetime
import sys
import uuid
from app.core.config import settings

# Configuration
DATABASE_URL = settings.DATABASE_URL


async def create_orbis_setup():
    """Créer la configuration ORBIS complète"""
    print("🏢 Création de la configuration ORBIS...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Créer la société ORBIS
        print("   📊 Création de la société ORBIS...")
        company_id = await conn.fetchval("""
            INSERT INTO companies (name, code, siret, address, phone, email, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
        """,
        "ORBIS",
        "ORBIS001", 
        "12345678901234",
        "123 Avenue de la Construction, 75001 Paris",
        "***********.89",
        "<EMAIL>",
        True,
        datetime.utcnow(),
        datetime.utcnow()
        )
        
        print(f"   ✅ Société ORBIS créée avec l'ID: {company_id}")
        
        # 2. Créer l'utilisateur admin
        print("   👤 Création de l'utilisateur admin...")
        admin_email = "<EMAIL>"
        
        user_id = await conn.fetchval("""
            INSERT INTO users (
                id, email, first_name, last_name, 
                role, is_active, is_superuser, is_verified, 
                created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING id
        """,
        uuid.uuid4(),
        admin_email,
        "Admin",
        "ORBIS",
        "ADMIN",
        True,
        True,
        True,
        datetime.utcnow(),
        datetime.utcnow()
        )
        
        print(f"   ✅ Utilisateur admin créé: {admin_email}")
        
        # 3. Associer l'utilisateur à la société
        print("   🔗 Association utilisateur-société...")
        await conn.execute("""
            INSERT INTO user_companies (user_id, company_id, is_default, created_at)
            VALUES ($1, $2, $3, $4)
        """, user_id, company_id, True, datetime.utcnow())
        
        print("   ✅ Association créée")
        
        # 4. Créer les paramètres de la société
        print("   ⚙️ Création des paramètres...")
        await conn.execute("""
            INSERT INTO company_settings (
                company_id, currency, timezone, date_format, 
                created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6)
        """,
        company_id,
        "EUR",
        "Europe/Paris", 
        "DD/MM/YYYY",
        datetime.utcnow(),
        datetime.utcnow()
        )
        
        print("   ✅ Paramètres créés")
        
        # 5. Créer quelques projets d'exemple
        print("   🏗️ Création de projets d'exemple...")
        
        projects_data = [
            ("Rénovation Bureau Central", "ORBIS-2024-001", "Rénovation complète des bureaux centraux", "en_cours", 150000.00),
            ("Construction Entrepôt", "ORBIS-2024-002", "Construction d'un nouvel entrepôt logistique", "en_cours", 300000.00),
            ("Aménagement Showroom", "ORBIS-2024-003", "Aménagement d'un showroom moderne", "termine", 80000.00)
        ]
        
        for name, code, description, status, budget in projects_data:
            project_id = await conn.fetchval("""
                INSERT INTO projects (
                    name, code, description, status, budget_total, company_id, 
                    start_date, created_at, updated_at, client_name
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING id
            """,
            name, code, description, status, budget, company_id,
            datetime.utcnow(), datetime.utcnow(), datetime.utcnow(),
            "Client ORBIS"
            )
            print(f"   ✅ Projet créé: {name}")
        
        return {
            "company_id": company_id,
            "user_id": user_id,
            "admin_email": admin_email
        }
        
    except Exception as e:
        print(f"❌ Erreur lors de la création: {e}")
        raise
    finally:
        await conn.close()


async def verify_setup():
    """Vérifier la configuration créée"""
    print("🔍 Vérification de la configuration...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier les données créées
        company_count = await conn.fetchval("SELECT COUNT(*) FROM companies WHERE name = 'ORBIS'")
        user_count = await conn.fetchval("SELECT COUNT(*) FROM users WHERE email = '<EMAIL>'")
        project_count = await conn.fetchval("SELECT COUNT(*) FROM projects")
        
        print(f"   📊 Sociétés ORBIS: {company_count}")
        print(f"   👤 Utilisateurs admin: {user_count}")
        print(f"   🏗️ Projets: {project_count}")
        
        if company_count == 1 and user_count == 1 and project_count >= 3:
            print("   ✅ Configuration vérifiée avec succès!")
            return True
        else:
            print("   ❌ Problème dans la configuration")
            return False
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Configuration Simple")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    try:
        # Créer la configuration ORBIS
        result = await create_orbis_setup()
        
        # Vérifier la configuration
        if await verify_setup():
            print("\n" + "="*60)
            print("✅ Configuration ORBIS terminée avec succès!")
            print("\n📋 Résumé:")
            print(f"   - Société: ORBIS (ID: {result['company_id']})")
            print(f"   - Utilisateur admin: {result['admin_email']}")
            print(f"   - User ID: {result['user_id']}")
            
            print("\n🔑 Pour vous connecter:")
            print(f"   Email: {result['admin_email']}")
            print("   Note: Vous devrez créer ce compte dans Supabase Auth")
            print("   ou utiliser l'authentification existante")
            
            print("\n📝 Prochaines étapes:")
            print("   1. Créer le compte admin dans Supabase Auth")
            print("   2. Lier le compte Supabase à l'utilisateur local")
            print("   3. Tester la connexion depuis le frontend")
            
            return True
        else:
            print("\n❌ Échec de la vérification")
            return False
        
    except Exception as e:
        print(f"\n❌ Erreur durant la configuration: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
