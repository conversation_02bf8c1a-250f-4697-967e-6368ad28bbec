#!/usr/bin/env python3
"""
Script pour appliquer la migration des stakeholders via SQL direct
Utilise psycopg2 pour éviter les problèmes d'async
"""

import sys
import os
import psycopg2
from psycopg2 import sql

def get_db_connection():
    """Obtient une connexion à la base de données"""
    try:
        # Paramètres de connexion (à adapter selon votre configuration)
        conn = psycopg2.connect(
            host="localhost",
            database="orbis_db",  # Remplacez par le nom de votre base
            user="postgres",      # Remplacez par votre utilisateur
            password="password"   # Remplacez par votre mot de passe
        )
        return conn
    except Exception as e:
        print(f"❌ Erreur de connexion à la base de données: {e}")
        print("💡 Vérifiez les paramètres de connexion dans le script")
        return None

def check_table_exists(cursor, table_name):
    """Vérifie si une table existe"""
    try:
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = %s
            );
        """, (table_name,))
        return cursor.fetchone()[0]
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de la table {table_name}: {e}")
        return False

def apply_stakeholders_migration():
    """Applique la migration des stakeholders"""
    print("🚀 Application de la migration stakeholders via SQL direct...")
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Vérifier si la table lot_intervenants existe
        if not check_table_exists(cursor, 'lot_intervenants'):
            print("⚠️  Table lot_intervenants n'existe pas, rien à migrer")
            return True
        
        # Vérifier si la table stakeholders existe déjà
        if check_table_exists(cursor, 'stakeholders'):
            print("✅ Table stakeholders existe déjà")
            return True
        
        print("📝 Renommage de la table lot_intervenants en stakeholders...")
        cursor.execute('ALTER TABLE lot_intervenants RENAME TO stakeholders')
        
        print("📝 Renommage des index...")
        # Renommer les index (ignorer les erreurs si ils n'existent pas)
        try:
            cursor.execute('ALTER INDEX ix_lot_intervenants_id RENAME TO ix_stakeholders_id')
        except Exception as e:
            print(f"⚠️  Index ix_lot_intervenants_id: {e}")
        
        try:
            cursor.execute('ALTER INDEX ix_lot_intervenants_lot_id RENAME TO ix_stakeholders_lot_id')
        except Exception as e:
            print(f"⚠️  Index ix_lot_intervenants_lot_id: {e}")
        
        try:
            cursor.execute('ALTER INDEX ix_lot_intervenants_company_id RENAME TO ix_stakeholders_company_id')
        except Exception as e:
            print(f"⚠️  Index ix_lot_intervenants_company_id: {e}")
        
        print("📝 Mise à jour des contraintes de clés étrangères...")
        # Supprimer les anciennes contraintes (ignorer les erreurs si elles n'existent pas)
        try:
            cursor.execute('ALTER TABLE stakeholders DROP CONSTRAINT IF EXISTS lot_intervenants_lot_id_fkey')
            cursor.execute('ALTER TABLE stakeholders DROP CONSTRAINT IF EXISTS lot_intervenants_company_id_fkey')
            cursor.execute('ALTER TABLE stakeholders DROP CONSTRAINT IF EXISTS lot_intervenants_created_by_fkey')
        except Exception as e:
            print(f"⚠️  Suppression contraintes: {e}")
        
        # Recréer les contraintes avec les nouveaux noms
        cursor.execute('''
            ALTER TABLE stakeholders 
            ADD CONSTRAINT stakeholders_lot_id_fkey 
            FOREIGN KEY (lot_id) REFERENCES lots(id)
        ''')
        
        cursor.execute('''
            ALTER TABLE stakeholders 
            ADD CONSTRAINT stakeholders_company_id_fkey 
            FOREIGN KEY (company_id) REFERENCES tcompanies(id)
        ''')
        
        cursor.execute('''
            ALTER TABLE stakeholders 
            ADD CONSTRAINT stakeholders_created_by_fkey 
            FOREIGN KEY (created_by) REFERENCES users(id)
        ''')
        
        # Valider les changements
        conn.commit()
        print("✅ Migration appliquée avec succès")
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Erreur lors de la migration: {e}")
        return False
    finally:
        conn.close()

def verify_migration():
    """Vérifie que la migration a été appliquée correctement"""
    print("\n🔍 Vérification de la migration...")
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Vérifier que la table stakeholders existe
        if not check_table_exists(cursor, 'stakeholders'):
            print("❌ Table 'stakeholders' non trouvée")
            return False
        
        print("✅ Table 'stakeholders' trouvée")
        
        # Vérifier que l'ancienne table lot_intervenants n'existe plus
        if check_table_exists(cursor, 'lot_intervenants'):
            print("⚠️  Ancienne table 'lot_intervenants' existe encore")
        else:
            print("✅ Ancienne table 'lot_intervenants' supprimée")
        
        # Compter les enregistrements
        cursor.execute("SELECT COUNT(*) FROM stakeholders")
        count = cursor.fetchone()[0]
        print(f"✅ {count} enregistrements dans la table stakeholders")
        
        # Vérifier les colonnes
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'stakeholders' 
            ORDER BY ordinal_position
        """)
        
        columns = [row[0] for row in cursor.fetchall()]
        expected_columns = ['id', 'lot_id', 'company_id', 'role', 'is_active', 'created_at', 'created_by']
        
        for expected in expected_columns:
            if expected in columns:
                print(f"✅ Colonne '{expected}' trouvée")
            else:
                print(f"❌ Colonne '{expected}' manquante")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False
    finally:
        conn.close()

def main():
    """Fonction principale"""
    print("🔄 Migration SQL Directe des Stakeholders (lot_intervenants → stakeholders)")
    print("=" * 70)
    
    # Vérifier que psycopg2 est installé
    try:
        import psycopg2
    except ImportError:
        print("❌ psycopg2 n'est pas installé. Installez-le avec: pip install psycopg2-binary")
        return False
    
    # Étape 1: Appliquer la migration
    if not apply_stakeholders_migration():
        print("❌ Échec de l'application de la migration")
        return False
    
    # Étape 2: Vérifier la migration
    if not verify_migration():
        print("❌ Échec de la vérification de la migration")
        return False
    
    print("\n" + "=" * 70)
    print("🎉 Migration des stakeholders terminée avec succès !")
    print("\nProchaines étapes:")
    print("1. Tester l'implémentation avec: python test_stakeholders_implementation.py")
    print("2. Mettre à jour le frontend pour utiliser les nouveaux endpoints")
    print("3. Supprimer les alias de compatibilité après validation complète")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
