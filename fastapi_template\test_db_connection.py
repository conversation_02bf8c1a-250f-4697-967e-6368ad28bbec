#!/usr/bin/env python3
"""
Script de test de connexion à la base de données pour FastAPI ORBIS
Ce script teste la connexion à Supabase PostgreSQL de plusieurs façons
"""

import asyncio
import sys
import os
from datetime import datetime
from pathlib import Path

# Ajouter le répertoire app au path Python
sys.path.insert(0, str(Path(__file__).parent))

# Imports pour les tests
import asyncpg
import psycopg2
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine
from supabase import create_client, Client

# Import des configurations de l'app
from app.core.config import settings
from app.db.session import engine, async_engine, SessionLocal, AsyncSessionLocal


def print_header(title: str):
    """Affiche un en-tête formaté"""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print(f"{'='*60}")


def print_test_result(test_name: str, success: bool, details: str = ""):
    """Affiche le résultat d'un test"""
    status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
    print(f"{status} - {test_name}")
    if details:
        print(f"   📝 {details}")


async def test_asyncpg_connection():
    """Test de connexion directe avec asyncpg"""
    print_header("Test de connexion AsyncPG")
    
    try:
        # Extraire les paramètres de connexion de l'URL
        url = settings.ASYNC_DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        
        conn = await asyncpg.connect(url)
        
        # Test simple
        version = await conn.fetchval("SELECT version()")
        await conn.close()
        
        print_test_result("Connexion AsyncPG", True, f"PostgreSQL: {version[:50]}...")
        return True
        
    except Exception as e:
        print_test_result("Connexion AsyncPG", False, str(e))
        return False


def test_psycopg2_connection():
    """Test de connexion avec psycopg2"""
    print_header("Test de connexion psycopg2")
    
    try:
        # Utiliser l'URL synchrone
        url = settings.DATABASE_URL
        
        conn = psycopg2.connect(url)
        cursor = conn.cursor()
        
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        print_test_result("Connexion psycopg2", True, f"PostgreSQL: {version[:50]}...")
        return True
        
    except Exception as e:
        print_test_result("Connexion psycopg2", False, str(e))
        return False


def test_sqlalchemy_sync():
    """Test de connexion SQLAlchemy synchrone"""
    print_header("Test SQLAlchemy Synchrone")
    
    try:
        # Test avec l'engine configuré
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
        
        print_test_result("SQLAlchemy Sync Engine", True, f"PostgreSQL: {version[:50]}...")
        
        # Test avec une session
        with SessionLocal() as session:
            result = session.execute(text("SELECT current_database()"))
            db_name = result.fetchone()[0]
        
        print_test_result("SQLAlchemy Sync Session", True, f"Base de données: {db_name}")
        return True
        
    except Exception as e:
        print_test_result("SQLAlchemy Synchrone", False, str(e))
        return False


async def test_sqlalchemy_async():
    """Test de connexion SQLAlchemy asynchrone"""
    print_header("Test SQLAlchemy Asynchrone")
    
    try:
        # Test avec l'engine async configuré
        async with async_engine.connect() as conn:
            result = await conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
        
        print_test_result("SQLAlchemy Async Engine", True, f"PostgreSQL: {version[:50]}...")
        
        # Test avec une session async
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT current_database()"))
            db_name = result.fetchone()[0]
        
        print_test_result("SQLAlchemy Async Session", True, f"Base de données: {db_name}")
        return True
        
    except Exception as e:
        print_test_result("SQLAlchemy Asynchrone", False, str(e))
        return False


def test_supabase_client():
    """Test de connexion avec le client Supabase"""
    print_header("Test Client Supabase")
    
    try:
        # Créer le client Supabase
        supabase: Client = create_client(settings.SUPABASE_URL, settings.SUPABASE_ANON_KEY)
        
        # Test simple avec une requête sur une table système
        response = supabase.rpc('version').execute()
        
        if response.data:
            print_test_result("Client Supabase", True, "Connexion API réussie")
            return True
        else:
            print_test_result("Client Supabase", False, "Pas de données retournées")
            return False
            
    except Exception as e:
        print_test_result("Client Supabase", False, str(e))
        return False


async def test_database_tables():
    """Test de vérification des tables de l'application"""
    print_header("Vérification des Tables")
    
    try:
        async with async_engine.connect() as conn:
            # Lister les tables existantes
            result = await conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            
            tables = [row[0] for row in result.fetchall()]
            
            if tables:
                print_test_result("Lecture des tables", True, f"{len(tables)} tables trouvées")
                print("   📋 Tables disponibles:")
                for table in tables[:10]:  # Afficher les 10 premières
                    print(f"      - {table}")
                if len(tables) > 10:
                    print(f"      ... et {len(tables) - 10} autres")
            else:
                print_test_result("Lecture des tables", False, "Aucune table trouvée")
            
            return len(tables) > 0
            
    except Exception as e:
        print_test_result("Vérification des tables", False, str(e))
        return False


def print_configuration():
    """Affiche la configuration de la base de données"""
    print_header("Configuration de la Base de Données")
    
    print(f"🔗 URL Supabase: {settings.SUPABASE_URL}")
    print(f"🔗 URL Base de données: {settings.DATABASE_URL[:50]}...")
    print(f"🔗 URL Async: {settings.ASYNC_DATABASE_URL[:50]}...")
    print(f"🔊 Echo SQL: {settings.DATABASE_ECHO}")
    print(f"🌍 Environnement: {settings.ENVIRONMENT}")
    print(f"🐛 Debug: {settings.DEBUG}")


async def main():
    """Fonction principale de test"""
    print("🚀 ORBIS - Test de Connexion à la Base de Données")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Afficher la configuration
    print_configuration()
    
    # Compteur de tests réussis
    tests_passed = 0
    total_tests = 6
    
    # Tests de connexion
    if test_psycopg2_connection():
        tests_passed += 1
    
    if await test_asyncpg_connection():
        tests_passed += 1
    
    if test_sqlalchemy_sync():
        tests_passed += 1
    
    if await test_sqlalchemy_async():
        tests_passed += 1
    
    if test_supabase_client():
        tests_passed += 1
    
    if await test_database_tables():
        tests_passed += 1
    
    # Résumé final
    print_header("Résumé des Tests")
    success_rate = (tests_passed / total_tests) * 100
    
    if tests_passed == total_tests:
        print(f"🎉 TOUS LES TESTS RÉUSSIS! ({tests_passed}/{total_tests}) - {success_rate:.0f}%")
        print("✅ Votre base de données est correctement configurée et accessible.")
    elif tests_passed > 0:
        print(f"⚠️  TESTS PARTIELLEMENT RÉUSSIS ({tests_passed}/{total_tests}) - {success_rate:.0f}%")
        print("🔧 Certaines connexions fonctionnent, vérifiez les erreurs ci-dessus.")
    else:
        print(f"❌ TOUS LES TESTS ONT ÉCHOUÉ ({tests_passed}/{total_tests}) - {success_rate:.0f}%")
        print("🚨 Problème de configuration de la base de données.")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
