"""Create project_company table

Revision ID: create_project_company
Revises: 
Create Date: 2025-01-03 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'create_project_company'
down_revision = None  # Remplacer par la dernière révision si nécessaire
branch_labels = None
depends_on = None

def upgrade():
    # Créer la table project_company pour gérer la relation many-to-many
    op.create_table('project_company',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('company_id', sa.Integer(), nullable=False),
        sa.Column('role', sa.String(50), nullable=False, default='OWNER'),  # OWNER, PARTNER, SUBCONTRACTOR
        sa.Column('is_active', sa.<PERSON>(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('NOW()')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('NOW()')),
        sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('project_id', 'company_id', name='unique_project_company')
    )
    
    # Créer les index pour optimiser les requêtes
    op.create_index('ix_project_company_project_id', 'project_company', ['project_id'])
    op.create_index('ix_project_company_company_id', 'project_company', ['company_id'])
    
    # Migrer les données existantes si la colonne company_id existe dans projects
    try:
        # Vérifier si la colonne company_id existe dans projects
        op.execute("""
            INSERT INTO project_company (project_id, company_id, role, is_active, created_at, updated_at)
            SELECT 
                p.id as project_id,
                p.company_id,
                'OWNER' as role,
                true as is_active,
                COALESCE(p.created_at, NOW()) as created_at,
                COALESCE(p.updated_at, NOW()) as updated_at
            FROM projects p
            WHERE p.company_id IS NOT NULL
        """)
        print("✅ Données migrées depuis projects.company_id vers project_company")
    except Exception as e:
        print(f"⚠️ Migration des données ignorée (normal si company_id n'existe pas): {e}")
    
    # Supprimer la colonne company_id de projects si elle existe
    try:
        op.drop_column('projects', 'company_id')
        print("✅ Colonne company_id supprimée de la table projects")
    except Exception as e:
        print(f"⚠️ Suppression de company_id ignorée (normal si elle n'existe pas): {e}")

def downgrade():
    # Recréer la colonne company_id dans projects
    op.add_column('projects', sa.Column('company_id', sa.Integer(), nullable=True))
    
    # Migrer les données de retour
    op.execute("""
        UPDATE projects 
        SET company_id = (
            SELECT pc.company_id 
            FROM project_company pc 
            WHERE pc.project_id = projects.id 
            AND pc.role = 'OWNER' 
            AND pc.is_active = true
            LIMIT 1
        )
    """)
    
    # Supprimer la table project_company
    op.drop_index('ix_project_company_company_id')
    op.drop_index('ix_project_company_project_id')
    op.drop_table('project_company')
