import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import DocumentCreationButtons from '../DocumentCreationButtons'
import { DocumentType } from '@/types/technical-document'

describe('DocumentCreationButtons', () => {
  const mockOnDocumentCreate = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render CCTP and DPGF creation buttons', () => {
    // Arrange & Act
    render(<DocumentCreationButtons onDocumentCreate={mockOnDocumentCreate} />)

    // Assert
    expect(screen.getByText('Nouveau CCTP')).toBeInTheDocument()
    expect(screen.getByText('Nouveau DPGF')).toBeInTheDocument()
  })

  it('should call onDocumentCreate with CCTP type when CCTP button is clicked', () => {
    // Arrange
    render(<DocumentCreationButtons onDocumentCreate={mockOnDocumentCreate} />)
    const cctpButton = screen.getByText('Nouveau CCTP')

    // Act
    fireEvent.click(cctpButton)

    // Assert
    expect(mockOnDocumentCreate).toHaveBeenCalledWith(DocumentType.CCTP)
    expect(mockOnDocumentCreate).toHaveBeenCalledTimes(1)
  })

  it('should call onDocumentCreate with DPGF type when DPGF button is clicked', () => {
    // Arrange
    render(<DocumentCreationButtons onDocumentCreate={mockOnDocumentCreate} />)
    const dpgfButton = screen.getByText('Nouveau DPGF')

    // Act
    fireEvent.click(dpgfButton)

    // Assert
    expect(mockOnDocumentCreate).toHaveBeenCalledWith(DocumentType.DPGF)
    expect(mockOnDocumentCreate).toHaveBeenCalledTimes(1)
  })

  it('should have proper styling classes for buttons', () => {
    // Arrange & Act
    render(<DocumentCreationButtons onDocumentCreate={mockOnDocumentCreate} />)
    const cctpButton = screen.getByText('Nouveau CCTP')
    const dpgfButton = screen.getByText('Nouveau DPGF')

    // Assert
    expect(cctpButton).toHaveClass('bg-blue-600', 'hover:bg-blue-700')
    expect(dpgfButton).toHaveClass('bg-green-600', 'hover:bg-green-700')
  })

  it('should display icons in buttons', () => {
    // Arrange & Act
    render(<DocumentCreationButtons onDocumentCreate={mockOnDocumentCreate} />)

    // Assert
    // Vérifier que les boutons contiennent des icônes (PlusIcon)
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button.querySelector('svg')).toBeInTheDocument()
    })
  })

  it('should be accessible with proper button roles', () => {
    // Arrange & Act
    render(<DocumentCreationButtons onDocumentCreate={mockOnDocumentCreate} />)

    // Assert
    const buttons = screen.getAllByRole('button')
    expect(buttons).toHaveLength(2)
    
    buttons.forEach(button => {
      expect(button).toBeEnabled()
      expect(button).toHaveAttribute('type', 'button')
    })
  })

  it('should handle multiple rapid clicks correctly', () => {
    // Arrange
    render(<DocumentCreationButtons onDocumentCreate={mockOnDocumentCreate} />)
    const cctpButton = screen.getByText('Nouveau CCTP')

    // Act
    fireEvent.click(cctpButton)
    fireEvent.click(cctpButton)
    fireEvent.click(cctpButton)

    // Assert
    expect(mockOnDocumentCreate).toHaveBeenCalledTimes(3)
    expect(mockOnDocumentCreate).toHaveBeenCalledWith(DocumentType.CCTP)
  })
})
