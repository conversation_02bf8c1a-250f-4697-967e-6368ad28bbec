'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname, useSearchParams } from 'next/navigation'
import { projectsAPI } from '@/lib/api/projects'
// Icônes essentielles uniquement pour la navigation mobile
import {
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface SidebarItem {
  name: string
  href: string
  badge?: number
}

// Navigation de base sans badges (seront ajoutés dynamiquement)
const baseNavigation: SidebarItem[] = [
  { name: 'Dashboard', href: '/dashboard' },
  { name: 'Carnet d\'adresses', href: '/tcompanies' },
  { name: 'Gestion des devis', href: '/projects?filter=devis' },
  { name: 'Gestion des AO', href: '/projects?filter=ao' },
  { name: 'Gestion des affaires', href: '/projects?filter=affaires' },
  { name: '<PERSON><PERSON><PERSON>', href: '/employees' },
  { name: 'Rap<PERSON>', href: '/reports' },
  { name: '<PERSON>m<PERSON><PERSON>', href: '/settings' },
]

interface ModernSidebarProps {
  user?: {
    name: string
    email: string
    avatar?: string
  }
}

export default function ModernSidebar({ user }: ModernSidebarProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [navigation, setNavigation] = useState<SidebarItem[]>(baseNavigation)
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Récupérer les statistiques pour les badges
  useEffect(() => {
    const fetchStats = async () => {
      try {
        const stats = await projectsAPI.getProjectsStats()

        // Mettre à jour la navigation avec les vrais badges
        const updatedNavigation = baseNavigation.map(item => {
          if (item.href.includes('filter=devis')) {
            return { ...item, badge: stats.by_nature?.DEVIS || 0 }
          } else if (item.href.includes('filter=ao')) {
            return { ...item, badge: stats.by_nature?.AO || 0 }
          } else if (item.href.includes('filter=affaires')) {
            return { ...item, badge: stats.by_nature?.AFFAIRE || 0 }
          }
          return item
        })

        setNavigation(updatedNavigation)
      } catch (error) {
        console.error('Erreur lors du chargement des statistiques:', error)
        // En cas d'erreur, utiliser la navigation de base
        setNavigation(baseNavigation)
      }
    }

    fetchStats()
  }, [])


  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="p-2 rounded-lg bg-white shadow-lg border border-ton3 hover:bg-ton3 transition-colors"
        >
          {isOpen ? (
            <XMarkIcon className="h-6 w-6 text-ton2" />
          ) : (
            <Bars3Icon className="h-6 w-6 text-ton2" />
          )}
        </button>
      </div>

      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed left-0 top-0 z-40 h-screen w-72 bg-white border-r border-ton3 shadow-lg
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-ton3">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-ton1 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">O</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-ton2">ORBIS</h1>
                <p className="text-xs text-ton1 font-medium">SUIVI TRAVAUX</p>
              </div>
            </div>
          </div>

          {/* Search - Simplifié sans icône */}
          <div className="p-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Rechercher..."
                className="w-full px-4 py-2 border border-ton3 rounded-lg focus:outline-none focus:ring-2 focus:ring-ton1 focus:border-transparent bg-ton3"
              />
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 space-y-2 pb-4">
            {navigation.map((item) => {
              const isActive = pathname === item.href || 
                (item.href.includes('filter=devis') && pathname.includes('projects') && searchParams?.get('filter') === 'devis') ||
                (item.href.includes('filter=ao') && pathname.includes('projects') && searchParams?.get('filter') === 'ao') ||
                (item.href.includes('filter=affaires') && pathname.includes('projects') && searchParams?.get('filter') === 'affaires') ||
                (item.href === '/projects' && pathname.startsWith('/projects') && !searchParams?.get('filter'))

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`sidebar-item justify-between ${isActive ? 'active' : ''}`}
                  onClick={() => setIsOpen(false)}
                >
                  <div className="flex items-center space-x-3">
                    <span className="font-medium">{item.name}</span>
                  </div>
                  {item.badge && (
                    <span className={`
                      px-2 py-1 text-xs font-medium rounded-full
                      ${isActive
                        ? 'bg-ton3 text-ton1'
                        : 'bg-ton3 text-ton1'
                      }
                    `}>
                      {item.badge}
                    </span>
                  )}
                </Link>
              )
            })}
          </nav>
        </div>
      </div>
    </>
  )
}
