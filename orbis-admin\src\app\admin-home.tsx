'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'
import {
  Building2,
  Activity,
  TrendingUp,
  AlertCircle,
  Plus,
  ArrowUpRight,
  Clock,
  CheckCircle
} from 'lucide-react'
import ProtectedRoute from '@/components/ProtectedRoute'
import { adminApi, type DashboardStats } from '@/lib/admin-api'
import { authMonitor } from '@/lib/auth-monitor'

export default function AdminHome() {
  const { user } = useAuth()
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null)
  const [recentActivity, setRecentActivity] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Démarrer le monitoring d'authentification - DÉSACTIVÉ temporairement
  // useEffect(() => {
  //   authMonitor.startMonitoring()
  //   return () => authMonitor.stopMonitoring()
  // }, [])

  // Fetch real data from API avec retry automatique
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)

        // Vérification robuste avec le monitoring - DÉSACTIVÉ
        // if (!authMonitor.forceCheck()) {
        //   console.error('❌ Échec vérification authentification')
        //   return
        // }

        console.log('🔑 Authentification validée, récupération des données...')

        // Fetch dashboard statistics and recent activity avec timeout
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), 10000)
        )

        const [stats, activity] = await Promise.race([
          Promise.all([
            adminApi.getDashboardStats(),
            adminApi.getRecentActivity()
          ]),
          timeoutPromise
        ]) as [any, any]

        console.log('✅ Données récupérées:', { stats, activity })
        setDashboardStats(stats)
        setRecentActivity(activity)

      } catch (error) {
        console.error('❌ Erreur récupération données dashboard:', error)

        // Gestion d'erreur robuste
        if (error.message.includes('Token invalide') || error.message.includes('401')) {
          console.error('❌ Token expiré - redirection vers login')
          localStorage.removeItem('orbis_jwt_token')
          localStorage.removeItem('orbis_user')
          window.location.href = '/login'
          return
        }

        // Set empty data au lieu de mock data
        setDashboardStats({
          total_companies: 0,
          total_users: 0,
          active_users: 0,
          total_projects: 0,
          active_projects: 0,
          completed_projects: 0
        })
        setRecentActivity([])
      } finally {
        setIsLoading(false)
      }
    }

    // Délai pour s'assurer que l'auth est bien initialisée
    const timer = setTimeout(fetchDashboardData, 500)
    return () => clearTimeout(timer)
  }, [])

  const adminModules = [
    {
      title: "Gestion des Entreprises",
      description: "Créer, modifier et gérer les entreprises clientes",
      icon: Building2,
      href: "/companies",
      color: "from-teal-500 to-cyan-600",
      stats: dashboardStats ? `${dashboardStats.total_companies} entreprises` : "Chargement..."
    }
  ]

  const quickStats = [
    {
      title: "Entreprises Actives",
      value: dashboardStats ? dashboardStats.total_companies.toString() : "0",
      change: "",
      changeType: "neutral",
      icon: Building2
    },
    {
      title: "Utilisateurs Total",
      value: dashboardStats ? dashboardStats.total_users.toString() : "0",
      change: "",
      changeType: "neutral",
      icon: Activity
    },
    {
      title: "Projets Actifs",
      value: dashboardStats ? dashboardStats.active_projects.toString() : "0",
      change: "",
      changeType: "neutral",
      icon: TrendingUp
    },
    {
      title: "Projets Terminés",
      value: dashboardStats ? dashboardStats.completed_projects.toString() : "0",
      change: "",
      changeType: "neutral",
      icon: CheckCircle
    }
  ]

  // Format recent activity for display
  const formatRecentActivities = () => {
    if (!recentActivity || recentActivity.length === 0) {
      return [{
        title: "Aucune activité récente",
        description: "Les activités apparaîtront ici",
        time: "",
        type: "info"
      }]
    }

    return recentActivity.map(activity => ({
      title: activity.description,
      description: activity.user || "Système",
      time: new Date(activity.timestamp).toLocaleDateString('fr-FR'),
      type: activity.type === 'company_created' ? 'success' : 'info'
    }))
  }

  if (isLoading) {
    return (
      <ProtectedRoute requireAdmin={true}>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requireAdmin={true}>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-teal-600 to-cyan-600 rounded-2xl p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">
                Bienvenue, {user?.user_metadata?.name || 'Admin'} 👋
              </h1>
              <p className="text-teal-100 text-lg">
                Gérez votre plateforme ORBIS depuis ce tableau de bord centralisé
              </p>
            </div>
            <div className="hidden md:block">
              <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
                <Building2 className="w-12 h-12 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <div key={index} className="modern-card p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                    <div className="flex items-center mt-2">
                      <span className={`text-sm font-medium ${
                        stat.changeType === 'increase' ? 'text-teal-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </span>
                      <span className="text-sm text-gray-500 ml-1">ce mois</span>
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                    <IconComponent className="w-6 h-6 text-teal-600" />
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Main Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Admin Modules - 2/3 width */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Modules d'Administration</h2>
              <button className="btn-outline">
                <Plus className="w-4 h-4 mr-2" />
                Nouveau
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {adminModules.map((module) => {
                const IconComponent = module.icon
                return (
                  <Link
                    key={module.href}
                    href={module.href}
                    className="group modern-card p-6 hover:scale-105 transition-all duration-300"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className={`w-12 h-12 bg-gradient-to-r ${module.color} rounded-xl flex items-center justify-center shadow-lg`}>
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <ArrowUpRight className="w-5 h-5 text-gray-400 group-hover:text-teal-600 transition-colors" />
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 group-hover:text-teal-600 transition-colors mb-2">
                      {module.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3">
                      {module.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="badge-success">
                        {module.stats}
                      </span>
                    </div>
                  </Link>
                )
              })}
            </div>
          </div>

          {/* Sidebar - 1/3 width */}
          <div className="space-y-6">
            {/* Recent Activities */}
            <div className="modern-card p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Activité Récente</h3>
                <Clock className="w-5 h-5 text-gray-400" />
              </div>

              <div className="space-y-4">
                {formatRecentActivities().map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className={`w-2 h-2 rounded-full mt-2 ${
                      activity.type === 'success' ? 'bg-teal-500' :
                      activity.type === 'warning' ? 'bg-yellow-500' :
                      'bg-blue-500'
                    }`} />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      <p className="text-xs text-gray-500 truncate">{activity.description}</p>
                      <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>

              <button className="w-full mt-4 text-sm text-teal-600 hover:text-teal-700 font-medium">
                Voir toute l'activité
              </button>
            </div>

            {/* Quick Actions */}
            <div className="modern-card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions Rapides</h3>

              <div className="space-y-3">
                <Link
                  href="/companies/new"
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-teal-50 transition-colors group"
                >
                  <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center group-hover:bg-teal-200 transition-colors">
                    <Plus className="w-4 h-4 text-teal-600" />
                  </div>
                  <span className="text-sm font-medium text-gray-900">Nouvelle Entreprise</span>
                </Link>


              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
