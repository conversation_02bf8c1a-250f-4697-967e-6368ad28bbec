#!/usr/bin/env python3
"""
Test simple de la base de données
"""
import asyncio
import asyncpg
from app.core.config import settings

async def test_db():
    print("🔍 Test de la base de données...")
    
    try:
        conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)
        
        # Tester si les tables existent
        tables = await conn.fetch("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('companies', 'user_companies', 'users')
            ORDER BY table_name
        """)
        
        print(f'📋 {len(tables)} table(s) trouvée(s):')
        for table in tables:
            print(f'  ✅ {table["table_name"]}')
        
        if not tables:
            print('❌ Aucune table trouvée!')
            await conn.close()
            return False
            
        # Tester une requête simple sur companies
        try:
            companies = await conn.fetch('SELECT id, name FROM companies LIMIT 3')
            print(f'\n🏢 {len(companies)} entreprise(s):')
            for company in companies:
                print(f'  - {company["id"]}: {company["name"]}')
        except Exception as e:
            print(f'❌ Erreur requête companies: {e}')
            
        # Tester user_companies
        try:
            user_companies = await conn.fetch('SELECT user_id, company_id FROM user_companies LIMIT 3')
            print(f'\n👥 {len(user_companies)} relation(s) user_companies:')
            for uc in user_companies:
                print(f'  - User {uc["user_id"]} -> Company {uc["company_id"]}')
        except Exception as e:
            print(f'❌ Erreur requête user_companies: {e}')
            
        await conn.close()
        return True
        
    except Exception as e:
        print(f'❌ Erreur connexion DB: {e}')
        return False

if __name__ == "__main__":
    success = asyncio.run(test_db())
    if success:
        print('\n✅ Test DB réussi!')
    else:
        print('\n❌ Test DB échoué!')
