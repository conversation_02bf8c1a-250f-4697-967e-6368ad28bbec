'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Modal } from '@/components/ui/Modal'
import { api } from '@/lib/api'

export default function Documents() {
  const [documents, setDocuments] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setLoading(true)
        const data = await api.getDocuments()
        setDocuments(data)
        setError(null)
      } catch (err) {
        console.error('Error fetching documents:', err)
        setError('Erreur lors du chargement des documents')
      } finally {
        setLoading(false)
      }
    }

    fetchDocuments()
  }, [])

  const filteredDocuments = documents.filter((doc: any) => {
    const matchesSearch = 
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.project.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.uploadedBy.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterType === 'all' || doc.category === filterType
    
    return matchesSearch && matchesFilter
  })

  const categories = [...new Set(documents.map((doc: any) => doc.category))]

  // Suppression des icônes pour un design sobre
  const getFileType = (type: string) => {
    return type || 'Document'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Validé': return 'bg-green-100 text-green-800'
      case 'En attente': return 'bg-yellow-100 text-yellow-800'
      case 'En révision': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleFileUpload = async () => {
    if (selectedFile) {
      try {
        const formData = new FormData()
        formData.append('file', selectedFile)
        
        await api.uploadDocument(formData)
        
        // Refresh documents list
        const data = await api.getDocuments()
        setDocuments(data)
        
        setIsUploadModalOpen(false)
        setSelectedFile(null)
      } catch (err) {
        console.error('Error uploading file:', err)
        alert('Erreur lors du téléchargement du fichier')
      }
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-lg text-gray-600">Chargement des documents...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-4">{error}</div>
          <Button onClick={() => window.location.reload()}>
            Réessayer
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Documents</h1>
          <p className="text-gray-600 mt-1">Gérez tous vos documents de projets</p>
        </div>
        <Button 
          className="bg-blue-600 hover:bg-blue-700"
          onClick={() => setIsUploadModalOpen(true)}
        >
          Télécharger Document
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-ton1 rounded-lg flex items-center justify-center mr-4">
              <span className="text-white font-bold">DOC</span>
            </div>
            <div>
              <div className="text-2xl font-bold text-ton2">{documents.length}</div>
              <div className="text-sm text-ton2-light">Total Documents</div>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">✅</div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {documents.filter((doc: any) => doc.status === 'Validé').length}
              </div>
              <div className="text-sm text-gray-600">Validés</div>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">⏳</div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">
                {documents.filter((doc: any) => doc.status === 'En attente').length}
              </div>
              <div className="text-sm text-gray-600">En attente</div>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">💾</div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {Math.round(documents.reduce((sum: number, doc: any) => {
                  const sizeInMB = parseFloat(doc.size.replace(/[^\d.]/g, ''))
                  return sum + (doc.size.includes('KB') ? sizeInMB / 1024 : sizeInMB)
                }, 0))} MB
              </div>
              <div className="text-sm text-gray-600">Espace utilisé</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Rechercher un document..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="flex gap-2 flex-wrap">
            <Button
              variant={filterType === 'all' ? 'primary' : 'outline'}
              onClick={() => setFilterType('all')}
            >
              Tous
            </Button>
            {categories.map((category) => (
              <Button
                key={category}
                variant={filterType === category ? 'primary' : 'outline'}
                onClick={() => setFilterType(category)}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </Card>

      {/* Documents List */}
      <Card className="p-6">
        <div className="space-y-4">
          {filteredDocuments.map((doc: any) => (
            <div key={doc.id} className="flex items-center justify-between p-4 border border-ton3 rounded-lg hover:bg-ton3">
              <div className="flex items-center flex-1">
                <div className="w-10 h-10 bg-ton1 rounded-lg flex items-center justify-center mr-4">
                  <span className="text-white text-xs font-bold">{getFileType(doc.type).substring(0, 3).toUpperCase()}</span>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-ton2">{doc.name}</h3>
                  <div className="flex items-center gap-4 text-sm text-ton2-light mt-1">
                    <span>{doc.project}</span>
                    <span>•</span>
                    <span>{doc.size}</span>
                    <span>•</span>
                    <span>Par {doc.uploadedBy}</span>
                    <span>•</span>
                    <span>{new Date(doc.uploadDate).toLocaleDateString('fr-FR')}</span>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(doc.status)}`}>
                    {doc.status}
                  </span>
                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                    {doc.category}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-2 ml-4">
                <Button variant="outline" size="sm">
                  Prévisualiser
                </Button>
                <Button variant="outline" size="sm">
                  Télécharger
                </Button>
                <Button variant="outline" size="sm">
                  Partager
                </Button>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Empty State */}
      {filteredDocuments.length === 0 && (
        <Card className="p-12 text-center">
          <div className="w-20 h-20 bg-ton3 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-ton1 text-2xl font-bold">DOC</span>
          </div>
          <h3 className="text-xl font-semibold text-ton2 mb-2">Aucun document trouvé</h3>
          <p className="text-ton2-light mb-6">
            {searchTerm || filterType !== 'all' 
              ? 'Aucun document ne correspond à vos critères de recherche.'
              : 'Vous n\'avez pas encore de documents. Téléchargez votre premier document pour commencer.'
            }
          </p>
          <Button 
            className="bg-blue-600 hover:bg-blue-700"
            onClick={() => setIsUploadModalOpen(true)}
          >
            Télécharger Document
          </Button>
        </Card>
      )}

      {/* Upload Modal */}
      <Modal isOpen={isUploadModalOpen} onClose={() => setIsUploadModalOpen(false)}>
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Télécharger un document</h2>
          <div className="space-y-4">
            <div className="border-2 border-dashed border-ton3 rounded-lg p-8 text-center">
              <div className="w-16 h-16 bg-ton3 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-ton1 text-lg font-bold">+</span>
              </div>
              <p className="text-ton2-light mb-4">
                Glissez-déposez votre fichier ici ou cliquez pour sélectionner
              </p>
              <input
                type="file"
                onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                className="hidden"
                id="file-upload"
              />
              <label htmlFor="file-upload">
                <Button variant="outline" className="cursor-pointer">
                  Sélectionner un fichier
                </Button>
              </label>
              {selectedFile && (
                <p className="mt-2 text-sm text-gray-600">
                  Fichier sélectionné: {selectedFile.name}
                </p>
              )}
            </div>
            <Input label="Nom du document" placeholder="Entrez le nom du document" />
            <div className="grid grid-cols-2 gap-4">
              <Input label="Projet" placeholder="Sélectionnez un projet" />
              <Input label="Catégorie" placeholder="Sélectionnez une catégorie" />
            </div>
            <Input label="Description" placeholder="Description du document (optionnel)" />
            <div className="flex justify-end gap-2 mt-6">
              <Button 
                variant="outline" 
                onClick={() => setIsUploadModalOpen(false)}
              >
                Annuler
              </Button>
              <Button onClick={handleFileUpload} disabled={!selectedFile}>
                Télécharger
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  )
}