classDiagram
    %% Base Classes
    class BaseModel {
        <<abstract>>
        +id: int
        +created_at: datetime
        +updated_at: datetime
        +is_active: bool
        +__init__()
        +save()
        +delete()
        +to_dict() dict
    }

    class MultiTenantMixin {
        <<mixin>>
        +company_id: int
        +get_by_company(company_id: int) List
        +filter_by_company(query: Query, company_id: int) Query
    }

    %% User Management
    class User {
        +id: int
        +email: str
        +hashed_password: str
        +first_name: str
        +last_name: str
        +is_active: bool
        +is_verified: bool
        +role: UserRole
        +companies: List[UserCompany]
        +__init__(email: str, password: str)
        +verify_password(password: str) bool
        +get_companies() List[Company]
        +has_permission(permission: str, company_id: int) bool
    }

    class UserRole {
        <<enumeration>>
        ADMIN
        PROJECT_MANAGER
        EMPLOYEE
        VIEWER
    }

    class UserCompany {
        +user_id: int
        +company_id: int
        +role: UserRole
        +permissions: List[str]
        +is_default: bool
        +created_at: datetime
    }

    %% Company Management
    class Company {
        +id: int
        +name: str
        +siret: str
        +address: str
        +phone: str
        +email: str
        +logo_url: str
        +settings: CompanySettings
        +projects: List[Project]
        +employees: List[Employee]
        +__init__(name: str, siret: str)
        +get_active_projects() List[Project]
        +get_employees_count() int
        +get_settings() CompanySettings
    }

    class CompanySettings {
        +company_id: int
        +default_currency: str
        +tax_rate: float
        +working_hours_per_day: int
        +auto_create_folders: bool
        +notification_settings: dict
        +__init__(company_id: int)
        +update_settings(settings: dict)
    }

    %% Project Management
    class Project {
        +id: int
        +company_id: int
        +name: str
        +description: str
        +client_name: str
        +client_contact: str
        +address: str
        +start_date: date
        +end_date: date
        +status: ProjectStatus
        +budget_total: Decimal
        +budget_used: Decimal
        +progress_percentage: float
        +documents: List[Document]
        +employees: List[ProjectEmployee]
        +purchase_orders: List[PurchaseOrder]
        +__init__(name: str, company_id: int)
        +get_progress() float
        +get_budget_remaining() Decimal
        +add_employee(employee: Employee, role: str)
        +create_folder_structure()
        +get_dashboard_data() dict
    }

    class ProjectStatus {
        <<enumeration>>
        DRAFT
        ACTIVE
        ON_HOLD
        COMPLETED
        ARCHIVED
    }

    class ProjectEmployee {
        +project_id: int
        +employee_id: int
        +role: str
        +hourly_rate: Decimal
        +assigned_date: date
        +is_active: bool
    }

    %% Employee Management
    class Employee {
        +id: int
        +company_id: int
        +employee_number: str
        +first_name: str
        +last_name: str
        +email: str
        +phone: str
        +position: str
        +hire_date: date
        +hourly_rate: Decimal
        +is_active: bool
        +time_entries: List[TimeEntry]
        +projects: List[ProjectEmployee]
        +__init__(employee_number: str, first_name: str, last_name: str)
        +get_current_projects() List[Project]
        +get_total_hours_month(month: int, year: int) float
        +calculate_monthly_salary(month: int, year: int) Decimal
    }

    class TimeEntry {
        +id: int
        +employee_id: int
        +project_id: int
        +date: date
        +start_time: time
        +end_time: time
        +break_duration: int
        +description: str
        +is_validated: bool
        +validated_by: int
        +__init__(employee_id: int, project_id: int, date: date)
        +calculate_hours() float
        +validate(user_id: int)
    }

    %% Supplier Management
    class Supplier {
        +id: int
        +company_id: int
        +name: str
        +supplier_type: SupplierType
        +siret: str
        +contact_name: str
        +email: str
        +phone: str
        +address: str
        +payment_terms: str
        +is_subcontractor: bool
        +rating: float
        +materials: List[Material]
        +purchase_orders: List[PurchaseOrder]
        +__init__(name: str, supplier_type: SupplierType)
        +get_purchase_history() List[PurchaseOrder]
        +calculate_average_rating() float
    }

    class SupplierType {
        <<enumeration>>
        MATERIAL_SUPPLIER
        SUBCONTRACTOR
        SERVICE_PROVIDER
        EQUIPMENT_RENTAL
    }

    %% Material Management
    class Material {
        +id: int
        +company_id: int
        +code: str
        +name: str
        +description: str
        +unit: str
        +category: MaterialCategory
        +supplier_id: int
        +current_price: Decimal
        +technical_sheet: TechnicalSheet
        +__init__(code: str, name: str, unit: str)
        +get_price_history() List[PriceHistory]
        +update_price(new_price: Decimal)
    }

    class MaterialCategory {
        +id: int
        +name: str
        +parent_id: int
        +children: List[MaterialCategory]
        +materials: List[Material]
    }

    class TechnicalSheet {
        +id: int
        +material_id: int
        +specifications: dict
        +safety_data: dict
        +certifications: List[str]
        +documents: List[Document]
        +__init__(material_id: int)
        +add_specification(key: str, value: str)
    }

    class PriceHistory {
        +id: int
        +material_id: int
        +price: Decimal
        +effective_date: date
        +supplier_id: int
    }

    %% Purchase Order Management
    class PurchaseOrder {
        +id: int
        +company_id: int
        +project_id: int
        +supplier_id: int
        +order_number: str
        +order_date: date
        +expected_delivery: date
        +status: OrderStatus
        +total_amount: Decimal
        +lines: List[PurchaseOrderLine]
        +deliveries: List[Delivery]
        +__init__(project_id: int, supplier_id: int)
        +add_line(material: Material, quantity: float, unit_price: Decimal)
        +calculate_total() Decimal
        +create_delivery(lines: List[PurchaseOrderLine])
    }

    class OrderStatus {
        <<enumeration>>
        DRAFT
        SENT
        CONFIRMED
        PARTIALLY_DELIVERED
        DELIVERED
        CANCELLED
    }

    class PurchaseOrderLine {
        +id: int
        +purchase_order_id: int
        +material_id: int
        +quantity: float
        +unit_price: Decimal
        +total_amount: Decimal
        +delivered_quantity: float
        +__init__(material_id: int, quantity: float, unit_price: Decimal)
        +get_remaining_quantity() float
    }

    class Delivery {
        +id: int
        +purchase_order_id: int
        +delivery_date: date
        +delivery_note: str
        +lines: List[DeliveryLine]
        +is_complete: bool
        +received_by: int
        +__init__(purchase_order_id: int, delivery_date: date)
    }

    class DeliveryLine {
        +id: int
        +delivery_id: int
        +purchase_order_line_id: int
        +delivered_quantity: float
        +quality_check: bool
        +notes: str
    }

    %% Quote Management
    class Quote {
        +id: int
        +company_id: int
        +project_id: int
        +quote_number: str
        +client_name: str
        +client_contact: str
        +quote_date: date
        +valid_until: date
        +status: QuoteStatus
        +total_amount: Decimal
        +lines: List[QuoteLine]
        +__init__(project_id: int, client_name: str)
        +add_line(description: str, quantity: float, unit_price: Decimal)
        +calculate_total() Decimal
        +convert_to_project() Project
        +generate_pdf() bytes
    }

    class QuoteStatus {
        <<enumeration>>
        DRAFT
        SENT
        ACCEPTED
        REJECTED
        EXPIRED
    }

    class QuoteLine {
        +id: int
        +quote_id: int
        +line_number: int
        +description: str
        +unit: str
        +quantity: float
        +unit_price: Decimal
        +total_amount: Decimal
        +margin_percentage: float
        +__init__(description: str, quantity: float, unit_price: Decimal)
    }

    %% Financial Management
    class Budget {
        +id: int
        +project_id: int
        +category: BudgetCategory
        +planned_amount: Decimal
        +actual_amount: Decimal
        +variance: Decimal
        +lines: List[BudgetLine]
        +__init__(project_id: int, category: BudgetCategory)
        +calculate_variance() Decimal
        +get_utilization_percentage() float
    }

    class BudgetCategory {
        <<enumeration>>
        MATERIALS
        LABOR
        EQUIPMENT
        SUBCONTRACTORS
        OVERHEAD
    }

    class BudgetLine {
        +id: int
        +budget_id: int
        +description: str
        +planned_amount: Decimal
        +actual_amount: Decimal
        +notes: str
    }

    class Invoice {
        +id: int
        +company_id: int
        +project_id: int
        +supplier_id: int
        +invoice_number: str
        +invoice_date: date
        +due_date: date
        +amount: Decimal
        +tax_amount: Decimal
        +total_amount: Decimal
        +status: InvoiceStatus
        +payment_date: date
        +__init__(supplier_id: int, amount: Decimal)
        +calculate_tax() Decimal
        +mark_as_paid(payment_date: date)
        +is_overdue() bool
    }

    class InvoiceStatus {
        <<enumeration>>
        DRAFT
        SENT
        RECEIVED
        VALIDATED
        PAID
        OVERDUE
    }

    %% Document Management
    class Document {
        +id: int
        +company_id: int
        +project_id: int
        +name: str
        +file_path: str
        +file_size: int
        +mime_type: str
        +category: DocumentCategory
        +version: int
        +uploaded_by: int
        +upload_date: datetime
        +tags: List[str]
        +__init__(name: str, file_path: str, project_id: int)
        +get_url() str
        +create_version() Document
        +get_versions() List[Document]
    }

    class DocumentCategory {
        <<enumeration>>
        DCE
        DAO
        DPGF
        FT
        EXE
        PLANNING
        DEVIS
        CANDIDATURE
        DEMAT
        NEGO
        DIVERS
    }

    %% Excel Integration
    class ExcelImport {
        +id: int
        +company_id: int
        +project_id: int
        +file_name: str
        +import_type: ImportType
        +status: ImportStatus
        +column_mapping: dict
        +imported_records: int
        +error_records: int
        +errors: List[str]
        +started_at: datetime
        +completed_at: datetime
        +__init__(file_name: str, import_type: ImportType)
        +process_file(file_data: bytes, mapping: dict)
        +validate_data() List[str]
    }

    class ImportType {
        <<enumeration>>
        DPGF
        MATERIALS
        EMPLOYEES
        SUPPLIERS
    }

    class ImportStatus {
        <<enumeration>>
        PENDING
        PROCESSING
        COMPLETED
        FAILED
    }

    %% Reporting
    class Report {
        +id: int
        +company_id: int
        +name: str
        +report_type: ReportType
        +parameters: dict
        +generated_at: datetime
        +file_path: str
        +__init__(name: str, report_type: ReportType)
        +generate() bytes
        +schedule(cron_expression: str)
    }

    class ReportType {
        <<enumeration>>
        PROJECT_PROGRESS
        FINANCIAL_SUMMARY
        EMPLOYEE_TIMESHEET
        MATERIAL_USAGE
        SUPPLIER_PERFORMANCE
    }

    %% API Services
    class AuthService {
        +authenticate(email: str, password: str) User
        +create_access_token(user: User) str
        +verify_token(token: str) User
        +refresh_token(refresh_token: str) str
    }

    class ProjectService {
        +create_project(data: ProjectCreate, company_id: int) Project
        +get_project_dashboard(project_id: int) dict
        +update_progress(project_id: int, progress: float)
        +archive_project(project_id: int)
    }

    class ExcelService {
        +import_dpgf(file: UploadFile, mapping: dict, project_id: int) ExcelImport
        +validate_columns(file: UploadFile) List[str]
        +export_report(report_type: ReportType, filters: dict) bytes
    }

    class DocumentService {
        +upload_document(file: UploadFile, project_id: int, category: DocumentCategory) Document
        +create_folder_structure(project: Project) bool
        +generate_pdf(template: str, data: dict) bytes
    }

    %% Relationships
    BaseModel <|-- User
    BaseModel <|-- Company
    BaseModel <|-- Project
    BaseModel <|-- Employee
    BaseModel <|-- Supplier
    BaseModel <|-- Material
    BaseModel <|-- PurchaseOrder
    BaseModel <|-- Quote
    BaseModel <|-- Invoice
    BaseModel <|-- Document
    BaseModel <|-- ExcelImport
    BaseModel <|-- Report
    
    MultiTenantMixin <|-- Company
    MultiTenantMixin <|-- Project
    MultiTenantMixin <|-- Employee
    MultiTenantMixin <|-- Supplier
    MultiTenantMixin <|-- Material
    MultiTenantMixin <|-- PurchaseOrder
    MultiTenantMixin <|-- Quote
    MultiTenantMixin <|-- Invoice
    MultiTenantMixin <|-- Document
    MultiTenantMixin <|-- ExcelImport
    MultiTenantMixin <|-- Report

    User ||--o{ UserCompany : "belongs to"
    Company ||--o{ UserCompany : "has"
    Company ||--|| CompanySettings : "has"
    Company ||--o{ Project : "owns"
    Company ||--o{ Employee : "employs"
    Company ||--o{ Supplier : "works with"
    
    Project ||--o{ ProjectEmployee : "has"
    Project ||--o{ PurchaseOrder : "contains"
    Project ||--o{ Document : "stores"
    Project ||--o{ Budget : "has"
    Project ||--o{ Quote : "generates"
    
    Employee ||--o{ ProjectEmployee : "assigned to"
    Employee ||--o{ TimeEntry : "logs"
    
    Supplier ||--o{ Material : "supplies"
    Supplier ||--o{ PurchaseOrder : "receives"
    Supplier ||--o{ Invoice : "sends"
    
    Material ||--|| TechnicalSheet : "has"
    Material ||--o{ PriceHistory : "has"
    Material ||--o{ PurchaseOrderLine : "ordered in"
    MaterialCategory ||--o{ Material : "contains"
    
    PurchaseOrder ||--o{ PurchaseOrderLine : "contains"
    PurchaseOrder ||--o{ Delivery : "has"
    PurchaseOrderLine ||--o{ DeliveryLine : "delivered in"
    
    Quote ||--o{ QuoteLine : "contains"
    Budget ||--o{ BudgetLine : "contains"
    
    Project ||--o{ TimeEntry : "tracks time for"
    
    Delivery ||--o{ DeliveryLine : "contains"