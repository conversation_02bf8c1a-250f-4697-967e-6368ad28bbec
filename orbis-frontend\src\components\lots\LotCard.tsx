'use client'

import { Lot, PHASE_LABELS, PHASE_COLORS, getPhaseProgressPercentage } from '@/types/lot';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface LotCardProps {
  lot: Lot;
  onEdit?: (lot: Lot) => void;
  onDelete?: (lot: Lot) => void;
  onViewDetails?: (lot: Lot) => void;
  showActions?: boolean;
}

export default function LotCard({ 
  lot, 
  onEdit, 
  onDelete, 
  onViewDetails, 
  showActions = true 
}: LotCardProps) {
  const progressPercentage = getPhaseProgressPercentage(lot);
  
  const handleCardClick = (e: React.MouseEvent) => {
    // Ne pas déclencher le clic si on clique sur un bouton d'action
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }
    onViewDetails?.(lot);
  };
  
  return (
    <div 
      className="p-6 bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-200 cursor-pointer hover:bg-gray-50" 
      onClick={handleCardClick}
    >
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{lot.name}</h3>
              <span className={`px-2 py-1 text-xs rounded-full font-medium ${PHASE_COLORS[lot.current_phase]}`}>
                {PHASE_LABELS[lot.current_phase]}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-1">Code: {lot.code}</p>
            {lot.description && (
              <p className="text-sm text-gray-500 line-clamp-2">{lot.description}</p>
            )}
          </div>
          
          {showActions && onDelete && (
            <div className="flex gap-2 ml-4">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(lot);
                }}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9zM4 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 112 0v4a1 1 0 11-2 0V9zm4 0a1 1 0 112 0v4a1 1 0 11-2 0V9z" clipRule="evenodd" />
                </svg>
              </Button>
            </div>
          )}
        </div>

        {/* Progression des phases */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Progression</span>
            <span className="text-sm font-medium text-gray-700">{Math.round(progressPercentage)}%</span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          
          {/* Indicateurs de phases */}
          <div className="flex justify-between text-xs">
            <span className={`${lot.esq_validated ? 'text-green-600 font-medium' : 'text-gray-400'}`}>
              ESQ {lot.esq_validated ? '✓' : '○'}
            </span>
            <span className={`${lot.apd_validated ? 'text-green-600 font-medium' : 'text-gray-400'}`}>
              APD {lot.apd_validated ? '✓' : '○'}
            </span>
            <span className={`${lot.prodce_validated ? 'text-green-600 font-medium' : 'text-gray-400'}`}>
              PRODCE {lot.prodce_validated ? '✓' : '○'}
            </span>
            <span className={`${lot.exe_validated ? 'text-green-600 font-medium' : 'text-gray-400'}`}>
              EXE {lot.exe_validated ? '✓' : '○'}
            </span>
          </div>
        </div>

        {/* Informations supplémentaires */}
        <div className="flex items-center justify-between text-sm text-gray-500 pt-2 border-t">
          <div className="flex items-center gap-4">
            {lot.intervenants && lot.intervenants.length > 0 && (
              <span className="flex items-center gap-1">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                </svg>
                {lot.intervenants.length} intervenant{lot.intervenants.length > 1 ? 's' : ''}
              </span>
            )}
            
            {/* Affichage des documents techniques du lot */}
            <span className="flex items-center gap-1">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
              </svg>
              Documents techniques
            </span>
          </div>
          
          <span>
            Créé le {new Date(lot.created_at).toLocaleDateString('fr-FR')}
          </span>
        </div>
      </div>
    </div>
  );
}
