#!/usr/bin/env python3
"""
Script pour vérifier la structure de la table companies
"""

import asyncio
import asyncpg
from app.core.config import settings

async def check_companies_table():
    """Vérifier la structure de la table companies"""
    print("🔍 Vérification de la structure de la table companies...")
    
    conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier les colonnes de la table companies
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'companies' 
            ORDER BY ordinal_position
        """)
        
        print("\n📋 Colonnes de la table 'companies':")
        for col in columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
            print(f"  {col['column_name']}: {col['data_type']} {nullable}{default}")
        
        # Vérifier s'il y a des données existantes
        count = await conn.fetchval("SELECT COUNT(*) FROM companies")
        print(f"\n📊 Nombre d'entreprises existantes: {count}")
        
        if count > 0:
            companies = await conn.fetch("SELECT * FROM companies LIMIT 5")
            print("\n🏢 Entreprises existantes:")
            for company in companies:
                print(f"  ID: {company['id']}, Nom: {company['name']}, Code: {company.get('code', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(check_companies_table())
