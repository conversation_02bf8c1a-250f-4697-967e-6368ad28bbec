"""Create tcompanies table

Revision ID: 002_create_tcompanies
Revises: rename_companies_to_workspaces
Create Date: 2025-01-09 18:39:00.000000

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '002'
down_revision: Union[str, None] = '001'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create the tcompanies table with standardized English column names"""
    
    # Create tcompanies table
    op.create_table(
        'tcompanies',
        sa.Column('id', sa.Integer(), nullable=False),
        
        # Basic information - STANDARDIZED IN ENGLISH
        sa.Column('company_name', sa.String(length=255), nullable=False),
        sa.Column('activity', sa.String(length=255), nullable=True),
        
        # Address - STANDARDIZED
        sa.Column('address', sa.Text(), nullable=True),
        sa.Column('postal_code', sa.String(length=10), nullable=True),
        sa.Column('city', sa.String(length=100), nullable=True),
        sa.Column('country', sa.String(length=100), nullable=True, default='France'),
        
        # Contact - STANDARDIZED
        sa.Column('phone', sa.String(length=20), nullable=True),
        sa.Column('fax', sa.String(length=20), nullable=True),
        sa.Column('email', sa.String(length=320), nullable=True),  # RFC 5321 max length
        
        # Legal information - STANDARDIZED
        sa.Column('siret', sa.String(length=14), nullable=True),
        sa.Column('vat_number', sa.String(length=20), nullable=True),
        
        # Legal representative - STANDARDIZED
        sa.Column('legal_representative_id', sa.Integer(), nullable=True),
        
        # Logo - STANDARDIZED
        sa.Column('logo_url', sa.String(length=500), nullable=True),
        sa.Column('logo_filename', sa.String(length=255), nullable=True),
        
        # Workspace relation
        sa.Column('workspace_id', sa.Integer(), nullable=False),
        
        # Metadata - STANDARDIZED
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        
        # Primary key
        sa.PrimaryKeyConstraint('id'),
        
        # Foreign keys
        sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ),
        sa.ForeignKeyConstraint(['legal_representative_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        
        # Unique constraints
        sa.UniqueConstraint('siret', name='unique_tcompany_siret'),
        sa.UniqueConstraint('workspace_id', 'company_name', name='unique_workspace_tcompany_name'),
    )
    
    # Create indexes for performance
    op.create_index('ix_tcompanies_id', 'tcompanies', ['id'], unique=False)
    op.create_index('ix_tcompanies_company_name', 'tcompanies', ['company_name'], unique=False)
    op.create_index('ix_tcompanies_email', 'tcompanies', ['email'], unique=False)
    op.create_index('ix_tcompanies_siret', 'tcompanies', ['siret'], unique=False)
    op.create_index('ix_tcompanies_workspace_id', 'tcompanies', ['workspace_id'], unique=False)
    op.create_index('ix_tcompanies_is_active', 'tcompanies', ['is_active'], unique=False)
    
    # Composite indexes for common queries
    op.create_index('ix_tcompanies_workspace_active', 'tcompanies', ['workspace_id', 'is_active'], unique=False)
    op.create_index('ix_tcompanies_workspace_activity', 'tcompanies', ['workspace_id', 'activity'], unique=False)
    
    print("✅ Table tcompanies créée avec succès")
    print("✅ Index et contraintes ajoutés")
    print("📝 Note: Cette table coexiste temporairement avec entreprises_tiers")


def downgrade() -> None:
    """Drop the tcompanies table"""
    
    # Drop indexes
    op.drop_index('ix_tcompanies_workspace_activity', table_name='tcompanies')
    op.drop_index('ix_tcompanies_workspace_active', table_name='tcompanies')
    op.drop_index('ix_tcompanies_is_active', table_name='tcompanies')
    op.drop_index('ix_tcompanies_workspace_id', table_name='tcompanies')
    op.drop_index('ix_tcompanies_siret', table_name='tcompanies')
    op.drop_index('ix_tcompanies_email', table_name='tcompanies')
    op.drop_index('ix_tcompanies_company_name', table_name='tcompanies')
    op.drop_index('ix_tcompanies_id', table_name='tcompanies')
    
    # Drop table
    op.drop_table('tcompanies')
    
    print("✅ Table tcompanies supprimée")
