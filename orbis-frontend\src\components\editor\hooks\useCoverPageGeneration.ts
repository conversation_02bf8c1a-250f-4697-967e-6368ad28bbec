import { useState } from 'react'
import { Editor } from '@tiptap/react'
import { generateTemplateCoverPageHTML, CoverPageData } from '@/services/templateCoverPageService'
import { TCompany } from '@/types/tcompany'
import { Lot } from '@/types/lot'
import { Stakeholder } from '@/types/stakeholder'
import { FastAuthService } from '@/lib/auth'

// Étendre TCompany pour inclure le rôle du stakeholder
interface TCompanyWithRole extends TCompany {
  role?: string;
}

interface UseCoverPageGenerationProps {
  editor: Editor | null
  lotId?: number | null
  documentType: string
  documentIndice?: string
}

export const useCoverPageGeneration = ({
  editor,
  lotId,
  documentType,
  documentIndice = "01"
}: UseCoverPageGenerationProps) => {
  const [isGenerating, setIsGenerating] = useState(false)

  const fetchLotData = async (lotId: number): Promise<Lot> => {
    const response = await FastAuthService.makeAuthenticatedRequest(`/lots/${lotId}`)
    if (!response.ok) {
      throw new Error('Erreur lors de la récupération du lot')
    }
    return response.json()
  }

  const fetchLotCompanies = async (lotId: number): Promise<TCompanyWithRole[]> => {
    // Récupérer les stakeholders (intervenants) du lot
    const response = await FastAuthService.makeAuthenticatedRequest(`/lots/${lotId}/stakeholders`)
    if (!response.ok) {
      throw new Error('Erreur lors de la récupération des intervenants')
    }

    const stakeholders: Stakeholder[] = await response.json()

    // Extraire les TCompanies des stakeholders et inclure le rôle
    const companies: TCompanyWithRole[] = stakeholders
      .filter(stakeholder => stakeholder.company)
      .map(stakeholder => {
        const company = stakeholder.company! as any // Cast temporaire pour accéder aux champs complets
        return {
          id: company.id,
          company_name: company.company_name,
          activity: company.activity,
          email: company.email,
          phone: company.phone,
          address: company.address || '', // Récupérer l'adresse de la company
          postal_code: company.postal_code || '',
          city: company.city || '',
          country: company.country || 'France',
          workspace_id: 0, // Valeur par défaut
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by: 0,
          logo_url: company.logo_url, // Récupérer le logo de la company
          role: stakeholder.role // Ajouter le rôle du stakeholder
        } as TCompanyWithRole
      })

    return companies
  }

  const generateCoverPage = async () => {
    if (!editor || !lotId) {
      console.error('Éditeur ou ID de lot manquant')
      return
    }

    setIsGenerating(true)
    try {
      // Récupérer les données du lot
      const lot = await fetchLotData(lotId)
      
      // Récupérer les entreprises associées au lot
      const companies = await fetchLotCompanies(lotId)

      // Préparer les données pour la page de garde
      const coverPageData: CoverPageData = {
        lot: {
          id: lot.id,
          name: lot.name,
          code: lot.code,
          description: lot.description,
          current_phase: lot.current_phase,
          photo_url: lot.photo_url,
          project: lot.project ? {
            id: lot.project.id,
            name: lot.project.name,
            address: (lot.project as any).address, // Cast temporaire en attendant la mise à jour du type
            photo_url: (lot.project as any).photo_url // Cast temporaire en attendant la mise à jour du type
          } : undefined
        },
        companies: companies.map(company => ({
          id: company.id,
          name: company.company_name,
          role: company.role || 'ENT',
          activity: company.activity,
          address: company.address,
          phone: company.phone,
          email: company.email,
          logo_url: company.logo_url
        })),
        documentType,
        documentIndice
      }

      console.log('🎯 Données pour page de garde:', {
        lotName: lot.name,
        projectName: lot.project?.name,
        projectId: lot.project?.id,
        companiesCount: companies.length,
        companies: companies.map(c => ({ name: c.company_name, activity: c.activity }))
      })

      // Générer le HTML de la page de garde avec le nouveau système
      console.log('🆕 Utilisation du nouveau système de templates')
      const coverPageHTML = generateTemplateCoverPageHTML(coverPageData)

      console.log('📄 HTML généré:', coverPageHTML.substring(0, 200) + '...')

      // Insérer la page de garde comme custom node
      console.log('📝 Insertion comme custom node dans l\'éditeur TipTap')

      // Utiliser le custom node CoverPage
      if (editor.commands.insertCoverPage) {
        editor.commands.insertCoverPage(coverPageHTML)
        console.log('✅ Page de garde insérée comme custom node!')
      } else {
        // Fallback vers setContent pour remplacer tout le contenu
        console.log('⚠️ Custom node non disponible, utilisation du fallback')
        const currentContent = editor.getHTML()
        const newContent = coverPageHTML + currentContent
        editor.commands.setContent(newContent)
        console.log('✅ Page de garde insérée avec fallback!')
      }

      console.log('Page de garde générée avec succès')
    } catch (error) {
      console.error('Erreur lors de la génération de la page de garde:', error)
      alert('Erreur lors de la génération de la page de garde. Veuillez réessayer.')
    } finally {
      setIsGenerating(false)
    }
  }

  return {
    generateCoverPage,
    isGenerating
  }
}
