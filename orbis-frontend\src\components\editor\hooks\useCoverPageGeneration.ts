import { useState } from 'react'
import { Editor } from '@tiptap/react'
import { generateCoverPageHTML, CoverPageData } from '@/services/coverPageService'
import { generateTemplateCoverPageHTML } from '@/services/templateCoverPageService'
import { TCompany } from '@/types/tcompany'
import { Lot } from '@/types/lot'
import { Stakeholder } from '@/types/stakeholder'
import { FastAuthService } from '@/lib/auth'

// Étendre TCompany pour inclure le rôle du stakeholder
interface TCompanyWithRole extends TCompany {
  role?: string;
}

interface UseCoverPageGenerationProps {
  editor: Editor | null
  lotId?: number | null
  documentType: string
  documentIndice?: string
}

export const useCoverPageGeneration = ({
  editor,
  lotId,
  documentType,
  documentIndice = "01"
}: UseCoverPageGenerationProps) => {
  const [isGenerating, setIsGenerating] = useState(false)

  const fetchLotData = async (lotId: number): Promise<Lot> => {
    const response = await FastAuthService.makeAuthenticatedRequest(`/lots/${lotId}`)
    if (!response.ok) {
      throw new Error('Erreur lors de la récupération du lot')
    }
    return response.json()
  }

  const fetchLotCompanies = async (lotId: number): Promise<TCompanyWithRole[]> => {
    // Récupérer les stakeholders (intervenants) du lot
    const response = await FastAuthService.makeAuthenticatedRequest(`/lots/${lotId}/stakeholders`)
    if (!response.ok) {
      throw new Error('Erreur lors de la récupération des intervenants')
    }

    const stakeholders: Stakeholder[] = await response.json()

    // Extraire les TCompanies des stakeholders et inclure le rôle
    const companies: TCompanyWithRole[] = stakeholders
      .filter(stakeholder => stakeholder.company)
      .map(stakeholder => {
        const company = stakeholder.company!
        return {
          id: company.id,
          company_name: company.company_name,
          activity: company.activity,
          email: company.email,
          phone: company.phone,
          address: '',
          postal_code: '',
          city: '',
          country: 'France',
          workspace_id: 0, // Valeur par défaut
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by: 0,
          role: stakeholder.role // Ajouter le rôle du stakeholder
        } as TCompanyWithRole
      })

    return companies
  }

  const generateCoverPage = async () => {
    if (!editor || !lotId) {
      console.error('Éditeur ou ID de lot manquant')
      return
    }

    setIsGenerating(true)
    try {
      // Récupérer les données du lot
      const lot = await fetchLotData(lotId)
      
      // Récupérer les entreprises associées au lot
      const companies = await fetchLotCompanies(lotId)

      // Préparer les données pour la page de garde
      const coverPageData: CoverPageData = {
        lot,
        companies,
        projectTitle: lot.project?.name || `Projet ${lot.project?.id || 'N/A'}`,
        documentType,
        documentIndice,
        date: new Date().toLocaleDateString('fr-FR')
      }

      console.log('🎯 Données pour page de garde:', {
        lotName: lot.name,
        projectName: lot.project?.name,
        projectId: lot.project?.id,
        companiesCount: companies.length,
        companies: companies.map(c => ({ name: c.company_name, activity: c.activity }))
      })

      // 🔄 SWITCH ENTRE ANCIEN ET NOUVEAU SYSTÈME
      const useNewTemplate = true // ← Changer ici pour basculer

      // 🎯 GÉNÉRATION SELON LE MODE CHOISI
      let coverPageHTML: string

      if (useNewTemplate) {
        console.log('🆕 Utilisation du nouveau système de templates')

        // Adapter les données pour le nouveau service
        const templateData = {
          lot: {
            id: lot.id,
            name: lot.name,
            project: lot.project
          },
          documentType,
          documentIndice,
          companies: companies.map(company => ({
            id: company.id,
            name: company.company_name, // Adapter le nom du champ
            role: company.role || 'ENT', // Valeur par défaut si undefined
            address: company.address || '',
            logo_url: company.logo_url // Inclure l'URL du logo
          }))
        }

        coverPageHTML = generateTemplateCoverPageHTML(templateData)
      } else {
        console.log('🔄 Utilisation de l\'ancien système')
        coverPageHTML = generateCoverPageHTML(coverPageData)
      }

      // Insérer la page de garde au début du document
      const currentContent = editor.getHTML()
      const newContent = coverPageHTML + currentContent

      // Mettre à jour le contenu de l'éditeur en préservant les styles
      editor.commands.setContent(newContent)

      console.log('Page de garde générée avec succès')
    } catch (error) {
      console.error('Erreur lors de la génération de la page de garde:', error)
      alert('Erreur lors de la génération de la page de garde. Veuillez réessayer.')
    } finally {
      setIsGenerating(false)
    }
  }

  return {
    generateCoverPage,
    isGenerating
  }
}
