/* Styles pour les headings numérotés dans l'éditeur TipTap */

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  font-weight: bold;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.3;
}

.ProseMirror h1 { 
  font-size: 2em; 
  color: #0F766E;
  border-bottom: 2px solid #0F766E;
  padding-bottom: 0.3em;
}

.ProseMirror h2 { 
  font-size: 1.75em; 
  color: #0F766E;
}

.ProseMirror h3 { 
  font-size: 1.5em; 
  color: #333333;
}

.ProseMirror h4 { 
  font-size: 1.25em; 
  color: #333333;
}

.ProseMirror h5 { 
  font-size: 1.1em; 
  color: #333333;
}

.ProseMirror h6 { 
  font-size: 1em; 
  color: #333333;
  font-weight: 600;
}

/* Style pour les numéros de section */
.ProseMirror h1::before,
.ProseMirror h2::before,
.ProseMirror h3::before,
.ProseMirror h4::before,
.ProseMirror h5::before,
.ProseMirror h6::before {
  color: #0F766E;
  font-weight: bold;
  margin-right: 0.5em;
}

/* Espacement pour les documents CCTP */
.ProseMirror .cctp-section {
  margin-bottom: 2em;
}

.ProseMirror .cctp-article {
  margin-bottom: 1.5em;
  padding-left: 1em;
}

/* Style pour les headings avec numérotation automatique */
.ProseMirror h1[data-numbered="true"],
.ProseMirror h2[data-numbered="true"],
.ProseMirror h3[data-numbered="true"],
.ProseMirror h4[data-numbered="true"],
.ProseMirror h5[data-numbered="true"],
.ProseMirror h6[data-numbered="true"] {
  position: relative;
}

/* Indicateur visuel pour les headings numérotés */
.ProseMirror h1[data-numbered="true"]::after,
.ProseMirror h2[data-numbered="true"]::after,
.ProseMirror h3[data-numbered="true"]::after,
.ProseMirror h4[data-numbered="true"]::after,
.ProseMirror h5[data-numbered="true"]::after,
.ProseMirror h6[data-numbered="true"]::after {
  content: "📝";
  position: absolute;
  right: 0;
  top: 0;
  font-size: 0.7em;
  opacity: 0.5;
}

/* Animation pour les nouveaux headings */
.ProseMirror h1.new-heading,
.ProseMirror h2.new-heading,
.ProseMirror h3.new-heading,
.ProseMirror h4.new-heading,
.ProseMirror h5.new-heading,
.ProseMirror h6.new-heading {
  animation: highlightHeading 2s ease-in-out;
}

@keyframes highlightHeading {
  0% {
    background-color: rgba(15, 118, 110, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

/* Style pour les boutons de numérotation dans la toolbar */
.numbering-button {
  background: linear-gradient(135deg, #0F766E, #14B8A6);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.numbering-button:hover {
  background: linear-gradient(135deg, #0D5B56, #0F766E);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.numbering-button:active {
  transform: translateY(0);
}

/* Styles pour la barre d'outils complète */
.ProseMirror-toolbar {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 8px;
}

.ProseMirror-toolbar button {
  border: none;
  background: transparent;
  padding: 8px;
  margin: 0 2px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333333;
}

.ProseMirror-toolbar button:hover {
  background: rgba(15, 118, 110, 0.1);
  color: #0F766E;
}

.ProseMirror-toolbar button.is-active {
  background: #0F766E;
  color: white;
}

.ProseMirror-toolbar select {
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px 8px;
  background: white;
  color: #333333;
}

.ProseMirror-toolbar input[type="color"] {
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
}

/* Styles pour la barre d'état */
.ProseMirror-statusbar {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 8px 16px;
  font-size: 12px;
  color: #6b7280;
}

.ProseMirror-statusbar .status-item {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
}

.ProseMirror-statusbar .status-item:last-child {
  margin-right: 0;
}

/* Styles pour l'éditeur avec barre d'outils fixe */
.ProseMirror-editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ProseMirror-toolbar-fixed {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.ProseMirror-content-scrollable {
  flex: 1;
  overflow-y: auto;
  background: white;
}

.ProseMirror-statusbar-fixed {
  position: sticky;
  bottom: 0;
  z-index: 10;
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Amélioration du scroll */
.ProseMirror-content-scrollable::-webkit-scrollbar {
  width: 8px;
}

.ProseMirror-content-scrollable::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.ProseMirror-content-scrollable::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.ProseMirror-content-scrollable::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Styles pour le contenu de l'éditeur */
.ProseMirror {
  outline: none;
  padding: 24px;
  min-height: calc(100vh - 200px);
}

/* Animation smooth pour le scroll */
.ProseMirror-content-scrollable {
  scroll-behavior: smooth;
}

/* Focus visible pour l'accessibilité */
.ProseMirror:focus-visible {
  outline: 2px solid #0F766E;
  outline-offset: 2px;
}

/* Responsive design pour les headings */
@media (max-width: 768px) {
  .ProseMirror h1 { font-size: 1.75em; }
  .ProseMirror h2 { font-size: 1.5em; }
  .ProseMirror h3 { font-size: 1.25em; }
  .ProseMirror h4 { font-size: 1.1em; }
  .ProseMirror h5 { font-size: 1em; }
  .ProseMirror h6 { font-size: 0.9em; }
}

/* Style pour l'impression */
@media print {
  .ProseMirror h1,
  .ProseMirror h2,
  .ProseMirror h3,
  .ProseMirror h4,
  .ProseMirror h5,
  .ProseMirror h6 {
    color: #000 !important;
    page-break-after: avoid;
  }
  
  .ProseMirror h1 {
    border-bottom: 1px solid #000;
  }
  
  .ProseMirror h1[data-numbered="true"]::after,
  .ProseMirror h2[data-numbered="true"]::after,
  .ProseMirror h3[data-numbered="true"]::after,
  .ProseMirror h4[data-numbered="true"]::after,
  .ProseMirror h5[data-numbered="true"]::after,
  .ProseMirror h6[data-numbered="true"]::after {
    display: none;
  }
}
