#!/usr/bin/env python3
"""
Script pour créer un utilisateur de test avec un mot de passe correctement hashé
"""

import asyncio
import asyncpg
from datetime import datetime
import sys
from pathlib import Path

# Ajouter le répertoire app au path Python
sys.path.insert(0, str(Path(__file__).parent))

from app.core.security import get_password_hash

# Configuration
DATABASE_URL = "**************************************************************************************************/postgres"


async def create_test_user():
    """Créer un utilisateur de test avec un mot de passe hashé"""
    print("🔐 Création d'un utilisateur de test...")
    
    # Données de l'utilisateur de test
    email = "<EMAIL>"
    password = "test123"
    first_name = "Test"
    last_name = "User"
    role = "ADMIN"
    
    # Hasher le mot de passe
    hashed_password = get_password_hash(password)
    print(f"   🔒 Mot de passe hashé: {hashed_password[:20]}...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier si l'utilisateur existe déjà
        existing_user = await conn.fetchval("SELECT id FROM users WHERE email = $1", email)
        
        if existing_user:
            # Mettre à jour le mot de passe
            await conn.execute("""
                UPDATE users 
                SET hashed_password = $1, updated_at = $2
                WHERE email = $3
            """, hashed_password, datetime.utcnow(), email)
            print(f"   ✅ Utilisateur {email} mis à jour")
        else:
            # Créer un nouvel utilisateur
            user_id = await conn.fetchval("""
                INSERT INTO users (email, first_name, last_name, hashed_password, role, is_active, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING id
            """, 
            email, first_name, last_name, hashed_password, role, True, 
            datetime.utcnow(), datetime.utcnow())
            print(f"   ✅ Nouvel utilisateur créé: {email} (ID: {user_id})")
        
        print(f"\n🎯 Identifiants de test:")
        print(f"   Email: {email}")
        print(f"   Mot de passe: {password}")
        print(f"   Rôle: {role}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def test_password_verification():
    """Tester la vérification du mot de passe"""
    print("\n🧪 Test de vérification du mot de passe...")
    
    from app.core.security import verify_password
    
    password = "test123"
    hashed = get_password_hash(password)
    
    # Test avec le bon mot de passe
    is_valid = verify_password(password, hashed)
    print(f"   ✅ Vérification avec '{password}': {is_valid}")
    
    # Test avec un mauvais mot de passe
    is_invalid = verify_password("wrong", hashed)
    print(f"   ❌ Vérification avec 'wrong': {is_invalid}")
    
    return is_valid and not is_invalid


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Création d'Utilisateur de Test")
    print("="*50)
    
    # Tester le système de hash
    if await test_password_verification():
        print("✅ Système de hash fonctionnel")
    else:
        print("❌ Problème avec le système de hash")
        return False
    
    # Créer l'utilisateur de test
    success = await create_test_user()
    
    if success:
        print("\n🎉 Utilisateur de test créé avec succès!")
        print("\n💡 Vous pouvez maintenant tester avec:")
        print("   - Email: <EMAIL>")
        print("   - Mot de passe: test123")
        print("\n🌐 Testez sur: http://localhost:8000/docs")
    else:
        print("\n❌ Échec de la création de l'utilisateur")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
