#!/usr/bin/env python3
"""
Script pour corriger automatiquement tous les imports obsolètes
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """Corriger les imports dans un fichier"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Remplacer les imports obsolètes
        replacements = [
            (r'from app\.models\.company import', 'from app.models.workspace import'),
            (r'from app\.schemas\.company import', 'from app.schemas.workspace import'),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        # Sauvegarder si des changements ont été faits
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"   ✅ Corrigé: {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur dans {file_path}: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔧 CORRECTION DES IMPORTS OBSOLÈTES")
    print("="*50)
    
    # Répertoires à traiter
    directories = [
        "fastapi_template/app/api",
        "fastapi_template/app/schemas",
        "fastapi_template/app/models",
        "fastapi_template/app/core",
        "fastapi_template"
    ]
    
    total_files = 0
    fixed_files = 0
    
    for directory in directories:
        if not os.path.exists(directory):
            continue
            
        print(f"\n📁 Traitement du répertoire: {directory}")
        
        # Parcourir tous les fichiers Python
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    total_files += 1
                    
                    if fix_imports_in_file(file_path):
                        fixed_files += 1
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   • Fichiers traités: {total_files}")
    print(f"   • Fichiers corrigés: {fixed_files}")
    
    if fixed_files > 0:
        print("✅ Imports corrigés avec succès!")
    else:
        print("ℹ️ Aucun import à corriger")

if __name__ == "__main__":
    main()
