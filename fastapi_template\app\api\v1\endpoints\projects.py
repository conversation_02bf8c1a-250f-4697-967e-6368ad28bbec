"""
Endpoints pour la gestion des projets
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.project import Project, ProjectStatus
from app.models.user import User
from app.api.deps import get_current_user
from pydantic import BaseModel
from datetime import datetime


# Schémas Pydantic
class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    client_name: str
    client_contact: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    budget_total: Optional[float] = None
    address: Optional[str] = None
    status: Optional[ProjectStatus] = ProjectStatus.DAO


class ProjectCreate(ProjectBase):
    pass


class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    client_name: Optional[str] = None
    client_contact: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    budget_total: Optional[float] = None
    address: Optional[str] = None
    status: Optional[ProjectStatus] = None


class ProjectResponse(ProjectBase):
    id: int
    workspace_id: int
    code: str
    is_archived: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    progress: int = 0  # Calculé dynamiquement

    class Config:
        from_attributes = True


router = APIRouter()


@router.get("/", response_model=List[ProjectResponse])
def get_projects(
    skip: int = 0,
    limit: int = 100,
    status: Optional[ProjectStatus] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer la liste des projets"""
    query = db.query(Project).filter(Project.workspace_id == current_user.workspace_id)
    
    if status:
        query = query.filter(Project.status == status)
    
    # Filtrer les projets non archivés par défaut
    query = query.filter(Project.is_archived == False)
    
    projects = query.offset(skip).limit(limit).all()
    
    # Ajouter le calcul de progression (simulé pour l'instant)
    result = []
    for project in projects:
        project_dict = {
            "id": project.id,
            "workspace_id": project.workspace_id,
            "name": project.name,
            "code": project.code,
            "description": project.description,
            "client_name": project.client_name,
            "client_contact": project.client_contact,
            "start_date": project.start_date,
            "end_date": project.end_date,
            "budget_total": project.budget_total,
            "address": project.address,
            "status": project.status,
            "is_archived": project.is_archived,
            "created_at": project.created_at,
            "updated_at": project.updated_at,
            "progress": _calculate_project_progress(project)
        }
        result.append(project_dict)
    
    return result


@router.get("/{project_id}", response_model=ProjectResponse)
def get_project(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Récupérer un projet spécifique"""
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.workspace_id == current_user.workspace_id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Projet non trouvé"
        )
    
    # Ajouter le calcul de progression
    project_dict = {
        "id": project.id,
        "workspace_id": project.workspace_id,
        "name": project.name,
        "code": project.code,
        "description": project.description,
        "client_name": project.client_name,
        "client_contact": project.client_contact,
        "start_date": project.start_date,
        "end_date": project.end_date,
        "budget_total": project.budget_total,
        "address": project.address,
        "status": project.status,
        "is_archived": project.is_archived,
        "created_at": project.created_at,
        "updated_at": project.updated_at,
        "progress": _calculate_project_progress(project)
    }
    
    return project_dict


@router.post("/", response_model=ProjectResponse)
def create_project(
    project: ProjectCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Créer un nouveau projet"""
    # Générer un code unique pour le projet
    project_count = db.query(Project).filter(Project.workspace_id == current_user.workspace_id).count()
    project_code = f"PRJ-{current_user.workspace_id:03d}-{project_count + 1:04d}"
    
    db_project = Project(
        workspace_id=current_user.workspace_id,
        code=project_code,
        **project.dict()
    )
    
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    
    # Retourner avec progression
    project_dict = {
        "id": db_project.id,
        "workspace_id": db_project.workspace_id,
        "name": db_project.name,
        "code": db_project.code,
        "description": db_project.description,
        "client_name": db_project.client_name,
        "client_contact": db_project.client_contact,
        "start_date": db_project.start_date,
        "end_date": db_project.end_date,
        "budget_total": db_project.budget_total,
        "address": db_project.address,
        "status": db_project.status,
        "is_archived": db_project.is_archived,
        "created_at": db_project.created_at,
        "updated_at": db_project.updated_at,
        "progress": 0
    }
    
    return project_dict


def _calculate_project_progress(project: Project) -> int:
    """Calculer la progression d'un projet (simulé)"""
    if project.status == ProjectStatus.COMPLETED:
        return 100
    elif project.status == ProjectStatus.EXE:
        return 65  # En cours d'exécution
    elif project.status == ProjectStatus.DAO:
        return 25  # En conception
    else:
        return 0
