'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { BusinessDataService } from '@/lib/auth'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Modal } from '@/components/ui/Modal'
import { Input } from '@/components/ui/Input'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import DataList, { FilterField, DataListAction } from '@/components/ui/DataList'
import { lotsApiService } from '@/lib/api/lots'
import { Lot, LotCreate, PHASE_COLORS, PHASE_LABELS } from '@/types/lot'
import { useLotPhoto } from '@/hooks/useLotPhoto'
import LotPhotoUpload from '@/components/lots/LotPhotoUpload'
import Link from 'next/link'
import { Plus, Package, Calendar, CheckCircle, Clock, AlertCircle, Camera } from 'lucide-react'
import ProjectModal from '@/components/ProjectModal'

export default function ProjectDetails() {
  const { user, signOut } = useAuth()
  const params = useParams()
  const router = useRouter()
  
  // États pour le projet
  const [project, setProject] = useState<Record<string, any> | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // États pour les lots
  const [lots, setLots] = useState<Lot[]>([])
  const [filteredLots, setFilteredLots] = useState<Lot[]>([])
  const [lotsLoading, setLotsLoading] = useState(false)
  
  // États pour les modales
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isCreateLotModalOpen, setIsCreateLotModalOpen] = useState(false)
  const [newLot, setNewLot] = useState<LotCreate>({ name: '', project_id: 0 })
  const [newLotPhotoFile, setNewLotPhotoFile] = useState<File | null>(null)
  const [newLotPhotoUrl, setNewLotPhotoUrl] = useState<string | null>(null)

  // Hook pour l'upload de photo des lots
  const lotPhotoHook = useLotPhoto()



  // Fonction pour charger le projet
  const fetchProject = async () => {
    try {
      setLoading(true)
      console.log('🔍 Fetching project with ID:', params.id)
      const projectId = parseInt(params.id as string)

      const data = await BusinessDataService.getProject(projectId)
      console.log('✅ Project data received:', data)
      setProject(data)
      setError(null)

      // Charger les lots automatiquement
      await fetchLots(projectId)
    } catch (err) {
      console.error('❌ Error fetching project:', err)
      setError('Erreur lors du chargement des détails du projet')
    } finally {
      setLoading(false)
    }
  }

  // Fonction pour mettre à jour le projet
  const handleProjectUpdate = async (formData: any) => {
    try {
      console.log('🔄 Mise à jour du projet:', { projectId: project?.id, formData })

      const response = await BusinessDataService.makeAuthenticatedRequest(`/projects/${project?.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Erreur détaillée:', errorText)
        throw new Error(`Erreur ${response.status}: ${errorText}`)
      }

      const updatedProject = await response.json()
      console.log('✅ Projet mis à jour:', updatedProject)

      // Recharger les données du projet
      await fetchProject()

    } catch (err) {
      console.error('❌ Erreur lors de la mise à jour:', err)
      throw err
    }
  }

  // Charger le projet au montage
  useEffect(() => {
    if (params.id) {
      fetchProject()
    }
  }, [params.id])

  // Fonction pour charger les lots du projet
  const fetchLots = async (projectId: number) => {
    try {
      setLotsLoading(true)
      console.log('🔍 Fetching lots for project:', projectId)
      
      const lotsData = await lotsApiService.getLotsByProject(projectId)
      console.log('✅ Lots loaded:', lotsData)
      setLots(lotsData)
      setFilteredLots(lotsData)
    } catch (err) {
      console.error('❌ Error fetching lots:', err)
    } finally {
      setLotsLoading(false)
    }
  }

  // Configuration des filtres pour les lots
  const filters: FilterField[] = [
    {
      key: 'name',
      label: 'Nom du lot',
      type: 'text',
      placeholder: 'Rechercher par nom...'
    },
    {
      key: 'code',
      label: 'Code',
      type: 'text',
      placeholder: 'Rechercher par code...'
    },
    {
      key: 'current_phase',
      label: 'Phase actuelle',
      type: 'select',
      options: [
        { value: 'ESQ', label: 'Esquisse (ESQ)' },
        { value: 'APD', label: 'Avant-projet (APD)' },
        { value: 'PRODCE', label: 'Projet (PRODCE)' },
        { value: 'EXE', label: 'Exécution (EXE)' }
      ]
    }
  ]

  // Filtres de statut simples pour l'affichage
  const [activeStatusFilter, setActiveStatusFilter] = useState<string>('TOUS')
  const statusFilters = [
    { key: 'TOUS', label: 'TOUS' },
    { key: 'ESQ', label: 'ESQ' },
    { key: 'APD', label: 'APD' },
    { key: 'PRODCE', label: 'PRODCE' },
    { key: 'EXE', label: 'EXE' }
  ]

  // Configuration des actions pour les lots
  const actions: DataListAction[] = [
    {
      label: 'Créer un lot',
      onClick: () => setIsCreateLotModalOpen(true),
      icon: <Plus className="w-5 h-5" />,
      variant: 'primary'
    }
  ]

  // Gérer les filtres des lots
  const handleLotsFiltersChange = (filterValues: Record<string, string>) => {
    let filtered = [...lots]

    Object.entries(filterValues).forEach(([key, value]) => {
      if (value) {
        filtered = filtered.filter(lot => {
          switch (key) {
            case 'name':
              return lot.name?.toLowerCase().includes(value.toLowerCase())
            case 'code':
              return lot.code?.toLowerCase().includes(value.toLowerCase())
            case 'current_phase':
              return lot.current_phase === value
            default:
              return true
          }
        })
      }
    })

    setFilteredLots(filtered)
  }

  // Appliquer le filtre de statut
  useEffect(() => {
    let filtered = [...lots]

    if (activeStatusFilter !== 'TOUS') {
      filtered = filtered.filter(lot => lot.current_phase === activeStatusFilter)
    }

    setFilteredLots(filtered)
  }, [lots, activeStatusFilter])

  // Fonctions pour gérer la photo du nouveau lot
  const handleLotPhotoUpload = async (file: File) => {
    setNewLotPhotoFile(file)
    // Créer une URL temporaire pour l'aperçu
    const tempUrl = URL.createObjectURL(file)
    setNewLotPhotoUrl(tempUrl)
  }

  const handleLotPhotoDelete = async () => {
    setNewLotPhotoFile(null)
    if (newLotPhotoUrl) {
      URL.revokeObjectURL(newLotPhotoUrl)
      setNewLotPhotoUrl(null)
    }
  }

  // Fonction pour créer un nouveau lot
  const handleCreateLot = async () => {
    try {
      if (!newLot.name.trim()) {
        alert('Veuillez saisir un nom pour le lot')
        return
      }

      if (!project?.id) {
        alert('Erreur: projet non trouvé')
        return
      }

      const lotData: LotCreate = {
        ...newLot,
        project_id: project.id
      }

      const createdLot = await lotsApiService.createLot(lotData)

      // Upload de la photo si une photo a été sélectionnée
      if (newLotPhotoFile && createdLot.id) {
        try {
          console.log('📤 Upload de la photo du lot...')
          await lotPhotoHook.uploadPhoto(createdLot.id, newLotPhotoFile)
          console.log('✅ Photo uploadée avec succès')
        } catch (photoError) {
          console.error('❌ Erreur upload photo:', photoError)
          alert('Lot créé mais erreur lors de l\'upload de la photo')
        }
      }

      setLots(prev => [...prev, createdLot])
      setFilteredLots(prev => [...prev, createdLot])
      setNewLot({ name: '', project_id: 0 })
      handleLotPhotoDelete()
      setIsCreateLotModalOpen(false)
    } catch (err) {
      console.error('❌ Error creating lot:', err)
      alert('Erreur lors de la création du lot')
    }
  }

  // Fonction pour supprimer un lot
  const handleDeleteLot = async (lot: Lot) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer le lot "${lot.name}" ?`)) {
      return
    }

    try {
      await lotsApiService.deleteLot(lot.id)
      setLots(prev => prev.filter(l => l.id !== lot.id))
      setFilteredLots(prev => prev.filter(l => l.id !== lot.id))
    } catch (err) {
      console.error('❌ Error deleting lot:', err)
      alert('Erreur lors de la suppression du lot')
    }
  }

  // Fonction pour obtenir la couleur de la phase
  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'ESQ':
        return 'bg-blue-100 text-blue-800'
      case 'APD':
        return 'bg-yellow-100 text-yellow-800'
      case 'PRODCE':
        return 'bg-orange-100 text-orange-800'
      case 'EXE':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Fonction pour obtenir l'icône de la phase
  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'ESQ':
        return <Clock className="w-4 h-4" />
      case 'APD':
        return <AlertCircle className="w-4 h-4" />
      case 'PRODCE':
        return <Calendar className="w-4 h-4" />
      case 'EXE':
        return <CheckCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }





  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-4">{error}</div>
          <Button onClick={() => window.location.reload()}>
            Réessayer
          </Button>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-gray-600">Projet non trouvé</div>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'En cours': return 'bg-blue-100 text-blue-800'
      case 'Terminé': return 'bg-green-100 text-green-800'
      case 'En attente': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-white">
        <div className="flex">
          <ModernSidebar user={user ? {
            name: `${user.first_name} ${user.last_name}`,
            email: user.email
          } : undefined} />

          <div className="flex-1 lg:ml-72">
            <ModernHeader
              title=""
              breadcrumbs={[
                { label: 'Dossiers', href: '/projects' },
                { label: project.name }
              ]}
              backButton={{
                label: 'Retour aux dossiers',
                href: '/projects'
              }}
              editButton={{
                label: 'Modifier',
                onClick: () => setIsEditModalOpen(true)
              }}
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={signOut}
            />

            <main className="p-6">
              <div className="space-y-6">
                {/* Nouvelle fiche projet compacte */}
                <Card className="p-6">
                  <div className="flex gap-6">
                    {/* Photo à gauche (lecture seule) */}
                    <div className="flex-shrink-0">
                      <div
                        className="border-2 border-gray-200 rounded-lg overflow-hidden bg-white"
                        style={{ width: '300px', height: '200px' }}
                      >
                        {project?.photo_url ? (
                          <img
                            src={`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${project.photo_url}`}
                            alt="Photo du projet"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              console.error('Erreur de chargement de la photo:', project.photo_url)
                              e.currentTarget.style.display = 'none'
                            }}
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-gray-50">
                            <div className="text-center">
                              <Camera className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                              <span className="text-sm text-gray-500">Aucune photo</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Informations à droite */}
                    <div className="flex-1">
                      {/* Titre + statut + REF */}
                      <div className="mb-4">
                        <div className="flex items-center gap-3 mb-2">
                          <h1 className="text-2xl font-bold text-gray-900">{project.name}</h1>
                          <span className={`px-3 py-1 text-sm rounded-full ${getStatusColor(project.status)}`}>
                            ({project.status})
                          </span>
                          <span className="text-lg text-gray-600">REF: {project.code}</span>
                        </div>
                        <hr className="border-gray-200" />
                      </div>

                      {/* Informations sur 3 colonnes compactes */}
                      <div className="grid grid-cols-3 gap-6 text-sm">
                        {/* Colonne 1 */}
                        <div className="space-y-2">
                          <div>
                            <span className="text-gray-500">Client</span>
                            <div className="font-medium">{project.client_name || 'Non spécifié'}</div>
                          </div>
                          <div>
                            <span className="text-gray-500">Nature</span>
                            <div className="font-medium">{project.nature || 'Non spécifiée'}</div>
                          </div>
                          <div>
                            <span className="text-gray-500">Budget</span>
                            <div className="font-medium text-green-600">
                              {project.budget_total ? parseFloat(project.budget_total).toLocaleString('fr-FR') : '0'} €
                            </div>
                          </div>
                        </div>

                        {/* Colonne 2 */}
                        <div className="space-y-2">
                          <div>
                            <span className="text-gray-500">Localisation</span>
                            <div className="font-medium">{project.address || 'Non spécifiée'}</div>
                          </div>
                          <div>
                            <span className="text-gray-500">Contact</span>
                            <div className="font-medium">{project.client_contact || 'Non spécifié'}</div>
                          </div>
                          <div>
                            <span className="text-gray-500">Progression</span>
                            <div className="font-medium">{project.progress || 0}%</div>
                          </div>
                        </div>

                        {/* Colonne 3 */}
                        <div className="space-y-2">
                          <div>
                            <span className="text-gray-500">Début</span>
                            <div className="font-medium">
                              {project.start_date ? new Date(project.start_date).toLocaleDateString('fr-FR') : 'Non spécifié'}
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-500">Fin prévue</span>
                            <div className="font-medium">
                              {project.end_date ? new Date(project.end_date).toLocaleDateString('fr-FR') : 'Non spécifiée'}
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-500">Créé le</span>
                            <div className="font-medium">
                              {project.created_at ? new Date(project.created_at).toLocaleDateString('fr-FR') : 'Non spécifié'}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Section des lots personnalisée */}
                <div className="space-y-6">
                  {/* Header avec titre, filtres et boutons sur la même ligne */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900">Lots du projet</h2>
                      <p className="text-sm text-gray-600 mt-1">
                        {filteredLots.length} lot{filteredLots.length > 1 ? 's' : ''} dans ce projet
                      </p>
                    </div>

                    <div className="flex items-center gap-4">
                      {/* Filtres */}
                      <div className="flex items-center gap-2">
                        {statusFilters.map((filter) => (
                          <button
                            key={filter.key}
                            onClick={() => setActiveStatusFilter(filter.key)}
                            className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-colors ${
                              activeStatusFilter === filter.key
                                ? 'bg-ton1 text-white'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                          >
                            {filter.label}
                          </button>
                        ))}
                      </div>

                      {/* Boutons d'action */}
                      <div className="flex items-center gap-2">
                        {actions.map((action, index) => (
                          <button
                            key={index}
                            onClick={action.onClick}
                            className={`flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                              action.variant === 'primary'
                                ? 'bg-ton1 text-white hover:bg-ton1/90'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                          >
                            {action.icon}
                            {action.label}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Contenu des lots */}
                  {lotsLoading ? (
                    <div className="flex justify-center py-12">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ton1"></div>
                    </div>
                  ) : filteredLots.length === 0 ? (
                    <div className="text-center py-12">
                      <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun lot créé</h3>
                      <p className="text-gray-600 mb-6">Créez votre premier lot pour organiser votre projet.</p>
                      <button
                        onClick={() => setIsCreateLotModalOpen(true)}
                        className="inline-flex items-center gap-2 px-4 py-2 bg-ton1 text-white rounded-lg hover:bg-ton1/90 transition-colors"
                      >
                        <Plus className="h-4 w-4" />
                        Créer un lot
                      </button>
                    </div>
                  ) : (
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                      {/* En-têtes du tableau */}
                      <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                        <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700 uppercase tracking-wider">
                          <div className="col-span-1 text-center">#</div>
                          <div className="col-span-4">LOT</div>
                          <div className="col-span-2 text-center">STATUT</div>
                          <div className="col-span-2 text-center">CCTP</div>
                          <div className="col-span-2 text-center">ESTIMATION</div>
                          <div className="col-span-1 text-center">ACTIONS</div>
                        </div>
                      </div>

                      {/* Lignes du tableau */}
                      <div className="divide-y divide-gray-200">
                        {filteredLots.map((lot, index) => (
                          <div
                            key={lot.id}
                            className="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50 transition-colors cursor-pointer"
                            onClick={() => router.push(`/projects/${project?.id}/lots/${lot.id}`)}
                          >
                            {/* Numéro */}
                            <div className="col-span-1 flex items-center justify-center">
                              <div className="w-8 h-8 bg-ton1 text-white rounded-lg flex items-center justify-center text-sm font-medium">
                                {index + 1}
                              </div>
                            </div>

                            {/* Informations du lot */}
                            <div className="col-span-4 flex items-center">
                              <div>
                                <h3 className="text-sm font-semibold text-gray-900">{lot.name}</h3>
                                <p className="text-xs text-gray-500">{lot.code}</p>
                                {lot.description && (
                                  <p className="text-xs text-gray-600 mt-1 line-clamp-1">{lot.description}</p>
                                )}
                              </div>
                            </div>

                            {/* Statut */}
                            <div className="col-span-2 flex items-center justify-center">
                              <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getPhaseColor(lot.current_phase)}`}>
                                {getPhaseIcon(lot.current_phase)}
                                <span className="ml-1">{PHASE_LABELS[lot.current_phase]}</span>
                              </div>
                            </div>

                            {/* CCTP */}
                            <div className="col-span-2 flex items-center justify-center">
                              <div className="text-xs text-gray-600">
                                <div>0 description</div>
                                <div className="text-gray-400">0% complété</div>
                              </div>
                            </div>

                            {/* Estimation */}
                            <div className="col-span-2 flex items-center justify-center">
                              <div className="text-xs text-gray-600">
                                <div>0 € HT</div>
                                <div className="text-gray-400">0% estimé</div>
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="col-span-1 flex items-center justify-center">
                              <button
                                onClick={(e) => {
                                  e.preventDefault()
                                  e.stopPropagation()
                                  handleDeleteLot(lot)
                                }}
                                className="p-1.5 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50"
                                title="Supprimer le lot"
                              >
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9zM4 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 112 0v4a1 1 0 11-2 0V9zm4 0a1 1 0 112 0v4a1 1 0 11-2 0V9z" clipRule="evenodd" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Modal de modification du projet */}
                <ProjectModal
                  isOpen={isEditModalOpen}
                  onClose={() => setIsEditModalOpen(false)}
                  project={project}
                  onSubmit={handleProjectUpdate}
                  onRefresh={fetchProject}
                />

                {/* Modal de création de lot */}
                <Modal isOpen={isCreateLotModalOpen} onClose={() => setIsCreateLotModalOpen(false)}>
                  <div className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Créer un nouveau lot</h2>
                    <div className="space-y-4">
                      <Input
                        label="Nom du lot"
                        value={newLot.name}
                        onChange={(e) => setNewLot(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Ex: Gros œuvre, Second œuvre..."
                      />
                      <Input
                        label="Code du lot (optionnel)"
                        value={newLot.code || ''}
                        onChange={(e) => setNewLot(prev => ({ ...prev, code: e.target.value }))}
                        placeholder="Ex: LOT-01"
                      />
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Description (optionnel)
                        </label>
                        <textarea
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          rows={3}
                          value={newLot.description || ''}
                          onChange={(e) => setNewLot(prev => ({ ...prev, description: e.target.value }))}
                          placeholder="Description du lot..."
                        />
                      </div>

                      {/* Upload de photo */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Photo du lot (optionnel)
                        </label>
                        <LotPhotoUpload
                          currentPhotoUrl={newLotPhotoUrl}
                          onUpload={handleLotPhotoUpload}
                          onDelete={handleLotPhotoDelete}
                          loading={lotPhotoHook.uploading}
                          width={300}
                          height={150}
                        />
                      </div>

                      <div className="flex justify-end gap-2 mt-6">
                        <Button variant="outline" onClick={() => setIsCreateLotModalOpen(false)}>
                          Annuler
                        </Button>
                        <Button onClick={handleCreateLot}>
                          Créer le lot
                        </Button>
                      </div>
                    </div>
                  </div>
                </Modal>
              </div>
            </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
