'use client'

import React, { useState } from 'react'
import { DocumentType } from '@/types/technical-document'
import { XMarkIcon } from '@heroicons/react/24/outline'

interface WordImportModalProps {
  isOpen: boolean
  fileName: string
  onConfirm: (documentType: DocumentType, documentName: string) => void
  onCancel: () => void
}

export default function WordImportModal({
  isOpen,
  fileName,
  onConfirm,
  onCancel
}: WordImportModalProps) {
  const [documentType, setDocumentType] = useState<DocumentType>(DocumentType.CCTP)
  const [documentName, setDocumentName] = useState(fileName.replace(/\.(doc|docx)$/i, ''))

  const handleConfirm = () => {
    onConfirm(documentType, documentName)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Importer un document Word
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fichier sélectionné
            </label>
            <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
              {fileName}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type de document
            </label>
            <select
              value={documentType}
              onChange={(e) => setDocumentType(e.target.value as DocumentType)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={DocumentType.CCTP}>CCTP (Cahier des Clauses Techniques Particulières)</option>
              <option value={DocumentType.DPGF}>DPGF (Décomposition du Prix Global et Forfaitaire)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom du document
            </label>
            <input
              type="text"
              value={documentName}
              onChange={(e) => setDocumentName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="Nom du document..."
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Annuler
          </button>
          <button
            onClick={handleConfirm}
            disabled={!documentName.trim()}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Importer
          </button>
        </div>
      </div>
    </div>
  )
}
