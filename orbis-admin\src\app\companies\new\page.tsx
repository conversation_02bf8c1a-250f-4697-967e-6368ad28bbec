'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft } from 'lucide-react'
import { useToast } from '@/components/ui/Toast'
import { FastCompanyService } from '@/lib/fast-auth'
import AuthGuard from '@/components/AuthGuard'
import CompanyForm from '@/components/CompanyForm'

function NewCompanyPageContent() {
  const { success, error: showError } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleSave = async (companyData: {
    name: string
    code: string
    description?: string
    address?: string
    phone?: string
    email?: string
    website?: string
  }) => {
    try {
      setIsLoading(true)
      console.log('🔄 Création de l\'entreprise...', companyData)
      
      const newCompany = await FastCompanyService.createCompany(companyData)
      
      console.log('✅ Entreprise créée:', newCompany)
      success('Succès', 'Entreprise créée avec succès')
      
      // Rediriger vers la liste des entreprises
      router.push('/companies')
      
    } catch (error) {
      console.error('❌ Erreur création entreprise:', error)
      showError('Erreur', error instanceof Error ? error.message : 'Impossible de créer l\'entreprise')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.push('/companies')
  }

  return (
    <div className="space-y-6">
      {/* Header avec bouton retour */}
      <div className="flex items-center gap-4">
        <button
          onClick={handleCancel}
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Nouvelle entreprise
          </h1>
          <p className="text-gray-600 mt-1">
            Créez une nouvelle entreprise cliente
          </p>
        </div>
      </div>

      {/* Formulaire */}
      <div className="max-w-4xl">
        <CompanyForm
          onSave={handleSave}
          onCancel={handleCancel}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}

export default function NewCompanyPage() {
  return (
    <AuthGuard requireSuperAdmin={true}>
      <NewCompanyPageContent />
    </AuthGuard>
  )
}
