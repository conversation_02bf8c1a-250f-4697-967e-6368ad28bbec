#!/usr/bin/env python3
"""
Script de test d'authentification pour l'API ORBIS
Teste les endpoints d'authentification et les permissions
"""

import requests
import json
from datetime import datetime


# Configuration
API_BASE_URL = "http://localhost:8000"
API_V1_URL = f"{API_BASE_URL}/api/v1"

# Identifiants de test (créés avec des mots de passe hashés)
TEST_USERS = [
    {
        "email": "<EMAIL>",
        "password": "test123",
        "role": "ADMIN",
        "name": "Test User"
    }
]


def print_header(title: str):
    """Affiche un en-tête formaté"""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print(f"{'='*60}")


def print_test_result(test_name: str, success: bool, details: str = ""):
    """Affiche le résultat d'un test"""
    status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
    print(f"{status} - {test_name}")
    if details:
        print(f"   📝 {details}")


def test_api_health():
    """Test de santé de l'API"""
    print_header("Test de Santé de l'API")
    
    try:
        # Test de l'endpoint racine
        response = requests.get(API_BASE_URL, timeout=5)
        if response.status_code == 200:
            print_test_result("API accessible", True, f"Status: {response.status_code}")
        else:
            print_test_result("API accessible", False, f"Status: {response.status_code}")
            return False
        
        # Test de l'endpoint docs
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print_test_result("Documentation accessible", True, "Swagger UI disponible")
        else:
            print_test_result("Documentation accessible", False, f"Status: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print_test_result("Connexion API", False, "Impossible de se connecter à l'API")
        print("💡 Assurez-vous que l'API est démarrée avec: uvicorn app.main:app --reload")
        return False
    except Exception as e:
        print_test_result("Test de santé", False, str(e))
        return False


def test_login(user_data):
    """Test de connexion pour un utilisateur"""
    print(f"\n🔐 Test de connexion - {user_data['name']}")
    
    try:
        # Préparer les données de connexion
        login_data = {
            "email": user_data["email"],
            "password": user_data["password"]
        }

        # Envoyer la requête de connexion
        response = requests.post(
            f"{API_V1_URL}/auth/login",
            json=login_data,  # JSON data
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            token_type = token_data.get("token_type", "bearer")
            
            print_test_result(
                f"Connexion {user_data['email']}", 
                True, 
                f"Token reçu: {access_token[:20]}..."
            )
            
            return {
                "access_token": access_token,
                "token_type": token_type,
                "user": user_data
            }
        else:
            print_test_result(
                f"Connexion {user_data['email']}", 
                False, 
                f"Status: {response.status_code}, Response: {response.text}"
            )
            return None
            
    except Exception as e:
        print_test_result(f"Connexion {user_data['email']}", False, str(e))
        return None


def test_protected_endpoint(auth_data):
    """Test d'un endpoint protégé"""
    if not auth_data:
        return False
    
    print(f"\n🛡️ Test d'endpoint protégé - {auth_data['user']['name']}")
    
    try:
        headers = {
            "Authorization": f"{auth_data['token_type']} {auth_data['access_token']}",
            "Content-Type": "application/json"
        }
        
        # Test de l'endpoint /me (profil utilisateur)
        response = requests.get(f"{API_V1_URL}/auth/me", headers=headers, timeout=10)
        
        if response.status_code == 200:
            user_info = response.json()
            print_test_result(
                "Endpoint protégé /me", 
                True, 
                f"Utilisateur: {user_info.get('email', 'N/A')}"
            )
            return True
        else:
            print_test_result(
                "Endpoint protégé /me", 
                False, 
                f"Status: {response.status_code}, Response: {response.text}"
            )
            return False
            
    except Exception as e:
        print_test_result("Endpoint protégé", False, str(e))
        return False


def test_companies_endpoint(auth_data):
    """Test de l'endpoint des entreprises"""
    if not auth_data:
        return False
    
    print(f"\n🏢 Test endpoint entreprises - {auth_data['user']['name']}")
    
    try:
        headers = {
            "Authorization": f"{auth_data['token_type']} {auth_data['access_token']}",
            "Content-Type": "application/json"
        }
        
        # Test de l'endpoint des entreprises
        response = requests.get(f"{API_V1_URL}/companies", headers=headers, timeout=10)
        
        if response.status_code == 200:
            companies = response.json()
            print_test_result(
                "Endpoint /companies", 
                True, 
                f"{len(companies)} entreprise(s) trouvée(s)"
            )
            
            if companies:
                print("   📋 Entreprises:")
                for company in companies[:3]:  # Afficher les 3 premières
                    print(f"      - {company.get('name', 'N/A')} (ID: {company.get('id', 'N/A')})")
            
            return True
        else:
            print_test_result(
                "Endpoint /companies", 
                False, 
                f"Status: {response.status_code}, Response: {response.text}"
            )
            return False
            
    except Exception as e:
        print_test_result("Endpoint entreprises", False, str(e))
        return False


def test_projects_endpoint(auth_data):
    """Test de l'endpoint des projets"""
    if not auth_data:
        return False
    
    print(f"\n🏗️ Test endpoint projets - {auth_data['user']['name']}")
    
    try:
        headers = {
            "Authorization": f"{auth_data['token_type']} {auth_data['access_token']}",
            "Content-Type": "application/json"
        }
        
        # Test de l'endpoint des projets
        response = requests.get(f"{API_V1_URL}/projects", headers=headers, timeout=10)
        
        if response.status_code == 200:
            projects = response.json()
            print_test_result(
                "Endpoint /projects", 
                True, 
                f"{len(projects)} projet(s) trouvé(s)"
            )
            
            if projects:
                print("   📋 Projets:")
                for project in projects[:3]:  # Afficher les 3 premiers
                    print(f"      - {project.get('name', 'N/A')} (Status: {project.get('status', 'N/A')})")
            
            return True
        else:
            print_test_result(
                "Endpoint /projects", 
                False, 
                f"Status: {response.status_code}, Response: {response.text}"
            )
            return False
            
    except Exception as e:
        print_test_result("Endpoint projets", False, str(e))
        return False


def main():
    """Fonction principale de test"""
    print("🚀 ORBIS - Test d'Authentification de l'API")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 URL de l'API: {API_BASE_URL}")
    
    # Test de santé de l'API
    if not test_api_health():
        print("\n❌ L'API n'est pas accessible. Arrêt des tests.")
        return False
    
    # Tests d'authentification pour chaque utilisateur
    successful_logins = 0
    auth_tokens = []
    
    for user in TEST_USERS:
        auth_data = test_login(user)
        if auth_data:
            successful_logins += 1
            auth_tokens.append(auth_data)
            
            # Test des endpoints protégés
            test_protected_endpoint(auth_data)
            test_companies_endpoint(auth_data)
            test_projects_endpoint(auth_data)
    
    # Résumé final
    print_header("Résumé des Tests")
    
    total_users = len(TEST_USERS)
    success_rate = (successful_logins / total_users) * 100
    
    if successful_logins == total_users:
        print(f"🎉 TOUS LES TESTS RÉUSSIS! ({successful_logins}/{total_users}) - {success_rate:.0f}%")
        print("✅ L'authentification fonctionne parfaitement.")
    elif successful_logins > 0:
        print(f"⚠️  TESTS PARTIELLEMENT RÉUSSIS ({successful_logins}/{total_users}) - {success_rate:.0f}%")
        print("🔧 Certaines authentifications fonctionnent.")
    else:
        print(f"❌ TOUS LES TESTS ONT ÉCHOUÉ ({successful_logins}/{total_users}) - {success_rate:.0f}%")
        print("🚨 Problème avec l'authentification.")
    
    print("\n💡 Pour tester manuellement:")
    print(f"   - Documentation: {API_BASE_URL}/docs")
    print(f"   - Redoc: {API_BASE_URL}/redoc")
    print("   - Utilisez les identifiants ci-dessus dans Swagger UI")
    
    return successful_logins > 0


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
