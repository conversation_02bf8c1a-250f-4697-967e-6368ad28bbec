# Plan d'Implémentation - Notion de Lot dans ORBIS

## Vue d'ensemble
Ajout de la notion de "Lot" comme entité intermédiaire entre les projets et les documents, avec gestion des phases et des intervenants.

## Architecture Actuelle Analysée

### Modèles existants pertinents :
- **Project** : Entité principale des projets
- **TCompany** : Entreprises tierces (remplace EntrepriseTiers)
- **TechnicalDocument** : Documents techniques liés aux projets
- **ProjectCompany** : Relation many-to-many entre projets et entreprises
- **TechnicalDocumentCompany** : Relation entre documents techniques et entreprises

### Structure Frontend :
- Interface React/Next.js avec composants modulaires
- Gestion des permissions via hooks
- API calls centralisées dans lib/api.ts

## Plan d'Implémentation

### Phase 1 : Modèles et Base de Données

#### 1.1 Création du modèle Lot
**Fichier** : `fastapi_template/app/models/lot.py`

```python
class LotPhase(str, enum.Enum):
    ESQ = "ESQ"      # Esquisse
    APD = "APD"      # Avant-Projet Détaillé
    PRODCE = "PRODCE" # Projet de Conception et d'Exécution
    EXE = "EXE"      # Exécution

class Lot(Base):
    __tablename__ = "lots"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    code = Column(String, nullable=False, index=True)
    description = Column(Text)
    
    # Relation obligatoire avec le projet
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    
    # Phase actuelle du lot
    current_phase = Column(Enum(LotPhase), default=LotPhase.ESQ)
    
    # Historique des phases validées
    esq_validated = Column(Boolean, default=False)
    esq_validated_at = Column(DateTime)
    esq_validated_by = Column(Integer, ForeignKey("users.id"))
    
    apd_validated = Column(Boolean, default=False)
    apd_validated_at = Column(DateTime)
    apd_validated_by = Column(Integer, ForeignKey("users.id"))
    
    prodce_validated = Column(Boolean, default=False)
    prodce_validated_at = Column(DateTime)
    prodce_validated_by = Column(Integer, ForeignKey("users.id"))
    
    exe_validated = Column(Boolean, default=False)
    exe_validated_at = Column(DateTime)
    exe_validated_by = Column(Integer, ForeignKey("users.id"))
    
    # Métadonnées
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(Integer, ForeignKey("users.id"))
    
    # Relations
    project = relationship("Project", back_populates="lots")
    workspace = relationship("Workspace")
    creator = relationship("User", foreign_keys=[created_by])
    
    # Relations pour les validateurs de phases
    esq_validator = relationship("User", foreign_keys=[esq_validated_by])
    apd_validator = relationship("User", foreign_keys=[apd_validated_by])
    prodce_validator = relationship("User", foreign_keys=[prodce_validated_by])
    exe_validator = relationship("User", foreign_keys=[exe_validated_by])
    
    # Relations avec les intervenants et documents
    intervenants = relationship("LotIntervenant", back_populates="lot")
    documents = relationship("LotDocument", back_populates="lot")
```

#### 1.2 Table de liaison Lot-Intervenants
```python
class LotIntervenant(Base):
    __tablename__ = "lot_intervenants"
    
    id = Column(Integer, primary_key=True, index=True)
    lot_id = Column(Integer, ForeignKey("lots.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("tcompanies.id"), nullable=False)
    role = Column(String(100))  # Architecte, Bureau d'études, etc.
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    lot = relationship("Lot", back_populates="intervenants")
    company = relationship("TCompany")
```

#### 1.3 Migration des documents vers les lots
```python
class LotDocument(Base):
    __tablename__ = "lot_documents"
    
    id = Column(Integer, primary_key=True, index=True)
    lot_id = Column(Integer, ForeignKey("lots.id"), nullable=False)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    phase = Column(Enum(LotPhase))  # Phase à laquelle appartient le document
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    lot = relationship("Lot", back_populates="documents")
    document = relationship("Document")
```

#### 1.4 Modification du modèle Project
**Fichier** : `fastapi_template/app/models/project.py`
- Ajouter la relation `lots = relationship("Lot", back_populates="project")`

#### 1.5 Modification du modèle TechnicalDocument
**Fichier** : `fastapi_template/app/models/document.py`
- Remplacer `project_id` par `lot_id` dans TechnicalDocument
- Ajouter la relation avec Lot

### Phase 2 : Schémas Pydantic

#### 2.1 Schémas pour Lot
**Fichier** : `fastapi_template/app/schemas/lot.py`

```python
class LotBase(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    current_phase: Optional[LotPhase] = LotPhase.ESQ

class LotCreate(LotBase):
    name: str
    project_id: int
    # Création automatique d'un lot par défaut lors de la création d'un projet

class LotUpdate(LotBase):
    pass

class LotPhaseValidation(BaseModel):
    phase: LotPhase
    validated: bool = True

class LotIntervenantCreate(BaseModel):
    company_id: Optional[int] = None  # Si None, créer une nouvelle TCompany
    company_data: Optional[TCompanyCreate] = None  # Données pour créer une nouvelle entreprise
    role: Optional[str] = None

class LotResponse(LotBase):
    id: int
    project_id: int
    esq_validated: bool
    apd_validated: bool
    prodce_validated: bool
    exe_validated: bool
    intervenants: List[LotIntervenantResponse] = []
    created_at: datetime
    updated_at: datetime
```

### Phase 3 : Migration Alembic

#### 3.1 Création des tables Lot
**Fichier** : `fastapi_template/alembic/versions/004_create_lots_tables.py`

```python
def upgrade():
    # Création table lots
    op.create_table('lots',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('code', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('current_phase', sa.Enum('ESQ', 'APD', 'PRODCE', 'EXE', name='lotphase'), nullable=True),
        # ... autres colonnes
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Création table lot_intervenants
    # Création table lot_documents
    
    # Migration des données existantes
    # Créer un lot par défaut pour chaque projet existant
    # Migrer les documents techniques vers les lots
```

#### 3.2 Migration des données existantes
- Créer un lot par défaut pour chaque projet existant
- Migrer les relations TechnicalDocument de project_id vers lot_id
- Migrer les relations TechnicalDocumentCompany vers LotIntervenant

### Phase 4 : CRUD et API

#### 4.1 CRUD Lot
**Fichier** : `fastapi_template/app/crud/lot.py`

```python
class CRUDLot(CRUDBase[Lot, LotCreate, LotUpdate]):
    def create_with_project(self, db: Session, *, obj_in: LotCreate, project_id: int) -> Lot
    def validate_phase(self, db: Session, *, lot_id: int, phase: LotPhase, user_id: int) -> Lot
    def add_intervenant(self, db: Session, *, lot_id: int, intervenant_data: LotIntervenantCreate) -> LotIntervenant
    def get_by_project(self, db: Session, project_id: int) -> List[Lot]
```

#### 4.2 Endpoints API
**Fichier** : `fastapi_template/app/api/api_v1/endpoints/lots.py`

```python
@router.post("/", response_model=LotResponse)
def create_lot(*, db: Session = Depends(get_db), lot_in: LotCreate, current_user: User = Depends(get_current_user))

@router.get("/{lot_id}", response_model=LotResponse)
def get_lot(*, db: Session = Depends(get_db), lot_id: int, current_user: User = Depends(get_current_user))

@router.put("/{lot_id}/validate-phase", response_model=LotResponse)
def validate_phase(*, db: Session = Depends(get_db), lot_id: int, phase_data: LotPhaseValidation, current_user: User = Depends(get_current_user))

@router.post("/{lot_id}/intervenants", response_model=LotIntervenantResponse)
def add_intervenant(*, db: Session = Depends(get_db), lot_id: int, intervenant_data: LotIntervenantCreate, current_user: User = Depends(get_current_user))

@router.get("/project/{project_id}", response_model=List[LotResponse])
def get_lots_by_project(*, db: Session = Depends(get_db), project_id: int, current_user: User = Depends(get_current_user))
```

### Phase 5 : Permissions et RBAC

#### 5.1 Nouvelles permissions
**Fichier** : `fastapi_template/app/models/rbac.py`

Ajouter les permissions :
- `lot:create`
- `lot:read`
- `lot:update`
- `lot:delete`
- `lot:validate_phase`
- `lot:manage_intervenants`

#### 5.2 Mise à jour des rôles
- **ADMIN** : Toutes les permissions sur les lots
- **MANAGER** : Peut créer, lire, modifier les lots et valider les phases
- **USER** : Peut lire les lots de ses projets
- **VIEWER** : Lecture seule

### Phase 6 : Frontend React

#### 6.1 Types TypeScript
**Fichier** : `orbis-frontend/src/types/lot.ts`

```typescript
export enum LotPhase {
  ESQ = 'ESQ',
  APD = 'APD',
  PRODCE = 'PRODCE',
  EXE = 'EXE'
}

export interface Lot {
  id: number;
  name: string;
  code: string;
  description?: string;
  project_id: number;
  current_phase: LotPhase;
  esq_validated: boolean;
  apd_validated: boolean;
  prodce_validated: boolean;
  exe_validated: boolean;
  intervenants: LotIntervenant[];
  created_at: string;
  updated_at: string;
}

export interface LotIntervenant {
  id: number;
  lot_id: number;
  company: TCompany;
  role?: string;
  is_active: boolean;
}
```

#### 6.2 Hooks personnalisés
**Fichier** : `orbis-frontend/src/hooks/useLots.ts`

```typescript
export const useLots = (projectId: number) => {
  // Gestion des lots d'un projet
}

export const useLotPhases = (lotId: number) => {
  // Gestion des phases d'un lot
}

export const useLotIntervenants = (lotId: number) => {
  // Gestion des intervenants d'un lot
}
```

#### 6.3 Composants React

**Composants à créer :**
- `components/lots/LotCard.tsx` - Carte d'affichage d'un lot
- `components/lots/LotForm.tsx` - Formulaire de création/édition
- `components/lots/LotPhaseTracker.tsx` - Suivi des phases
- `components/lots/LotIntervenantManager.tsx` - Gestion des intervenants
- `components/lots/LotDocumentManager.tsx` - Gestion des documents du lot

#### 6.4 Pages
- `app/projects/[id]/lots/page.tsx` - Liste des lots d'un projet
- `app/lots/[id]/page.tsx` - Détail d'un lot
- `app/lots/[id]/intervenants/page.tsx` - Gestion des intervenants

#### 6.5 Modification des pages existantes
- **Projets** : Ajouter l'onglet "Lots" dans la vue détail
- **Documents techniques** : Modifier pour lier aux lots au lieu des projets
- **TCompanies** : Ajouter la possibilité de créer depuis la gestion des intervenants

### Phase 7 : Logique Métier Spécifique

#### 7.1 Création automatique de lot
Lors de la création d'un projet, créer automatiquement un lot par défaut :
```python
def create_project_with_default_lot(db: Session, project_data: ProjectCreate) -> Project:
    # Créer le projet
    project = crud.project.create(db, obj_in=project_data)
    
    # Créer le lot par défaut
    default_lot = LotCreate(
        name=f"Lot principal - {project.name}",
        code=f"{project.code}-L01",
        project_id=project.id
    )
    crud.lot.create(db, obj_in=default_lot)
    
    return project
```

#### 7.2 Validation des phases
Logique de validation séquentielle :
```python
def validate_phase(db: Session, lot_id: int, phase: LotPhase, user_id: int) -> bool:
    lot = crud.lot.get(db, id=lot_id)
    
    # Vérifier que les phases précédentes sont validées
    if phase == LotPhase.APD and not lot.esq_validated:
        raise HTTPException(status_code=400, detail="ESQ must be validated first")
    
    # Logique de validation...
```

#### 7.3 Gestion des intervenants
```python
def add_intervenant_to_lot(db: Session, lot_id: int, intervenant_data: LotIntervenantCreate) -> LotIntervenant:
    if intervenant_data.company_id:
        # Utiliser une entreprise existante
        company = crud.tcompany.get(db, id=intervenant_data.company_id)
    else:
        # Créer une nouvelle entreprise
        company = crud.tcompany.create(db, obj_in=intervenant_data.company_data)
    
    # Créer la relation lot-intervenant
    return crud.lot_intervenant.create(db, lot_id=lot_id, company_id=company.id, role=intervenant_data.role)
```

## Ordre d'Implémentation Recommandé

1. **Modèles et migrations** (Phase 1 + 3)
2. **Schémas Pydantic** (Phase 2)
3. **CRUD et API** (Phase 4)
4. **Permissions** (Phase 5)
5. **Types et hooks Frontend** (Phase 6.1 + 6.2)
6. **Composants React** (Phase 6.3)
7. **Pages et intégration** (Phase 6.4 + 6.5)
8. **Logique métier** (Phase 7)

## Points d'Attention

### Migration des données
- Assurer la compatibilité avec les documents techniques existants
- Créer des lots par défaut pour tous les projets existants
- Migrer les relations TechnicalDocumentCompany vers LotIntervenant

### Performance
- Indexer les colonnes fréquemment utilisées (project_id, current_phase)
- Optimiser les requêtes avec les relations

### UX/UI
- Interface intuitive pour la gestion des phases
- Workflow clair pour l'ajout d'intervenants
- Visualisation claire de l'état d'avancement des lots

### Permissions
- Contrôle d'accès granulaire par phase
- Validation des permissions avant chaque action critique

## Tests à Implémenter

1. **Tests unitaires** pour les modèles et CRUD
2. **Tests d'intégration** pour les API
3. **Tests de migration** pour la compatibilité des données
4. **Tests frontend** pour les composants React
5. **Tests de permissions** pour la sécurité

Cette implémentation respecte l'architecture existante et ajoute la notion de Lot de manière cohérente avec le système ORBIS.
