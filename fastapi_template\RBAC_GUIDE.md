# 🔐 Guide du Système RBAC ORBIS

## 📋 Vue d'ensemble

Le système RBAC (Role-Based Access Control) d'ORBIS permet une gestion flexible des permissions par entreprise. Chaque utilisateur peut avoir des rôles différents dans différentes entreprises, avec des permissions spécifiques à chaque contexte.

## 🏗️ Architecture

### Tables principales

1. **`roles`** - Définit tous les rôles disponibles
2. **`permissions`** - Définit toutes les permissions granulaires
3. **`company_role_permissions`** - Lie les rôles aux permissions par entreprise
4. **`user_companies`** - Associe les utilisateurs aux entreprises avec leurs rôles

### Rôles disponibles

#### Rôles de gestion
- **ADMIN** - Administrateur d'entreprise (toutes permissions)
- **MANAGER** - Gestionnaire (gestion projets et équipes)
- **USER** - Utilisateur standard
- **VIEWER** - Lecture seule

#### R<PERSON><PERSON> métier BTP
- **MOA** - Maître d'Ouvrage
- **MOADEL** - Maître d'Ouvrage Délégué
- **ARCHI** - Architecte
- **BE** - Bureau d'Études
- **BC** - Bureau de Contrôle
- **OPC** - Ordonnancement Pilotage Coordination
- **ENT** - Entreprise
- **FO** - Fournisseur

## 🔑 Permissions par ressource

### Projects
- `projects.create` - Créer des projets
- `projects.read` - Voir les projets
- `projects.update` - Modifier les projets
- `projects.delete` - Supprimer les projets
- `projects.manage_team` - Gérer l'équipe projet
- `projects.view_financial` - Voir les données financières

### Users
- `users.create` - Créer des utilisateurs
- `users.read` - Voir les utilisateurs
- `users.update` - Modifier les utilisateurs
- `users.delete` - Supprimer les utilisateurs
- `users.invite` - Inviter des utilisateurs
- `users.manage_roles` - Gérer les rôles

### Documents
- `documents.create` - Créer des documents
- `documents.read` - Voir les documents
- `documents.update` - Modifier les documents
- `documents.delete` - Supprimer les documents
- `documents.download` - Télécharger les documents
- `documents.upload` - Uploader des documents

### Budgets, Quotes, Purchase Orders, Invoices, etc.
- Permissions similaires avec actions : `create`, `read`, `update`, `delete`, `approve`, `send`, `validate`

## 🛠️ Utilisation dans le code

### Service RBAC

```python
from app.services.rbac_service import RBACService

# Vérifier une permission
rbac_service = RBACService(db)
has_permission = await rbac_service.check_permission(user_id, company_id, "projects.create")

# Récupérer toutes les permissions d'un utilisateur
permissions = await rbac_service.get_user_permissions(user_id, company_id)

# Configurer les permissions d'un rôle
await rbac_service.set_role_permissions(company_id, "MANAGER", ["projects.read", "projects.update"])
```

### Middleware RBAC

```python
from app.middleware.rbac_middleware import require_company_permission, get_user_context

# Dépendance pour vérifier une permission
@router.get("/projects")
async def get_projects(
    company_id: int,
    user_context = Depends(require_company_permission("projects.read"))
):
    # L'utilisateur a la permission projects.read dans cette entreprise
    pass

# Récupérer le contexte utilisateur avec permissions
@router.get("/dashboard")
async def get_dashboard(
    user_context = Depends(get_user_context)
):
    user_id = user_context["id"]
    permissions = user_context.get("permissions", [])
    pass
```

### Dépendances FastAPI

```python
from app.api.deps import require_projects_read, require_super_admin_new

# Permission spécifique
@router.get("/projects/{project_id}")
async def get_project(
    project_id: int,
    company_id: int,
    user_context = Depends(require_projects_read())
):
    pass

# Super admin uniquement
@router.post("/admin/companies")
async def create_company(
    company_data: CompanyCreate,
    current_user = Depends(require_super_admin_new)
):
    pass
```

## 🌐 Endpoints API

### Gestion des rôles et permissions
- `GET /admin/rbac/roles` - Liste des rôles disponibles
- `GET /admin/rbac/permissions` - Liste des permissions disponibles
- `GET /admin/rbac/companies/{company_id}/permissions-matrix` - Matrice des permissions d'une entreprise

### Gestion des permissions par rôle
- `GET /admin/rbac/companies/{company_id}/roles/{role_name}/permissions` - Permissions d'un rôle
- `POST /admin/rbac/companies/{company_id}/roles/{role_name}/permissions` - Définir les permissions d'un rôle
- `POST /admin/rbac/companies/{company_id}/roles/{role_name}/permissions/add` - Ajouter des permissions
- `POST /admin/rbac/companies/{company_id}/roles/{role_name}/permissions/remove` - Supprimer des permissions

### Gestion des utilisateurs
- `GET /admin/rbac/users/{user_id}/companies/{company_id}/permissions` - Permissions d'un utilisateur
- `POST /admin/rbac/users/{user_id}/companies/{company_id}/assign-role` - Assigner un rôle
- `POST /admin/rbac/users/{user_id}/companies/{company_id}/check-permission` - Vérifier une permission

## 📊 Données de test

Le système a été initialisé avec :
- **5 entreprises** : ORBIS Construction, MOA Développement, Architectes Associés, Bureau Études Techniques, Entreprise Générale BTP
- **10 utilisateurs de test** avec différents rôles métier
- **13 rôles** configurés
- **46 permissions** définies
- **745 associations** rôle-permission par entreprise

### Utilisateurs de test

| Email | Entreprise | Rôle | Permissions |
|-------|------------|------|-------------|
| <EMAIL> | ORBIS Construction | ADMIN | Toutes (46) |
| <EMAIL> | ORBIS Construction | MANAGER | Gestion (27) |
| <EMAIL> | MOA Développement | MOA | Maîtrise d'ouvrage (12) |
| <EMAIL> | Architectes Associés | ARCHI | Architecture (11) |
| <EMAIL> | Bureau Études Techniques | BE | Bureau d'études (10) |

## 🚀 Démarrage

1. **Migration effectuée** ✅
2. **Données de test créées** ✅
3. **Système testé** ✅

Pour démarrer l'application :
```bash
# Backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

# Admin (dans orbis-admin)
npm run dev
```

## 🔧 Configuration personnalisée

Pour personnaliser les permissions d'un rôle dans une entreprise :

```python
# Via l'API
POST /admin/rbac/companies/8/roles/MANAGER/permissions
{
  "permissions": ["projects.read", "projects.update", "users.read"]
}

# Via le service
rbac_service = RBACService(db)
await rbac_service.set_role_permissions(8, "MANAGER", [
    "projects.read", "projects.update", "users.read"
])
```

Le système est maintenant opérationnel et prêt pour la production ! 🎉
