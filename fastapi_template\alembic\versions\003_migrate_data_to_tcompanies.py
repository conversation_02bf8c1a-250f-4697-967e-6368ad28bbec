"""Migrate data from entreprises_tiers to tcompanies and update foreign keys

Revision ID: 003_migrate_data_to_tcompanies
Revises: 002_create_tcompanies
Create Date: 2025-01-10 09:00:00.000000

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '003'
down_revision: Union[str, None] = '002'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Migrate data from entreprises_tiers to tcompanies and update foreign keys"""
    
    print("🔄 Début de la migration des données entreprises_tiers → tcompanies")
    
    # 1. Migrer les données de entreprises_tiers vers tcompanies
    print("📋 1. Migration des données...")
    op.execute("""
        INSERT INTO tcompanies (
            id, company_name, activity, address, postal_code, city, country,
            phone, fax, email, siret, vat_number, legal_representative_id,
            logo_url, logo_filename, workspace_id, is_active, created_at, updated_at, created_by
        )
        SELECT 
            id, nom_entreprise, activite, adresse, code_postal, ville, pays,
            telephone, fax, email, siret, tva_intracommunautaire, representant_legal_id,
            logo_url, logo_filename, workspace_id, is_active, created_at, updated_at, created_by
        FROM entreprises_tiers
        WHERE NOT EXISTS (
            SELECT 1 FROM tcompanies WHERE tcompanies.id = entreprises_tiers.id
        )
    """)
    
    # 2. Mettre à jour les foreign keys dans employees
    print("🔗 2. Mise à jour des foreign keys dans employees...")
    
    # Ajouter la nouvelle colonne tcompany_id
    op.add_column('employees', sa.Column('tcompany_id', sa.Integer(), nullable=True))
    
    # Copier les données de entreprise_tiers_id vers tcompany_id
    op.execute("""
        UPDATE employees 
        SET tcompany_id = entreprise_tiers_id 
        WHERE entreprise_tiers_id IS NOT NULL
    """)
    
    # Ajouter la foreign key constraint
    op.create_foreign_key(
        'fk_employees_tcompany_id', 'employees', 'tcompanies', 
        ['tcompany_id'], ['id']
    )
    
    # 3. Mettre à jour les foreign keys dans technical_document_companies
    print("🔗 3. Mise à jour des foreign keys dans technical_document_companies...")
    
    # Supprimer l'ancienne foreign key constraint
    op.drop_constraint('technical_document_companies_company_id_fkey', 'technical_document_companies', type_='foreignkey')
    
    # Créer la nouvelle foreign key constraint vers tcompanies
    op.create_foreign_key(
        'fk_technical_document_companies_tcompany_id', 'technical_document_companies', 'tcompanies',
        ['company_id'], ['id']
    )
    
    # 4. Mettre à jour les foreign keys dans project_company
    print("🔗 4. Mise à jour des foreign keys dans project_company...")
    
    # Supprimer l'ancienne foreign key constraint
    op.drop_constraint('project_company_company_id_fkey', 'project_company', type_='foreignkey')
    
    # Créer la nouvelle foreign key constraint vers tcompanies
    op.create_foreign_key(
        'fk_project_company_tcompany_id', 'project_company', 'tcompanies',
        ['company_id'], ['id']
    )
    
    print("✅ Migration des données terminée avec succès")
    print("📝 Note: L'ancienne table entreprises_tiers est conservée temporairement")


def downgrade() -> None:
    """Rollback the migration"""
    
    print("🔄 Rollback de la migration tcompanies → entreprises_tiers")
    
    # 1. Restaurer les foreign keys dans project_company
    op.drop_constraint('fk_project_company_tcompany_id', 'project_company', type_='foreignkey')
    op.create_foreign_key(
        'project_company_company_id_fkey', 'project_company', 'entreprises_tiers',
        ['company_id'], ['id']
    )
    
    # 2. Restaurer les foreign keys dans technical_document_companies
    op.drop_constraint('fk_technical_document_companies_tcompany_id', 'technical_document_companies', type_='foreignkey')
    op.create_foreign_key(
        'technical_document_companies_company_id_fkey', 'technical_document_companies', 'entreprises_tiers',
        ['company_id'], ['id']
    )
    
    # 3. Supprimer la colonne tcompany_id des employees
    op.drop_constraint('fk_employees_tcompany_id', 'employees', type_='foreignkey')
    op.drop_column('employees', 'tcompany_id')
    
    # 4. Supprimer les données de tcompanies (optionnel, selon la stratégie)
    op.execute("DELETE FROM tcompanies")
    
    print("✅ Rollback terminé")
