"""
Test spécifique pour la récupération des lots par projet
Utilisateur: <EMAIL>
Mot de passe: orbis123!
"""

import requests
import json
from typing import List, Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "orbis123!"

def authenticate() -> str:
    """Authentification et récupération du token"""
    print("🔐 Authentification...")
    
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/auth/login",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        token_data = response.json()
        access_token = token_data.get("access_token")
        print(f"✅ Authentification réussie!")
        return access_token
    else:
        print(f"❌ Erreur d'authentification: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def get_projects(token: str) -> List[Dict[str, Any]]:
    """Récupérer les projets"""
    print(f"\n🏗️ Récupération des projets...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/projects/",
        headers=headers
    )
    
    if response.status_code == 200:
        projects = response.json()
        print(f"✅ {len(projects)} projet(s) trouvé(s)")
        for project in projects:
            print(f"   - {project.get('name', 'N/A')} (ID: {project.get('id', 'N/A')})")
        return projects
    else:
        print(f"❌ Erreur récupération projets: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def test_get_lots_by_project(token: str, project_id: int) -> List[Dict[str, Any]]:
    """Test de récupération des lots par projet - ENDPOINT PRINCIPAL À TESTER"""
    print(f"\n📊 TEST PRINCIPAL: Récupération des lots du projet {project_id}...")
    print("=" * 60)
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/lots/project/{project_id}",
            headers=headers
        )
        
        print(f"🔍 Status Code: {response.status_code}")
        print(f"🔍 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            lots = response.json()
            print(f"✅ SUCCESS: {len(lots)} lot(s) trouvé(s) dans le projet {project_id}")
            
            if lots:
                print(f"\n📋 Détails des lots:")
                for i, lot in enumerate(lots, 1):
                    print(f"   {i}. Lot ID: {lot.get('id')}")
                    print(f"      Nom: {lot.get('name', 'N/A')}")
                    print(f"      Code: {lot.get('code', 'N/A')}")
                    print(f"      Phase: {lot.get('current_phase', 'N/A')}")
                    print(f"      Description: {lot.get('description', 'N/A')[:50]}...")
                    print(f"      Actif: {lot.get('is_active', 'N/A')}")
                    print(f"      Créé le: {lot.get('created_at', 'N/A')}")
                    
                    # Vérifier les relations
                    intervenants = lot.get('intervenants', [])
                    documents = lot.get('documents', [])
                    print(f"      Intervenants: {len(intervenants)}")
                    print(f"      Documents: {len(documents)}")
                    print()
            else:
                print("ℹ️  Aucun lot trouvé dans ce projet")
            
            return lots
            
        elif response.status_code == 500:
            print(f"❌ ERREUR SERVEUR 500 - Possible DetachedInstanceError")
            print(f"Response: {response.text}")
            return []
        else:
            print(f"❌ Erreur récupération lots par projet: {response.status_code}")
            print(f"Response: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Exception lors de la requête: {e}")
        import traceback
        traceback.print_exc()
        return []

def run_project_lots_test():
    """Test spécifique pour les lots par projet"""
    print("🚀 TEST SPÉCIFIQUE: RÉCUPÉRATION DES LOTS PAR PROJET")
    print("=" * 60)
    print("🎯 Objectif: Vérifier que l'endpoint GET /api/v1/lots/project/{project_id}")
    print("   ne génère plus d'erreur DetachedInstanceError")
    print()
    
    # 1. Authentification
    token = authenticate()
    if not token:
        print("❌ Impossible de continuer sans authentification")
        return
    
    # 2. Récupération des projets
    projects = get_projects(token)
    if not projects:
        print("❌ Aucun projet trouvé - impossible de tester")
        return
    
    # 3. Test pour chaque projet
    success_count = 0
    error_count = 0
    
    for project in projects:
        project_id = project.get('id')
        project_name = project.get('name', 'Projet sans nom')
        
        print(f"\n🔍 Test pour le projet: {project_name} (ID: {project_id})")
        print("-" * 50)
        
        lots = test_get_lots_by_project(token, project_id)
        
        if lots is not None:  # None indique une erreur, [] indique aucun lot
            success_count += 1
            print(f"✅ Test réussi pour le projet {project_id}")
        else:
            error_count += 1
            print(f"❌ Test échoué pour le projet {project_id}")
    
    # 4. Résumé final
    print(f"\n🎉 RÉSUMÉ DU TEST")
    print("=" * 30)
    print(f"✅ Tests réussis: {success_count}")
    print(f"❌ Tests échoués: {error_count}")
    print(f"📊 Total projets testés: {len(projects)}")
    
    if error_count == 0:
        print(f"\n🎊 SUCCÈS COMPLET!")
        print("✅ L'endpoint GET /api/v1/lots/project/{{project_id}} fonctionne correctement")
        print("✅ Plus d'erreur DetachedInstanceError")
    else:
        print(f"\n⚠️  Des erreurs persistent sur {error_count} projet(s)")
        print("❌ Le problème DetachedInstanceError n'est pas complètement résolu")

if __name__ == "__main__":
    try:
        run_project_lots_test()
    except requests.exceptions.ConnectionError:
        print("❌ Serveur non accessible sur http://localhost:8000")
        print("💡 Assurez-vous que le serveur FastAPI est démarré")
    except Exception as e:
        print(f"❌ Erreur durant le test: {e}")
        import traceback
        traceback.print_exc()
