#!/usr/bin/env python3
"""
Script pour synchroniser automatiquement tous les utilisateurs avec Supabase
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au PYTHONPATH
sys.path.append(str(Path(__file__).parent.parent))

from app.core.database import get_db
from app.services.user_service import UserService

async def auto_sync_all_users():
    """Synchronise automatiquement tous les utilisateurs"""
    
    print("🚀 Synchronisation automatique avec Supabase")
    print("=" * 50)
    
    async for db in get_db():
        try:
            user_service = UserService(db)
            
            # Synchroniser tous les utilisateurs
            stats = await user_service.sync_all_users_to_supabase()
            
            print(f"\n🎉 Synchronisation terminée !")
            print(f"✅ Succès: {stats['success']}")
            print(f"ℹ️  Déjà existants: {stats['already_exists']}")
            print(f"❌ Erreurs: {stats['errors']}")
            
            total_ok = stats['success'] + stats['already_exists']
            total_users = total_ok + stats['errors']
            
            if total_ok == total_users:
                print(f"\n🎉 Tous les utilisateurs sont maintenant synchronisés !")
                print(f"🔑 Vous pouvez vous connecter avec n'importe quel utilisateur et le mot de passe: orbis123!")
            else:
                print(f"\n⚠️  {stats['errors']} utilisateurs n'ont pas pu être synchronisés")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            raise
        
        break

if __name__ == "__main__":
    asyncio.run(auto_sync_all_users())
