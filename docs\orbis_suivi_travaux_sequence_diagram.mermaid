sequenceDiagram
    participant U as User
    participant F as Frontend (Next.js)
    participant A as API Gateway
    participant AS as AuthService
    participant PS as ProjectService
    participant ES as ExcelService
    participant DS as DocumentService
    participant DB as PostgreSQL
    participant R as Redis Cache
    participant S3 as File Storage
    participant W as Celery Worker

    %% Authentication Flow
    Note over U,AS: User Authentication
    U->>F: Enter credentials
    F->>A: POST /api/v1/auth/login
    A->>AS: authenticate(email, password)
    AS->>DB: SELECT * FROM users WHERE email=?
    DB-->>AS: User data
    AS->>AS: verify_password()
    AS->>AS: create_access_token()
    AS-->>A: {access_token, refresh_token, user}
    A-->>F: Authentication response
    F->>R: Cache user session
    F-->>U: Redirect to dashboard

    %% Company Selection
    Note over U,DB: Multi-tenant Company Selection
    U->>F: Select company from dropdown
    F->>A: GET /api/v1/users/me/companies
    A->>DB: SELECT * FROM user_companies WHERE user_id=?
    DB-->>A: User companies list
    A-->>F: Companies data
    F->>F: Update company context
    F->>R: Cache selected company_id

    %% Project Creation
    Note over U,PS: Project Management Flow
    U->>F: Fill project creation form
    F->>F: Validate form data (Zod)
    F->>A: POST /api/v1/projects
    A->>PS: create_project(data, company_id)
    PS->>DB: BEGIN TRANSACTION
    PS->>DB: INSERT INTO projects (...)
    DB-->>PS: project_id
    PS->>DS: create_folder_structure(project)
    DS->>S3: Create folder hierarchy
    S3-->>DS: Success
    PS->>DB: INSERT INTO project_documents (folders)
    PS->>DB: COMMIT TRANSACTION
    PS-->>A: Created project
    A-->>F: Project response
    F->>F: Update projects list
    F-->>U: Show success message

    %% Employee Management
    Note over U,DB: Employee Assignment
    U->>F: Assign employee to project
    F->>A: POST /api/v1/projects/{id}/employees
    A->>DB: INSERT INTO project_employees
    DB-->>A: Assignment created
    A-->>F: Success response
    
    U->>F: Log work hours
    F->>A: POST /api/v1/time-entries
    A->>DB: INSERT INTO time_tracking
    DB-->>A: Time entry created
    A-->>F: Success response

    %% Excel Import Flow
    Note over U,W: DPGF Excel Import Process
    U->>F: Upload DPGF Excel file
    F->>F: Validate file format
    F->>A: POST /api/v1/excel/import/dpgf
    A->>ES: import_dpgf(file, mapping, project_id)
    ES->>DB: INSERT INTO excel_imports (status='PENDING')
    DB-->>ES: import_id
    ES->>W: Queue import task (Celery)
    ES-->>A: {import_id, status='PENDING'}
    A-->>F: Import started response
    F-->>U: Show progress indicator
    
    %% Async Excel Processing
    W->>W: Process Excel file
    W->>W: Validate data structure
    W->>W: Apply column mapping
    W->>DB: BEGIN TRANSACTION
    loop For each row
        W->>DB: INSERT INTO quote_lines
    end
    W->>DB: UPDATE excel_imports SET status='COMPLETED'
    W->>DB: COMMIT TRANSACTION
    
    %% Progress Polling
    F->>A: GET /api/v1/excel/import/{id}/status
    A->>DB: SELECT status FROM excel_imports
    DB-->>A: Import status
    A-->>F: Status response
    F-->>U: Update progress

    %% Material Management
    Note over U,DB: Material Catalog Management
    U->>F: Add new material
    F->>A: POST /api/v1/materials
    A->>DB: INSERT INTO materials
    DB-->>A: Material created
    A-->>F: Success response
    
    U->>F: Create technical sheet
    F->>A: POST /api/v1/materials/{id}/technical-sheet
    A->>DB: INSERT INTO technical_sheets
    A->>S3: Upload technical documents
    S3-->>A: File URLs
    A->>DB: UPDATE technical_sheets SET documents
    DB-->>A: Technical sheet created
    A-->>F: Success response

    %% Purchase Order Flow
    Note over U,DB: Purchase Order Management
    U->>F: Create purchase order
    F->>A: POST /api/v1/purchase-orders
    A->>DB: BEGIN TRANSACTION
    A->>DB: INSERT INTO purchase_orders
    DB-->>A: order_id
    loop For each line item
        A->>DB: INSERT INTO purchase_order_lines
    end
    A->>DB: COMMIT TRANSACTION
    A-->>F: Purchase order created
    
    U->>F: Record delivery
    F->>A: POST /api/v1/purchase-orders/{id}/deliveries
    A->>DB: BEGIN TRANSACTION
    A->>DB: INSERT INTO deliveries
    loop For each delivered item
        A->>DB: INSERT INTO delivery_lines
        A->>DB: UPDATE purchase_order_lines SET delivered_quantity
    end
    A->>DB: UPDATE purchase_orders SET status
    A->>DB: COMMIT TRANSACTION
    A-->>F: Delivery recorded

    %% Subcontractor Validation
    Note over U,DB: Subcontractor Work Validation
    U->>F: Open validation interface
    F->>A: GET /api/v1/projects/{id}/validation-data
    A->>DB: Complex JOIN query (projects, budgets, quotes)
    DB-->>A: Validation data
    A->>R: Cache validation data (30min)
    A-->>F: Validation interface data
    
    U->>F: Select work items for validation
    F->>F: Calculate budget impact (JavaScript)
    F->>F: Show real-time simulations
    U->>F: Submit validation
    F->>A: POST /api/v1/validations
    A->>DB: BEGIN TRANSACTION
    A->>DB: INSERT INTO subcontractor_validations
    A->>DB: UPDATE budget_lines SET actual_amount
    A->>DB: INSERT INTO validation_history
    A->>DB: COMMIT TRANSACTION
    A->>R: Invalidate cached budget data
    A-->>F: Validation saved
    F-->>U: Show validation confirmation

    %% Financial Management
    Note over U,DB: Invoice Processing
    U->>F: Create supplier invoice
    F->>A: POST /api/v1/invoices
    A->>DB: INSERT INTO invoices
    DB-->>A: Invoice created
    A-->>F: Success response
    
    U->>F: Mark invoice as paid
    F->>A: PUT /api/v1/invoices/{id}/pay
    A->>DB: UPDATE invoices SET status='PAID', payment_date=NOW()
    A->>DB: UPDATE project budgets
    DB-->>A: Invoice updated
    A-->>F: Payment recorded

    %% Document Management
    Note over U,S3: Document Upload and Management
    U->>F: Upload project document
    F->>F: Validate file size/type
    F->>A: POST /api/v1/documents/upload
    A->>DS: upload_document(file, project_id, category)
    DS->>S3: Store file with versioning
    S3-->>DS: File URL and metadata
    DS->>DB: INSERT INTO documents
    DB-->>DS: Document record created
    DS-->>A: Document metadata
    A-->>F: Upload success
    F-->>U: Show document in list

    %% Dashboard and Reporting
    Note over U,W: Dashboard Data Loading
    U->>F: Load project dashboard
    F->>A: GET /api/v1/projects/{id}/dashboard
    A->>R: Check cached dashboard data
    alt Cache hit
        R-->>A: Cached dashboard data
    else Cache miss
        A->>DB: Complex aggregation queries
        DB-->>A: Raw dashboard data
        A->>A: Process and format data
        A->>R: Cache processed data (15min TTL)
    end
    A-->>F: Dashboard data
    F->>F: Render charts and metrics
    F-->>U: Display dashboard
    
    %% Report Generation
    U->>F: Request project report
    F->>A: POST /api/v1/reports/generate
    A->>W: Queue report generation (Celery)
    A-->>F: Report job started
    F-->>U: Show generation progress
    
    W->>DB: Query report data
    DB-->>W: Report dataset
    W->>W: Generate PDF/Excel report
    W->>S3: Store generated report
    S3-->>W: Report URL
    W->>DB: UPDATE reports SET file_path, status='COMPLETED'
    
    F->>A: GET /api/v1/reports/{id}/status
    A->>DB: SELECT status FROM reports
    DB-->>A: Report status
    A-->>F: Status response
    F-->>U: Download link available

    %% Error Handling and Logging
    Note over A,DB: Error Handling Flow
    alt Database Error
        DB-->>A: Error response
        A->>A: Log error details
        A->>A: Rollback transaction
        A-->>F: {error: "Database error", code: 500}
        F-->>U: Show error message
    end
    
    alt Validation Error
        A->>A: Validate input data
        A-->>F: {error: "Validation failed", details: [...]}
        F-->>U: Show field-specific errors
    end
    
    alt Permission Error
        A->>AS: check_permission(user, resource, action)
        AS-->>A: Permission denied
        A-->>F: {error: "Access denied", code: 403}
        F-->>U: Show access denied message
    end

    %% Background Tasks
    Note over W,DB: Scheduled Background Tasks
    W->>W: Daily backup task
    W->>DB: pg_dump database
    W->>S3: Store backup file
    
    W->>W: Clean old files task
    W->>S3: List old temporary files
    W->>S3: Delete expired files
    
    W->>W: Generate weekly reports
    W->>DB: Query weekly metrics
    W->>W: Generate report PDFs
    W->>S3: Store reports
    W->>DB: INSERT INTO reports