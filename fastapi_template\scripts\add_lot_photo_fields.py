#!/usr/bin/env python3
"""
Script pour ajouter les champs photo aux lots
"""

import asyncio
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine

async def add_lot_photo_fields():
    """Ajouter les champs photo_url et photo_filename à la table lots"""
    
    try:
        async with engine.begin() as conn:
            print("🔍 Vérification de l'existence des champs photo dans la table lots...")
            
            # Vérifier si les champs existent déjà
            check_photo_url = await conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'lots' AND column_name = 'photo_url'
            """))
            
            check_photo_filename = await conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'lots' AND column_name = 'photo_filename'
            """))
            
            photo_url_exists = check_photo_url.fetchone() is not None
            photo_filename_exists = check_photo_filename.fetchone() is not None
            
            if photo_url_exists and photo_filename_exists:
                print("✅ Les champs photo existent déjà dans la table lots")
                return
            
            print("📝 Ajout des champs photo à la table lots...")
            
            # Ajouter photo_url si il n'existe pas
            if not photo_url_exists:
                await conn.execute(text("""
                    ALTER TABLE lots 
                    ADD COLUMN photo_url VARCHAR(500)
                """))
                print("✅ Champ photo_url ajouté")
            
            # Ajouter photo_filename si il n'existe pas
            if not photo_filename_exists:
                await conn.execute(text("""
                    ALTER TABLE lots 
                    ADD COLUMN photo_filename VARCHAR(255)
                """))
                print("✅ Champ photo_filename ajouté")
            
            print("🎉 Migration terminée avec succès !")
            
    except Exception as e:
        print(f"❌ Erreur lors de la migration : {e}")
        raise
    finally:
        await engine.dispose()

if __name__ == "__main__":
    print("🚀 Démarrage de la migration des champs photo pour les lots...")
    asyncio.run(add_lot_photo_fields())
