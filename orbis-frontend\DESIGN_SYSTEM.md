# Système de Design ORBIS - 4 Tons

## Vue d'ensemble

Le système de design d'ORBIS utilise une palette de couleurs simplifiée basée sur 4 tons principaux pour créer une interface sobre et cohérente, sans aucun fond vert.

## Palette de Couleurs

### Ton 1 - Vert Principal (#0F766E)
- **Usage** : Éléments actifs, boutons primaires, liens, états de focus
- **Variantes** :
  - `--ton1` : #0F766E (couleur principale)
  - `--ton1-light` : #14b8a6 (version plus claire pour les hovers)
  - `--ton1-dark` : #0d5c55 (version plus foncée pour les états actifs)

### Ton 2 - <PERSON><PERSON> (#333333)
- **Usage** : Texte principal, éléments secondaires, bordures importantes
- **Variantes** :
  - `--ton2` : #333333 (couleur principale)
  - `--ton2-light` : #666666 (texte secondaire)
  - `--ton2-dark` : #1a1a1a (texte très important)

### Ton 3 - <PERSON><PERSON><PERSON> (#F8F9FA)
- **Usage** : Arrière-plans neutres, états hover, bordures subtiles
- **Variantes** :
  - `--ton3` : #F8F9FA (couleur principale)
  - `--ton3-dark` : #E9ECEF (version plus foncée)

### Ton 4 - Blanc Pur (#FFFFFF)
- **Usage** : Arrière-plans de cartes, contrastes maximaux, fond principal
- `--ton4` : #FFFFFF

## Classes CSS Utilitaires

### Couleurs de Texte
```css
.text-ton1        /* Vert principal */
.text-ton1-light  /* Vert clair */
.text-ton1-dark   /* Vert foncé */
.text-ton2        /* Gris principal */
.text-ton2-light  /* Gris clair */
.text-ton2-dark   /* Gris foncé */
.text-ton3        /* Vert très clair */
.text-ton3-dark   /* Vert très clair foncé */
```

### Couleurs d'Arrière-plan
```css
.bg-ton1        /* Vert principal */
.bg-ton1-light  /* Vert clair */
.bg-ton1-dark   /* Vert foncé */
.bg-ton2        /* Gris principal */
.bg-ton2-light  /* Gris clair */
.bg-ton2-dark   /* Gris foncé */
.bg-ton3        /* Vert très clair */
.bg-ton3-dark   /* Vert très clair foncé */
```

### Couleurs de Bordure
```css
.border-ton1        /* Vert principal */
.border-ton1-light  /* Vert clair */
.border-ton1-dark   /* Vert foncé */
.border-ton2        /* Gris principal */
.border-ton2-light  /* Gris clair */
.border-ton2-dark   /* Gris foncé */
.border-ton3        /* Vert très clair */
.border-ton3-dark   /* Vert très clair foncé */
```

## Configuration Tailwind

Le système est intégré dans la configuration Tailwind avec des alias pour la compatibilité :

```javascript
colors: {
  // Système de couleurs 3 tons
  ton1: {
    DEFAULT: '#0F766E',
    light: '#14b8a6',
    dark: '#0d5c55',
  },
  ton2: {
    DEFAULT: '#333333',
    light: '#666666',
    dark: '#1a1a1a',
  },
  ton3: {
    DEFAULT: '#F0FDF4',
    dark: '#dcfce7',
  },
  // Alias pour compatibilité
  primary: {
    50: '#F0FDF4',
    100: '#dcfce7',
    500: '#14b8a6',
    600: '#0d9488',
    700: '#0F766E',
  },
}
```

## Composants

### Boutons
- **Primaire** : `bg-ton1` avec `text-white`
- **Secondaire** : `bg-ton3` avec `text-ton2`
- **Outline** : `border-ton3` avec `text-ton2`

### Cartes
- **Arrière-plan** : `bg-white`
- **Bordures** : `border-ton3`
- **Hover** : `hover:bg-ton3`

### Formulaires
- **Labels** : `text-ton2`
- **Inputs** : `border-ton3` avec `focus:border-ton1`
- **Placeholders** : `text-ton2-light`

## Suppression des Icônes

Pour maintenir un design sobre, les icônes non essentielles ont été supprimées et remplacées par :

1. **Texte descriptif** : Utilisation de labels clairs
2. **Formes géométriques** : Cercles, carrés avec initiales ou abréviations
3. **Couleurs significatives** : Utilisation des 3 tons pour indiquer l'état ou l'importance

### Exemples de Remplacement
- **Icônes de fichiers** → Badges avec type de fichier (PDF, DOC, etc.)
- **Icônes de navigation** → Texte simple avec couleurs d'état
- **Icônes d'action** → Boutons avec texte descriptif

## Avantages du Système

1. **Cohérence** : Toutes les couleurs dérivent des 3 tons de base
2. **Simplicité** : Facile à maintenir et à modifier
3. **Accessibilité** : Contrastes optimisés
4. **Performance** : Moins de ressources graphiques
5. **Flexibilité** : Changement global possible en modifiant les 3 variables CSS

## Migration

Pour changer toute la palette de couleurs du site :

1. Modifier les 3 variables CSS dans `globals.css`
2. Optionnel : Ajuster les variantes (light/dark) si nécessaire
3. Le changement s'applique automatiquement à tout le site

```css
:root {
  --ton1: #NOUVELLE_COULEUR_1;  /* Couleur principale */
  --ton2: #NOUVELLE_COULEUR_2;  /* Couleur secondaire */
  --ton3: #NOUVELLE_COULEUR_3;  /* Couleur d'arrière-plan */
}
```
