{"project_overview": {"project_name": "EITP Suivi Travaux - SAAS Application", "client": "EITP", "contact_person": "<PERSON>", "industry": "Construction/Building", "language": "French", "description": "Construction project management and tracking SAAS application for managing construction sites, subcontractors, employees, quotes, invoices, and materials."}, "technical_stack": {"backend": {"framework": "Python FastAPI", "database": "PostgreSQL", "features": ["RESTful API endpoints", "User authentication and authorization", "File upload and management", "Data validation and serialization", "Automatic backup system", "Excel import/export functionality"]}, "frontend": {"framework": "Next.js with TypeScript", "features": ["Responsive web interface", "Dynamic forms and tables", "File management interface", "Dashboard and reporting", "Print functionality (A4 format)", "Real-time data updates"]}, "deployment": {"type": "SAAS (Software as a Service)", "hosting": "Cloud-based", "backup": "Automated backup system"}}, "user_management": {"authentication": {"login_system": true, "password_protection": true, "user_roles": ["Administrator", "Project Manager", "Employee", "Viewer"]}, "permissions": {"role_based_access": true, "module_protection": true, "data_isolation": "Multi-tenant architecture"}}, "core_modules": {"company_management": {"description": "Management of internal companies", "features": ["Add/Edit/Archive internal companies", "Support for up to 10 companies", "Dynamic button display", "Company-specific data isolation"], "database_tables": ["companies", "company_settings"]}, "project_management": {"description": "Construction site and project tracking", "features": ["Project creation and management", "DAO (Design-Build) to EXE (Execution) conversion", "Project dashboard and progress tracking", "Document management with folder structure", "Project archiving system"], "database_tables": ["projects", "project_documents", "project_status"]}, "employee_management": {"description": "Employee tracking and time management", "features": ["Employee registration and profiles", "Time tracking and attendance", "Project assignment", "Employee archiving", "Payroll integration data"], "database_tables": ["employees", "time_tracking", "employee_assignments"]}, "supplier_subcontractor_management": {"description": "Supplier and subcontractor management", "features": ["Supplier/subcontractor registration", "Contact information management", "Performance tracking", "Contract management", "Invoice validation system"], "database_tables": ["suppliers", "subcontractors", "contracts"]}, "quote_management": {"description": "Quote creation and management", "features": ["Quote creation and editing", "Quote templates", "Multi-project quote linking", "Quote approval workflow", "Quote to contract conversion"], "database_tables": ["quotes", "quote_lines", "quote_templates"]}, "purchase_order_management": {"description": "Purchase order tracking and management", "features": ["Purchase order creation", "Supplier linking", "Delivery date tracking", "Order status management", "Budget tracking and calculations"], "database_tables": ["purchase_orders", "order_lines", "deliveries"]}, "material_management": {"description": "Material and equipment tracking", "features": ["Material catalog management", "DAF (Technical Data Sheet) creation", "Supplier linking", "Technical specifications", "Price library management"], "database_tables": ["materials", "technical_sheets", "price_library"]}, "financial_management": {"description": "Financial tracking and reporting", "features": ["Budget management", "Invoice processing", "Payment tracking", "Cost analysis", "Financial reporting", "Subcontractor validation"], "database_tables": ["budgets", "invoices", "payments", "financial_reports"]}, "document_management": {"description": "Document storage and organization", "features": ["Hierarchical folder structure", "PDF file management", "Document linking to projects", "Automatic folder creation", "Document versioning"], "folder_structure": {"root": "Z:\\AFFAIRES EITP\\NOM AFFAIRES", "subfolders": ["DCE", "DAO", "DPGF", "FT", "EXE", "PLANNING", "DEVIS", "CANDIDATURE", "DEMAT", "NEGO", "DIVERS"]}}, "reporting_dashboard": {"description": "Progress tracking and reporting", "features": ["Project progress dashboard", "Work advancement tracking", "Employee time reports", "Financial summaries", "Detailed task planning", "Statistical analysis"]}}, "specific_functionalities": {"excel_integration": {"description": "Excel file import/export capabilities", "features": ["DPGF (Price Schedule) import from Excel", "Flexible column mapping", "Data validation during import", "Export reports to Excel format"], "required_fields": ["Work description", "Unit", "Quantity", "Unit price", "Total HT", "Purchase budget", "Hours", "Hourly rate"], "optional_fields": ["CCTP Index", "Sales coefficient"]}, "subcontractor_validation": {"description": "Subcontractor work validation system", "features": ["Work validation workflow", "Budget integration", "Performance simulation", "Cell selection for calculations", "Visual highlighting"]}, "purchase_tracking": {"description": "Non-purchase order buying tracking", "features": ["Off-order purchase management", "Automatic calculations", "Integration with subcontractor tracking"]}, "progress_simulation": {"description": "Project progress simulation", "features": ["Subcontractor assignment simulation", "Cost calculation", "Interactive cell selection", "Scenario planning"]}}, "database_schema": {"estimated_tables": 42, "key_entities": {"companies": {"description": "Internal company information", "estimated_records": 10}, "projects": {"description": "Construction projects/sites", "estimated_records": 999999}, "employees": {"description": "Employee information", "estimated_records": 999999}, "suppliers": {"description": "Suppliers and subcontractors", "estimated_records": 999999}, "purchase_orders": {"description": "Purchase orders", "estimated_records": 999999}, "quotes": {"description": "Project quotes", "estimated_records": 999999}, "materials": {"description": "Material catalog", "estimated_records": 999999}, "time_tracking": {"description": "Employee time tracking", "estimated_records": 999999}, "invoices": {"description": "Financial invoices", "estimated_records": 999999}, "documents": {"description": "Document management", "estimated_records": 999999}}}, "technical_requirements": {"performance": {"response_time": "< 2 seconds for standard operations", "concurrent_users": "Multi-user support", "data_volume": "Support for large datasets (999,999+ records)"}, "security": {"authentication": "Secure login system", "authorization": "Role-based access control", "data_protection": "Encrypted data transmission", "backup": "Automated daily backups"}, "integration": {"file_formats": ["Excel (.xlsx)", "PDF", "CSV"], "print_support": "A4 format printing", "browser_compatibility": "Modern web browsers"}}, "user_interface_requirements": {"design": {"responsive": true, "language": "French", "print_format": "A4", "custom_branding": false}, "navigation": {"menu_structure": "Hierarchical menu system", "search_functionality": "Global search across modules", "filtering": "Advanced filtering options"}, "forms": {"dynamic_forms": true, "validation": "Real-time form validation", "auto_save": "Automatic save functionality"}}, "migration_considerations": {"current_system": "Windows-based application", "data_migration": "Manual data entry or Excel import", "training_requirements": "User training for web interface", "deployment_strategy": "Cloud-first SAAS deployment"}, "future_enhancements": {"mobile_app": "Potential mobile application development", "api_extensions": "Third-party integration capabilities", "advanced_reporting": "Enhanced analytics and reporting", "workflow_automation": "Automated business process workflows"}, "compliance_requirements": {"data_ownership": "Client owns exclusive rights to software and source code", "maintenance_contract": "Ongoing maintenance and support contract required", "documentation": "Basic user documentation (not initially required)", "help_system": "Built-in help system (not initially required)"}}