import React from 'react'

interface EditorSidebarProps {
  lotId?: number | null
  isSaving: boolean
  hasUnsavedChanges: boolean
  onSave: () => void
  onClose: () => void
  onGenerateCoverPage?: () => void
  onShowAddArticleModal?: () => void
  isGeneratingCoverPage?: boolean
  isGeneratingArticle?: boolean
}

export default function EditorSidebar({
  lotId,
  isSaving,
  hasUnsavedChanges,
  onSave,
  onClose,
  onGenerateCoverPage,
  onShowAddArticleModal,
  isGeneratingCoverPage = false,
  isGeneratingArticle = false
}: EditorSidebarProps) {
  return (
    <div className="simple-sidebar">
      <div className="lot-info">
        <div className="lot-name">Lot {lotId || 'N/A'}</div>
        <div className="lot-subtitle">Document technique</div>
      </div>

      {/* Bloc du haut : Actions de création */}
      <div className="sidebar-top-actions">
        {onGenerateCoverPage && (
          <button
            onClick={onGenerateCoverPage}
            disabled={isGeneratingCoverPage}
            className={`sidebar-btn generate-cover-btn ${isGeneratingCoverPage ? 'loading' : ''}`}
            title="Générer une page de garde"
          >
            {isGeneratingCoverPage ? (
              <>
                <span className="spinner">⏳</span> Génération...
              </>
            ) : (
              '📄 Générer Page de Garde'
            )}
          </button>
        )}

        {onShowAddArticleModal && (
          <button
            onClick={onShowAddArticleModal}
            disabled={isGeneratingArticle}
            className={`sidebar-btn add-article-btn ${isGeneratingArticle ? 'loading' : ''}`}
            title="Ajouter un article CCTP"
          >
            {isGeneratingArticle ? (
              <>
                <span className="spinner">⏳</span> Génération...
              </>
            ) : (
              '➕ Ajouter un Article'
            )}
          </button>
        )}
      </div>

      {/* Bloc du bas : Actions de sauvegarde */}
      <div className="sidebar-bottom-actions">
        <button
          onClick={onSave}
          disabled={isSaving}
          className={`sidebar-btn save-btn ${hasUnsavedChanges ? 'unsaved' : ''}`}
          title="Sauvegarder le document"
        >
          {isSaving ? '⏳ Sauvegarde...' : '💾 Sauvegarder'}
        </button>

        <button
          onClick={onClose}
          className="sidebar-btn close-btn"
          title="Fermer l'éditeur"
        >
          ✕ Fermer
        </button>
      </div>
    </div>
  )
}
