#!/usr/bin/env python3
"""
Script pour synchroniser les utilisateurs locaux avec Supabase
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au PYTHONPATH
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.models.user import User
from app.services.supabase_auth import SupabaseAuthService

async def sync_users_to_supabase():
    """Synchronise tous les utilisateurs locaux avec Supabase"""
    
    new_password = "orbis123!"
    
    print(f"🔄 Synchronisation des utilisateurs avec Supabase...")
    print(f"📝 Mot de passe par défaut: {new_password}")
    
    async for db in get_db():
        try:
            # Récupérer tous les utilisateurs
            result = await db.execute(select(User))
            users = result.scalars().all()
            
            print(f"👥 {len(users)} utilisateurs trouvés")
            
            if len(users) == 0:
                print("❌ Aucun utilisateur trouvé dans la base de données")
                return
            
            # Demander confirmation
            print("\n📋 Liste des utilisateurs qui seront synchronisés:")
            for user in users:
                print(f"  - {user.email} (ID: {user.id})")
            
            print(f"\n⚠️  ATTENTION: Cette action va créer/mettre à jour tous les utilisateurs dans Supabase avec le mot de passe '{new_password}'")
            confirmation = input("Êtes-vous sûr de vouloir continuer ? (oui/non): ").lower().strip()
            
            if confirmation not in ['oui', 'o', 'yes', 'y']:
                print("❌ Opération annulée")
                return
            
            # Initialiser le service Supabase
            supabase_auth = SupabaseAuthService()
            
            # Synchroniser chaque utilisateur
            success_count = 0
            error_count = 0
            
            for user in users:
                try:
                    print(f"\n🔄 Traitement de: {user.email}")
                    
                    # Essayer de créer l'utilisateur dans Supabase
                    try:
                        result = await supabase_auth.create_user(
                            email=user.email,
                            password=new_password,
                            user_metadata={
                                "first_name": user.first_name or "",
                                "last_name": user.last_name or "",
                                "role": str(user.role) if user.role else "USER"
                            }
                        )
                        
                        if result.get("user"):
                            print(f"✅ Utilisateur créé dans Supabase: {user.email}")
                            success_count += 1
                        else:
                            print(f"❌ Échec création Supabase pour: {user.email}")
                            error_count += 1
                            
                    except Exception as create_error:
                        # Si l'utilisateur existe déjà, essayer de mettre à jour le mot de passe
                        if "already registered" in str(create_error).lower() or "user_already_exists" in str(create_error).lower():
                            print(f"ℹ️  Utilisateur existe déjà, tentative de mise à jour du mot de passe...")
                            
                            try:
                                # Essayer de se connecter pour vérifier si le mot de passe est déjà correct
                                login_result = await supabase_auth.login_user(user.email, new_password)
                                if login_result.get("user"):
                                    print(f"✅ Mot de passe déjà correct pour: {user.email}")
                                    success_count += 1
                                else:
                                    print(f"⚠️  Impossible de vérifier le mot de passe pour: {user.email}")
                                    error_count += 1
                            except Exception as login_error:
                                print(f"⚠️  Impossible de vérifier le mot de passe pour {user.email}: {login_error}")
                                error_count += 1
                        else:
                            print(f"❌ Erreur création pour {user.email}: {create_error}")
                            error_count += 1
                    
                except Exception as e:
                    print(f"❌ Erreur générale pour {user.email}: {e}")
                    error_count += 1
            
            print(f"\n🎉 Synchronisation terminée!")
            print(f"✅ Succès: {success_count}/{len(users)}")
            print(f"❌ Erreurs: {error_count}/{len(users)}")
            
            if success_count > 0:
                print(f"\n🔑 Vous pouvez maintenant vous connecter avec:")
                print(f"   - Email: n'importe quel utilisateur de la liste")
                print(f"   - Mot de passe: {new_password}")
            
        except Exception as e:
            print(f"❌ Erreur générale: {e}")
            raise
        
        break  # Sortir de la boucle async for

async def main():
    """Fonction principale"""
    print("🚀 Script de synchronisation Supabase ORBIS")
    print("=" * 50)
    
    try:
        await sync_users_to_supabase()
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
