#!/usr/bin/env python3
"""
Script de test complet pour valider la migration companies → workspaces
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter les répertoires au path Python
sys.path.insert(0, str(Path(__file__).parent / "fastapi_template"))

def test_database_structure():
    """Tester la structure de la base de données"""
    print("🗄️ Test de la structure de la base de données...")
    
    try:
        import asyncpg
        
        async def check_db():
            conn = await asyncpg.connect(
                '**************************************************************************************************/postgres',
                statement_cache_size=0
            )
            
            # Vérifier les nouvelles tables
            new_tables = ['workspaces', 'workspace_settings', 'user_workspaces', 'workspace_role_permissions']
            for table in new_tables:
                result = await conn.fetchval(f"""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_name = '{table}'
                    )
                """)
                if result:
                    count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                    print(f"   ✅ Table {table}: {count} enregistrements")
                else:
                    print(f"   ❌ Table {table}: MANQUANTE")
                    return False
            
            # Vérifier que les anciennes tables n'existent plus
            old_tables = ['companies', 'company_settings', 'user_companies']
            for table in old_tables:
                result = await conn.fetchval(f"""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_name = '{table}'
                    )
                """)
                if result:
                    print(f"   ❌ Ancienne table {table}: ENCORE PRÉSENTE")
                    return False
                else:
                    print(f"   ✅ Ancienne table {table}: Supprimée")
            
            # Vérifier les tables préservées
            preserved_tables = ['technical_document_companies', 'project_company']
            for table in preserved_tables:
                result = await conn.fetchval(f"""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_name = '{table}'
                    )
                """)
                if result:
                    count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                    print(f"   ✅ Table préservée {table}: {count} enregistrements")
                else:
                    print(f"   ⚠️ Table préservée {table}: Non trouvée")
            
            await conn.close()
            return True
        
        return asyncio.run(check_db())
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_backend_models():
    """Tester les modèles backend"""
    print("\n🏗️ Test des modèles backend...")
    
    try:
        # Test des imports
        from app.models.workspace import Workspace, UserWorkspace, WorkspaceSettings
        from app.models.rbac import WorkspaceRolePermission
        from app.models.audit import WorkspaceInvitation
        print("   ✅ Imports des nouveaux modèles")
        
        # Test des alias de compatibilité
        from app.models.workspace import Company, UserCompany, CompanySettings
        from app.models.rbac import CompanyRolePermission
        from app.models.audit import CompanyInvitation
        print("   ✅ Imports des alias de compatibilité")
        
        # Test des noms de tables
        expected_tables = {
            Workspace: "workspaces",
            UserWorkspace: "user_workspaces",
            WorkspaceSettings: "workspace_settings",
            WorkspaceRolePermission: "workspace_role_permissions",
            WorkspaceInvitation: "workspace_invitations"
        }
        
        for model, expected_table in expected_tables.items():
            if model.__tablename__ == expected_table:
                print(f"   ✅ {model.__name__}: {expected_table}")
            else:
                print(f"   ❌ {model.__name__}: attendu '{expected_table}', trouvé '{model.__tablename__}'")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_backend_schemas():
    """Tester les schémas Pydantic"""
    print("\n📋 Test des schémas Pydantic...")
    
    try:
        # Test des nouveaux schémas
        from app.schemas.workspace import (
            Workspace, WorkspaceCreate, WorkspaceUpdate,
            UserWorkspace, WorkspaceSettings, WorkspaceInvitation
        )
        print("   ✅ Imports des nouveaux schémas")
        
        # Test des alias de compatibilité
        from app.schemas.workspace import (
            Company, CompanyCreate, CompanyUpdate,
            UserCompany, CompanySettings, CompanyInvitation
        )
        print("   ✅ Imports des alias de compatibilité")
        
        # Test de création d'instances
        workspace_data = {
            "name": "Test Workspace",
            "code": "TEST_WS"
        }
        workspace_create = WorkspaceCreate(**workspace_data)
        print(f"   ✅ Création WorkspaceCreate: {workspace_create.name}")
        
        # Test avec alias
        company_create = CompanyCreate(**workspace_data)
        print(f"   ✅ Création CompanyCreate (alias): {company_create.name}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_backend_endpoints():
    """Tester les endpoints API"""
    print("\n🌐 Test des endpoints API...")
    
    try:
        # Test des imports d'endpoints
        from app.api.api_v1.endpoints import workspaces, admin_workspaces
        print("   ✅ Imports des nouveaux endpoints")
        
        # Test des endpoints de compatibilité
        from app.api.api_v1.endpoints import companies, admin_companies
        print("   ✅ Imports des endpoints de compatibilité")
        
        # Test du router principal
        from app.api.api_v1.api import api_router
        
        # Vérifier les routes
        all_routes = [route.path for route in api_router.routes if hasattr(route, 'path')]
        
        workspace_routes = [route for route in all_routes if '/workspaces' in route]
        admin_workspace_routes = [route for route in all_routes if '/admin/workspaces' in route]
        
        if len(workspace_routes) > 0:
            print(f"   ✅ Routes /workspaces: {len(workspace_routes)} trouvées")
        else:
            print("   ❌ Routes /workspaces: Aucune trouvée")
            return False
        
        if len(admin_workspace_routes) > 0:
            print(f"   ✅ Routes /admin/workspaces: {len(admin_workspace_routes)} trouvées")
        else:
            print("   ❌ Routes /admin/workspaces: Aucune trouvée")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_frontend_files():
    """Tester les fichiers frontend"""
    print("\n🎨 Test des fichiers frontend...")
    
    try:
        frontend_files = [
            "orbis-admin/src/app/workspaces/page.tsx",
            "orbis-admin/src/app/workspaces/new/page.tsx",
            "orbis-admin/src/components/WorkspaceForm.tsx"
        ]
        
        for file_path in frontend_files:
            if os.path.exists(file_path):
                print(f"   ✅ {file_path}")
            else:
                print(f"   ❌ {file_path}: MANQUANT")
                return False
        
        # Vérifier le service
        service_file = "orbis-admin/src/lib/fast-auth.ts"
        if os.path.exists(service_file):
            with open(service_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'FastWorkspaceService' in content:
                    print("   ✅ FastWorkspaceService trouvé dans fast-auth.ts")
                else:
                    print("   ❌ FastWorkspaceService manquant dans fast-auth.ts")
                    return False
        else:
            print("   ❌ fast-auth.ts: MANQUANT")
            return False
        
        # Vérifier la navigation
        sidebar_file = "orbis-admin/src/components/layout/Sidebar.tsx"
        if os.path.exists(sidebar_file):
            with open(sidebar_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'Espaces de travail' in content and '/workspaces' in content:
                    print("   ✅ Navigation mise à jour dans Sidebar.tsx")
                else:
                    print("   ❌ Navigation non mise à jour dans Sidebar.tsx")
                    return False
        else:
            print("   ❌ Sidebar.tsx: MANQUANT")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_compatibility():
    """Tester la compatibilité avec les anciens noms"""
    print("\n🔄 Test de compatibilité...")
    
    try:
        # Test que les alias fonctionnent
        from app.models.workspace import Company, UserCompany
        from app.schemas.workspace import CompanyCreate, CompanyUpdate
        
        # Vérifier que les alias pointent vers les bonnes classes
        from app.models.workspace import Workspace, UserWorkspace
        from app.schemas.workspace import WorkspaceCreate, WorkspaceUpdate
        
        if Company is Workspace:
            print("   ✅ Alias Company → Workspace")
        else:
            print("   ❌ Alias Company incorrect")
            return False
        
        if UserCompany is UserWorkspace:
            print("   ✅ Alias UserCompany → UserWorkspace")
        else:
            print("   ❌ Alias UserCompany incorrect")
            return False
        
        if CompanyCreate is WorkspaceCreate:
            print("   ✅ Alias CompanyCreate → WorkspaceCreate")
        else:
            print("   ❌ Alias CompanyCreate incorrect")
            return False
        
        # Test que les anciens fichiers existent encore
        old_files = [
            "orbis-admin/src/app/companies/page.tsx",
            "orbis-admin/src/components/CompanyForm.tsx"
        ]
        
        for file_path in old_files:
            if os.path.exists(file_path):
                print(f"   ✅ Fichier de compatibilité: {file_path}")
            else:
                print(f"   ❌ Fichier de compatibilité manquant: {file_path}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_data_integrity():
    """Tester l'intégrité des données"""
    print("\n🔍 Test d'intégrité des données...")
    
    try:
        import asyncpg
        
        async def check_data():
            conn = await asyncpg.connect(
                '**************************************************************************************************/postgres',
                statement_cache_size=0
            )
            
            # Vérifier que les données ont été migrées
            workspace_count = await conn.fetchval("SELECT COUNT(*) FROM workspaces")
            user_workspace_count = await conn.fetchval("SELECT COUNT(*) FROM user_workspaces")
            
            print(f"   ✅ Workspaces: {workspace_count} enregistrements")
            print(f"   ✅ User-Workspaces: {user_workspace_count} relations")
            
            if workspace_count > 0:
                # Test d'une requête simple
                sample = await conn.fetch("SELECT id, name, code FROM workspaces LIMIT 3")
                print("   ✅ Exemples de workspaces:")
                for ws in sample:
                    print(f"     • ID: {ws['id']}, Nom: {ws['name']}, Code: {ws['code']}")
            
            # Vérifier les contraintes FK
            fk_check = await conn.fetchval("""
                SELECT COUNT(*) FROM user_workspaces uw
                LEFT JOIN workspaces w ON uw.workspace_id = w.id
                WHERE w.id IS NULL
            """)
            
            if fk_check == 0:
                print("   ✅ Contraintes FK respectées")
            else:
                print(f"   ❌ {fk_check} relations orphelines trouvées")
                return False
            
            await conn.close()
            return True
        
        return asyncio.run(check_data())
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 TEST COMPLET DE LA MIGRATION COMPANIES → WORKSPACES")
    print("="*60)
    
    tests = [
        ("Structure de la base de données", test_database_structure),
        ("Modèles backend", test_backend_models),
        ("Schémas Pydantic", test_backend_schemas),
        ("Endpoints API", test_backend_endpoints),
        ("Fichiers frontend", test_frontend_files),
        ("Compatibilité", test_compatibility),
        ("Intégrité des données", test_data_integrity)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 {test_name}...")
            result = test_func()
            results.append(result)
            if result:
                print(f"✅ {test_name}: RÉUSSI")
            else:
                print(f"❌ {test_name}: ÉCHOUÉ")
        except Exception as e:
            print(f"💥 {test_name}: ERREUR - {e}")
            results.append(False)
    
    # Résumé final
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 RÉSUMÉ FINAL: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 MIGRATION COMPLÈTE RÉUSSIE !")
        print("✅ Tous les composants fonctionnent correctement")
        print("✅ La migration companies → workspaces est terminée")
        print("✅ La compatibilité est préservée")
        return True
    else:
        print(f"\n❌ MIGRATION INCOMPLÈTE")
        print(f"❌ {total - passed} test(s) ont échoué")
        print("❌ Vérifiez les erreurs ci-dessus")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
