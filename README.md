# ORBIS Suivi Travaux - SAAS Application

## Description

ORBIS Suivi Travaux is a comprehensive SAAS application for construction project management, designed for EITP (Entreprise Individuelle du Bâtiment et des Travaux Publics). This application provides complete project tracking, employee management, document handling, and time tracking capabilities.

## Features

### Core Functionalities
- **Project Management**: Create, track, and manage construction projects
- **Employee Management**: Manage employees, assignments, and time tracking
- **Document Management**: Upload, organize, and version control project documents
- **User Authentication**: Secure JWT-based authentication system
- **Company Management**: Multi-tenant architecture for different companies
- **Time Tracking**: Track employee hours and project time allocation

### Technical Features
- **RESTful API**: FastAPI backend with automatic OpenAPI documentation
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Authentication**: JWT tokens with role-based access control
- **File Upload**: Secure document upload and management
- **Data Validation**: Pydantic models for data validation
- **Database Migrations**: Alembic for database schema management

## Technology Stack

### Backend
- **FastAPI** - Modern, fast web framework for building APIs
- **SQLAlchemy** - SQL toolkit and ORM
- **PostgreSQL** - Advanced open source relational database
- **Alembic** - Database migration tool
- **Pydantic** - Data validation using Python type annotations
- **JWT** - JSON Web Tokens for authentication
- **Uvicorn** - ASGI server implementation

### Frontend (Planned)
- **Next.js** - React framework for production
- **TypeScript** - Typed superset of JavaScript
- **Tailwind CSS** - Utility-first CSS framework

## Project Structure

```
orbis-suivi-travaux/
├── fastapi_template/          # Backend FastAPI application
│   ├── app/
│   │   ├── api/              # API routes
│   │   │   └── api_v1/       # API version 1
│   │   │       └── endpoints/ # Individual endpoint files
│   │   ├── core/             # Core functionality
│   │   │   ├── config.py     # Application configuration
│   │   │   ├── database.py   # Database configuration
│   │   │   └── security.py   # Security utilities
│   │   ├── models/           # SQLAlchemy models
│   │   ├── schemas/          # Pydantic schemas
│   │   └── main.py           # FastAPI application entry point
│   ├── alembic/              # Database migrations
│   └── requirements.txt      # Python dependencies
├── docs/                     # Documentation
│   ├── system_design.json    # System architecture
│   ├── prd.json             # Product requirements
│   └── *.mermaid            # Diagrams
└── README.md                # This file
```

## Installation

### Prerequisites
- Python 3.8+
- PostgreSQL 12+
- Git

### Backend Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd orbis-suivi-travaux
   ```

2. **Navigate to backend directory**
   ```bash
   cd fastapi_template
   ```

3. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

4. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

5. **Set up environment variables**
   Create a `.env` file in the `fastapi_template` directory:
   ```env
   DATABASE_URL=postgresql://username:password@localhost/orbis_db
   SECRET_KEY=your-secret-key-here
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   ```

6. **Set up PostgreSQL database**
   ```sql
   CREATE DATABASE orbis_db;
   CREATE USER orbis_user WITH ENCRYPTED PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE orbis_db TO orbis_user;
   ```

7. **Run database migrations**
   ```bash
   alembic upgrade head
   ```

8. **Start the development server**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

## API Documentation

Once the server is running, you can access:
- **Interactive API docs (Swagger UI)**: http://localhost:8000/docs
- **Alternative API docs (ReDoc)**: http://localhost:8000/redoc
- **OpenAPI schema**: http://localhost:8000/openapi.json

## Database Schema

The application includes the following main entities:
- **Users**: System users with authentication
- **Companies**: Multi-tenant support
- **Projects**: Construction projects
- **Employees**: Company employees
- **Documents**: Project and company documents
- **Time Entries**: Employee time tracking

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/refresh` - Refresh JWT token

### Users
- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/me` - Update current user profile

### Companies
- `GET /api/v1/companies/` - List companies
- `POST /api/v1/companies/` - Create company
- `GET /api/v1/companies/{id}` - Get company details
- `PUT /api/v1/companies/{id}` - Update company

### Projects
- `GET /api/v1/projects/` - List projects
- `POST /api/v1/projects/` - Create project
- `GET /api/v1/projects/{id}` - Get project details
- `PUT /api/v1/projects/{id}` - Update project

### Employees
- `GET /api/v1/employees/` - List employees
- `POST /api/v1/employees/` - Create employee
- `GET /api/v1/employees/{id}` - Get employee details
- `PUT /api/v1/employees/{id}` - Update employee

### Documents
- `GET /api/v1/documents/` - List documents
- `POST /api/v1/documents/upload` - Upload document
- `GET /api/v1/documents/{id}` - Get document details
- `DELETE /api/v1/documents/{id}` - Delete document

## Testing

```bash
# Run tests
pytest

# Run tests with coverage
pytest --cov=app
```

## Deployment

### Using Docker

1. **Build the image**
   ```bash
   docker build -t orbis-suivi-travaux .
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

### Using Cloud Platforms

The application can be deployed on:
- **Heroku**
- **AWS ECS/Fargate**
- **Google Cloud Run**
- **Azure Container Instances**

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Development Guidelines

- Follow PEP 8 style guide for Python code
- Write comprehensive tests for new features
- Update documentation when adding new features
- Use meaningful commit messages
- Ensure all tests pass before submitting PR

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions, please open an issue in the GitHub repository.

## Roadmap

- [ ] Frontend implementation with Next.js
- [ ] Real-time notifications
- [ ] Mobile application
- [ ] Advanced reporting and analytics
- [ ] Integration with accounting software
- [ ] Multi-language support

## Architecture

The application follows a clean architecture pattern:
- **API Layer**: FastAPI routes and dependencies
- **Business Logic**: Service classes and business rules
- **Data Access**: SQLAlchemy models and database operations
- **Security**: JWT authentication and authorization

## Performance

- Database connection pooling
- Async/await support for I/O operations
- Efficient database queries with proper indexing
- Response caching where appropriate

---

**Built with ❤️ for construction project management**