# app/services/rbac_service.py
"""
Service pour la gestion du système RBAC (Role-Based Access Control)
"""
from typing import List, Dict, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, delete
from sqlalchemy.orm import selectinload

from app.models.user import User, UserRole
from app.models.workspace import Workspace, UserWorkspace, WorkspaceRole
from app.models.rbac import Role, Permission, WorkspaceRolePermission
from app.schemas.rbac import (
    RolePermissionsConfig, UserPermissions, PermissionCheck,
    BulkPermissionUpdate, PermissionMatrix
)

class RBACService:
    """Service pour gérer les rôles et permissions"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_user_permissions(self, user_id: int, workspace_id: int) -> List[str]:
        """Récupère toutes les permissions d'un utilisateur dans une entreprise"""
        # Récupérer le rôle de l'utilisateur dans l'entreprise
        user_workspace = await self.db.execute(
            select(UserWorkspace).where(
                and_(
                    UserWorkspace.user_id == user_id,
                    UserWorkspace.workspace_id == workspace_id,
                    UserWorkspace.is_active == True
                )
            )
        )
        user_workspace = user_workspace.scalar_one_or_none()
        
        if not user_workspace:
            return []
        
        # Récupérer les permissions pour ce rôle dans cette entreprise
        permissions = await self.db.execute(
            select(Permission.name).join(
                WorkspaceRolePermission,
                WorkspaceRolePermission.permission_id == Permission.id
            ).where(
                and_(
                    WorkspaceRolePermission.workspace_id == workspace_id,
                    WorkspaceRolePermission.role_name == user_workspace.role_name
                )
            )
        )
        
        return [perm[0] for perm in permissions.all()]
    
    async def check_permission(self, user_id: int, workspace_id: int, permission: str) -> bool:
        """Vérifie si un utilisateur a une permission spécifique"""
        permissions = await self.get_user_permissions(user_id, workspace_id)
        return permission in permissions
    
    async def get_role_permissions(self, workspace_id: int, role_name: str) -> List[str]:
        """Récupère toutes les permissions d'un rôle dans une entreprise"""
        permissions = await self.db.execute(
            select(Permission.name).join(
                WorkspaceRolePermission,
                WorkspaceRolePermission.permission_id == Permission.id
            ).where(
                and_(
                    WorkspaceRolePermission.workspace_id == workspace_id,
                    WorkspaceRolePermission.role_name == role_name
                )
            )
        )
        
        return [perm[0] for perm in permissions.all()]
    
    async def set_role_permissions(self, workspace_id: int, role_name: str, permissions: List[str]) -> bool:
        """Définit les permissions d'un rôle dans une entreprise"""
        try:
            print(f"🔄 Sauvegarde permissions - workspace_id: {workspace_id}, role: {role_name}, permissions: {permissions}")

            # Supprimer les permissions existantes pour ce rôle
            delete_result = await self.db.execute(
                delete(WorkspaceRolePermission).where(
                    and_(
                        WorkspaceRolePermission.workspace_id == workspace_id,
                        WorkspaceRolePermission.role_name == role_name
                    )
                )
            )
            print(f"🗑️ Permissions supprimées: {delete_result.rowcount}")

            # Ajouter les nouvelles permissions
            added_count = 0
            for permission_name in permissions:
                # Récupérer l'ID de la permission
                permission = await self.db.execute(
                    select(Permission).where(Permission.name == permission_name)
                )
                permission = permission.scalar_one_or_none()

                if permission:
                    # Créer la liaison
                    company_role_permission = WorkspaceRolePermission(
                        workspace_id=workspace_id,
                        role_name=role_name,
                        permission_id=permission.id
                    )
                    self.db.add(company_role_permission)
                    added_count += 1
                    print(f"✅ Permission ajoutée: {permission_name} (ID: {permission.id})")
                else:
                    print(f"❌ Permission non trouvée: {permission_name}")

            await self.db.commit()
            print(f"💾 Commit réussi - {added_count} permissions sauvegardées")
            return True

        except Exception as e:
            print(f"❌ Erreur sauvegarde permissions: {e}")
            await self.db.rollback()
            raise e
    
    async def add_permissions_to_role(self, workspace_id: int, role_name: str, permissions: List[str]) -> bool:
        """Ajoute des permissions à un rôle dans une entreprise"""
        try:
            for permission_name in permissions:
                # Vérifier si la permission existe déjà
                existing = await self.db.execute(
                    select(WorkspaceRolePermission).join(
                        Permission,
                        WorkspaceRolePermission.permission_id == Permission.id
                    ).where(
                        and_(
                            WorkspaceRolePermission.workspace_id == workspace_id,
                            WorkspaceRolePermission.role_name == role_name,
                            Permission.name == permission_name
                        )
                    )
                )
                
                if existing.scalar_one_or_none():
                    continue  # Permission déjà accordée
                
                # Récupérer l'ID de la permission
                permission = await self.db.execute(
                    select(Permission).where(Permission.name == permission_name)
                )
                permission = permission.scalar_one_or_none()
                
                if permission:
                    # Créer la liaison
                    company_role_permission = WorkspaceRolePermission(
                        workspace_id=workspace_id,
                        role_name=role_name,
                        permission_id=permission.id
                    )
                    self.db.add(company_role_permission)
            
            await self.db.commit()
            return True
            
        except Exception as e:
            await self.db.rollback()
            raise e
    
    async def remove_permissions_from_role(self, workspace_id: int, role_name: str, permissions: List[str]) -> bool:
        """Supprime des permissions d'un rôle dans une entreprise"""
        try:
            for permission_name in permissions:
                # Supprimer la liaison
                await self.db.execute(
                    delete(WorkspaceRolePermission).where(
                        and_(
                            WorkspaceRolePermission.workspace_id == workspace_id,
                            WorkspaceRolePermission.role_name == role_name,
                            WorkspaceRolePermission.permission_id.in_(
                                select(Permission.id).where(Permission.name == permission_name)
                            )
                        )
                    )
                )
            
            await self.db.commit()
            return True
            
        except Exception as e:
            await self.db.rollback()
            raise e
    
    async def get_company_permission_matrix(self, workspace_id: int) -> Dict[str, Any]:
        """Récupère la matrice des permissions pour une entreprise"""
        # Récupérer tous les rôles qui ont des permissions configurées dans ce workspace
        roles_with_perms_result = await self.db.execute(
            select(WorkspaceRolePermission.role_name).where(
                WorkspaceRolePermission.workspace_id == workspace_id
            ).distinct()
        )
        roles_with_perms = [role[0] for role in roles_with_perms_result.all()]

        # Récupérer aussi les rôles des utilisateurs actifs
        user_roles_result = await self.db.execute(
            select(UserWorkspace.role_name).where(
                UserWorkspace.workspace_id == workspace_id
            ).distinct()
        )
        user_roles = [role[0] for role in user_roles_result.all()]

        # Combiner les deux listes et supprimer les doublons
        all_roles = list(set(roles_with_perms + user_roles))

        # Si aucun rôle trouvé, utiliser les rôles par défaut de l'enum
        if not all_roles:
            from app.models.workspace import WorkspaceRole
            all_roles = [role.value for role in WorkspaceRole]

        print(f"🔍 Rôles trouvés pour workspace {workspace_id}: {all_roles}")
        roles = all_roles
        
        # Récupérer toutes les permissions
        permissions_result = await self.db.execute(select(Permission.name))
        permissions = [perm[0] for perm in permissions_result.all()]
        
        # Construire la matrice
        matrix = {}
        for role in roles:
            role_permissions = await self.get_role_permissions(workspace_id, role)
            matrix[role] = role_permissions
        
        return {
            "roles": roles,
            "permissions": permissions,
            "matrix": matrix
        }
    
    async def assign_user_role(self, user_id: int, workspace_id: int, role_name: str) -> bool:
        """Assigne un rôle à un utilisateur dans une entreprise"""
        try:
            # Mettre à jour le rôle de l'utilisateur
            user_workspace = await self.db.execute(
                select(UserWorkspace).where(
                    and_(
                        UserWorkspace.user_id == user_id,
                        UserWorkspace.workspace_id == workspace_id
                    )
                )
            )
            user_workspace = user_workspace.scalar_one_or_none()
            
            if user_workspace:
                user_workspace.role_name = role_name
                await self.db.commit()
                return True
            
            return False
            
        except Exception as e:
            await self.db.rollback()
            raise e
    
    async def get_available_roles(self) -> List[str]:
        """Récupère la liste de tous les rôles disponibles depuis la base de données"""
        try:
            # Récupérer les rôles depuis la table roles
            result = await self.db.execute(select(Role.name))
            roles = [row[0] for row in result.all()]

            # Si aucun rôle en base, utiliser l'enum par défaut
            if not roles:
                return [role.value for role in WorkspaceRole]

            return roles
        except Exception as e:
            print(f"❌ Erreur récupération rôles: {e}")
            # Fallback sur l'enum
            return [role.value for role in WorkspaceRole]
    
    async def get_all_permissions(self) -> List[Dict[str, str]]:
        """Récupère toutes les permissions disponibles"""
        permissions = await self.db.execute(select(Permission))
        return [
            {
                "name": perm.name,
                "resource": perm.resource,
                "action": perm.action,
                "description": perm.description
            }
            for perm in permissions.scalars().all()
        ]
