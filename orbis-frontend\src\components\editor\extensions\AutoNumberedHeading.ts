import { Extension } from '@tiptap/core'
import { Plugin, Plugin<PERSON>ey } from 'prosemirror-state'
import { Selection } from 'prosemirror-state'

// Déclaration des types pour les commandes personnalisées
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    autoNumberedHeading: {
      renumberHeadings: () => ReturnType
      addNumberedHeading: (level: number, title?: string) => ReturnType
    }
  }
}

export const AutoNumberedHeading = Extension.create({
  name: 'autoNumberedHeading',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('autoNumberedHeading'),
        
        props: {
          handleKeyDown: (view, event) => {
            if (event.key === 'Enter') {
              const { state } = view
              const { selection } = state
              const { $from } = selection
              
              // Vérifier si on est dans un heading
              const currentNode = $from.node()
              if (currentNode.type.name === 'heading') {
                const currentText = currentNode.textContent
                const numberMatch = currentText.match(/^(\d+(?:\.\d+)*)\s+(.*)$/)
                
                if (numberMatch) {
                  const currentNumber = numberMatch[1]
                  const title = numberMatch[2]
                  const level = currentNode.attrs.level
                  
                  // Calculer le prochain numéro
                  const parts = currentNumber.split('.')
                  parts[parts.length - 1] = String(parseInt(parts[parts.length - 1]) + 1)
                  const nextNumber = parts.join('.')
                  
                  // Insérer le nouveau heading
                  const tr = state.tr
                  const pos = $from.after()
                  
                  tr.insert(pos, state.schema.nodes.heading.create(
                    { level },
                    state.schema.text(nextNumber + ' ')
                  ))
                  
                  // Positionner le curseur après le numéro
                  const newPos = pos + nextNumber.length + 2
                  tr.setSelection(Selection.near(tr.doc.resolve(newPos)))
                  
                  view.dispatch(tr)
                  return true
                }
              }
            }
            return false
          }
        }
      })
    ]
  },

  addCommands() {
    return {
      // Commande pour recalculer tous les numéros
      renumberHeadings: () => ({ tr, state, dispatch }) => {
        const headings: Array<{
          node: any
          pos: number
          level: number
          title: string
        }> = []
        
        state.doc.descendants((node: any, pos: number) => {
          if (node.type.name === 'heading') {
            const level = node.attrs.level
            const text = node.textContent
            const match = text.match(/^(\d+(?:\.\d+)*)\s+(.*)$/)
            
            if (match) {
              headings.push({ node, pos, level, title: match[2] })
            } else {
              // Si pas de numéro, ajouter le titre tel quel
              headings.push({ node, pos, level, title: text })
            }
          }
        })
        
        // Recalculer les numéros
        const counters = Array(6).fill(0)

        headings.forEach(({ node, pos, level, title }, index) => {
          // Calculer le nouveau numéro
          counters[level - 1]++

          // Remettre à zéro les niveaux inférieurs
          for (let j = level; j < 6; j++) {
            counters[j] = 0
          }

          // Construire le numéro
          const numberParts = []
          for (let i = 0; i < level; i++) {
            if (counters[i] > 0) {
              numberParts.push(counters[i])
            }
          }

          const newNumber = numberParts.join('.')
          const newText = `${newNumber} ${title}`

          tr.replaceWith(
            pos,
            pos + node.nodeSize,
            state.schema.nodes.heading.create(
              { level },
              state.schema.text(newText)
            )
          )
        })

        if (dispatch) {
          dispatch(tr)
        }
        return true
      },

      // Commande pour ajouter un heading numéroté
      addNumberedHeading: (level: number, title: string = '') => ({ tr, state, dispatch }) => {
        const headings: Array<{
          level: number
          title: string
        }> = []
        
        // Collecter tous les headings existants
        state.doc.descendants((node: any) => {
          if (node.type.name === 'heading') {
            const text = node.textContent
            const match = text.match(/^(\d+(?:\.\d+)*)\s+(.*)$/)
            
            if (match) {
              headings.push({ level: node.attrs.level, title: match[2] })
            }
          }
        })
        
        // Calculer le nouveau numéro
        const counters = Array(6).fill(0)

        // Compter les headings existants
        headings.forEach(heading => {
          const headingLevel = heading.level
          counters[headingLevel - 1]++

          // Remettre à zéro les niveaux inférieurs
          for (let j = headingLevel; j < 6; j++) {
            counters[j] = 0
          }
        })

        // Incrémenter le niveau demandé
        counters[level - 1]++

        // Construire le numéro
        const numberParts = []
        for (let i = 0; i < level; i++) {
          if (counters[i] > 0) {
            numberParts.push(counters[i])
          }
        }

        const newNumber = numberParts.join('.')
        const newText = title ? `${newNumber} ${title}` : `${newNumber} `
        
        // Insérer le nouveau heading à la position du curseur
        const { from } = state.selection
        tr.insert(from, state.schema.nodes.heading.create(
          { level },
          state.schema.text(newText)
        ))

        if (dispatch) {
          dispatch(tr)
        }
        return true
      }
    }
  },


})

// Styles CSS pour les headings numérotés
export const headingStyles = `
  .ProseMirror h1,
  .ProseMirror h2,
  .ProseMirror h3,
  .ProseMirror h4,
  .ProseMirror h5,
  .ProseMirror h6 {
    font-weight: bold;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    line-height: 1.3;
  }
  
  .ProseMirror h1 { 
    font-size: 2em; 
    color: #0F766E;
    border-bottom: 2px solid #0F766E;
    padding-bottom: 0.3em;
  }
  .ProseMirror h2 { 
    font-size: 1.75em; 
    color: #0F766E;
  }
  .ProseMirror h3 { 
    font-size: 1.5em; 
    color: #333333;
  }
  .ProseMirror h4 { 
    font-size: 1.25em; 
    color: #333333;
  }
  .ProseMirror h5 { 
    font-size: 1.1em; 
    color: #333333;
  }
  .ProseMirror h6 { 
    font-size: 1em; 
    color: #333333;
    font-weight: 600;
  }
  
  /* Style pour les numéros de section */
  .ProseMirror h1::before,
  .ProseMirror h2::before,
  .ProseMirror h3::before,
  .ProseMirror h4::before,
  .ProseMirror h5::before,
  .ProseMirror h6::before {
    color: #0F766E;
    font-weight: bold;
    margin-right: 0.5em;
  }
  
  /* Espacement pour les documents CCTP */
  .ProseMirror .cctp-section {
    margin-bottom: 2em;
  }
  
  .ProseMirror .cctp-article {
    margin-bottom: 1.5em;
    padding-left: 1em;
  }
`
