#!/usr/bin/env python3
"""
Test des endpoints API pour le dashboard
"""

import requests
import json
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:8000"
API_V1_URL = f"{BACKEND_URL}/api/v1"


def test_dashboard_endpoints():
    """Test des endpoints du dashboard"""
    print("🧪 TEST DES ENDPOINTS API DASHBOARD")
    print("="*60)
    
    # Créer un utilisateur de test et se connecter
    user_data = {
        "email": f"testapi{datetime.now().strftime('%H%M%S')}@gmail.com",
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "API",
        "company": "Test Company API",
        "phone": "0123456789"
    }
    
    print(f"📧 Email de test: {user_data['email']}")
    
    # 1. Inscription
    print("\n1️⃣ Test d'inscription...")
    try:
        response = requests.post(f"{API_V1_URL}/auth/register", json=user_data, timeout=10)
        if response.status_code == 200:
            print("   ✅ Inscription réussie")
        else:
            print(f"   ❌ Inscription échouée: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erreur inscription: {e}")
        return False
    
    # 2. Connexion
    print("\n2️⃣ Test de connexion...")
    login_data = {"email": user_data["email"], "password": user_data["password"]}
    
    try:
        response = requests.post(f"{API_V1_URL}/auth/login", json=login_data, timeout=10)
        if response.status_code == 200:
            auth_data = response.json()
            token = auth_data.get("access_token")
            print("   ✅ Connexion réussie")
            print(f"   🎫 Token reçu: {token[:20] if token else 'N/A'}...")
        else:
            print(f"   ❌ Connexion échouée: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erreur connexion: {e}")
        return False
    
    # Headers avec authentification
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 3. Test endpoint projets
    print("\n3️⃣ Test endpoint /projects...")
    try:
        response = requests.get(f"{API_V1_URL}/projects", headers=headers, timeout=10)
        print(f"   📊 Status: {response.status_code}")
        if response.status_code == 200:
            projects = response.json()
            print(f"   ✅ Projets récupérés: {len(projects)} projets")
            if projects:
                print(f"   📝 Premier projet: {projects[0].get('name', 'N/A')}")
        else:
            print(f"   ⚠️  Réponse: {response.text[:100]}...")
    except Exception as e:
        print(f"   ❌ Erreur projets: {e}")
    
    # 4. Test endpoint employés
    print("\n4️⃣ Test endpoint /employees...")
    try:
        response = requests.get(f"{API_V1_URL}/employees", headers=headers, timeout=10)
        print(f"   📊 Status: {response.status_code}")
        if response.status_code == 200:
            employees = response.json()
            print(f"   ✅ Employés récupérés: {len(employees)} employés")
            if employees:
                print(f"   👤 Premier employé: {employees[0].get('first_name', 'N/A')} {employees[0].get('last_name', 'N/A')}")
        else:
            print(f"   ⚠️  Réponse: {response.text[:100]}...")
    except Exception as e:
        print(f"   ❌ Erreur employés: {e}")
    
    # 5. Test endpoint statistiques dashboard
    print("\n5️⃣ Test endpoint /dashboard/stats...")
    try:
        response = requests.get(f"{API_V1_URL}/dashboard/stats", headers=headers, timeout=10)
        print(f"   📊 Status: {response.status_code}")
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ Statistiques récupérées")
            print(f"   📈 Projets totaux: {stats.get('totalProjects', 'N/A')}")
            print(f"   📈 Projets actifs: {stats.get('activeProjects', 'N/A')}")
            print(f"   👥 Employés: {stats.get('totalEmployees', 'N/A')}")
        else:
            print(f"   ⚠️  Réponse: {response.text[:100]}...")
    except Exception as e:
        print(f"   ❌ Erreur stats: {e}")
    
    # 6. Test de la documentation API
    print("\n6️⃣ Test documentation API...")
    try:
        response = requests.get(f"{BACKEND_URL}/docs", timeout=10)
        if response.status_code == 200:
            print(f"   ✅ Documentation accessible: {BACKEND_URL}/docs")
        else:
            print(f"   ⚠️  Documentation non accessible")
    except Exception as e:
        print(f"   ❌ Erreur documentation: {e}")
    
    return True


def print_summary():
    """Afficher le résumé"""
    print("\n" + "="*60)
    print("📋 RÉSUMÉ DES TESTS")
    print("="*60)
    
    print(f"\n🔧 Services testés :")
    print(f"   - Backend API: {BACKEND_URL}")
    print(f"   - Endpoints: /auth, /projects, /employees, /dashboard")
    print(f"   - Documentation: {BACKEND_URL}/docs")
    
    print(f"\n🎯 Prochaines étapes :")
    print(f"   1. Tester le dashboard frontend: http://localhost:3000/dashboard")
    print(f"   2. Vérifier que les données s'affichent correctement")
    print(f"   3. Créer quelques projets de test")
    print(f"   4. Ajouter des employés de test")
    
    print(f"\n🏆 ENDPOINTS API PRÊTS POUR LE DASHBOARD !")


def main():
    """Fonction principale"""
    success = test_dashboard_endpoints()
    
    print_summary()
    
    if success:
        print(f"\n✅ TOUS LES TESTS PASSÉS !")
        print(f"🎉 Le dashboard peut maintenant récupérer les données !")
    else:
        print(f"\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print(f"🔧 Vérifiez que le backend est démarré")
    
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
