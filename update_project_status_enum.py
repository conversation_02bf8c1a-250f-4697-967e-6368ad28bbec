#!/usr/bin/env python3
"""
Script pour mettre à jour l'enum ProjectStatus dans la base de données
"""

import asyncio
import asyncpg

DATABASE_URL = "**************************************************************************************************/postgres"

async def update_project_status_enum():
    """Mettre à jour l'enum ProjectStatus dans la base de données"""
    print("🚀 Mise à jour de l'enum ProjectStatus...")
    
    try:
        # Connexion à la base de données avec statement_cache_size=0 pour pgbouncer
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        print("✅ Connexion à la base de données réussie")
        
        # 1. Vérifier les valeurs actuelles de l'enum
        current_enum_values = await conn.fetch("""
            SELECT enumlabel 
            FROM pg_enum 
            WHERE enumtypid = (
                SELECT oid 
                FROM pg_type 
                WHERE typname = 'projectstatus'
            )
            ORDER BY enumsortorder
        """)
        
        print(f"\n📊 Valeurs actuelles de l'enum ProjectStatus:")
        for row in current_enum_values:
            print(f"   • {row['enumlabel']}")
        
        # 2. Ajouter les nouvelles valeurs à l'enum s'elles n'existent pas
        new_values = ['En cours', 'En attente', 'Terminé', 'Archivé']
        existing_values = [row['enumlabel'] for row in current_enum_values]
        
        for new_value in new_values:
            if new_value not in existing_values:
                try:
                    await conn.execute(f"ALTER TYPE projectstatus ADD VALUE '{new_value}'")
                    print(f"✅ Ajouté '{new_value}' à l'enum ProjectStatus")
                except Exception as e:
                    print(f"⚠️ Erreur lors de l'ajout de '{new_value}': {e}")
        
        # 3. Mettre à jour les projets existants avec les nouvelles valeurs
        status_mapping = {
            'DAO': 'En cours',
            'dao': 'En cours',
            'exe': 'En cours', 
            'completed': 'Terminé',
            'archived': 'Archivé'
        }
        
        total_updated = 0
        for old_status, new_status in status_mapping.items():
            try:
                # Vérifier combien de projets ont ce statut
                count = await conn.fetchval("""
                    SELECT COUNT(*) FROM projects WHERE status = $1
                """, old_status)
                
                if count > 0:
                    # Mettre à jour
                    await conn.execute("""
                        UPDATE projects 
                        SET status = $1
                        WHERE status = $2
                    """, new_status, old_status)
                    
                    print(f"✅ Mis à jour {count} projet(s) de '{old_status}' vers '{new_status}'")
                    total_updated += count
                    
            except Exception as e:
                print(f"⚠️ Erreur lors de la mise à jour {old_status} -> {new_status}: {e}")
        
        print(f"\n📈 Total mis à jour: {total_updated} projet(s)")
        
        # 4. Vérifier les nouveaux statuts
        new_statuses = await conn.fetch("""
            SELECT status, COUNT(*) as count
            FROM projects 
            GROUP BY status
            ORDER BY status
        """)
        
        print(f"\n📊 Nouveaux statuts:")
        for row in new_statuses:
            print(f"   • {row['status']}: {row['count']} projet(s)")
        
        # 5. Vérifier les nouvelles valeurs de l'enum
        updated_enum_values = await conn.fetch("""
            SELECT enumlabel 
            FROM pg_enum 
            WHERE enumtypid = (
                SELECT oid 
                FROM pg_type 
                WHERE typname = 'projectstatus'
            )
            ORDER BY enumsortorder
        """)
        
        print(f"\n📊 Valeurs finales de l'enum ProjectStatus:")
        for row in updated_enum_values:
            print(f"   • {row['enumlabel']}")
        
        await conn.close()
        print("\n🎉 Mise à jour de l'enum ProjectStatus terminée avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_updated_enum():
    """Tester l'enum mis à jour"""
    print("\n🧪 Test de l'enum mis à jour...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        # Test: récupérer les projets avec leurs nouveaux statuts
        projects = await conn.fetch("""
            SELECT name, code, status, nature
            FROM projects
            LIMIT 5
        """)
        
        print("✅ Test de lecture réussi:")
        for project in projects:
            print(f"   • {project['name']} ({project['code']}) → {project['status']} / {project['nature']}")
        
        # Test: essayer d'insérer un projet avec le nouveau statut
        try:
            await conn.execute("""
                INSERT INTO projects (name, code, status, nature, created_at, updated_at)
                VALUES ('Test Enum', 'TEST-ENUM-001', 'En cours', 'Devis', NOW(), NOW())
            """)
            print("✅ Test d'insertion avec nouveau statut réussi")
            
            # Supprimer le projet de test
            await conn.execute("DELETE FROM projects WHERE code = 'TEST-ENUM-001'")
            print("✅ Projet de test supprimé")
            
        except Exception as e:
            print(f"⚠️ Erreur lors du test d'insertion: {e}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Mise à jour de l'enum ProjectStatus")
    print("=" * 50)
    
    async def main():
        # Mettre à jour l'enum
        success = await update_project_status_enum()
        
        if success:
            # Tester l'enum mis à jour
            await test_updated_enum()
        
        return success
    
    result = asyncio.run(main())
    
    if result:
        print("\n✅ Enum ProjectStatus mis à jour avec succès!")
        print("🚀 Le CRUD des projets devrait maintenant fonctionner")
    else:
        print("\n❌ Échec de la mise à jour de l'enum")
