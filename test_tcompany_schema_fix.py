#!/usr/bin/env python3
"""
Test simple pour vérifier que le schéma TCompanyResponse fonctionne correctement
"""

import sys
import os

# Ajouter le chemin du projet
sys.path.append(os.path.join(os.path.dirname(__file__), 'fastapi_template'))

from datetime import datetime
from fastapi_template.app.schemas.tcompany import TCompanyResponse

def test_tcompany_response_schema():
    """Test du schéma TCompanyResponse avec des données simulées"""
    
    print("🔍 Test du schéma TCompanyResponse")
    
    # Données de test simulant un objet TCompany
    test_data = {
        "id": 1,
        "company_name": "Test Company",
        "activity": "Construction",
        "address": "123 Rue de Test",
        "postal_code": "75001",
        "city": "Paris",
        "country": "France",
        "phone": "0123456789",
        "email": "<EMAIL>",
        "siret": "12345678901234",
        "workspace_id": 1,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "created_by": 1,
        "is_active": True,
        # Simuler la méthode is_siret_valid comme une fonction callable
        "is_siret_valid": lambda: True
    }
    
    try:
        # Créer une instance du schéma
        company_response = TCompanyResponse(**test_data)
        print("✅ Schéma TCompanyResponse créé avec succès")
        print(f"📊 Company: {company_response.company_name}")
        print(f"📊 SIRET valid: {company_response.is_siret_valid}")
        print(f"📊 Full address: {company_response.full_address}")
        
        # Test de sérialisation
        company_dict = company_response.dict()
        print("✅ Sérialisation réussie")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création du schéma: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tcompany_response_with_method():
    """Test avec une méthode simulée (comme venant de SQLAlchemy)"""
    
    print("\n🔍 Test avec méthode simulée (SQLAlchemy)")
    
    class MockTCompany:
        def __init__(self):
            self.id = 2
            self.company_name = "Mock Company"
            self.activity = "Test"
            self.address = "456 Mock Street"
            self.postal_code = "69000"
            self.city = "Lyon"
            self.country = "France"
            self.phone = "0987654321"
            self.email = "<EMAIL>"
            self.siret = "98765432109876"
            self.workspace_id = 2
            self.created_at = datetime.now()
            self.updated_at = datetime.now()
            self.created_by = 2
            self.is_active = True
        
        def is_siret_valid(self):
            """Méthode simulée comme dans le modèle SQLAlchemy"""
            return len(self.siret) == 14 and self.siret.isdigit()
    
    try:
        mock_company = MockTCompany()
        
        # Convertir en dictionnaire en appelant la méthode
        data = {
            "id": mock_company.id,
            "company_name": mock_company.company_name,
            "activity": mock_company.activity,
            "address": mock_company.address,
            "postal_code": mock_company.postal_code,
            "city": mock_company.city,
            "country": mock_company.country,
            "phone": mock_company.phone,
            "email": mock_company.email,
            "siret": mock_company.siret,
            "workspace_id": mock_company.workspace_id,
            "created_at": mock_company.created_at,
            "updated_at": mock_company.updated_at,
            "created_by": mock_company.created_by,
            "is_active": mock_company.is_active,
            "is_siret_valid": mock_company.is_siret_valid  # Passer la méthode
        }
        
        company_response = TCompanyResponse(**data)
        print("✅ Schéma avec méthode créé avec succès")
        print(f"📊 Company: {company_response.company_name}")
        print(f"📊 SIRET valid: {company_response.is_siret_valid}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur avec méthode simulée: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Test des corrections du schéma TCompanyResponse\n")
    
    success1 = test_tcompany_response_schema()
    success2 = test_tcompany_response_with_method()
    
    if success1 and success2:
        print("\n✅ Tous les tests réussis - Le problème DetachedInstanceError devrait être résolu")
    else:
        print("\n❌ Certains tests ont échoué")
