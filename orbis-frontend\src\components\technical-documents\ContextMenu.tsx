'use client'

import React, { useEffect, useRef } from 'react'
import { DocumentType, PromptType, PROMPT_TYPES } from '@/types/technical-document'
import { SparklesIcon } from '@heroicons/react/24/outline'

interface ContextMenuProps {
  visible: boolean
  position: { x: number; y: number }
  selectedText: string
  documentType: DocumentType
  onAction: (promptType: PromptType) => void
  onClose: () => void
}

export default function ContextMenu({
  visible,
  position,
  selectedText,
  documentType,
  onAction,
  onClose
}: ContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null)

  // Fermer le menu en cliquant à l'extérieur
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [visible, onClose])

  // Ajuster la position du menu pour qu'il reste dans la fenêtre
  const getAdjustedPosition = () => {
    if (!menuRef.current) return position

    const menuRect = menuRef.current.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let { x, y } = position

    // Ajuster horizontalement
    if (x + menuRect.width > viewportWidth) {
      x = viewportWidth - menuRect.width - 10
    }

    // Ajuster verticalement
    if (y + menuRect.height > viewportHeight) {
      y = y - menuRect.height - 10
    }

    return { x, y }
  }

  const handleAction = (promptType: PromptType) => {
    onAction(promptType)
    onClose()
  }

  if (!visible || !selectedText.trim()) {
    return null
  }

  const adjustedPosition = getAdjustedPosition()

  return (
    <>
      {/* Overlay pour capturer les clics */}
      <div className="fixed inset-0 z-40" onClick={onClose} />
      
      {/* Menu contextuel */}
      <div
        ref={menuRef}
        className="fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-64"
        style={{
          left: adjustedPosition.x,
          top: adjustedPosition.y,
        }}
      >
        {/* En-tête avec texte sélectionné */}
        <div className="px-4 py-2 border-b border-gray-100">
          <div className="flex items-center space-x-2">
            <SparklesIcon className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium text-gray-900">
              Améliorer avec l'IA
            </span>
          </div>
          <div className="mt-1 text-xs text-gray-500 max-w-xs truncate">
            "{selectedText.length > 50 ? selectedText.substring(0, 50) + '...' : selectedText}"
          </div>
          <div className="mt-1">
            <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
              documentType === DocumentType.CCTP 
                ? 'bg-blue-100 text-blue-800' 
                : 'bg-green-100 text-green-800'
            }`}>
              {documentType}
            </span>
          </div>
        </div>

        {/* Actions d'amélioration */}
        <div className="py-1">
          {PROMPT_TYPES.map((promptType) => (
            <button
              key={promptType.key}
              onClick={() => handleAction(promptType.key)}
              className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors"
            >
              <div className="flex items-center space-x-3">
                <span className="text-lg">{promptType.icon}</span>
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {promptType.label}
                  </div>
                  <div className="text-xs text-gray-500">
                    {promptType.description}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Raccourci clavier */}
        <div className="px-4 py-2 border-t border-gray-100">
          <div className="text-xs text-gray-400">
            Raccourci : Ctrl+Shift+A
          </div>
        </div>
      </div>
    </>
  )
}

// Hook pour gérer le menu contextuel
export function useContextMenu() {
  const [contextMenu, setContextMenu] = React.useState<{
    visible: boolean
    position: { x: number; y: number }
    selectedText: string
  }>({
    visible: false,
    position: { x: 0, y: 0 },
    selectedText: ''
  })

  const showContextMenu = (event: MouseEvent, selectedText: string) => {
    event.preventDefault()
    
    if (!selectedText.trim()) {
      return
    }

    setContextMenu({
      visible: true,
      position: { x: event.clientX, y: event.clientY },
      selectedText: selectedText.trim()
    })
  }

  const hideContextMenu = () => {
    setContextMenu(prev => ({
      ...prev,
      visible: false
    }))
  }

  return {
    contextMenu,
    showContextMenu,
    hideContextMenu
  }
}
