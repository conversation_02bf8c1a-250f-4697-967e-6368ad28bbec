# app/schemas/workspace.py
from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List
from datetime import datetime
from app.models.workspace import WorkspaceRole

class WorkspaceBase(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    siret: Optional[str] = None
    is_active: Optional[bool] = True

class WorkspaceCreate(WorkspaceBase):
    name: str = Field(..., min_length=1, max_length=255, description="Nom de l'espace de travail")
    code: str = Field(..., min_length=1, max_length=50, description="Code unique de l'espace de travail")

class WorkspaceUpdate(WorkspaceBase):
    pass

class WorkspaceInDBBase(WorkspaceBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Workspace(WorkspaceInDBBase):
    pass

class WorkspaceWithStats(Workspace):
    """Espace de travail avec statistiques"""
    user_count: Optional[int] = 0
    project_count: Optional[int] = 0
    active_projects: Optional[int] = 0

# Schémas pour UserWorkspace
class UserWorkspaceBase(BaseModel):
    user_id: int
    workspace_id: int
    role_name: str = Field(..., max_length=50, description="Nom du rôle dans l'espace de travail")
    is_default: Optional[bool] = False
    is_active: Optional[bool] = True

class UserWorkspaceCreate(UserWorkspaceBase):
    invited_by: Optional[int] = None

class UserWorkspaceUpdate(BaseModel):
    role_name: Optional[str] = Field(None, max_length=50)
    is_default: Optional[bool] = None
    is_active: Optional[bool] = None

class UserWorkspaceInDBBase(UserWorkspaceBase):
    id: Optional[int] = None
    invited_at: Optional[datetime] = None
    joined_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class UserWorkspace(UserWorkspaceInDBBase):
    pass

class UserWorkspaceWithUser(UserWorkspace):
    """UserWorkspace avec les détails de l'utilisateur"""
    user: Optional[dict] = None  # Sera remplacé par UserBase si nécessaire

class UserWorkspaceWithWorkspace(UserWorkspace):
    """UserWorkspace avec les détails de l'espace de travail"""
    workspace: Optional[Workspace] = None

# Schémas pour WorkspaceSettings
class WorkspaceSettingsBase(BaseModel):
    workspace_id: int
    default_currency: Optional[str] = "EUR"
    date_format: Optional[str] = "DD/MM/YYYY"
    time_format: Optional[str] = "24h"
    language: Optional[str] = "fr"
    logo_url: Optional[str] = None

class WorkspaceSettingsCreate(WorkspaceSettingsBase):
    pass

class WorkspaceSettingsUpdate(BaseModel):
    default_currency: Optional[str] = None
    date_format: Optional[str] = None
    time_format: Optional[str] = None
    language: Optional[str] = None
    logo_url: Optional[str] = None

class WorkspaceSettingsInDBBase(WorkspaceSettingsBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class WorkspaceSettings(WorkspaceSettingsInDBBase):
    pass

# Schémas pour les invitations d'espace de travail
class WorkspaceInvitationBase(BaseModel):
    workspace_id: int
    email: EmailStr = Field(..., description="Email de la personne invitée")
    role: str = Field(..., max_length=50, description="Rôle à attribuer")

class WorkspaceInvitationCreate(WorkspaceInvitationBase):
    pass

class WorkspaceInvitationUpdate(BaseModel):
    role: Optional[str] = Field(None, max_length=50)
    is_active: Optional[bool] = None

class WorkspaceInvitationInDBBase(WorkspaceInvitationBase):
    id: Optional[int] = None
    invited_by: Optional[int] = None
    invitation_token: Optional[str] = None
    expires_at: Optional[datetime] = None
    accepted_at: Optional[datetime] = None
    rejected_at: Optional[datetime] = None
    is_active: Optional[bool] = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class WorkspaceInvitation(WorkspaceInvitationInDBBase):
    pass

class WorkspaceInvitationWithDetails(WorkspaceInvitation):
    """Invitation avec détails de l'espace de travail et de l'inviteur"""
    workspace: Optional[Workspace] = None
    inviter: Optional[dict] = None  # Sera remplacé par UserBase si nécessaire

# Schémas pour les rôles et permissions
class WorkspaceRoleAssignment(BaseModel):
    """Schéma pour assigner un rôle à un utilisateur dans un espace de travail"""
    user_id: int
    workspace_id: int
    role_name: str = Field(..., max_length=50)

class WorkspaceRolePermissionBase(BaseModel):
    workspace_id: int
    role_name: str = Field(..., max_length=50)
    permission_id: int

class WorkspaceRolePermissionCreate(WorkspaceRolePermissionBase):
    pass

class WorkspaceRolePermissionInDBBase(WorkspaceRolePermissionBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class WorkspaceRolePermission(WorkspaceRolePermissionInDBBase):
    pass

class WorkspaceRolePermissionWithDetails(WorkspaceRolePermission):
    """Permission avec détails de l'espace de travail et de la permission"""
    workspace: Optional[Workspace] = None
    permission: Optional[dict] = None  # Sera remplacé par PermissionBase si nécessaire

# Schémas de réponse complexes
class WorkspaceWithUsers(Workspace):
    """Espace de travail avec la liste des utilisateurs"""
    users: List[UserWorkspaceWithUser] = []

class WorkspaceWithSettings(Workspace):
    """Espace de travail avec ses paramètres"""
    settings: Optional[WorkspaceSettings] = None

class WorkspaceComplete(WorkspaceWithStats):
    """Espace de travail complet avec utilisateurs, paramètres et statistiques"""
    users: List[UserWorkspaceWithUser] = []
    settings: Optional[WorkspaceSettings] = None


