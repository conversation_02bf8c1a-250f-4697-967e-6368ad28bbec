#!/usr/bin/env python3
"""
Audit rapide des tables de la base de données ORBIS
"""

import asyncio
import asyncpg

# Configuration
DATABASE_URL = "**************************************************************************************************/postgres"

# Tables critiques attendues
CRITICAL_TABLES = [
    "companies", "users", "projects", "employees", "suppliers", 
    "materials", "budgets", "invoices", "user_companies"
]

# Tables optionnelles
OPTIONAL_TABLES = [
    "purchase_orders", "quotes", "documents", "project_materials", 
    "project_employees", "user_profiles"
]


async def quick_audit():
    """Audit rapide des tables"""
    print("🔍 AUDIT RAPIDE DES TABLES")
    print("="*50)
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Récupérer la liste des tables
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        
        existing_table_names = [table['table_name'] for table in tables]
        
        print(f"📊 {len(existing_table_names)} tables trouvées dans la base")
        
        # Vérifier les tables critiques
        print("\n🔴 TABLES CRITIQUES:")
        missing_critical = []
        for table in CRITICAL_TABLES:
            if table in existing_table_names:
                # Compter les enregistrements
                try:
                    count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                    print(f"   ✅ {table:<20} | {count:>6} enregistrements")
                except Exception as e:
                    print(f"   ⚠️  {table:<20} | Erreur: {str(e)[:30]}...")
            else:
                missing_critical.append(table)
                print(f"   ❌ {table:<20} | MANQUANTE")
        
        # Vérifier les tables optionnelles
        print("\n🟡 TABLES OPTIONNELLES:")
        missing_optional = []
        for table in OPTIONAL_TABLES:
            if table in existing_table_names:
                try:
                    count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                    print(f"   ✅ {table:<20} | {count:>6} enregistrements")
                except Exception as e:
                    print(f"   ⚠️  {table:<20} | Erreur: {str(e)[:30]}...")
            else:
                missing_optional.append(table)
                print(f"   ❌ {table:<20} | MANQUANTE")
        
        # Tables inconnues
        all_expected = CRITICAL_TABLES + OPTIONAL_TABLES
        unknown_tables = [t for t in existing_table_names if t not in all_expected]
        
        if unknown_tables:
            print("\n❓ TABLES INCONNUES:")
            for table in unknown_tables:
                try:
                    count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                    print(f"   🔍 {table:<20} | {count:>6} enregistrements")
                except:
                    print(f"   🔍 {table:<20} | Erreur lecture")
        
        # Vérifier la structure de la table users (importante pour l'auth)
        if "users" in existing_table_names:
            print("\n👤 STRUCTURE TABLE USERS:")
            columns = await conn.fetch("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'users' AND table_schema = 'public'
                ORDER BY ordinal_position
            """)
            
            required_user_columns = ["id", "email", "first_name", "last_name", "role", "supabase_user_id"]
            existing_columns = [col['column_name'] for col in columns]
            
            for col in required_user_columns:
                if col in existing_columns:
                    print(f"   ✅ {col}")
                else:
                    print(f"   ❌ {col} (MANQUANTE)")
        
        # Résumé et recommandations
        print("\n💡 RÉSUMÉ ET RECOMMANDATIONS:")
        print("-" * 50)
        
        if missing_critical:
            print(f"🚨 {len(missing_critical)} tables critiques manquantes:")
            for table in missing_critical:
                print(f"   - {table}")
            print("   → ACTION URGENTE: Créer ces tables")
        
        if missing_optional:
            print(f"⚠️  {len(missing_optional)} tables optionnelles manquantes:")
            for table in missing_optional[:3]:  # Afficher les 3 premières
                print(f"   - {table}")
            if len(missing_optional) > 3:
                print(f"   - ... et {len(missing_optional) - 3} autres")
            print("   → ACTION: Créer selon les besoins fonctionnels")
        
        if unknown_tables:
            print(f"🔍 {len(unknown_tables)} tables inconnues trouvées:")
            for table in unknown_tables[:3]:  # Afficher les 3 premières
                print(f"   - {table}")
            if len(unknown_tables) > 3:
                print(f"   - ... et {len(unknown_tables) - 3} autres")
            print("   → ACTION: Vérifier l'utilité et nettoyer si nécessaire")
        
        # Score de santé
        total_critical = len(CRITICAL_TABLES)
        present_critical = total_critical - len(missing_critical)
        health_score = (present_critical / total_critical) * 100
        
        print(f"\n📊 SCORE DE SANTÉ: {health_score:.0f}%")
        if health_score == 100:
            print("   🎉 Parfait - Toutes les tables critiques sont présentes")
        elif health_score >= 80:
            print("   ✅ Bon - La plupart des tables critiques sont présentes")
        elif health_score >= 60:
            print("   ⚠️  Moyen - Plusieurs tables critiques manquent")
        else:
            print("   🚨 Critique - Beaucoup de tables manquent")
        
        # Actions prioritaires
        if missing_critical:
            print(f"\n🎯 ACTIONS PRIORITAIRES:")
            print("1. Créer les tables critiques manquantes")
            print("2. Vérifier la structure de la table users")
            print("3. Tester l'authentification")
        elif "supabase_user_id" not in existing_columns if "users" in existing_table_names else True:
            print(f"\n🎯 ACTIONS PRIORITAIRES:")
            print("1. Ajouter la colonne supabase_user_id à la table users")
            print("2. Créer la table user_profiles si nécessaire")
            print("3. Tester l'authentification avec middleware")
        else:
            print(f"\n🎯 PROCHAINES ÉTAPES:")
            print("1. Tester l'authentification complète")
            print("2. Créer les tables optionnelles selon les besoins")
            print("3. Optimiser les performances")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


if __name__ == "__main__":
    success = asyncio.run(quick_audit())
    exit(0 if success else 1)
