#!/usr/bin/env python3
"""
Script pour déboguer les accès utilisateur aux documents techniques
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.core.database import engine

async def debug_user_access():
    """Déboguer les accès utilisateur"""
    
    print("🔍 Débogage des accès utilisateur")
    print("=" * 50)
    
    try:
        async with engine.begin() as conn:
            # 1. Vérifier l'utilisateur <EMAIL>
            print("1. 👤 Utilisateur <EMAIL>:")
            user_result = await conn.execute(text("""
                SELECT id, email, first_name, last_name, is_active
                FROM users 
                WHERE email = '<EMAIL>'
            """))
            user = user_result.fetchone()
            
            if user:
                user_id = user[0]
                print(f"   ✅ Trouvé: {user[1]} - {user[2]} {user[3]} (ID: {user_id}, Actif: {user[4]})")
                
                # 2. Vérifier l'entreprise de l'utilisateur
                print("\n2. 🏢 Entreprise de l'utilisateur:")
                company_result = await conn.execute(text("""
                    SELECT c.id, c.name, c.code, uc.is_active
                    FROM companies c
                    JOIN user_companies uc ON c.id = uc.company_id
                    WHERE uc.user_id = :user_id
                """), {'user_id': user_id})
                
                companies = company_result.fetchall()
                if companies:
                    for company in companies:
                        company_id = company[0]
                        print(f"   ✅ Entreprise: {company[1]} ({company[2]}) - ID: {company_id}, Actif: {company[3]}")
                        
                        # 3. Vérifier les documents techniques accessibles
                        print(f"\n3. 📋 Documents techniques accessibles pour l'entreprise {company_id}:")
                        docs_result = await conn.execute(text("""
                            SELECT td.id, td.name, td.type_document, td.project_id, p.name as project_name
                            FROM technical_documents td
                            JOIN technical_document_companies tdc ON td.id = tdc.technical_document_id
                            JOIN projects p ON td.project_id = p.id
                            WHERE tdc.company_id = :company_id
                            AND td.is_active = true
                            ORDER BY td.created_at DESC
                        """), {'company_id': company_id})
                        
                        docs = docs_result.fetchall()
                        if docs:
                            for doc in docs:
                                print(f"   ✅ {doc[1]} ({doc[2]}) - Projet: {doc[4]} (ID: {doc[3]})")
                        else:
                            print("   ❌ Aucun document technique accessible")
                            
                        # 4. Vérifier spécifiquement le projet ID=7
                        print(f"\n4. 🎯 Accès au projet ID=7:")
                        project7_result = await conn.execute(text("""
                            SELECT td.id, td.name, td.type_document
                            FROM technical_documents td
                            JOIN technical_document_companies tdc ON td.id = tdc.technical_document_id
                            WHERE tdc.company_id = :company_id
                            AND td.project_id = 7
                            AND td.is_active = true
                        """), {'company_id': company_id})
                        
                        project7_docs = project7_result.fetchall()
                        if project7_docs:
                            print(f"   ✅ {len(project7_docs)} documents accessibles dans le projet 7:")
                            for doc in project7_docs:
                                print(f"     - {doc[1]} ({doc[2]})")
                        else:
                            print("   ❌ Aucun document accessible dans le projet 7")
                            
                            # Vérifier si les documents existent mais ne sont pas associés
                            all_docs_result = await conn.execute(text("""
                                SELECT id, name, type_document
                                FROM technical_documents
                                WHERE project_id = 7
                                AND is_active = true
                            """))
                            all_docs = all_docs_result.fetchall()
                            
                            if all_docs:
                                print(f"   ⚠️  {len(all_docs)} documents existent dans le projet 7 mais ne sont pas associés à votre entreprise:")
                                for doc in all_docs:
                                    print(f"     - {doc[1]} ({doc[2]}) - ID: {doc[0]}")
                                    
                                # Associer automatiquement les documents à l'entreprise
                                print(f"\n5. 🔧 Association automatique des documents à l'entreprise {company_id}:")
                                for doc in all_docs:
                                    await conn.execute(text("""
                                        INSERT INTO technical_document_companies (technical_document_id, company_id, created_at)
                                        VALUES (:doc_id, :company_id, CURRENT_TIMESTAMP)
                                        ON CONFLICT (technical_document_id, company_id) DO NOTHING
                                    """), {'doc_id': doc[0], 'company_id': company_id})
                                    print(f"   ✅ Document {doc[1]} associé à l'entreprise")
                else:
                    print("   ❌ Aucune entreprise associée à cet utilisateur")
            else:
                print("   ❌ Utilisateur <EMAIL> non trouvé")
                
                # Lister les utilisateurs disponibles
                print("\n📋 Utilisateurs disponibles:")
                all_users_result = await conn.execute(text("SELECT id, email FROM users ORDER BY id LIMIT 10"))
                all_users = all_users_result.fetchall()
                for user in all_users:
                    print(f"   - {user[1]} (ID: {user[0]})")
            
            print("\n✅ Débogage terminé!")
            
    except Exception as e:
        print(f"❌ Erreur lors du débogage: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(debug_user_access())
