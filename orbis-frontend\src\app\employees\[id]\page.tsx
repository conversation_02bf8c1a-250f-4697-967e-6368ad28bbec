'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { TimeTracker } from '@/components/employees/TimeTracker'
import Link from 'next/link'
import { api } from '@/lib/api'

export default function EmployeeDetails() {
  const params = useParams()
  const router = useRouter()
  const [employee, setEmployee] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchEmployee = async () => {
      try {
        setLoading(true)
        const employeeId = parseInt(params.id as string)
        const data = await api.getEmployee(employeeId)
        setEmployee(data)
        setError(null)
      } catch (err) {
        console.error('Error fetching employee:', err)
        setError('Erreur lors du chargement des détails de l\'employé')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchEmployee()
    }
  }, [params.id])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-4">{error}</div>
          <Button onClick={() => window.location.reload()}>
            Réessayer
          </Button>
        </div>
      </div>
    )
  }

  if (!employee) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-gray-600">Employé non trouvé</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-6">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-2xl font-bold text-blue-600">
              {employee.firstName[0]}{employee.lastName[0]}
            </span>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {employee.firstName} {employee.lastName}
            </h1>
            <p className="text-xl text-gray-600">{employee.role}</p>
            <div className="flex items-center gap-4 mt-2">
              <span className={`px-3 py-1 text-sm rounded-full ${
                employee.status === 'Actif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {employee.status}
              </span>
              <span className="text-gray-500">
                Depuis le {new Date(employee.joinDate).toLocaleDateString('fr-FR')}
              </span>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            Modifier
          </Button>
          <Link href="/employees">
            <Button variant="outline">Retour</Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">⏰</div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{employee.totalHours}h</div>
              <div className="text-sm text-gray-600">Heures totales</div>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">📊</div>
            <div>
              <div className="text-2xl font-bold text-green-600">{employee.activeProjects}</div>
              <div className="text-sm text-gray-600">Projets actifs</div>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">💰</div>
            <div>
              <div className="text-2xl font-bold text-purple-600">{employee.hourlyRate}€</div>
              <div className="text-sm text-gray-600">Taux horaire</div>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="text-3xl mr-4">⭐</div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">
                {Math.round((employee.performance.efficiency + employee.performance.punctuality + employee.performance.teamwork + employee.performance.quality) / 4)}%
              </div>
              <div className="text-sm text-gray-600">Performance</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Vue d\'ensemble' },
            { id: 'projects', label: 'Projets' },
            { id: 'time', label: 'Temps de travail' },
            { id: 'performance', label: 'Performance' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Informations personnelles</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Email:</span>
                <span className="font-medium">{employee.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Téléphone:</span>
                <span className="font-medium">{employee.phone}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Adresse:</span>
                <span className="font-medium text-right">{employee.address}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Contact d'urgence:</span>
                <span className="font-medium text-right">{employee.emergencyContact}</span>
              </div>
            </div>
          </Card>
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Compétences</h2>
            <div className="flex flex-wrap gap-2">
              {employee.skills.map((skill: string, index: number) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                >
                  {skill}
                </span>
              ))}
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'projects' && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Projets assignés</h2>
          <div className="space-y-4">
            {employee.projects.map((project: any) => (
              <div key={project.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="font-medium text-gray-900">{project.name}</h3>
                    <p className="text-sm text-gray-600">{project.role}</p>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    project.status === 'En cours' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {project.status}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  Heures travaillées: <span className="font-medium">{project.hours}h</span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {activeTab === 'time' && (
        <div className="space-y-6">
          <TimeTracker employeeId={employee.id} />
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Historique récent</h2>
            <div className="space-y-3">
              {employee.timeEntries.map((entry: any) => (
                <div key={entry.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">{entry.project}</div>
                    <div className="text-sm text-gray-600">{entry.description}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{entry.hours}h</div>
                    <div className="text-sm text-gray-600">
                      {new Date(entry.date).toLocaleDateString('fr-FR')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'performance' && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-6">Évaluation de performance</h2>
          <div className="space-y-6">
            {Object.entries(employee.performance).map(([key, value]: [string, any]) => (
              <div key={key}>
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium capitalize">
                    {key === 'efficiency' ? 'Efficacité' :
                     key === 'punctuality' ? 'Ponctualité' :
                     key === 'teamwork' ? 'Travail d\'équipe' :
                     'Qualité du travail'}
                  </span>
                  <span className="text-sm font-medium">{value}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      value >= 90 ? 'bg-green-500' :
                      value >= 75 ? 'bg-blue-500' :
                      value >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${value}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  )
}