/**
 * Service de génération de page de garde SIMPLE et FONCTIONNEL
 * Génère directement le HTML sans système de template complexe
 */

export interface CoverPageData {
  lot: {
    id: number
    name: string
    code?: string
    description?: string
    current_phase?: string
    photo_url?: string
    project?: {
      id: number
      name: string
      address?: string
      photo_url?: string
    }
  }
  documentType: string
  documentIndice: string
  companies: Array<{
    id: number
    name: string
    role: string
    activity?: string
    address?: string
    phone?: string
    email?: string
    logo_url?: string | null
  }>
}

export function generateSimpleCoverPageHTML(data: CoverPageData): string {
  console.log('🎯 Génération SIMPLE de page de garde')
  console.log('📊 Données:', data)

  // Préparer les données
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
  
  // Trouver les entreprises par rôle
  const moaCompany = data.companies.find(c => c.role === 'MOA')
  const otherCompanies = data.companies.filter(c => c.role !== 'MOA')

  // Titre du document
  const getDocumentTitle = (type: string): string => {
    switch (type.toUpperCase()) {
      case 'CCTP': return 'CAHIER DES CLAUSES TECHNIQUES PARTICULIÈRES'
      case 'DPGF': return 'DÉCOMPOSITION DU PRIX GLOBAL ET FORFAITAIRE'
      default: return type.toUpperCase()
    }
  }

  // Traduction des rôles en français
  const getRoleDisplayName = (role: string): string => {
    const roleNames: Record<string, string> = {
      'MOA': 'Maître d\'ouvrage',
      'MOADEL': 'Maître d\'ouvrage délégué',
      'ARCHI': 'Architecte',
      'BE': 'Bureau d\'études',
      'BC': 'Bureau de contrôle',
      'OPC': 'Coordinateur OPC',
      'ENT': 'Entreprise',
      'FO': 'Fournisseur'
    }
    return roleNames[role] || role
  }

  // Générer la liste des entreprises (colonne gauche) avec tables
  const companiesHTML = otherCompanies.map(company => {
    console.log('🏢 Company data:', company) // Debug pour voir les données
    return `
    <table style="width: 100%; margin-bottom: 8px; border-bottom: 1px solid rgba(255,255,255,0.3); padding-bottom: 6px;">
      <tr>
        <td style="width: 25px; vertical-align: top; padding-right: 6px;">
          ${company.logo_url ? `<img src="${baseUrl}${company.logo_url}" style="height: 20px; width: auto; border: 1px solid rgba(255,255,255,0.5); background-color: white; padding: 1px;" alt="Logo" />` : '<div style="width: 20px; height: 20px; background-color: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.5);"></div>'}
        </td>
        <td style="vertical-align: top;">
          <p style="margin: 0; font-size: 8px; font-weight: bold; color: white; margin-bottom: 1px;">
            ${getRoleDisplayName(company.role)} :
          </p>
          <p style="margin: 0; font-size: 7px; font-weight: bold; color: white; line-height: 1.2; margin-bottom: 1px;">
            ${company.name}
          </p>
          ${company.activity ? `<p style="margin: 0; font-size: 6px; color: rgba(255,255,255,0.8); font-style: italic; margin-bottom: 1px;">${company.activity}</p>` : ''}
          ${company.address ? `<p style="margin: 0; font-size: 6px; color: rgba(255,255,255,0.8); line-height: 1.1; margin-bottom: 1px;">${company.address}</p>` : ''}
          ${company.phone ? `<p style="margin: 0; font-size: 6px; color: rgba(255,255,255,0.8); line-height: 1.1;">Tél : ${company.phone}</p>` : ''}
          ${company.email ? `<p style="margin: 0; font-size: 6px; color: rgba(255,255,255,0.8); line-height: 1.1;">Mail : ${company.email}</p>` : ''}
        </td>
      </tr>
    </table>
  `}).join('')

  // Générer les infos du projet (colonne droite) avec tables
  const projectHTML = `
    <table style="width: 100%; margin-bottom: 15px;">
      <tr>
        <td style="text-align: center;">
          <p style="margin: 0; font-size: 16px; font-weight: bold; margin-bottom: 6px; line-height: 1.3;">
            ${data.lot.project?.name || 'Projet non défini'}
          </p>
          ${data.lot.project?.address ? `<p style="margin: 0; font-size: 13px; margin-bottom: 15px; font-weight: 500;">${data.lot.project.address}</p>` : ''}
        </td>
      </tr>
    </table>

    ${data.lot.photo_url || data.lot.project?.photo_url ? `
      <table style="width: 100%; margin-bottom: 15px;">
        <tr>
          <td style="text-align: center;">
            <img src="${baseUrl}${data.lot.photo_url || data.lot.project?.photo_url}"
                 style="width: 100%; max-height: 180px; object-fit: cover; border: 1px solid #ccc;"
                 alt="Image du projet" />
          </td>
        </tr>
      </table>
    ` : ''}

    ${moaCompany ? `
      <table style="width: 100%; border: 2px solid #000; margin-bottom: 15px; background-color: #fafafa;">
        <tr>
          <td style="text-align: center; padding: 8px;">
            <p style="margin: 0; font-size: 11px; font-weight: bold; margin-bottom: 6px;">
              MAÎTRE D'OUVRAGE
            </p>
          </td>
        </tr>
        <tr>
          <td style="text-align: center; padding: 8px;">
            <table style="width: 100%;">
              <tr>
                <td style="text-align: center; width: 40px;">
                  ${moaCompany.logo_url ? `<img src="${baseUrl}${moaCompany.logo_url}" style="height: 30px; width: auto;" alt="Logo MOA" />` : ''}
                </td>
                <td style="text-align: left; vertical-align: middle;">
                  <p style="margin: 0; font-size: 10px; font-weight: bold; margin-bottom: 2px;">
                    ${moaCompany.name}
                  </p>
                  ${moaCompany.address ? `<p style="margin: 0; font-size: 9px; color: #666;">${moaCompany.address}</p>` : ''}
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    ` : ''}

    <table style="width: 100%; margin-bottom: 15px; border: 1px solid #ccc; background-color: #f9f9f9;">
      <tr>
        <td style="text-align: center; padding: 12px;">
          <p style="margin: 0; font-size: 16px; font-weight: bold; margin-bottom: 6px; color: #333;">
            Lot n°${data.lot.code || data.lot.id}
          </p>
          <p style="margin: 0; font-size: 13px; font-weight: bold; text-transform: uppercase; letter-spacing: 0.8px; color: #555;">
            ${data.lot.description || data.lot.name}
          </p>
        </td>
      </tr>
    </table>

    <table style="width: 100%; margin-bottom: 15px; border: 2px solid #000; border-radius: 12px; background-color: #f5f5f5;">
      <tr>
        <td style="text-align: center; padding: 15px;">
          <p style="margin: 0; font-size: 20px; font-weight: bold; letter-spacing: 1px;">
            ${getDocumentTitle(data.documentType)}
          </p>
        </td>
      </tr>
    </table>

    <table style="width: 100%; border-collapse: collapse; border: 2px solid #000; font-size: 12px; margin-top: 10px;">
      <tr>
        <td style="padding: 10px; border: 1px solid #000; font-weight: bold; background-color: #e8e8e8; width: 40%;">Dossier</td>
        <td style="padding: 10px; border: 1px solid #000; text-align: center; font-weight: bold;">001</td>
      </tr>
      <tr>
        <td style="padding: 10px; border: 1px solid #000; font-weight: bold; background-color: #e8e8e8;">Date</td>
        <td style="padding: 10px; border: 1px solid #000; text-align: center; font-weight: bold;">${new Date().toLocaleDateString('fr-FR')}</td>
      </tr>
      <tr>
        <td style="padding: 10px; border: 1px solid #000; font-weight: bold; background-color: #e8e8e8;">Phase</td>
        <td style="padding: 10px; border: 1px solid #000; text-align: center; font-weight: bold; color: #0066cc;">${data.lot.current_phase || 'ESQ'}</td>
      </tr>
      <tr>
        <td style="padding: 10px; border: 1px solid #000; font-weight: bold; background-color: #e8e8e8;">Indice</td>
        <td style="padding: 10px; border: 1px solid #000; text-align: center; font-weight: bold;">${data.documentIndice}</td>
      </tr>
    </table>
  `

  // HTML final avec layout tableau
  const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }
    .main-container {
      margin: 0 auto;
      background-color: white;
      border: 3px solid #000;
      width: 210mm;
      min-height: 280mm;
      padding: 25px;
      position: relative;
      box-sizing: border-box;
    }
    table {
      border-collapse: collapse;
    }
    p {
      margin: 0;
    }
  </style>
</head>
<body>
  <div class="main-container">
    <div style="position: absolute; top: 5px; left: 5px; font-size: 8px; color: #0F766E; font-weight: bold; background-color: rgba(15, 118, 110, 0.1); padding: 2px 5px; border-radius: 3px;">
      ✅ Layout 2 colonnes
    </div>
    
    <table style="width: 100%; margin-top: 60px; border-collapse: collapse;">
      <tr>
        <td style="width: 45%; vertical-align: top; padding: 15px; background-color: #0F766E; border-right: 2px solid #0F766E;">
          <div style="color: white;">
            ${companiesHTML}
          </div>
        </td>
        <td style="width: 55%; vertical-align: top; padding: 15px; background-color: white;">
          ${projectHTML}
        </td>
      </tr>
    </table>
  </div>
</body>
</html>`

  console.log('✅ HTML généré avec succès (version simple)')
  return html
}
