/**
 * Service de génération de page de garde SIMPLE et FONCTIONNEL
 * Génère directement le HTML sans système de template complexe
 */

export interface CoverPageData {
  lot: {
    id: number
    name: string
    code?: string
    description?: string
    current_phase?: string
    photo_url?: string
    project?: {
      id: number
      name: string
      address?: string
      photo_url?: string
    }
  }
  documentType: string
  documentIndice: string
  companies: Array<{
    id: number
    name: string
    role: string
    activity?: string
    address?: string
    phone?: string
    email?: string
    logo_url?: string | null
  }>
}

export function generateSimpleCoverPageHTML(data: CoverPageData): string {
  console.log('🎯 Génération SIMPLE de page de garde')
  console.log('📊 Données:', data)

  // Préparer les données
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
  
  // Trouver les entreprises par rôle
  const moaCompany = data.companies.find(c => c.role === 'MOA')
  const otherCompanies = data.companies.filter(c => c.role !== 'MOA')
  
  // Titre du document
  const getDocumentTitle = (type: string): string => {
    switch (type.toUpperCase()) {
      case 'CCTP': return 'CAHIER DES CLAUSES TECHNIQUES PARTICULIÈRES'
      case 'DPGF': return 'DÉCOMPOSITION DU PRIX GLOBAL ET FORFAITAIRE'
      default: return type.toUpperCase()
    }
  }

  // Générer la liste des entreprises (colonne gauche)
  const companiesHTML = otherCompanies.map(company => `
    <div style="margin-bottom: 20px; border-bottom: 1px solid #ddd; padding-bottom: 15px;">
      <div style="display: flex; align-items: flex-start; gap: 10px; margin-bottom: 8px;">
        ${company.logo_url ? `<img src="${baseUrl}${company.logo_url}" style="height: 30px; width: auto; flex-shrink: 0;" alt="Logo" />` : ''}
        <div style="flex: 1;">
          <div style="font-size: 12px; font-weight: bold; margin-bottom: 3px; text-transform: uppercase;">
            ${company.role} :
          </div>
          <div style="font-size: 11px; font-weight: bold; margin-bottom: 2px;">
            ${company.name}
          </div>
          ${company.activity ? `<div style="font-size: 10px; color: #666; font-style: italic; margin-bottom: 2px;">${company.activity}</div>` : ''}
        </div>
      </div>
      ${company.address ? `<div style="font-size: 10px; color: #666; margin-bottom: 2px;">${company.address}</div>` : ''}
      ${company.phone ? `<div style="font-size: 10px; color: #666;">Tél : ${company.phone}</div>` : ''}
      ${company.email ? `<div style="font-size: 10px; color: #666;">Mail : ${company.email}</div>` : ''}
    </div>
  `).join('')

  // Générer les infos du projet (colonne droite)
  const projectHTML = `
    <div style="text-align: center; margin-bottom: 20px;">
      <h2 style="font-size: 16px; font-weight: bold; margin-bottom: 8px; line-height: 1.3;">
        ${data.lot.project?.name || 'Projet non défini'}
      </h2>
      ${data.lot.project?.address ? `<div style="font-size: 13px; margin-bottom: 20px; font-weight: 500;">${data.lot.project.address}</div>` : ''}
    </div>

    ${data.lot.photo_url || data.lot.project?.photo_url ? `
      <div style="text-align: center; margin-bottom: 20px;">
        <img src="${baseUrl}${data.lot.photo_url || data.lot.project?.photo_url}" 
             style="width: 100%; max-height: 180px; object-fit: cover; border: 1px solid #ccc;" 
             alt="Image du projet" />
      </div>
    ` : ''}

    ${moaCompany ? `
      <div style="border: 2px solid #000; padding: 12px; margin-bottom: 20px; background-color: #fafafa;">
        <div style="font-size: 11px; font-weight: bold; margin-bottom: 8px; text-align: center;">
          MAÎTRE D'OUVRAGE
        </div>
        <div style="display: flex; align-items: center; justify-content: center; gap: 12px;">
          ${moaCompany.logo_url ? `<img src="${baseUrl}${moaCompany.logo_url}" style="height: 35px; width: auto;" alt="Logo MOA" />` : ''}
          <div>
            <div style="font-size: 10px; font-weight: bold; margin-bottom: 3px;">
              ${moaCompany.name}
            </div>
            ${moaCompany.address ? `<div style="font-size: 9px; color: #666;">${moaCompany.address}</div>` : ''}
          </div>
        </div>
      </div>
    ` : ''}

    <div style="text-align: center; margin-bottom: 20px; padding: 15px; border: 1px solid #ccc; background-color: #f9f9f9;">
      <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px; color: #333;">
        Lot n°${data.lot.code || data.lot.id}
      </div>
      <div style="font-size: 13px; font-weight: bold; text-transform: uppercase; letter-spacing: 0.8px; color: #555;">
        ${data.lot.description || data.lot.name}
      </div>
    </div>

    <div style="border: 2px solid #000; border-radius: 12px; background-color: #f5f5f5; padding: 20px; text-align: center; margin-bottom: 20px;">
      <div style="font-size: 20px; font-weight: bold; letter-spacing: 1px;">
        ${getDocumentTitle(data.documentType)}
      </div>
    </div>

    <table style="width: 100%; border-collapse: collapse; border: 2px solid #000; font-size: 12px; margin-top: 10px;">
      <tr>
        <td style="padding: 10px; border: 1px solid #000; font-weight: bold; background-color: #e8e8e8; width: 40%;">Dossier</td>
        <td style="padding: 10px; border: 1px solid #000; text-align: center; font-weight: bold;">001</td>
      </tr>
      <tr>
        <td style="padding: 10px; border: 1px solid #000; font-weight: bold; background-color: #e8e8e8;">Date</td>
        <td style="padding: 10px; border: 1px solid #000; text-align: center; font-weight: bold;">${new Date().toLocaleDateString('fr-FR')}</td>
      </tr>
      <tr>
        <td style="padding: 10px; border: 1px solid #000; font-weight: bold; background-color: #e8e8e8;">Phase</td>
        <td style="padding: 10px; border: 1px solid #000; text-align: center; font-weight: bold; color: #0066cc;">${data.lot.current_phase || 'ESQ'}</td>
      </tr>
      <tr>
        <td style="padding: 10px; border: 1px solid #000; font-weight: bold; background-color: #e8e8e8;">Indice</td>
        <td style="padding: 10px; border: 1px solid #000; text-align: center; font-weight: bold;">${data.documentIndice}</td>
      </tr>
    </table>
  `

  // HTML final avec layout tableau
  const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body { 
      margin: 0; 
      padding: 20px; 
      font-family: Arial, sans-serif; 
      background-color: #f5f5f5;
    }
    .main-container {
      margin: 0 auto;
      background-color: white;
      border: 3px solid #000;
      width: 210mm;
      min-height: 280mm;
      padding: 25px;
      position: relative;
      box-sizing: border-box;
    }
  </style>
</head>
<body>
  <div class="main-container">
    <div style="position: absolute; top: 5px; left: 5px; font-size: 10px; color: red; font-weight: bold; background-color: yellow; padding: 2px 5px;">
      🚀 SIMPLE TEMPLATE V1.0
    </div>
    
    <table style="width: 100%; margin-top: 60px; border-collapse: separate; border-spacing: 20px 0;">
      <tr>
        <td style="width: 45%; vertical-align: top; padding-right: 10px; border-right: 2px solid #ccc;">
          ${companiesHTML}
        </td>
        <td style="width: 55%; vertical-align: top; padding-left: 10px;">
          ${projectHTML}
        </td>
      </tr>
    </table>
  </div>
</body>
</html>`

  console.log('✅ HTML généré avec succès (version simple)')
  return html
}
