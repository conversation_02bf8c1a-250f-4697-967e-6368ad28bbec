"""Add nature field to projects table

Revision ID: add_project_nature
Revises: 
Create Date: 2025-01-03 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_project_nature'
down_revision = None  # Remplacer par la dernière révision si nécessaire
branch_labels = None
depends_on = None

def upgrade():
    # Créer l'enum pour ProjectNature
    project_nature_enum = postgresql.ENUM('Devis', 'AO', 'Affaire', name='projectnature')
    project_nature_enum.create(op.get_bind())
    
    # Mettre à jour l'enum ProjectStatus avec les nouvelles valeurs
    op.execute("ALTER TYPE projectstatus RENAME TO projectstatus_old")
    
    project_status_enum = postgresql.ENUM('En cours', 'En attente', 'Terminé', 'Archivé', name='projectstatus')
    project_status_enum.create(op.get_bind())
    
    # Ajouter la colonne nature
    op.add_column('projects', sa.Column('nature', project_nature_enum, nullable=True))
    
    # Mettre à jour la colonne status avec le nouveau type
    op.execute("""
        ALTER TABLE projects 
        ALTER COLUMN status TYPE projectstatus 
        USING CASE 
            WHEN status::text = 'dao' THEN 'En cours'::projectstatus
            WHEN status::text = 'exe' THEN 'En cours'::projectstatus
            WHEN status::text = 'completed' THEN 'Terminé'::projectstatus
            WHEN status::text = 'archived' THEN 'Archivé'::projectstatus
            ELSE 'En cours'::projectstatus
        END
    """)
    
    # Définir une valeur par défaut pour nature sur les projets existants
    op.execute("UPDATE projects SET nature = 'Devis' WHERE nature IS NULL")
    
    # Rendre la colonne nature non nullable
    op.alter_column('projects', 'nature', nullable=False)
    
    # Supprimer l'ancien enum
    op.execute("DROP TYPE projectstatus_old")

def downgrade():
    # Supprimer la colonne nature
    op.drop_column('projects', 'nature')
    
    # Recréer l'ancien enum ProjectStatus
    op.execute("ALTER TYPE projectstatus RENAME TO projectstatus_new")
    
    project_status_old_enum = postgresql.ENUM('dao', 'exe', 'completed', 'archived', name='projectstatus')
    project_status_old_enum.create(op.get_bind())
    
    # Restaurer l'ancienne colonne status
    op.execute("""
        ALTER TABLE projects 
        ALTER COLUMN status TYPE projectstatus 
        USING CASE 
            WHEN status::text = 'En cours' THEN 'dao'::projectstatus
            WHEN status::text = 'En attente' THEN 'dao'::projectstatus
            WHEN status::text = 'Terminé' THEN 'completed'::projectstatus
            WHEN status::text = 'Archivé' THEN 'archived'::projectstatus
            ELSE 'dao'::projectstatus
        END
    """)
    
    # Supprimer les nouveaux enums
    op.execute("DROP TYPE projectstatus_new")
    op.execute("DROP TYPE projectnature")
