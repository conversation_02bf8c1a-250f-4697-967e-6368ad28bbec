#!/usr/bin/env python3
"""
Script pour appliquer directement la migration des stakeholders
Évite les problèmes d'Alembic en appliquant directement les changements SQL
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import engine
from sqlalchemy import text

def check_table_exists(table_name):
    """Vérifie si une table existe"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = '{table_name}'
                );
            """))
            return result.fetchone()[0]
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de la table {table_name}: {e}")
        return False

def apply_stakeholders_migration():
    """Applique la migration des stakeholders directement"""
    print("🚀 Application directe de la migration stakeholders...")
    
    try:
        with engine.connect() as conn:
            # Commencer une transaction
            trans = conn.begin()
            
            try:
                # Vérifier si la table lot_intervenants existe
                if not check_table_exists('lot_intervenants'):
                    print("⚠️  Table lot_intervenants n'existe pas, rien à migrer")
                    return True
                
                # Vérifier si la table stakeholders existe déjà
                if check_table_exists('stakeholders'):
                    print("✅ Table stakeholders existe déjà")
                    return True
                
                print("📝 Renommage de la table lot_intervenants en stakeholders...")
                conn.execute(text('ALTER TABLE lot_intervenants RENAME TO stakeholders'))
                
                print("📝 Renommage des index...")
                # Renommer les index (ignorer les erreurs si ils n'existent pas)
                try:
                    conn.execute(text('ALTER INDEX ix_lot_intervenants_id RENAME TO ix_stakeholders_id'))
                except Exception as e:
                    print(f"⚠️  Index ix_lot_intervenants_id: {e}")
                
                try:
                    conn.execute(text('ALTER INDEX ix_lot_intervenants_lot_id RENAME TO ix_stakeholders_lot_id'))
                except Exception as e:
                    print(f"⚠️  Index ix_lot_intervenants_lot_id: {e}")
                
                try:
                    conn.execute(text('ALTER INDEX ix_lot_intervenants_company_id RENAME TO ix_stakeholders_company_id'))
                except Exception as e:
                    print(f"⚠️  Index ix_lot_intervenants_company_id: {e}")
                
                print("📝 Mise à jour des contraintes de clés étrangères...")
                # Supprimer les anciennes contraintes (ignorer les erreurs si elles n'existent pas)
                try:
                    conn.execute(text('ALTER TABLE stakeholders DROP CONSTRAINT IF EXISTS lot_intervenants_lot_id_fkey'))
                    conn.execute(text('ALTER TABLE stakeholders DROP CONSTRAINT IF EXISTS lot_intervenants_company_id_fkey'))
                    conn.execute(text('ALTER TABLE stakeholders DROP CONSTRAINT IF EXISTS lot_intervenants_created_by_fkey'))
                except Exception as e:
                    print(f"⚠️  Suppression contraintes: {e}")
                
                # Recréer les contraintes avec les nouveaux noms
                conn.execute(text('''
                    ALTER TABLE stakeholders 
                    ADD CONSTRAINT stakeholders_lot_id_fkey 
                    FOREIGN KEY (lot_id) REFERENCES lots(id)
                '''))
                
                conn.execute(text('''
                    ALTER TABLE stakeholders 
                    ADD CONSTRAINT stakeholders_company_id_fkey 
                    FOREIGN KEY (company_id) REFERENCES tcompanies(id)
                '''))
                
                conn.execute(text('''
                    ALTER TABLE stakeholders 
                    ADD CONSTRAINT stakeholders_created_by_fkey 
                    FOREIGN KEY (created_by) REFERENCES users(id)
                '''))
                
                # Valider la transaction
                trans.commit()
                print("✅ Migration appliquée avec succès")
                return True
                
            except Exception as e:
                # Annuler la transaction en cas d'erreur
                trans.rollback()
                print(f"❌ Erreur lors de la migration: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

def verify_migration():
    """Vérifie que la migration a été appliquée correctement"""
    print("\n🔍 Vérification de la migration...")
    
    try:
        with engine.connect() as conn:
            # Vérifier que la table stakeholders existe
            if not check_table_exists('stakeholders'):
                print("❌ Table 'stakeholders' non trouvée")
                return False
            
            print("✅ Table 'stakeholders' trouvée")
            
            # Vérifier que l'ancienne table lot_intervenants n'existe plus
            if check_table_exists('lot_intervenants'):
                print("⚠️  Ancienne table 'lot_intervenants' existe encore")
            else:
                print("✅ Ancienne table 'lot_intervenants' supprimée")
            
            # Compter les enregistrements
            result = conn.execute(text("SELECT COUNT(*) FROM stakeholders"))
            count = result.fetchone()[0]
            print(f"✅ {count} enregistrements dans la table stakeholders")
            
            # Vérifier les colonnes
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'stakeholders' 
                ORDER BY ordinal_position
            """))
            
            columns = [row[0] for row in result.fetchall()]
            expected_columns = ['id', 'lot_id', 'company_id', 'role', 'is_active', 'created_at', 'created_by']
            
            for expected in expected_columns:
                if expected in columns:
                    print(f"✅ Colonne '{expected}' trouvée")
                else:
                    print(f"❌ Colonne '{expected}' manquante")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔄 Migration Directe des Stakeholders (lot_intervenants → stakeholders)")
    print("=" * 70)
    
    # Étape 1: Appliquer la migration
    if not apply_stakeholders_migration():
        print("❌ Échec de l'application de la migration")
        return False
    
    # Étape 2: Vérifier la migration
    if not verify_migration():
        print("❌ Échec de la vérification de la migration")
        return False
    
    print("\n" + "=" * 70)
    print("🎉 Migration des stakeholders terminée avec succès !")
    print("\nProchaines étapes:")
    print("1. Tester l'implémentation avec: python test_stakeholders_implementation.py")
    print("2. Mettre à jour le frontend pour utiliser les nouveaux endpoints")
    print("3. Supprimer les alias de compatibilité après validation complète")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
