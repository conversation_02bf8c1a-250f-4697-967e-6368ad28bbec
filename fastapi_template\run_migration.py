#!/usr/bin/env python3
"""
Script pour exécuter la migration companies → workspaces manuellement
"""

import asyncio
import asyncpg

DATABASE_URL = "**************************************************************************************************/postgres"

async def execute_migration():
    """Exécuter la migration companies → workspaces"""
    print("🔄 Exécution de la migration companies → workspaces...")
    print("="*60)
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Commencer une transaction
        async with conn.transaction():
            
            # 1. Renommer les tables principales
            print("📋 1. Renommage des tables...")
            
            await conn.execute("ALTER TABLE companies RENAME TO workspaces")
            print("   ✅ companies → workspaces")
            
            await conn.execute("ALTER TABLE company_settings RENAME TO workspace_settings")
            print("   ✅ company_settings → workspace_settings")
            
            await conn.execute("ALTER TABLE user_companies RENAME TO user_workspaces")
            print("   ✅ user_companies → user_workspaces")
            
            try:
                await conn.execute("ALTER TABLE company_role_permissions RENAME TO workspace_role_permissions")
                print("   ✅ company_role_permissions → workspace_role_permissions")
            except Exception as e:
                print(f"   ⚠️ company_role_permissions non trouvée: {e}")
            
            try:
                await conn.execute("ALTER TABLE company_invitations RENAME TO workspace_invitations")
                print("   ✅ company_invitations → workspace_invitations")
            except Exception as e:
                print(f"   ⚠️ company_invitations non trouvée: {e}")
            
            # 2. Renommer les colonnes company_id → workspace_id
            print("\n🔗 2. Renommage des colonnes company_id → workspace_id...")
            
            tables_to_update = [
                'workspace_settings',
                'user_workspaces',
                'audit_logs',
                'budgets',
                'documents',
                'employees',
                'entreprises_tiers',
                'financial_reports',
                'invoices',
                'materials',
                'purchase_orders',
                'quote_templates',
                'quotes',
                'suppliers'
            ]
            
            # Ajouter les tables optionnelles
            optional_tables = ['workspace_role_permissions', 'workspace_invitations']
            
            for table in tables_to_update + optional_tables:
                try:
                    await conn.execute(f"ALTER TABLE {table} RENAME COLUMN company_id TO workspace_id")
                    print(f"   ✅ {table}.company_id → workspace_id")
                except Exception as e:
                    print(f"   ⚠️ {table}: {e}")
            
            # 3. Mettre à jour les contraintes FK
            print("\n🔗 3. Mise à jour des contraintes FK...")
            
            # Liste des contraintes à mettre à jour
            fk_updates = [
                ('workspace_settings', 'company_settings_company_id_fkey', 'workspace_settings_workspace_id_fkey'),
                ('user_workspaces', 'user_companies_company_id_fkey', 'user_workspaces_workspace_id_fkey'),
                ('audit_logs', 'audit_logs_company_id_fkey', 'audit_logs_workspace_id_fkey'),
                ('budgets', 'budgets_company_id_fkey', 'budgets_workspace_id_fkey'),
                ('documents', 'documents_company_id_fkey', 'documents_workspace_id_fkey'),
                ('employees', 'employees_company_id_fkey', 'employees_workspace_id_fkey'),
                ('entreprises_tiers', 'entreprises_tiers_company_id_fkey', 'entreprises_tiers_workspace_id_fkey'),
                ('financial_reports', 'financial_reports_company_id_fkey', 'financial_reports_workspace_id_fkey'),
                ('invoices', 'invoices_company_id_fkey', 'invoices_workspace_id_fkey'),
                ('materials', 'materials_company_id_fkey', 'materials_workspace_id_fkey'),
                ('purchase_orders', 'purchase_orders_company_id_fkey', 'purchase_orders_workspace_id_fkey'),
                ('quote_templates', 'quote_templates_company_id_fkey', 'quote_templates_workspace_id_fkey'),
                ('quotes', 'quotes_company_id_fkey', 'quotes_workspace_id_fkey'),
                ('suppliers', 'suppliers_company_id_fkey', 'suppliers_workspace_id_fkey')
            ]
            
            # Ajouter les contraintes optionnelles
            optional_fks = [
                ('workspace_role_permissions', 'company_role_permissions_company_id_fkey', 'workspace_role_permissions_workspace_id_fkey'),
                ('workspace_invitations', 'company_invitations_company_id_fkey', 'workspace_invitations_workspace_id_fkey')
            ]
            
            for table, old_constraint, new_constraint in fk_updates + optional_fks:
                try:
                    # Supprimer l'ancienne contrainte
                    await conn.execute(f"ALTER TABLE {table} DROP CONSTRAINT IF EXISTS {old_constraint}")
                    
                    # Créer la nouvelle contrainte
                    await conn.execute(f"""
                        ALTER TABLE {table} 
                        ADD CONSTRAINT {new_constraint} 
                        FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE
                    """)
                    print(f"   ✅ {table}: {old_constraint} → {new_constraint}")
                except Exception as e:
                    print(f"   ⚠️ {table}: {e}")
            
            # 4. Mettre à jour quelques index importants
            print("\n📊 4. Mise à jour des index...")
            
            index_updates = [
                ('user_workspaces', 'ix_user_companies_user_id', 'ix_user_workspaces_user_id', 'user_id'),
                ('workspace_settings', 'ix_company_settings_id', 'ix_workspace_settings_id', 'id')
            ]
            
            for table, old_index, new_index, column in index_updates:
                try:
                    await conn.execute(f"DROP INDEX IF EXISTS {old_index}")
                    await conn.execute(f"CREATE INDEX {new_index} ON {table} ({column})")
                    print(f"   ✅ {old_index} → {new_index}")
                except Exception as e:
                    print(f"   ⚠️ {table}: {e}")
            
            print("\n✅ Migration terminée avec succès!")
            print("\n📝 Résumé:")
            print("  - Tables renommées: companies → workspaces, etc.")
            print("  - Colonnes renommées: company_id → workspace_id")
            print("  - Contraintes FK mises à jour")
            print("  - Tables préservées: technical_document_companies, project_company")
            
            # Vérification finale
            count = await conn.fetchval("SELECT COUNT(*) FROM workspaces")
            print(f"\n📊 Vérification: {count} workspaces dans la nouvelle table")
            
    except Exception as e:
        print(f"❌ Erreur lors de la migration: {e}")
        raise
    finally:
        await conn.close()

async def verify_migration():
    """Vérifier que la migration s'est bien passée"""
    print("\n🔍 Vérification post-migration...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier que les nouvelles tables existent
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('workspaces', 'workspace_settings', 'user_workspaces')
            ORDER BY table_name
        """)
        
        print("✅ Nouvelles tables créées:")
        for table in tables:
            count = await conn.fetchval(f"SELECT COUNT(*) FROM {table['table_name']}")
            print(f"  - {table['table_name']}: {count} enregistrements")
        
        # Vérifier que les anciennes tables n'existent plus
        old_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('companies', 'company_settings', 'user_companies')
        """)
        
        if old_tables:
            print("❌ ATTENTION: Anciennes tables encore présentes:")
            for table in old_tables:
                print(f"  - {table['table_name']}")
        else:
            print("✅ Anciennes tables supprimées")
        
        # Vérifier que les tables préservées existent toujours
        preserved = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('technical_document_companies', 'project_company')
            ORDER BY table_name
        """)
        
        print("✅ Tables préservées:")
        for table in preserved:
            count = await conn.fetchval(f"SELECT COUNT(*) FROM {table['table_name']}")
            print(f"  - {table['table_name']}: {count} enregistrements")
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
    finally:
        await conn.close()

if __name__ == "__main__":
    async def main():
        try:
            await execute_migration()
            await verify_migration()
            print("\n🎉 Migration companies → workspaces terminée avec succès!")
        except Exception as e:
            print(f"\n💥 Échec de la migration: {e}")
    
    asyncio.run(main())
