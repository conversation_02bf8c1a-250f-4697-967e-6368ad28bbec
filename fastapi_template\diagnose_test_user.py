"""
Script de diagnostic complet pour l'utilisateur <EMAIL>
Vérifie l'existence, les permissions et les associations workspace
"""

import asyncio
import sys
import os
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from app.db.session import AsyncSessionLocal
from app.models.user import User
from app.models.workspace import UserWorkspace, Workspace
from app.core.config import settings

async def diagnose_test_user():
    """Diagnostic complet de l'utilisateur <EMAIL>"""
    print("🔍 <NAME_EMAIL>")
    print("=" * 50)
    
    async with AsyncSessionLocal() as session:
        try:
            # 1. Vérifier l'utilisateur dans la base locale
            print("\n1️⃣ VÉRIFICATION UTILISATEUR LOCAL")
            result = await session.execute(
                select(User).where(User.email == "<EMAIL>")
            )
            user = result.scalar_one_or_none()
            
            if user:
                print(f"✅ Utilisateur trouvé dans la base locale:")
                print(f"   📧 Email: {user.email}")
                print(f"   🆔 ID local: {user.id}")
                print(f"   🔑 Supabase ID: {user.supabase_user_id}")
                print(f"   👤 Nom: {user.first_name} {user.last_name}")
                print(f"   🎭 Rôle: {user.role}")
                print(f"   ✅ Actif: {user.is_active}")
                print(f"   📅 Créé le: {user.created_at}")
                print(f"   🔄 Modifié le: {user.updated_at}")
                
                # 2. Vérifier les workspaces associés
                print("\n2️⃣ VÉRIFICATION WORKSPACES")
                workspace_result = await session.execute(
                    select(UserWorkspace, Workspace).join(
                        Workspace, UserWorkspace.workspace_id == Workspace.id
                    ).where(UserWorkspace.user_id == user.id)
                )
                user_workspaces = workspace_result.all()
                
                print(f"📁 Workspaces associés: {len(user_workspaces)}")
                if user_workspaces:
                    for uw, ws in user_workspaces:
                        print(f"   🏢 Workspace: {ws.name} (ID: {ws.id})")
                        print(f"      🎭 Rôle: {uw.role_name}")
                        print(f"      ✅ Actif: {uw.is_active}")
                        print(f"      📅 Ajouté le: {uw.created_at}")
                else:
                    print("   ❌ Aucun workspace associé!")
                    
                # 3. Vérifier tous les workspaces disponibles
                print("\n3️⃣ WORKSPACES DISPONIBLES")
                all_workspaces_result = await session.execute(select(Workspace))
                all_workspaces = all_workspaces_result.scalars().all()
                print(f"🏢 Total workspaces dans le système: {len(all_workspaces)}")
                for ws in all_workspaces:
                    print(f"   - {ws.name} (ID: {ws.id}, Actif: {ws.is_active})")
                
                # 4. Vérifier la structure des tables
                print("\n4️⃣ VÉRIFICATION STRUCTURE TABLES")
                
                # Vérifier la table users
                users_count = await session.execute(text("SELECT COUNT(*) FROM users"))
                print(f"👥 Total utilisateurs: {users_count.scalar()}")
                
                # Vérifier la table user_workspaces
                user_workspaces_count = await session.execute(text("SELECT COUNT(*) FROM user_workspaces"))
                print(f"🔗 Total associations user_workspaces: {user_workspaces_count.scalar()}")
                
                # Vérifier la table workspaces
                workspaces_count = await session.execute(text("SELECT COUNT(*) FROM workspaces"))
                print(f"🏢 Total workspaces: {workspaces_count.scalar()}")
                
                # 5. Suggestions de résolution
                print("\n5️⃣ DIAGNOSTIC ET SUGGESTIONS")
                if not user_workspaces:
                    print("❌ PROBLÈME IDENTIFIÉ: L'utilisateur n'a aucun workspace associé")
                    print("💡 SOLUTIONS POSSIBLES:")
                    print("   1. Créer un workspace pour cet utilisateur")
                    print("   2. L'associer à un workspace existant")
                    print("   3. Vérifier le processus d'inscription/invitation")
                elif not any(uw.is_active for uw, ws in user_workspaces):
                    print("❌ PROBLÈME IDENTIFIÉ: Toutes les associations workspace sont inactives")
                    print("💡 SOLUTION: Activer au moins une association workspace")
                else:
                    print("✅ L'utilisateur a des workspaces actifs")
                    print("🔍 Le problème 403 peut venir d'ailleurs:")
                    print("   - Vérifier les permissions spécifiques aux endpoints")
                    print("   - Vérifier le middleware d'authentification")
                    print("   - Vérifier la validation du token JWT")
                
            else:
                print("❌ Utilisateur <EMAIL> NON TROUVÉ dans la base locale")
                
                # Chercher des utilisateurs similaires
                print("\n🔍 RECHERCHE D'UTILISATEURS SIMILAIRES:")
                similar_users = await session.execute(
                    select(User).where(User.email.like("%test%"))
                )
                similar = similar_users.scalars().all()
                if similar:
                    print("📧 Utilisateurs avec 'test' dans l'email:")
                    for u in similar:
                        print(f"   - {u.email} (ID: {u.id})")
                
                # Afficher quelques utilisateurs pour debug
                print("\n📊 ÉCHANTILLON D'UTILISATEURS:")
                sample_users = await session.execute(select(User).limit(5))
                for u in sample_users.scalars():
                    print(f"   - {u.email} (ID: {u.id}, Supabase: {u.supabase_user_id})")
                    
                print("\n💡 ACTIONS RECOMMANDÉES:")
                print("   1. Créer l'utilisateur <EMAIL>")
                print("   2. Vérifier la synchronisation avec Supabase")
                print("   3. Vérifier le processus d'inscription")
                
        except Exception as e:
            print(f"❌ ERREUR lors du diagnostic: {e}")
            import traceback
            traceback.print_exc()

async def suggest_fixes():
    """Suggère des corrections automatiques"""
    print("\n" + "=" * 50)
    print("🛠️  SUGGESTIONS DE CORRECTION")
    print("=" * 50)
    
    async with AsyncSessionLocal() as session:
        # Vérifier si l'utilisateur existe
        result = await session.execute(
            select(User).where(User.email == "<EMAIL>")
        )
        user = result.scalar_one_or_none()
        
        if user:
            # Vérifier les workspaces
            workspace_result = await session.execute(
                select(UserWorkspace).where(UserWorkspace.user_id == user.id)
            )
            user_workspaces = workspace_result.scalars().all()
            
            if not user_workspaces:
                print("🔧 CORRECTION AUTOMATIQUE POSSIBLE:")
                print("   Créer un workspace par défaut et l'associer à l'utilisateur")
                
                # Vérifier s'il existe un workspace par défaut
                default_ws_result = await session.execute(
                    select(Workspace).where(Workspace.name == "Default Workspace")
                )
                default_ws = default_ws_result.scalar_one_or_none()
                
                if default_ws:
                    print(f"   ✅ Workspace par défaut trouvé: {default_ws.name} (ID: {default_ws.id})")
                    print("   💡 Commande pour associer l'utilisateur:")
                    print(f"      python create_user_workspace_association.py {user.id} {default_ws.id}")
                else:
                    print("   ❌ Aucun workspace par défaut trouvé")
                    print("   💡 Commandes pour créer et associer:")
                    print("      1. python create_default_workspace.py")
                    print("      2. python associate_user_to_workspace.py")
        else:
            print("🔧 CORRECTION AUTOMATIQUE POSSIBLE:")
            print("   Créer l'utilisateur <EMAIL> avec un workspace")
            print("   💡 Commande: python create_test_user_complete.py")

if __name__ == "__main__":
    print("🚀 Démarrage du diagnostic...")
    asyncio.run(diagnose_test_user())
    asyncio.run(suggest_fixes())
    print("\n✅ Diagnostic terminé!")
