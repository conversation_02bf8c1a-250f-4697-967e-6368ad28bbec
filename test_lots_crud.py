"""
Test CRUD pour les Lots - Vérification du bon fonctionnement de l'API
"""

import asyncio
import aiohttp
import json
from datetime import datetime
from typing import Dict, Any, Optional

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

class LotCRUDTester:
    def __init__(self):
        self.session = None
        self.auth_token = None
        self.workspace_id = None
        self.project_id = None
        self.created_lots = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def get_headers(self) -> Dict[str, str]:
        """Retourne les headers avec authentification"""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    async def authenticate(self) -> bool:
        """Authentification avec un utilisateur test"""
        try:
            # Utiliser les credentials de test
            auth_data = {
                "email": "<EMAIL>",
                "password": "orbis123!"
            }
            
            async with self.session.post(
                f"{API_BASE}/auth/login",
                json=auth_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.auth_token = data.get("access_token")
                    print(f"✅ Authentification réussie")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Échec authentification: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Erreur authentification: {e}")
            return False
    
    async def get_workspace_and_project(self) -> bool:
        """Récupère un workspace et un projet pour les tests"""
        try:
            # Récupérer les workspaces
            async with self.session.get(
                f"{API_BASE}/workspaces/",
                headers=self.get_headers()
            ) as response:
                if response.status == 200:
                    workspaces = await response.json()
                    if workspaces:
                        self.workspace_id = workspaces[0]["id"]
                        print(f"✅ Workspace trouvé: {self.workspace_id}")
                    else:
                        print("❌ Aucun workspace trouvé")
                        return False
                else:
                    print(f"❌ Erreur récupération workspaces: {response.status}")
                    return False
            
            # Récupérer les projets
            async with self.session.get(
                f"{API_BASE}/projects/",
                headers=self.get_headers()
            ) as response:
                if response.status == 200:
                    projects = await response.json()
                    if projects:
                        self.project_id = projects[0]["id"]
                        print(f"✅ Projet trouvé: {self.project_id}")
                        return True
                    else:
                        print("❌ Aucun projet trouvé")
                        return False
                else:
                    print(f"❌ Erreur récupération projets: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"❌ Erreur récupération workspace/projet: {e}")
            return False
    
    async def test_create_lot(self) -> Optional[Dict[str, Any]]:
        """Test de création d'un lot"""
        print("\n🧪 Test CREATE Lot")
        
        lot_data = {
            "name": f"Lot Test CRUD {datetime.now().strftime('%H%M%S')}",
            "code": f"TEST-{datetime.now().strftime('%H%M%S')}",
            "description": "Lot créé pour tester l'API CRUD",
            "project_id": self.project_id
        }
        
        try:
            async with self.session.post(
                f"{API_BASE}/lots/",
                json=lot_data,
                headers=self.get_headers()
            ) as response:
                if response.status == 201:
                    lot = await response.json()
                    self.created_lots.append(lot["id"])
                    print(f"✅ Lot créé avec succès - ID: {lot['id']}")
                    print(f"   Nom: {lot['name']}")
                    print(f"   Code: {lot['code']}")
                    print(f"   Phase: {lot['current_phase']}")
                    return lot
                else:
                    error_text = await response.text()
                    print(f"❌ Échec création lot: {response.status} - {error_text}")
                    return None
                    
        except Exception as e:
            print(f"❌ Erreur création lot: {e}")
            return None
    
    async def test_get_lots(self) -> bool:
        """Test de récupération de la liste des lots"""
        print("\n🧪 Test GET Lots (liste)")
        
        try:
            async with self.session.get(
                f"{API_BASE}/lots/",
                headers=self.get_headers()
            ) as response:
                if response.status == 200:
                    lots = await response.json()
                    print(f"✅ {len(lots)} lots récupérés")
                    for lot in lots[:3]:  # Afficher les 3 premiers
                        print(f"   - {lot['name']} ({lot['code']}) - Phase: {lot['current_phase']}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Échec récupération lots: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Erreur récupération lots: {e}")
            return False
    
    async def test_get_lot_by_id(self, lot_id: int) -> Optional[Dict[str, Any]]:
        """Test de récupération d'un lot par ID"""
        print(f"\n🧪 Test GET Lot by ID ({lot_id})")
        
        try:
            async with self.session.get(
                f"{API_BASE}/lots/{lot_id}",
                headers=self.get_headers()
            ) as response:
                if response.status == 200:
                    lot = await response.json()
                    print(f"✅ Lot récupéré: {lot['name']}")
                    print(f"   Code: {lot['code']}")
                    print(f"   Phase: {lot['current_phase']}")
                    print(f"   Intervenants: {len(lot.get('intervenants', []))}")
                    print(f"   Documents: {len(lot.get('documents', []))}")
                    return lot
                else:
                    error_text = await response.text()
                    print(f"❌ Échec récupération lot: {response.status} - {error_text}")
                    return None
                    
        except Exception as e:
            print(f"❌ Erreur récupération lot: {e}")
            return None
    
    async def test_update_lot(self, lot_id: int) -> bool:
        """Test de mise à jour d'un lot"""
        print(f"\n🧪 Test UPDATE Lot ({lot_id})")
        
        update_data = {
            "name": f"Lot Modifié {datetime.now().strftime('%H%M%S')}",
            "description": "Description mise à jour par le test CRUD"
        }
        
        try:
            async with self.session.put(
                f"{API_BASE}/lots/{lot_id}",
                json=update_data,
                headers=self.get_headers()
            ) as response:
                if response.status == 200:
                    lot = await response.json()
                    print(f"✅ Lot mis à jour: {lot['name']}")
                    print(f"   Nouvelle description: {lot['description']}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Échec mise à jour lot: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Erreur mise à jour lot: {e}")
            return False
    
    async def test_validate_phase(self, lot_id: int) -> bool:
        """Test de validation de phase"""
        print(f"\n🧪 Test VALIDATE Phase ({lot_id})")
        
        # Valider la phase ESQ
        validation_data = {
            "phase": "ESQ",
            "validated": True
        }
        
        try:
            async with self.session.put(
                f"{API_BASE}/lots/{lot_id}/validate-phase",
                json=validation_data,
                headers=self.get_headers()
            ) as response:
                if response.status == 200:
                    lot = await response.json()
                    print(f"✅ Phase ESQ validée")
                    print(f"   Phase actuelle: {lot['current_phase']}")
                    print(f"   ESQ validé: {lot['esq_validated']}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Échec validation phase: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Erreur validation phase: {e}")
            return False
    
    async def test_get_lots_by_project(self) -> bool:
        """Test de récupération des lots par projet"""
        print(f"\n🧪 Test GET Lots by Project ({self.project_id})")
        
        try:
            async with self.session.get(
                f"{API_BASE}/lots/project/{self.project_id}",
                headers=self.get_headers()
            ) as response:
                if response.status == 200:
                    lots = await response.json()
                    print(f"✅ {len(lots)} lots trouvés pour le projet {self.project_id}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Échec récupération lots par projet: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Erreur récupération lots par projet: {e}")
            return False
    
    async def test_get_lot_stats(self) -> bool:
        """Test de récupération des statistiques"""
        print("\n🧪 Test GET Lot Stats")
        
        try:
            async with self.session.get(
                f"{API_BASE}/lots/stats",
                headers=self.get_headers()
            ) as response:
                if response.status == 200:
                    stats = await response.json()
                    print(f"✅ Statistiques récupérées:")
                    print(f"   Lots actifs: {stats.get('total_active', 0)}")
                    print(f"   Lots inactifs: {stats.get('total_inactive', 0)}")
                    print(f"   Par phase: {stats.get('by_phase', {})}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Échec récupération stats: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Erreur récupération stats: {e}")
            return False
    
    async def test_delete_lot(self, lot_id: int) -> bool:
        """Test de suppression d'un lot"""
        print(f"\n🧪 Test DELETE Lot ({lot_id})")
        
        try:
            async with self.session.delete(
                f"{API_BASE}/lots/{lot_id}",
                headers=self.get_headers()
            ) as response:
                if response.status == 204:
                    print(f"✅ Lot supprimé (soft delete)")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Échec suppression lot: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Erreur suppression lot: {e}")
            return False
    
    async def run_all_tests(self):
        """Exécute tous les tests CRUD"""
        print("🚀 Démarrage des tests CRUD pour les Lots")
        print("=" * 50)
        
        # Authentification
        if not await self.authenticate():
            print("❌ Impossible de s'authentifier, arrêt des tests")
            return
        
        # Récupération workspace et projet
        if not await self.get_workspace_and_project():
            print("❌ Impossible de récupérer workspace/projet, arrêt des tests")
            return
        
        # Tests CRUD
        results = {
            "create": False,
            "read_list": False,
            "read_single": False,
            "update": False,
            "validate_phase": False,
            "read_by_project": False,
            "stats": False,
            "delete": False
        }
        
        # CREATE
        created_lot = await self.test_create_lot()
        if created_lot:
            results["create"] = True
            lot_id = created_lot["id"]
            
            # READ (liste)
            results["read_list"] = await self.test_get_lots()
            
            # READ (single)
            if await self.test_get_lot_by_id(lot_id):
                results["read_single"] = True
            
            # UPDATE
            results["update"] = await self.test_update_lot(lot_id)
            
            # VALIDATE PHASE
            results["validate_phase"] = await self.test_validate_phase(lot_id)
            
            # READ by project
            results["read_by_project"] = await self.test_get_lots_by_project()
            
            # STATS
            results["stats"] = await self.test_get_lot_stats()
            
            # DELETE
            results["delete"] = await self.test_delete_lot(lot_id)
        
        # Résumé des résultats
        print("\n" + "=" * 50)
        print("📊 RÉSUMÉ DES TESTS")
        print("=" * 50)
        
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name.upper():20} {status}")
        
        print(f"\nRésultat global: {passed_tests}/{total_tests} tests réussis")
        
        if passed_tests == total_tests:
            print("🎉 Tous les tests CRUD sont passés avec succès!")
        else:
            print("⚠️  Certains tests ont échoué, vérifiez les logs ci-dessus")


async def main():
    """Fonction principale pour exécuter les tests"""
    async with LotCRUDTester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    print("🧪 Test CRUD pour les Lots")
    print("Assurez-vous que le serveur FastAPI est démarré sur http://localhost:8000")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur lors de l'exécution des tests: {e}")
