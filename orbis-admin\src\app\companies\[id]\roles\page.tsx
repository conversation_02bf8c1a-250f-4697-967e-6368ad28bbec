'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import {
  Building2,
  Shield,
  ArrowLeft,
  Settings,
  Users,
  CheckCircle,
  XCircle,
  Save,
  RotateCcw
} from 'lucide-react'
import { useToast } from '@/components/ui/Toast'
import { FastCompanyService, RBACService, Permission } from '@/lib/fast-auth'
import { useAuth } from '@/contexts/AuthContext'

interface Company {
  id: number
  name: string
  code: string
}

// Permission interface is now imported from fast-auth.ts

interface RolePermissions {
  [roleName: string]: string[]
}

interface PermissionsByResource {
  [resource: string]: Permission[]
}

const ROLE_DESCRIPTIONS = {
  'ADMIN': 'Administrateur d\'entreprise - Accès complet',
  'MANAGER': 'Gestionnaire - Gestion des projets et équipes',
  'USER': 'Utilisateur standard - Accès de base',
  'VIEWER': 'Lecture seule - Consultation uniquement',
  'MOA': 'Maître d\'Ouvrage - Gestion des projets',
  'MOADEL': 'Maître d\'Ouvrage Délégué - Assistance MOA',
  'ARCHI': 'Architecte - Conception et plans',
  'BE': 'Bureau d\'Études - Études techniques',
  'BC': 'Bureau de Contrôle - Contrôle et validation',
  'OPC': 'Ordonnancement Pilotage Coordination',
  'ENT': 'Entreprise - Exécution des travaux',
  'FO': 'Fournisseur - Fourniture de matériaux'
}

const RESOURCE_LABELS = {
  'projects': 'Projets',
  'users': 'Utilisateurs',
  'documents': 'Documents',
  'budgets': 'Budgets',
  'quotes': 'Devis',
  'purchase_orders': 'Commandes',
  'invoices': 'Factures',
  'companies': 'Entreprises',
  'reports': 'Rapports'
}

const ACTION_LABELS = {
  'create': 'Créer',
  'read': 'Voir',
  'update': 'Modifier',
  'delete': 'Supprimer',
  'manage_team': 'Gérer équipe',
  'view_financial': 'Voir finances',
  'invite': 'Inviter',
  'manage_roles': 'Gérer rôles',
  'download': 'Télécharger',
  'upload': 'Uploader',
  'approve': 'Approuver',
  'send': 'Envoyer',
  'validate': 'Valider',
  'pay': 'Marquer payé',
  'manage_settings': 'Gérer paramètres',
  'export': 'Exporter'
}

export default function CompanyRolesPage() {
  const { success, error: showError } = useToast()
  const [company, setCompany] = useState<Company | null>(null)
  const [roles, setRoles] = useState<string[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [rolePermissions, setRolePermissions] = useState<RolePermissions>({})
  const [selectedRole, setSelectedRole] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [originalPermissions, setOriginalPermissions] = useState<RolePermissions>({})
  
  const params = useParams()
  const router = useRouter()
  const companyId = parseInt(params.id as string)
  const { user, loading: authLoading } = useAuth()

  useEffect(() => {
    if (!authLoading && user && companyId) {
      loadData()
    } else if (!authLoading && !user) {
      router.push('/login')
    }
  }, [authLoading, user, companyId])

  const loadData = async () => {
    try {
      setLoading(true)
      
      // Charger les informations de l'entreprise
      const companiesData = await FastCompanyService.getCompanies()
      const foundCompany = companiesData.find(c => c.id === companyId)
      if (!foundCompany) {
        showError('Erreur', 'Entreprise non trouvée')
        router.push('/companies')
        return
      }
      setCompany(foundCompany)

      // Charger les rôles disponibles
      const rolesData = await RBACService.getRoles()
      setRoles(rolesData)
      if (rolesData.length > 0 && !selectedRole) {
        setSelectedRole(rolesData[0])
      }

      // Charger toutes les permissions
      const permissionsData = await RBACService.getPermissions()
      setPermissions(permissionsData)

      // Charger la matrice des permissions pour cette entreprise
      const matrixData = await RBACService.getCompanyPermissionMatrix(companyId)
      setRolePermissions(matrixData.matrix || {})
      setOriginalPermissions(matrixData.matrix || {})

    } catch (error) {
      console.error('❌ Erreur chargement données:', error)
      showError('Erreur', 'Impossible de charger les données')
    } finally {
      setLoading(false)
    }
  }

  const groupPermissionsByResource = (permissions: Permission[]): PermissionsByResource => {
    return permissions.reduce((acc, permission) => {
      if (!acc[permission.resource]) {
        acc[permission.resource] = []
      }
      acc[permission.resource].push(permission)
      return acc
    }, {} as PermissionsByResource)
  }

  const handlePermissionToggle = (roleName: string, permissionName: string) => {
    setRolePermissions(prev => {
      const rolePerms = prev[roleName] || []
      const newPerms = rolePerms.includes(permissionName)
        ? rolePerms.filter(p => p !== permissionName)
        : [...rolePerms, permissionName]
      
      const newRolePermissions = {
        ...prev,
        [roleName]: newPerms
      }
      
      // Vérifier s'il y a des changements
      const hasChanges = JSON.stringify(newRolePermissions) !== JSON.stringify(originalPermissions)
      setHasChanges(hasChanges)
      
      return newRolePermissions
    })
  }

  const savePermissions = async () => {
    if (!selectedRole) return

    try {
      setSaving(true)

      await RBACService.setRolePermissions(companyId, selectedRole, rolePermissions[selectedRole] || [])

      success('Succès', `Permissions du rôle ${selectedRole} mises à jour`)
      setOriginalPermissions({ ...rolePermissions })
      setHasChanges(false)

    } catch (error) {
      console.error('❌ Erreur sauvegarde:', error)
      showError('Erreur', error instanceof Error ? error.message : 'Impossible de sauvegarder les permissions')
    } finally {
      setSaving(false)
    }
  }

  const resetPermissions = () => {
    setRolePermissions({ ...originalPermissions })
    setHasChanges(false)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des rôles et permissions...</p>
        </div>
      </div>
    )
  }

  if (!company) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Entreprise non trouvée</h3>
          <p className="text-gray-600 mb-4">L'entreprise demandée n'existe pas.</p>
          <button 
            onClick={() => router.push('/companies')}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Retour à la liste
          </button>
        </div>
      </div>
    )
  }

  const permissionsByResource = groupPermissionsByResource(permissions)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => router.push(`/companies/${companyId}`)}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          title="Retour aux détails"
        >
          <ArrowLeft className="w-5 h-5 text-gray-600" />
        </button>
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Shield className="w-8 h-8 text-green-600" />
            Gestion des rôles - {company.name}
          </h1>
          <p className="text-gray-600 mt-1">
            Configurez les permissions pour chaque rôle dans cette entreprise
          </p>
        </div>
        
        {/* Actions */}
        {hasChanges && (
          <div className="flex items-center gap-2">
            <button
              onClick={resetPermissions}
              disabled={saving}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
            >
              <RotateCcw className="w-4 h-4" />
              Annuler
            </button>
            <button
              onClick={savePermissions}
              disabled={saving}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
            >
              {saving ? (
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              Sauvegarder
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Liste des rôles */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Users className="w-5 h-5 text-green-600" />
                Rôles ({roles.length})
              </h2>
            </div>
            <div className="p-2">
              {roles.map((role) => (
                <button
                  key={role}
                  onClick={() => setSelectedRole(role)}
                  className={`w-full text-left p-3 rounded-lg transition-colors mb-1 ${
                    selectedRole === role
                      ? 'bg-green-50 border border-green-200 text-green-900'
                      : 'hover:bg-gray-50 text-gray-700'
                  }`}
                >
                  <div className="font-medium">{role}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {ROLE_DESCRIPTIONS[role as keyof typeof ROLE_DESCRIPTIONS] || 'Rôle personnalisé'}
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    {(rolePermissions[role] || []).length} permissions
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Permissions du rôle sélectionné */}
        <div className="lg:col-span-3">
          {selectedRole ? (
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Settings className="w-5 h-5 text-green-600" />
                  Permissions pour le rôle : {selectedRole}
                </h2>
                <p className="text-gray-600 text-sm mt-1">
                  {ROLE_DESCRIPTIONS[selectedRole as keyof typeof ROLE_DESCRIPTIONS] || 'Rôle personnalisé'}
                </p>
                <p className="text-gray-500 text-sm mt-1">
                  {(rolePermissions[selectedRole] || []).length} permissions accordées
                </p>
              </div>
              
              <div className="p-6 space-y-6">
                {Object.entries(permissionsByResource).map(([resource, resourcePermissions]) => (
                  <div key={resource} className="border border-gray-200 rounded-lg">
                    <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                      <h3 className="font-medium text-gray-900">
                        {RESOURCE_LABELS[resource as keyof typeof RESOURCE_LABELS] || resource}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {resourcePermissions.length} actions disponibles
                      </p>
                    </div>
                    <div className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {resourcePermissions.map((permission) => {
                          const isGranted = (rolePermissions[selectedRole] || []).includes(permission.name)
                          return (
                            <label
                              key={permission.name}
                              className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                                isGranted
                                  ? 'bg-green-50 border-green-200 text-green-900'
                                  : 'bg-white border-gray-200 hover:bg-gray-50'
                              }`}
                            >
                              <input
                                type="checkbox"
                                checked={isGranted}
                                onChange={() => handlePermissionToggle(selectedRole, permission.name)}
                                className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2">
                                  {isGranted ? (
                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                  ) : (
                                    <XCircle className="w-4 h-4 text-gray-400" />
                                  )}
                                  <span className="font-medium text-sm">
                                    {ACTION_LABELS[permission.action as keyof typeof ACTION_LABELS] || permission.action}
                                  </span>
                                </div>
                                <p className="text-xs text-gray-500 mt-1 truncate">
                                  {permission.description}
                                </p>
                              </div>
                            </label>
                          )
                        })}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Sélectionnez un rôle</h3>
              <p className="text-gray-600">
                Choisissez un rôle dans la liste de gauche pour voir et modifier ses permissions.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
