#!/usr/bin/env python3
"""
Script pour tester et corriger le problème d'enum
"""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def fix_enum_issue():
    """Corriger le problème d'enum en mettant à jour l'utilisateur existant"""
    
    DATABASE_URL = os.getenv("DATABASE_URL")
    if not DATABASE_URL:
        print("❌ DATABASE_URL non trouvé dans .env")
        return False
    
    print("🔧 Correction du problème d'enum pour l'utilisateur super admin")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        print("✅ Connexion à la base de données réussie")
        
        # 1. Vérifier l'utilisateur actuel
        print("\n1. 🔍 Vérification de l'utilisateur actuel:")
        user = await conn.fetchrow("SELECT * FROM users WHERE email = '<EMAIL>'")
        
        if user:
            print(f"  - ID: {user['id']}")
            print(f"  - Email: {user['email']}")
            print(f"  - Role actuel: {user['role']}")
            print(f"  - Is Superuser: {user['is_superuser']}")
        else:
            print("  ❌ Utilisateur non trouvé")
            return False
        
        # 2. Mettre à jour l'utilisateur pour s'assurer que l'enum est correct
        print("\n2. 🔄 Mise à jour de l'utilisateur...")
        
        await conn.execute("""
            UPDATE users 
            SET role = 'super_admin'::userrole,
                is_superuser = true,
                is_active = true,
                is_verified = true,
                updated_at = NOW()
            WHERE email = '<EMAIL>'
        """)
        
        print("  ✅ Utilisateur mis à jour")
        
        # 3. Vérifier la mise à jour
        print("\n3. ✅ Vérification finale:")
        updated_user = await conn.fetchrow("SELECT id, email, role, is_superuser, is_active FROM users WHERE email = '<EMAIL>'")
        
        if updated_user:
            print(f"  - ID: {updated_user['id']}")
            print(f"  - Email: {updated_user['email']}")
            print(f"  - Role: {updated_user['role']}")
            print(f"  - Is Superuser: {updated_user['is_superuser']}")
            print(f"  - Is Active: {updated_user['is_active']}")
        
        await conn.close()
        print("\n🎉 Correction terminée avec succès!")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_enum_issue())
    if success:
        print("\n✅ Vous pouvez maintenant redémarrer le serveur et tester la connexion.")
    else:
        print("\n❌ Correction échouée.")
