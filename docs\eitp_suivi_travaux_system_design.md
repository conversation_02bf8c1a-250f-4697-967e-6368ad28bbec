# EITP Suivi Travaux - Système SAAS
## Conception Système Complète

### Vue d'ensemble du projet
**Nom du projet :** EITP Suivi Travaux SAAS  
**Type :** Application SAAS de gestion de projets de construction  
**Stack technique :** Python/FastAPI + Next.js/TypeScript + PostgreSQL  
**Architecture :** Multi-tenant avec isolation des données  

## Approche d'implémentation

Nous développerons une plateforme SAAS moderne de gestion de chantiers de construction en utilisant une architecture microservices modulaire :

**Points difficiles identifiés :**
- Gestion multi-tenant avec isolation sécurisée des données pour jusqu'à 10 entreprises internes
- Traitement de gros volumes de données (999,999+ enregistrements par entité)
- Intégration Excel complexe avec mapping flexible des colonnes DPGF
- Système de validation des sous-traitants avec simulation budgétaire
- Architecture de documents hiérarchique avec gestion automatique des dossiers

**Frameworks et bibliothèques open source sélectionnés :**

**Backend (Python/FastAPI) :**
- **FastAPI** : Framework web moderne avec validation automatique et documentation OpenAPI
- **SQLAlchemy 2.0** : ORM avec support async et gestion des relations complexes
- **Alembic** : Migrations de base de données
- **Pydantic v2** : Validation des données et sérialisation
- **FastAPI-Users** : Système d'authentification et autorisation
- **Celery + Redis** : Traitement asynchrone pour l'import Excel et génération de rapports
- **Pandas** : Traitement des fichiers Excel et analyse de données
- **ReportLab** : Génération de PDF
- **boto3** : Stockage de fichiers sur AWS S3 ou compatible
- **asyncpg** : Driver PostgreSQL asynchrone haute performance

**Frontend (Next.js/TypeScript) :**
- **Next.js 14** : Framework React avec App Router et Server Components
- **TypeScript** : Typage statique pour la robustesse
- **Tailwind CSS** : Framework CSS utilitaire pour un design responsive
- **shadcn/ui** : Composants UI modernes et accessibles
- **React Hook Form + Zod** : Gestion des formulaires avec validation
- **TanStack Query (React Query)** : Gestion d'état serveur et cache
- **Zustand** : Gestion d'état client léger
- **React-Table v8** : Tableaux de données avancés avec tri/filtrage
- **Recharts** : Graphiques et visualisations de données
- **react-dropzone** : Upload de fichiers drag & drop

**Base de données :**
- **PostgreSQL 15+** : Base de données principale avec support JSON et extensions
- **Redis** : Cache et sessions utilisateur
- **pgvector** : Extension pour recherche vectorielle (future IA)

**Infrastructure & DevOps :**
- **Docker + Docker Compose** : Conteneurisation
- **Nginx** : Reverse proxy et serveur de fichiers statiques
- **AWS RDS PostgreSQL** : Base de données managée
- **AWS S3** : Stockage de fichiers et documents
- **AWS CloudFront** : CDN pour les assets statiques
- **GitHub Actions** : CI/CD pipeline
- **Sentry** : Monitoring des erreurs
- **Prometheus + Grafana** : Monitoring des performances

## Structures de données et interfaces

Voir fichier séparé : `eitp_suivi_travaux_class_diagram.mermaid`

## Flux d'appel du programme

Voir fichier séparé : `eitp_suivi_travaux_sequence_diagram.mermaid`

## Architecture de déploiement

### Architecture Cloud (AWS)
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CloudFront    │────│   Load Balancer  │────│   EKS Cluster   │
│      (CDN)      │    │      (ALB)       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────────────────────┼─────────────────┐
                       │                                 │                 │
                ┌──────▼──────┐                 ┌────────▼────────┐       │
                │  Frontend   │                 │    Backend      │       │
                │  (Next.js)  │                 │   (FastAPI)     │       │
                │   Pods      │                 │     Pods        │       │
                └─────────────┘                 └─────────────────┘       │
                                                         │                 │
                                               ┌─────────▼─────────┐       │
                                               │     Worker        │       │
                                               │   (Celery)        │       │
                                               │     Pods          │       │
                                               └───────────────────┘       │
                                                         │                 │
        ┌─────────────────┐    ┌─────────────────┐    ┌─▼───────────────┐ │
        │      RDS        │    │      Redis      │    │       S3        │ │
        │  (PostgreSQL)   │    │   (ElastiCache) │    │  (File Storage) │ │
        └─────────────────┘    └─────────────────┘    └─────────────────┘ │
                                                                           │
        ┌─────────────────────────────────────────────────────────────────┘
        │
 ┌──────▼──────┐
 │ Monitoring  │
 │   Stack     │
 │ (Prometheus │
 │  Grafana    │
 │   Sentry)   │
 └─────────────┘
```

### Configuration Multi-tenant

**Architecture de données :**
- **Row-Level Security (RLS)** : Isolation au niveau des lignes avec `company_id`
- **Schémas séparés** : Option pour les gros clients (Enterprise)
- **Connexions de base données poolées** : pgbouncer pour la performance

**Sécurité :**
- **JWT tokens** : Authentication avec refresh tokens
- **RBAC** : Role-Based Access Control granulaire
- **Audit logging** : Traçabilité complète des actions
- **Chiffrement** : TLS 1.3 en transit, AES-256 au repos

## Architecture API (FastAPI)

### Structure des modules
```
app/
├── api/
│   ├── v1/
│   │   ├── endpoints/
│   │   │   ├── auth.py
│   │   │   ├── companies.py
│   │   │   ├── projects.py
│   │   │   ├── employees.py
│   │   │   ├── suppliers.py
│   │   │   ├── materials.py
│   │   │   ├── quotes.py
│   │   │   ├── purchase_orders.py
│   │   │   ├── financials.py
│   │   │   ├── documents.py
│   │   │   └── reports.py
│   │   └── router.py
├── core/
│   ├── config.py
│   ├── security.py
│   ├── database.py
│   └── multi_tenant.py
├── models/
│   ├── base.py
│   ├── user.py
│   ├── company.py
│   ├── project.py
│   └── ...
├── schemas/
│   ├── user.py
│   ├── company.py
│   └── ...
├── services/
│   ├── auth_service.py
│   ├── excel_service.py
│   ├── document_service.py
│   └── ...
└── workers/
    ├── excel_import.py
    ├── report_generation.py
    └── backup_service.py
```

### Endpoints principaux
```
POST   /api/v1/auth/login
POST   /api/v1/auth/refresh
GET    /api/v1/companies
POST   /api/v1/companies
GET    /api/v1/projects
POST   /api/v1/projects
GET    /api/v1/projects/{id}/dashboard
POST   /api/v1/excel/import/dpgf
GET    /api/v1/reports/project/{id}/progress
POST   /api/v1/documents/upload
GET    /api/v1/employees/{id}/timesheet
```

## Architecture Frontend (Next.js)

### Structure des composants
```
src/
├── app/
│   ├── (auth)/
│   │   ├── login/
│   │   └── layout.tsx
│   ├── dashboard/
│   │   ├── page.tsx
│   │   └── loading.tsx
│   ├── companies/
│   ├── projects/
│   ├── employees/
│   ├── suppliers/
│   ├── materials/
│   ├── quotes/
│   ├── purchase-orders/
│   ├── financials/
│   ├── documents/
│   ├── reports/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── ui/
│   │   ├── button.tsx
│   │   ├── form.tsx
│   │   ├── table.tsx
│   │   └── ...
│   ├── layout/
│   │   ├── sidebar.tsx
│   │   ├── header.tsx
│   │   └── navigation.tsx
│   ├── forms/
│   │   ├── project-form.tsx
│   │   ├── employee-form.tsx
│   │   └── ...
│   └── charts/
│       ├── progress-chart.tsx
│       └── financial-chart.tsx
├── lib/
│   ├── api.ts
│   ├── auth.ts
│   ├── utils.ts
│   └── validations.ts
├── hooks/
│   ├── use-auth.ts
│   ├── use-companies.ts
│   └── use-projects.ts
├── store/
│   ├── auth-store.ts
│   ├── company-store.ts
│   └── ui-store.ts
└── types/
    ├── api.ts
    ├── auth.ts
    └── models.ts
```

### Fonctionnalités clés du frontend

**Dashboard principal :**
- Sélecteur d'entreprise dynamique
- Métriques temps réel (projets actifs, employés, budget)
- Graphiques de progression des projets
- Notifications et alertes

**Gestion des formulaires :**
- Validation en temps réel avec Zod
- Auto-sauvegarde automatique
- Upload de fichiers drag & drop
- Mapping dynamique des colonnes Excel

**Tables de données avancées :**
- Tri et filtrage multi-colonnes
- Pagination serveur pour gros datasets
- Export Excel/PDF
- Actions groupées

**Interface responsive :**
- Mobile-first design avec Tailwind CSS
- Navigation collapse pour tablettes
- Optimisation tactile

## Gestion des données volumineuses

**Stratégies de performance :**
- **Pagination** : Limitée à 50-100 enregistrements par page
- **Indexation** : Index composites sur colonnes fréquemment filtrées
- **Cache Redis** : Cache des requêtes fréquentes (30min TTL)
- **Lazy loading** : Chargement différé des composants lourds
- **Virtual scrolling** : Pour les très longues listes

**Optimisations base de données :**
```sql
-- Index composites pour requêtes multi-tenant
CREATE INDEX idx_projects_company_status ON projects(company_id, status, created_at);
CREATE INDEX idx_employees_company_active ON employees(company_id, is_active, last_name);

-- Partitioning pour les gros volumes
CREATE TABLE time_tracking (
    id BIGSERIAL,
    company_id INT NOT NULL,
    created_at TIMESTAMP NOT NULL,
    ...
) PARTITION BY RANGE (created_at);
```

## Sécurité et conformité

**Protection des données (RGPD) :**
- Chiffrement AES-256 au repos
- Anonymisation des données de test
- Logs d'audit détaillés
- Droit à l'oubli implémenté

**Sécurité applicative :**
- Rate limiting : 100 req/min par utilisateur
- Validation stricte des inputs
- Sanitisation des données utilisateur
- Protection CSRF/XSS

**Monitoring et alertes :**
- Health checks applicatifs
- Métriques business (projets créés, utilisateurs actifs)
- Alertes automatiques (erreurs 5xx, latence >2s)

## Intégration Excel avancée

**Service d'import DPGF :**
```python
class DPGFImportService:
    async def import_dpgf(
        self, 
        file: UploadFile, 
        column_mapping: Dict[str, str],
        company_id: int,
        project_id: int
    ) -> ImportResult:
        # Validation du format Excel
        # Mapping dynamique des colonnes
        # Traitement asynchrone avec Celery
        # Rapport d'import détaillé
```

**Champs requis et optionnels :**
- **Obligatoires** : Description travaux, Unité, Quantité, Prix unitaire, Total HT
- **Calculés** : Budget achat, Heures, Taux horaire
- **Optionnels** : Index CCTP, Coefficient de vente

## Gestion documentaire

**Structure hiérarchique :**
```
Entreprise/
├── Projet_001/
│   ├── DCE/
│   ├── DAO/
│   ├── DPGF/
│   ├── FT/
│   ├── EXE/
│   ├── PLANNING/
│   ├── DEVIS/
│   ├── CANDIDATURE/
│   ├── DEMAT/
│   ├── NEGO/
│   └── DIVERS/
```

**Fonctionnalités :**
- Création automatique des dossiers projet
- Versioning des documents
- Métadonnées enrichies (tags, description)
- Recherche full-text avec PostgreSQL
- Génération de PDF avec templates

## Système de validation sous-traitants

**Workflow de validation :**
1. Sélection des travaux à valider
2. Simulation budgétaire temps réel
3. Comparaison avec budget prévisionnel
4. Validation multi-niveaux (Chef de chantier → Chef de projet → Directeur)
5. Intégration automatique dans le budget projet

**Interface de simulation :**
- Sélection interactive des cellules
- Calculs dynamiques JavaScript
- Visualisation des écarts budgétaires
- Historique des validations

## Points peu clairs à clarifier

**Migration des données :**
- Format exact des données de l'application Windows existante ?
- Stratégie de migration : Big Bang ou progressive ?
- Période de coexistence avec l'ancien système ?

**Intégrations tierces :**
- Quels logiciels de comptabilité à intégrer en priorité (Sage, Ciel, EBP) ?
- APIs disponibles pour l'intégration paie ?
- Intégration avec les banques pour les paiements automatiques ?

**Personnalisation métier :**
- Spécificités régionales françaises (DTU, normes) ?
- Templates de documents personnalisés par entreprise ?
- Workflows d'approbation configurables ?

**Performance et scalabilité :**
- Nombre d'utilisateurs concurrents attendus par entreprise ?
- Pics de charge saisonniers dans le BTP ?
- SLA de disponibilité requis (99.9% ? 99.95%) ?

**Conformité et audit :**
- Exigences d'audit comptable spécifiques ?
- Durée de conservation des données légalement requise ?
- Certification sécurité requise (ISO 27001, SOC 2) ?

**Formation et support :**
- Niveau technique des utilisateurs finaux ?
- Support multilingue nécessaire (anglais, espagnol) ?
- Formation sur site ou à distance ?

Ces points nécessitent une clarification avec le client pour affiner l'architecture et les spécifications fonctionnelles.