'use client'

import { ReactNode } from 'react'

interface ModernStatsCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon?: ReactNode
  trend?: {
    value: number
    isPositive: boolean
  }
  gradient: 'blue' | 'purple' | 'orange' | 'green' | 'pink' | 'indigo'
  className?: string
}

// Système de couleurs simplifié avec les 3 tons
const cardClasses = {
  blue: 'bg-ton1',
  purple: 'bg-ton1',
  orange: 'bg-ton1',
  green: 'bg-ton1',
  pink: 'bg-ton1',
  indigo: 'bg-ton1',
}

export default function ModernStatsCard({
  title,
  value,
  subtitle,
  icon,
  trend,
  gradient,
  className = ''
}: ModernStatsCardProps) {
  return (
    <div className={`
      relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1
      ${cardClasses[gradient]} text-white p-6 ${className}
    `}>
      {/* Background Pattern simplifié */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute -right-4 -top-4 w-24 h-24 rounded-full bg-white"></div>
        <div className="absolute -right-8 -bottom-8 w-32 h-32 rounded-full bg-white"></div>
      </div>

      {/* Content */}
      <div className="relative z-10">
        {/* Header simplifié sans icônes */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <h3 className="text-sm font-medium text-white opacity-90">{title}</h3>
          </div>
          {trend && (
            <div className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white">
              <span>{trend.isPositive ? '↗' : '↘'}</span>
              <span>{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>

        {/* Value */}
        <div className="mb-2">
          <div className="text-3xl font-bold text-white mb-1">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </div>
          {subtitle && (
            <div className="text-sm text-white opacity-75">
              {subtitle}
            </div>
          )}
        </div>

        {/* Progress Bar (optional) */}
        {trend && (
          <div className="mt-4">
            <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
              <div 
                className="bg-white rounded-full h-2 transition-all duration-500"
                style={{ width: `${Math.min(Math.abs(trend.value), 100)}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Composants de cartes de statistiques simplifiés sans icônes
export function ProjectStatsCard({ totalProjects, activeProjects }: { totalProjects: number, activeProjects: number }) {
  return (
    <ModernStatsCard
      title="Projets Totaux"
      value={totalProjects}
      subtitle={`${activeProjects} actifs`}
      gradient="blue"
      trend={{ value: 12, isPositive: true }}
    />
  )
}

export function EmployeeStatsCard({ totalEmployees }: { totalEmployees: number }) {
  return (
    <ModernStatsCard
      title="Équipe"
      value={totalEmployees}
      subtitle="employés actifs"
      gradient="purple"
      trend={{ value: 8, isPositive: true }}
    />
  )
}

export function HoursStatsCard({ totalHours }: { totalHours: number }) {
  return (
    <ModernStatsCard
      title="Heures Travaillées"
      value={`${totalHours.toFixed(0)}h`}
      subtitle="ce mois"
      gradient="green"
      trend={{ value: 15, isPositive: true }}
    />
  )
}

export function TasksStatsCard({ pendingTasks }: { pendingTasks: number }) {
  return (
    <ModernStatsCard
      title="Tâches en Attente"
      value={pendingTasks}
      subtitle="à traiter"
      gradient="orange"
      trend={{ value: 5, isPositive: false }}
    />
  )
}
