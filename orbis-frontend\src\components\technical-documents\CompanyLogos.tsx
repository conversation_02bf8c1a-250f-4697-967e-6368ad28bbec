'use client'

import React from 'react'
import Image from 'next/image'
import { BuildingOfficeIcon } from '@heroicons/react/24/outline'

interface Company {
  id: number
  name: string
  code: string
  logo_url?: string
  logo_filename?: string
}

interface CompanyLogosProps {
  companies: Company[]
  className?: string
  logoSize?: 'sm' | 'md' | 'lg'
  showNames?: boolean
  layout?: 'horizontal' | 'vertical' | 'grid'
}

export default function CompanyLogos({
  companies,
  className = '',
  logoSize = 'md',
  showNames = true,
  layout = 'horizontal'
}: CompanyLogosProps) {
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

  // Tailles des logos
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-16 h-16',
    lg: 'w-24 h-24'
  }

  // Classes de layout
  const layoutClasses = {
    horizontal: 'flex flex-wrap items-center gap-4',
    vertical: 'flex flex-col gap-4',
    grid: 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'
  }

  if (!companies || companies.length === 0) {
    return null
  }

  return (
    <div className={`${layoutClasses[layout]} ${className}`}>
      {companies.map((company) => (
        <div
          key={company.id}
          className={`flex ${layout === 'horizontal' ? 'items-center gap-2' : 'flex-col items-center gap-2'}`}
        >
          {/* Logo */}
          <div className={`${sizeClasses[logoSize]} rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center flex-shrink-0`}>
            {company.logo_url ? (
              <Image
                src={`${API_BASE_URL}${company.logo_url}`}
                alt={`Logo ${company.name}`}
                width={logoSize === 'sm' ? 32 : logoSize === 'md' ? 64 : 96}
                height={logoSize === 'sm' ? 32 : logoSize === 'md' ? 64 : 96}
                className="w-full h-full object-contain"
                onError={(e) => {
                  console.error('Erreur de chargement du logo:', company.logo_url)
                  // Masquer l'image et afficher l'icône de fallback
                  e.currentTarget.style.display = 'none'
                  const fallback = e.currentTarget.nextElementSibling as HTMLElement
                  if (fallback) {
                    fallback.style.display = 'block'
                  }
                }}
              />
            ) : null}
            
            {/* Icône de fallback */}
            <BuildingOfficeIcon 
              className={`${logoSize === 'sm' ? 'h-5 w-5' : logoSize === 'md' ? 'h-8 w-8' : 'h-12 w-12'} text-gray-400 ${company.logo_url ? 'hidden' : 'block'}`}
            />
          </div>

          {/* Nom de l'entreprise */}
          {showNames && (
            <div className={`text-center ${layout === 'horizontal' ? 'flex-1' : ''}`}>
              <p className={`font-medium text-gray-900 ${logoSize === 'sm' ? 'text-xs' : logoSize === 'md' ? 'text-sm' : 'text-base'}`}>
                {company.name}
              </p>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

// Composant spécialisé pour l'en-tête de CCTP
export function CCTPHeader({ companies, className = '' }: { companies: Company[], className?: string }) {
  return (
    <div className={`bg-white border-b border-gray-200 p-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Cahier des Clauses Techniques Particulières
          </h1>
          <p className="text-gray-600 mt-1">CCTP</p>
        </div>
        
        {companies && companies.length > 0 && (
          <div className="flex items-center gap-6">
            <CompanyLogos
              companies={companies}
              logoSize="lg"
              showNames={true}
              layout="horizontal"
            />
          </div>
        )}
      </div>
    </div>
  )
}

// Composant pour afficher les logos en pied de page
export function CCTPFooter({ companies, className = '' }: { companies: Company[], className?: string }) {
  return (
    <div className={`bg-gray-50 border-t border-gray-200 p-4 ${className}`}>
      <div className="flex items-center justify-center">
        {companies && companies.length > 0 && (
          <CompanyLogos
            companies={companies}
            logoSize="sm"
            showNames={false}
            layout="horizontal"
          />
        )}
      </div>
    </div>
  )
}
