#!/usr/bin/env python3
"""
Test complet du système d'authentification avec middleware de synchronisation
"""

import requests
import json
from datetime import datetime


# Configuration
API_BASE_URL = "http://localhost:8000"
API_V1_URL = f"{API_BASE_URL}/api/v1"


def print_header(title: str):
    """Affiche un en-tête formaté"""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print(f"{'='*60}")


def print_test_result(test_name: str, success: bool, details: str = ""):
    """Affiche le résultat d'un test"""
    status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
    print(f"{status} - {test_name}")
    if details:
        print(f"   📝 {details}")


def test_api_health():
    """Test de santé de l'API"""
    print_header("Test de Santé de l'API")
    
    try:
        response = requests.get(API_BASE_URL, timeout=5)
        if response.status_code == 200:
            print_test_result("API accessible", True, f"Status: {response.status_code}")
            return True
        else:
            print_test_result("API accessible", False, f"Status: {response.status_code}")
            return False
    except Exception as e:
        print_test_result("API accessible", False, str(e))
        return False


def test_register_user():
    """Test d'inscription avec Supabase"""
    print_header("Test d'Inscription Supabase")
    
    user_data = {
        "email": f"testuser{datetime.now().strftime('%H%M%S')}@gmail.com",
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "User",
        "company": "Test Company",
        "phone": "**********"
    }
    
    try:
        response = requests.post(
            f"{API_V1_URL}/auth/register",
            json=user_data,
            timeout=10
        )
        
        print(f"   📤 Requête envoyée: {user_data['email']}")
        print(f"   📥 Status: {response.status_code}")
        print(f"   📄 Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            result = response.json()
            print_test_result("Inscription", True, f"Utilisateur créé: {user_data['email']}")
            return user_data, result
        else:
            print_test_result("Inscription", False, f"Status: {response.status_code}")
            return None, None
            
    except Exception as e:
        print_test_result("Inscription", False, str(e))
        return None, None


def test_login_user(email: str, password: str):
    """Test de connexion"""
    print_header("Test de Connexion")
    
    login_data = {
        "email": email,
        "password": password
    }
    
    try:
        response = requests.post(
            f"{API_V1_URL}/auth/login",
            json=login_data,
            timeout=10
        )
        
        print(f"   📤 Tentative de connexion: {email}")
        print(f"   📥 Status: {response.status_code}")
        print(f"   📄 Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            result = response.json()
            access_token = result.get("access_token")
            print_test_result("Connexion", True, f"Token reçu: {access_token[:20] if access_token else 'N/A'}...")
            return result
        else:
            print_test_result("Connexion", False, f"Status: {response.status_code}")
            return None
            
    except Exception as e:
        print_test_result("Connexion", False, str(e))
        return None


def test_protected_endpoint_with_middleware(token_data):
    """Test d'endpoint protégé avec middleware"""
    if not token_data:
        return False
    
    print_header("Test d'Endpoint Protégé (avec Middleware)")
    
    access_token = token_data.get("access_token")
    token_type = token_data.get("token_type", "Bearer")
    
    try:
        headers = {
            "Authorization": f"{token_type} {access_token}",
            "Content-Type": "application/json"
        }
        
        # Test de l'endpoint /companies (protégé par le middleware)
        response = requests.get(f"{API_V1_URL}/companies", headers=headers, timeout=10)
        
        print(f"   📤 Requête vers /companies avec token")
        print(f"   📥 Status: {response.status_code}")
        print(f"   📄 Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            companies = response.json()
            print_test_result("Endpoint protégé /companies", True, f"{len(companies)} entreprise(s)")
            return True
        elif response.status_code == 401:
            print_test_result("Endpoint protégé /companies", False, "Token invalide ou expiré")
            return False
        else:
            print_test_result("Endpoint protégé /companies", False, f"Status: {response.status_code}")
            return False
            
    except Exception as e:
        print_test_result("Endpoint protégé", False, str(e))
        return False


def test_middleware_sync():
    """Test de synchronisation du middleware"""
    print_header("Test de Synchronisation Middleware")
    
    # Ce test vérifie que le middleware synchronise bien les utilisateurs
    # en tentant d'accéder à un endpoint protégé
    
    print("   🔄 Le middleware devrait automatiquement:")
    print("      1. Vérifier le token Supabase")
    print("      2. Synchroniser l'utilisateur avec la table locale")
    print("      3. Enrichir la requête avec les données utilisateur")
    
    return True


def main():
    """Fonction principale de test"""
    print("🚀 ORBIS - Test Complet d'Authentification avec Middleware")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 URL de l'API: {API_BASE_URL}")
    
    # Test de santé
    if not test_api_health():
        print("\n❌ L'API n'est pas accessible. Arrêt des tests.")
        return False
    
    # Test d'inscription
    user_data, register_result = test_register_user()
    
    if not user_data:
        print("\n❌ Échec de l'inscription. Impossible de continuer.")
        return False
    
    # Test de connexion
    token_data = test_login_user(user_data["email"], user_data["password"])
    
    if not token_data:
        print("\n❌ Échec de la connexion. Impossible de continuer.")
        return False
    
    # Test du middleware
    test_middleware_sync()
    
    # Test d'endpoint protégé
    protected_success = test_protected_endpoint_with_middleware(token_data)
    
    # Résumé final
    print_header("Résumé des Tests")
    
    if protected_success:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("✅ Le système d'authentification avec middleware fonctionne parfaitement")
        print("\n🔧 Architecture validée:")
        print("   - Supabase Auth pour l'authentification")
        print("   - Middleware de synchronisation")
        print("   - Table users locale pour les métadonnées")
        print("   - Endpoints protégés fonctionnels")
        
        print(f"\n🔑 Identifiants créés pour les tests:")
        print(f"   - Email: {user_data['email']}")
        print(f"   - Mot de passe: {user_data['password']}")
        
        print(f"\n💡 Testez manuellement sur:")
        print(f"   - Documentation: {API_BASE_URL}/docs")
        print(f"   - Redoc: {API_BASE_URL}/redoc")
        
        return True
    else:
        print("❌ ÉCHEC DES TESTS")
        print("🚨 Problème avec le middleware ou les endpoints protégés")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
