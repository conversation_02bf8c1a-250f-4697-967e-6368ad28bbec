"""
Tests CRUD complets pour l'entité User
Tests SQLAlchemy et Pydantic avec authentification Supabase
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from fastapi.testclient import TestClient
from fastapi import status

# Imports des modèles et schémas
from app.models.user import User, UserRole
from app.models.workspace import Workspace, UserWorkspace, WorkspaceRole
from app.schemas.user import UserCreate, UserUpdate, UserResponse, LoginRequest, Token
from app.core.database import get_db, engine
from app.core.security import create_access_token, verify_password, get_password_hash
from app.main import app

# Configuration des tests
pytestmark = pytest.mark.asyncio

class TestUserCRUD:
    """Tests CRUD pour l'entité User"""
    
    @pytest.fixture
    async def db_session(self):
        """Fixture pour la session de base de données"""
        async with AsyncSession(engine) as session:
            yield session
            await session.rollback()
    
    @pytest.fixture
    async def test_workspace(self, db_session: AsyncSession):
        """Fixture pour créer un workspace de test"""
        workspace = Workspace(
            name="Test Workspace",
            code="TEST_WS",
            description="Workspace pour les tests",
            is_active=True
        )
        db_session.add(workspace)
        await db_session.commit()
        await db_session.refresh(workspace)
        return workspace
    
    @pytest.fixture
    def client(self):
        """Client de test FastAPI"""
        return TestClient(app)
    
    # Tests SQLAlchemy
    
    async def test_create_user_sqlalchemy(self, db_session: AsyncSession):
        """Test création d'un utilisateur avec SQLAlchemy"""
        # Données de test
        user_data = {
            "email": "<EMAIL>",
            "hashed_password": get_password_hash("TestPassword123!"),
            "first_name": "John",
            "last_name": "Doe",
            "role": UserRole.EMPLOYE,
            "is_active": True,
            "supabase_user_id": "550e8400-e29b-41d4-a716-446655440000"
        }
        
        # Création
        user = User(**user_data)
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        
        # Vérifications
        assert user.id is not None
        assert user.email == "<EMAIL>"
        assert user.full_name == "John Doe"
        assert user.display_name == "John Doe"
        assert user.role == UserRole.EMPLOYE
        assert user.is_active is True
        assert user.created_at is not None
        assert user.updated_at is not None
        
        # Test des propriétés calculées
        assert user.full_name == "John Doe"
        assert user.display_name == "John Doe"
        assert not user.is_account_locked()
    
    async def test_read_user_sqlalchemy(self, db_session: AsyncSession):
        """Test lecture d'un utilisateur avec SQLAlchemy"""
        # Création d'un utilisateur
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="Jane",
            last_name="Smith",
            role=UserRole.ADMIN
        )
        db_session.add(user)
        await db_session.commit()
        
        # Lecture par ID
        result = await db_session.execute(select(User).where(User.id == user.id))
        found_user = result.scalar_one_or_none()
        
        assert found_user is not None
        assert found_user.email == "<EMAIL>"
        assert found_user.first_name == "Jane"
        
        # Lecture par email
        result = await db_session.execute(select(User).where(User.email == "<EMAIL>"))
        found_user = result.scalar_one_or_none()
        
        assert found_user is not None
        assert found_user.id == user.id
    
    async def test_update_user_sqlalchemy(self, db_session: AsyncSession):
        """Test mise à jour d'un utilisateur avec SQLAlchemy"""
        # Création
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="Old",
            last_name="Name",
            role=UserRole.EMPLOYE
        )
        db_session.add(user)
        await db_session.commit()
        
        # Mise à jour
        user.first_name = "New"
        user.last_name = "Name"
        user.role = UserRole.CHEF_PROJET
        user.phone = "+33123456789"
        
        await db_session.commit()
        await db_session.refresh(user)
        
        # Vérifications
        assert user.first_name == "New"
        assert user.role == UserRole.CHEF_PROJET
        assert user.phone == "+33123456789"
        assert user.updated_at > user.created_at
    
    async def test_delete_user_sqlalchemy(self, db_session: AsyncSession):
        """Test suppression d'un utilisateur avec SQLAlchemy"""
        # Création
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="To",
            last_name="Delete"
        )
        db_session.add(user)
        await db_session.commit()
        user_id = user.id
        
        # Suppression logique (recommandée)
        user.is_active = False
        await db_session.commit()
        
        # Vérification suppression logique
        result = await db_session.execute(select(User).where(User.id == user_id))
        found_user = result.scalar_one_or_none()
        assert found_user is not None
        assert found_user.is_active is False
        
        # Suppression physique (pour les tests)
        await db_session.delete(user)
        await db_session.commit()
        
        # Vérification suppression physique
        result = await db_session.execute(select(User).where(User.id == user_id))
        found_user = result.scalar_one_or_none()
        assert found_user is None
    
    async def test_user_workspace_relationship(self, db_session: AsyncSession, test_workspace: Workspace):
        """Test des relations User-Workspace"""
        # Création utilisateur
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="Workspace",
            last_name="User"
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        
        # Association avec workspace
        user_workspace = UserWorkspace(
            user_id=user.id,
            workspace_id=test_workspace.id,
            role_name="ADMIN",
            is_default=True,
            is_active=True
        )
        db_session.add(user_workspace)
        await db_session.commit()
        
        # Vérification de la relation
        await db_session.refresh(user)
        assert len(user.workspaces) == 1
        assert user.workspaces[0].workspace_id == test_workspace.id
        assert user.workspaces[0].role_name == "ADMIN"
    
    # Tests Pydantic
    
    def test_user_create_schema_validation(self):
        """Test validation du schéma UserCreate"""
        # Données valides
        valid_data = {
            "email": "<EMAIL>",
            "password": "ValidPass123!",
            "first_name": "John",
            "last_name": "Doe"
        }
        
        user_create = UserCreate(**valid_data)
        assert user_create.email == "<EMAIL>"
        assert user_create.full_name == "John Doe"
        
        # Test validation mot de passe faible
        with pytest.raises(ValueError, match="Password must contain at least one uppercase letter"):
            UserCreate(
                email="<EMAIL>",
                password="weakpass",
                first_name="John",
                last_name="Doe"
            )
        
        # Test validation email invalide
        with pytest.raises(ValueError):
            UserCreate(
                email="invalid-email",
                password="ValidPass123!",
                first_name="John",
                last_name="Doe"
            )
    
    def test_user_update_schema_validation(self):
        """Test validation du schéma UserUpdate"""
        # Mise à jour partielle valide
        update_data = {
            "first_name": "Updated",
            "phone": "+33123456789"
        }
        
        user_update = UserUpdate(**update_data)
        assert user_update.first_name == "Updated"
        assert user_update.phone == "+33123456789"
        assert user_update.email is None  # Non modifié
        
        # Test validation mot de passe optionnel
        user_update = UserUpdate(password="NewValidPass123!")
        assert user_update.password == "NewValidPass123!"
        
        # Test validation mot de passe faible
        with pytest.raises(ValueError):
            UserUpdate(password="weak")
    
    def test_user_response_schema(self):
        """Test du schéma de réponse UserResponse"""
        user_data = {
            "id": 1,
            "email": "<EMAIL>",
            "first_name": "Response",
            "last_name": "User",
            "role": UserRole.ADMIN,
            "is_active": True,
            "created_at": datetime.utcnow(),
            "permissions": ["users.read", "users.write"],
            "can_manage_users": True
        }
        
        user_response = UserResponse(**user_data)
        assert user_response.id == 1
        assert user_response.display_name == "Response User"
        assert "users.read" in user_response.permissions
        assert user_response.can_manage_users is True
    
    # Tests API avec authentification
    
    async def test_create_user_api(self, client: TestClient, db_session: AsyncSession):
        """Test création utilisateur via API"""
        # Données de création
        user_data = {
            "email": "<EMAIL>",
            "password": "ApiPassword123!",
            "first_name": "API",
            "last_name": "User",
            "role": "EMPLOYE"
        }
        
        # Création d'un token admin pour l'autorisation
        admin_token = create_access_token(
            data={"sub": "1", "role": "SUPER_ADMIN", "permissions": ["users.create"]}
        )
        
        # Requête de création
        response = client.post(
            "/api/v1/admin/users/",
            json=user_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data["email"] == "<EMAIL>"
        assert response_data["full_name"] == "API User"
        assert "id" in response_data
    
    async def test_login_user_api(self, client: TestClient, db_session: AsyncSession):
        """Test connexion utilisateur via API"""
        # Création d'un utilisateur de test
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("LoginPass123!"),
            first_name="Login",
            last_name="User",
            is_active=True,
            is_verified=True
        )
        db_session.add(user)
        await db_session.commit()
        
        # Tentative de connexion
        login_data = {
            "email": "<EMAIL>",
            "password": "LoginPass123!"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "access_token" in response_data
        assert response_data["token_type"] == "bearer"
        assert response_data["user"]["email"] == "<EMAIL>"
    
    async def test_get_current_user_api(self, client: TestClient, db_session: AsyncSession):
        """Test récupération utilisateur courant via API"""
        # Création d'un utilisateur
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="Current",
            last_name="User",
            is_active=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        
        # Création du token
        token = create_access_token(data={"sub": str(user.id)})
        
        # Requête
        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["email"] == "<EMAIL>"
        assert response_data["full_name"] == "Current User"
    
    # Tests de sécurité et permissions
    
    async def test_user_permissions_system(self, db_session: AsyncSession):
        """Test du système de permissions utilisateur"""
        # Utilisateur super admin
        super_admin = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="Super",
            last_name="Admin",
            role=UserRole.SUPER_ADMIN
        )
        
        # Utilisateur normal
        normal_user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="Normal",
            last_name="User",
            role=UserRole.EMPLOYE
        )
        
        db_session.add_all([super_admin, normal_user])
        await db_session.commit()
        
        # Tests permissions système
        assert super_admin.has_system_permission("any.permission") is True
        assert normal_user.has_system_permission("any.permission") is False
    
    async def test_account_locking(self, db_session: AsyncSession):
        """Test du verrouillage de compte"""
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="Locked",
            last_name="User",
            failed_login_attempts=5,
            locked_until=datetime.utcnow() + timedelta(hours=1)
        )
        db_session.add(user)
        await db_session.commit()
        
        # Vérification du verrouillage
        assert user.is_account_locked() is True
        
        # Déverrouillage
        user.locked_until = datetime.utcnow() - timedelta(hours=1)
        assert user.is_account_locked() is False
    
    # Tests d'intégration multi-tenant
    
    async def test_multi_tenant_user_isolation(self, db_session: AsyncSession):
        """Test isolation des utilisateurs par workspace"""
        # Création de deux workspaces
        workspace1 = Workspace(name="Workspace 1", code="WS1")
        workspace2 = Workspace(name="Workspace 2", code="WS2")
        db_session.add_all([workspace1, workspace2])
        await db_session.commit()
        
        # Création d'un utilisateur
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="Multi",
            last_name="Tenant"
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        
        # Association avec workspace1 seulement
        user_workspace1 = UserWorkspace(
            user_id=user.id,
            workspace_id=workspace1.id,
            role_name="ADMIN",
            is_active=True
        )
        db_session.add(user_workspace1)
        await db_session.commit()
        
        # Vérification isolation
        await db_session.refresh(user)
        workspace_ids = [uw.workspace_id for uw in user.workspaces if uw.is_active]
        assert workspace1.id in workspace_ids
        assert workspace2.id not in workspace_ids
    
    # Tests de performance
    
    async def test_bulk_user_operations(self, db_session: AsyncSession):
        """Test des opérations en lot sur les utilisateurs"""
        # Création en lot
        users = []
        for i in range(100):
            user = User(
                email=f"bulk{i}@example.com",
                hashed_password=get_password_hash("password"),
                first_name=f"User{i}",
                last_name="Bulk"
            )
            users.append(user)
        
        db_session.add_all(users)
        await db_session.commit()
        
        # Lecture en lot
        result = await db_session.execute(
            select(User).where(User.email.like("<EMAIL>"))
        )
        found_users = result.scalars().all()
        
        assert len(found_users) == 100
        
        # Nettoyage
        await db_session.execute(
            delete(User).where(User.email.like("<EMAIL>"))
        )
        await db_session.commit()


# Tests de régression et edge cases
class TestUserEdgeCases:
    """Tests des cas limites et de régression"""
    
    async def test_user_with_empty_names(self, db_session: AsyncSession):
        """Test utilisateur avec noms vides"""
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="",
            last_name=""
        )
        db_session.add(user)
        await db_session.commit()
        
        # Le display_name devrait fallback sur l'email
        assert user.full_name == " "  # Espace entre prénom et nom vides
        assert user.display_name == "<EMAIL>"
    
    async def test_duplicate_email_constraint(self, db_session: AsyncSession):
        """Test contrainte d'unicité sur l'email"""
        user1 = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="First",
            last_name="User"
        )
        db_session.add(user1)
        await db_session.commit()
        
        # Tentative de création d'un utilisateur avec le même email
        user2 = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            first_name="Second",
            last_name="User"
        )
        db_session.add(user2)
        
        with pytest.raises(Exception):  # IntegrityError attendue
            await db_session.commit()


if __name__ == "__main__":
    # Exécution des tests
    pytest.main([__file__, "-v"])
