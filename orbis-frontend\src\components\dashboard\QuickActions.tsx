import React from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'

interface QuickAction {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  gradient: string
  href: string
}

interface QuickActionsProps {
  actions?: QuickAction[]
}

export const QuickActions: React.FC<QuickActionsProps> = ({ actions }) => {
  const defaultActions: QuickAction[] = [
    {
      id: 'new-project',
      title: 'Nouveau Projet',
      description: 'Créer un nouveau projet BTP',
      gradient: 'from-blue-500 to-blue-600',
      href: '/projects/create',
      icon: (
        <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      )
    },
    {
      id: 'add-employee',
      title: 'Ajouter Employé',
      description: 'Inviter un nouvel employé',
      gradient: 'from-emerald-500 to-emerald-600',
      href: '/employees/create',
      icon: (
        <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
        </svg>
      )
    },
    {
      id: 'upload-document',
      title: 'Télécharger Document',
      description: 'Ajouter plans et fichiers',
      gradient: 'from-purple-500 to-purple-600',
      href: '/documents/upload',
      icon: (
        <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
      )
    },
    {
      id: 'generate-report',
      title: 'Générer Rapport',
      description: 'Créer un rapport détaillé',
      gradient: 'from-amber-500 to-amber-600',
      href: '/reports/generate',
      icon: (
        <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0-6l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
        </svg>
      )
    }
  ]

  const displayActions = actions || defaultActions

  return (
    <Card className="p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Actions Rapides</h3>
        <p className="text-sm text-gray-500 mt-1">Raccourcis vers les fonctionnalités principales</p>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {displayActions.map((action, index) => (
          <Link key={action.id} href={action.href}>
            <div 
              className="group relative overflow-hidden rounded-xl p-6 bg-white border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:scale-[1.02] hover:shadow-lg cursor-pointer animate-fadeIn"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Background gradient overlay */}
              <div className={`absolute inset-0 bg-gradient-to-br ${action.gradient} opacity-0 group-hover:opacity-5 transition-opacity`}></div>
              
              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-br ${action.gradient} text-white shadow-lg group-hover:scale-110 transition-transform`}>
                    {action.icon}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 group-hover:text-gray-700 transition-colors">
                      {action.title}
                    </h4>
                    <p className="text-sm text-gray-600 mt-1">
                      {action.description}
                    </p>
                  </div>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <svg className="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </Card>
  )
}