#!/usr/bin/env python3
"""
Test du frontend avec le système d'authentification hybride
"""

import requests
import json

FRONTEND_BASE = "http://localhost:3001"
API_BASE = "http://localhost:8000"

def test_frontend_login_flow():
    """Test du flow de connexion complet frontend + backend"""
    print("🌐 Test du flow frontend + backend\n")
    
    # 1. Test de connexion via l'API backend
    print("🔐 1. Test de connexion backend...")
    response = requests.post(f"{API_BASE}/api/v1/fast-auth/login", json={
        "email": "<EMAIL>",
        "password": "orbis123!"
    })
    
    if response.status_code != 200:
        print(f"❌ Erreur connexion: {response.text}")
        return False
    
    data = response.json()
    token = data['access_token']
    print(f"✅ Connexion réussie - Token: {token[:50]}...")
    
    # 2. Test d'accès aux companies avec le token
    print("\n🏢 2. Test d'accès aux companies...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{API_BASE}/api/v1/admin/companies/", headers=headers)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        companies = response.json()
        print(f"✅ Accès autorisé - {len(companies)} companies")
        return True
    elif response.status_code == 401:
        print(f"❌ Erreur d'authentification: {response.text}")
        return False
    elif response.status_code == 500:
        print(f"⚠️  Erreur serveur (probablement base de données): {response.text[:200]}...")
        print("✅ Authentification OK, mais erreur DB")
        return True
    else:
        print(f"❌ Erreur inattendue: {response.text}")
        return False

def test_frontend_accessibility():
    """Test d'accessibilité du frontend"""
    print("\n🌐 3. Test d'accessibilité frontend...")
    
    try:
        response = requests.get(FRONTEND_BASE, timeout=5)
        if response.status_code == 200:
            print("✅ Frontend accessible")
            return True
        else:
            print(f"❌ Frontend erreur: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend inaccessible: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test complet Frontend + Backend\n")
    
    # Test d'accessibilité
    frontend_ok = test_frontend_accessibility()
    
    # Test du flow d'authentification
    auth_ok = test_frontend_login_flow()
    
    print(f"\n📊 Résultats:")
    print(f"Frontend accessible: {'✅' if frontend_ok else '❌'}")
    print(f"Authentification: {'✅' if auth_ok else '❌'}")
    
    if frontend_ok and auth_ok:
        print("\n🎉 Système hybride opérationnel !")
        print("👉 Vous pouvez maintenant tester manuellement sur http://localhost:3001")
    else:
        print("\n❌ Des problèmes subsistent")
