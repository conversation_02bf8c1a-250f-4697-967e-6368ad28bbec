#!/usr/bin/env python3
"""
Test script pour vérifier que l'API des documents techniques fonctionne correctement
avec la structure basée sur les lots (lot_id au lieu de project_id)
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'fastapi_template'))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.core.database import get_db
from app.models.document import TechnicalDocument, DocumentType
from app.models.lot import Lot
from app.models.project import Project
from app.models.workspace import Workspace
from app.models.user import User

async def test_technical_documents_structure():
    """Test la structure des documents techniques avec lot_id"""
    print("🔍 Test de la structure des documents techniques...")
    
    async for db in get_db():
        try:
            # 1. Vérifier qu'on peut créer un document technique avec lot_id
            print("\n1. Test de création d'un document technique...")
            
            # Récupérer un lot existant
            lot_result = await db.execute(
                select(Lot).limit(1)
            )
            lot = lot_result.scalar_one_or_none()
            
            if not lot:
                print("❌ Aucun lot trouvé dans la base de données")
                return False
                
            print(f"✅ Lot trouvé: {lot.name} (ID: {lot.id})")
            
            # Récupérer un utilisateur pour created_by
            user_result = await db.execute(
                select(User).limit(1)
            )
            user = user_result.scalar_one_or_none()
            
            if not user:
                print("❌ Aucun utilisateur trouvé dans la base de données")
                return False
                
            print(f"✅ Utilisateur trouvé: {user.email} (ID: {user.id})")
            
            # Créer un document technique de test
            test_document = TechnicalDocument(
                name="Document de test - CCTP",
                type_document=DocumentType.CCTP,
                content="<p>Contenu de test pour le CCTP</p>",
                lot_id=lot.id,
                created_by=user.id,
                is_active=True
            )
            
            db.add(test_document)
            await db.flush()
            
            print(f"✅ Document technique créé avec succès (ID: {test_document.id})")
            
            # 2. Vérifier qu'on peut récupérer le document avec ses relations
            print("\n2. Test de récupération avec relations...")
            
            result = await db.execute(
                select(TechnicalDocument)
                .where(TechnicalDocument.id == test_document.id)
            )
            retrieved_doc = result.scalar_one_or_none()
            
            if not retrieved_doc:
                print("❌ Document non trouvé après création")
                return False
                
            print(f"✅ Document récupéré: {retrieved_doc.name}")
            print(f"   - Type: {retrieved_doc.type_document}")
            print(f"   - Lot ID: {retrieved_doc.lot_id}")
            print(f"   - Créé par: {retrieved_doc.created_by}")
            
            # 3. Vérifier la relation avec le lot
            print("\n3. Test de la relation avec le lot...")
            
            result = await db.execute(
                select(TechnicalDocument)
                .join(Lot)
                .where(TechnicalDocument.id == test_document.id)
            )
            doc_with_lot = result.scalar_one_or_none()
            
            if not doc_with_lot:
                print("❌ Impossible de joindre le document avec son lot")
                return False
                
            print(f"✅ Relation lot vérifiée")
            
            # 4. Vérifier qu'on peut filtrer par lot
            print("\n4. Test de filtrage par lot...")
            
            result = await db.execute(
                select(TechnicalDocument)
                .where(TechnicalDocument.lot_id == lot.id)
            )
            docs_in_lot = result.scalars().all()
            
            print(f"✅ {len(docs_in_lot)} document(s) trouvé(s) dans le lot {lot.name}")
            
            # 5. Vérifier qu'on peut filtrer par projet via les lots
            print("\n5. Test de filtrage par projet via les lots...")
            
            result = await db.execute(
                select(TechnicalDocument)
                .join(Lot)
                .where(Lot.project_id == lot.project_id)
            )
            docs_in_project = result.scalars().all()
            
            print(f"✅ {len(docs_in_project)} document(s) trouvé(s) dans le projet via les lots")
            
            # Nettoyer le document de test
            await db.delete(test_document)
            await db.commit()
            
            print("\n✅ Tous les tests sont passés avec succès!")
            print("✅ La structure des documents techniques avec lot_id fonctionne correctement")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test: {str(e)}")
            import traceback
            traceback.print_exc()
            await db.rollback()
            return False

async def test_schema_compatibility():
    """Test la compatibilité des schémas"""
    print("\n🔍 Test de compatibilité des schémas...")
    
    try:
        from app.schemas.technical_document import (
            TechnicalDocumentCreate,
            TechnicalDocumentResponse,
            TechnicalDocumentList,
            LotSimple
        )
        
        # Test de création d'un schéma avec lot_id
        create_schema = TechnicalDocumentCreate(
            name="Test Document",
            type_document=DocumentType.CCTP,
            lot_id=1,
            company_ids=[]
        )
        
        print(f"✅ Schéma de création valide: {create_schema.name}")
        print(f"   - Type: {create_schema.type_document}")
        print(f"   - Lot ID: {create_schema.lot_id}")
        
        # Test du schéma LotSimple
        lot_simple = LotSimple(
            id=1,
            name="Test Lot",
            code="LOT001",
            project_id=1
        )
        
        print(f"✅ Schéma LotSimple valide: {lot_simple.name}")
        
        print("✅ Tous les schémas sont compatibles!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur de compatibilité des schémas: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Fonction principale de test"""
    print("🚀 Test de correction des documents techniques")
    print("=" * 50)
    
    # Test de la structure de base de données
    db_test = await test_technical_documents_structure()
    
    # Test de compatibilité des schémas
    schema_test = await test_schema_compatibility()
    
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    print(f"Structure BDD: {'✅ PASS' if db_test else '❌ FAIL'}")
    print(f"Schémas:       {'✅ PASS' if schema_test else '❌ FAIL'}")
    
    if db_test and schema_test:
        print("\n🎉 TOUS LES TESTS SONT PASSÉS!")
        print("✅ Les documents techniques utilisent maintenant lot_id au lieu de project_id")
        print("✅ L'erreur 'project_id is an invalid keyword argument' est corrigée")
        return True
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
