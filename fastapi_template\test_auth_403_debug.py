"""
Script pour déboguer le problème 403 de l'utilisateur <EMAIL>
Test l'authentification et l'accès aux endpoints
"""

import requests
import json

# Configuration de base
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "password123"  # Mot de passe par défaut

def test_authentication_flow():
    """Test complet du flux d'authentification"""
    print("🔐 TEST D'AUTHENTIFICATION COMPLET")
    print("=" * 50)
    
    # 1. Test de connexion (login)
    print("\n1️⃣ TEST DE CONNEXION")
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        login_response = requests.post(
            f"{BASE_URL}/api/v1/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {login_response.status_code}")
        print(f"Response: {login_response.text}")
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data.get("access_token")
            print(f"✅ Connexion réussie!")
            print(f"🔑 Token obtenu: {access_token[:50]}...")
            
            # 2. Test d'accès aux workspaces
            print("\n2️⃣ TEST D'ACCÈS AUX WORKSPACES")
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            workspace_response = requests.get(
                f"{BASE_URL}/api/v1/workspaces/",
                headers=headers
            )
            
            print(f"Status Code: {workspace_response.status_code}")
            print(f"Response: {workspace_response.text}")
            
            if workspace_response.status_code == 200:
                print("✅ Accès aux workspaces réussi!")
                workspaces = workspace_response.json()
                print(f"📁 Workspaces trouvés: {len(workspaces)}")
                for ws in workspaces:
                    print(f"   - {ws.get('name', 'N/A')} (ID: {ws.get('id', 'N/A')})")
            else:
                print("❌ ERREUR 403 - Accès aux workspaces refusé!")
                print("🔍 Analysons les headers de la requête...")
                print(f"Headers envoyés: {headers}")
                
                # 3. Test de validation du token
                print("\n3️⃣ TEST DE VALIDATION DU TOKEN")
                me_response = requests.get(
                    f"{BASE_URL}/api/v1/auth/me",
                    headers=headers
                )
                print(f"Status Code /me: {me_response.status_code}")
                print(f"Response /me: {me_response.text}")
                
                # 4. Test d'autres endpoints
                print("\n4️⃣ TEST D'AUTRES ENDPOINTS")
                
                # Test projects
                projects_response = requests.get(
                    f"{BASE_URL}/api/v1/projects/",
                    headers=headers
                )
                print(f"Status Code /projects: {projects_response.status_code}")
                print(f"Response /projects: {projects_response.text[:200]}...")
                
        else:
            print("❌ ERREUR DE CONNEXION!")
            if login_response.status_code == 401:
                print("🔍 Problème d'authentification - vérifier le mot de passe")
            elif login_response.status_code == 404:
                print("🔍 Endpoint de login non trouvé")
            else:
                print(f"🔍 Erreur inattendue: {login_response.status_code}")
                
    except requests.exceptions.ConnectionError:
        print("❌ ERREUR DE CONNEXION - Le serveur n'est pas accessible")
        print("🔍 Vérifiez que le serveur FastAPI tourne sur http://localhost:8000")
    except Exception as e:
        print(f"❌ ERREUR INATTENDUE: {e}")

def test_different_passwords():
    """Test avec différents mots de passe possibles"""
    print("\n" + "=" * 50)
    print("🔑 TEST AVEC DIFFÉRENTS MOTS DE PASSE")
    print("=" * 50)
    
    possible_passwords = [
        "password123",
        "test123",
        "orbis123",
        "admin123",
        "password",
        "test"
    ]
    
    for password in possible_passwords:
        print(f"\n🔐 Test avec mot de passe: {password}")
        login_data = {
            "email": TEST_EMAIL,
            "password": password
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/v1/auth/login",
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                print(f"✅ SUCCÈS avec le mot de passe: {password}")
                return password
            else:
                print(f"❌ Échec ({response.status_code})")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    print("\n❌ Aucun mot de passe ne fonctionne!")
    return None

def test_api_endpoints():
    """Test des différents endpoints de l'API"""
    print("\n" + "=" * 50)
    print("🌐 TEST DES ENDPOINTS API")
    print("=" * 50)
    
    endpoints_to_test = [
        "/api/v1/auth/login",
        "/api/v1/workspaces/",
        "/api/v1/projects/",
        "/api/v1/users/me",
        "/docs",
        "/health"
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            print(f"📍 {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"📍 {endpoint}: ERREUR - {e}")

if __name__ == "__main__":
    print("🚀 Démarrage du debug d'authentification...")
    
    # Test de base des endpoints
    test_api_endpoints()
    
    # Test avec le mot de passe par défaut
    test_authentication_flow()
    
    # Test avec différents mots de passe
    working_password = test_different_passwords()
    
    if working_password:
        print(f"\n✅ Mot de passe fonctionnel trouvé: {working_password}")
        print("🔄 Relancement du test complet avec le bon mot de passe...")
        # Relancer le test avec le bon mot de passe
        global TEST_PASSWORD
        TEST_PASSWORD = working_password
        test_authentication_flow()
    
    print("\n✅ Debug terminé!")
