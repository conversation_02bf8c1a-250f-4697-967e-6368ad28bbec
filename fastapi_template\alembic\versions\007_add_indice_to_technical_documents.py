"""Add indice field to technical_documents

Revision ID: 007
Revises: 006
Create Date: 2025-01-16 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '007'
down_revision = '006'
branch_labels = None
depends_on = None


def upgrade():
    """Add indice column to technical_documents table"""
    # Add the indice column with default value
    op.add_column('technical_documents', 
                  sa.Column('indice', sa.String(length=10), nullable=False, server_default='01'))
    
    # Remove the server_default after adding the column
    op.alter_column('technical_documents', 'indice', server_default=None)


def downgrade():
    """Remove indice column from technical_documents table"""
    op.drop_column('technical_documents', 'indice')
