/**
 * Service de génération de page de garde basé sur des templates JSON
 */

import { TemplateRenderer, Template } from './templateRenderer'
import coverPageTemplate from './templates/coverPageTemplate.json'

export interface CoverPageData {
  lot: {
    id: number
    name: string
    project?: {
      id: number
      name: string
    }
  }
  documentType: string
  documentIndice: string
  companies: Array<{
    id: number
    name: string
    role: string
    address?: string
    logo_url?: string | null
  }>
}

/**
 * Générer le HTML de la page de garde à partir d'un template JSON
 */
export function generateTemplateCoverPageHTML(data: CoverPageData): string {
  console.log('🎨 GÉNÉRATION TEMPLATE - Début')
  console.log('📊 Données reçues:', data)

  try {
    // Préparer les données pour le template
    const templateData = prepareTemplateData(data)
    console.log('📋 Données préparées pour le template:', templateData)

    // Créer le renderer et générer le HTML
    const renderer = new TemplateRenderer(templateData)
    const html = renderer.render(coverPageTemplate as Template)

    console.log('✅ HTML généré avec succès')
    console.log('📄 Longueur du HTML:', html.length)
    
    return html

  } catch (error) {
    console.error('❌ Erreur lors de la génération du template:', error)
    
    // Fallback vers un template minimal
    return generateFallbackHTML(data)
  }
}

/**
 * Préparer les données pour le template
 */
function prepareTemplateData(data: CoverPageData): Record<string, any> {
  // Trouver les entreprises par rôle
  const moaCompany = data.companies.find(c => c.role === 'MOA')
  const moeCompany = data.companies.find(c => c.role === 'MOADEL' || c.role === 'ARCHI')
  const otherCompanies = data.companies.filter(c => 
    !['MOA', 'MOADEL', 'ARCHI'].includes(c.role)
  )

  // Déterminer le titre du document
  const getDocumentTitle = (type: string): string => {
    switch (type.toUpperCase()) {
      case 'CCTP': return 'CAHIER DES CLAUSES TECHNIQUES PARTICULIÈRES'
      case 'DPGF': return 'DÉCOMPOSITION DU PRIX GLOBAL ET FORFAITAIRE'
      case 'CCAP': return 'CAHIER DES CLAUSES ADMINISTRATIVES PARTICULIÈRES'
      default: return type.toUpperCase()
    }
  }

  // Construire les URLs complètes des logos
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
  const moaLogoUrl = moaCompany?.logo_url ? `${baseUrl}${moaCompany.logo_url}` : null
  const moeLogoUrl = moeCompany?.logo_url ? `${baseUrl}${moeCompany.logo_url}` : null

  return {
    // Titre et projet
    title: getDocumentTitle(data.documentType),
    projectName: data.lot.project?.name || 'Projet non défini',

    // Maître d'ouvrage
    moaName: moaCompany?.name || 'Non défini',
    moaAddress: moaCompany?.address || '',
    moaLogoUrl: moaLogoUrl,

    // Maîtrise d'œuvre
    moeName: moeCompany?.name || 'Non défini',
    moeLogoUrl: moeLogoUrl,
    
    // Autres entreprises
    hasOtherCompanies: otherCompanies.length > 0,
    otherCompanies: otherCompanies.map(company => ({
      name: company.name,
      role: getRoleDisplayName(company.role)
    })),
    
    // Lot
    lotTitle: `LOT ${data.lot.id} - ${data.lot.name}`,
    
    // Document
    documentType: getDocumentTitle(data.documentType),
    documentIndice: data.documentIndice || 'A',
    documentDate: new Date().toLocaleDateString('fr-FR')
  }
}

/**
 * Convertir les codes de rôle en noms affichables
 */
function getRoleDisplayName(role: string): string {
  const roleNames: Record<string, string> = {
    'MOA': 'Maître d\'ouvrage',
    'MOADEL': 'Maître d\'ouvrage délégué',
    'ARCHI': 'Architecte',
    'BE': 'Bureau d\'études',
    'BC': 'Bureau de contrôle',
    'OPC': 'Coordinateur OPC',
    'ENT': 'Entreprise',
    'FO': 'Fournisseur'
  }
  
  return roleNames[role] || role
}

/**
 * Template de fallback en cas d'erreur
 */
function generateFallbackHTML(data: CoverPageData): string {
  console.log('⚠️ Utilisation du template de fallback')
  
  const moaCompany = data.companies.find(c => c.role === 'MOA')
  const projectName = data.lot.project?.name || 'Projet non défini'
  
  return `
    <!-- FALLBACK TEMPLATE -->
    <div style="page-break-after: always; font-family: Arial, sans-serif !important; padding: 15px; margin: 0; background-color: white; text-align: center !important;">
      <div style="display: inline-block; text-align: center !important; max-width: 1200px;">
        
        <!-- Titre principal -->
        <table style="width: 100% !important; border-collapse: collapse !important; border: 2px solid #000 !important; margin-bottom: 10px !important; font-family: Arial, sans-serif !important;">
          <tr>
            <td style="padding: 30px; text-align: center !important; background-color: white; border: none;">
              <div style="font-size: 30px !important; font-weight: bold; color: #333; margin-bottom: 8px; text-align: center !important;">
                CAHIER DES CLAUSES TECHNIQUES PARTICULIÈRES
              </div>
              <div style="font-size: 25px !important; color: #333; text-align: center !important;">
                ${projectName}
              </div>
            </td>
          </tr>
        </table>

        <!-- Maître d'ouvrage -->
        <table style="width: 100% !important; border-collapse: collapse !important; border: 2px solid #000 !important; margin-bottom: 10px !important;">
          <tr>
            <td style="padding: 8px; text-align: center !important; background-color: #f0f0f0; font-weight: bold; font-size: 14px; border: none;">
              MAITRE D'OUVRAGE
            </td>
          </tr>
          <tr>
            <td style="padding: 15px; text-align: center !important; background-color: white; font-size: 16px; font-weight: bold; border: none;">
              ${moaCompany?.name || 'Non défini'}
            </td>
          </tr>
        </table>

        <!-- Lot -->
        <table style="width: 100% !important; border-collapse: collapse !important; border: 2px solid #000 !important; margin-bottom: 10px !important;">
          <tr>
            <td style="padding: 15px; text-align: center !important; background-color: white; font-size: 18px; font-weight: bold; border: none;">
              LOT ${data.lot.id} - ${data.lot.name}
            </td>
          </tr>
        </table>

      </div>
    </div>
    <!-- FIN FALLBACK TEMPLATE -->
  `
}
