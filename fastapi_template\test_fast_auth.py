#!/usr/bin/env python3
"""
Script pour tester l'authentification rapide
"""

import asyncio
import httpx
import json

async def test_fast_auth():
    """Tester l'authentification rapide"""
    print("🚀 Test de l'authentification rapide...")
    
    base_url = "http://localhost:8001"
    
    async with httpx.AsyncClient() as client:
        try:
            # Test 1: Endpoint de santé
            print("\n1. Test endpoint de santé...")
            response = await client.get(f"{base_url}/health")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print(f"   ✅ API accessible")
            
            # Test 2: Endpoint fast-auth login (sans credentials)
            print("\n2. Test endpoint fast-auth (sans credentials)...")
            response = await client.post(f"{base_url}/api/v1/fast-auth/login", 
                json={"email": "<EMAIL>", "password": "wrongpassword"})
            print(f"   Status: {response.status_code}")
            if response.status_code == 401:
                print(f"   ✅ Authentification échoue correctement")
            else:
                print(f"   Response: {response.text}")
            
            # Test 3: Endpoint admin companies (sans auth)
            print("\n3. Test endpoint admin companies (sans auth)...")
            response = await client.get(f"{base_url}/api/v1/admin/companies/")
            print(f"   Status: {response.status_code}")
            if response.status_code == 401:
                print(f"   ✅ Endpoint protégé correctement")
            else:
                print(f"   Response: {response.text}")
            
            # Test 4: Créer un JWT de test
            print("\n4. Test création JWT...")
            from app.core.jwt_auth import JWTManager
            
            test_token = JWTManager.create_user_token(
                user_id=6,  # ID de Jeremy
                email="<EMAIL>",
                role="SUPER_ADMIN",
                is_superuser=True
            )
            print(f"   ✅ JWT créé: {test_token[:50]}...")
            
            # Test 5: Utiliser le JWT pour accéder aux entreprises
            print("\n5. Test endpoint admin companies (avec JWT)...")
            headers = {"Authorization": f"Bearer {test_token}"}
            response = await client.get(f"{base_url}/api/v1/admin/companies/", headers=headers)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                companies = response.json()
                print(f"   ✅ {len(companies)} entreprises récupérées")
                for company in companies[:2]:  # Afficher les 2 premières
                    print(f"      - {company['name']} ({company['code']})")
            else:
                print(f"   ❌ Erreur: {response.text}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test: {e}")
            return False

async def main():
    """Fonction principale"""
    print("🔑 ORBIS - Test authentification rapide")
    print("="*50)
    
    success = await test_fast_auth()
    
    if success:
        print(f"\n✅ Tests terminés")
        print(f"\n💡 L'authentification rapide fonctionne !")
        print(f"   - JWT créé et vérifié ✅")
        print(f"   - Endpoints protégés ✅") 
        print(f"   - API rapide ✅")
        
        print(f"\n🎯 Prochaines étapes:")
        print(f"   1. Tester la connexion frontend")
        print(f"   2. Utiliser <EMAIL> + mot de passe Supabase")
        print(f"   3. Profiter de l'authentification ultra-rapide !")
    else:
        print(f"\n❌ Erreur lors des tests")

if __name__ == "__main__":
    asyncio.run(main())
