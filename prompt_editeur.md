# Cahier des charges – Module **« Ajouter un article »**  
_Éditeur Tiptap • Next.js • FastAPI_

## 1. Objectif
Permettre à l’utilisateur, depuis l’éditeur CCTP, d’insérer un **nouvel article** structuré via un menu contextuel :
- choix du **heading parent** (filtrable) ;
- **formulaire** de saisie (champs jaunes de la matrice) ;
- génération du texte par **ChatGPT** ;
- insertion automatique du heading + corps dans le document.

---

## 2. Stack & conventions

| Couche | Choix | Règles clés |
| ------ | ----- | ----------- |
| **Front** | Next.js 13+, React 18, TypeScript 5 | Tailwind CSS, Radix UI. |
| **Éditeur** | **Tiptap v3** (MIT) | Extensions perso en React. |
| **Back** | FastAPI + Pydantic | Async/await ; OpenAI SDK v1.12. |
| **State** | <PERSON>ustand (page) + SWR (API) | Autosave : POST `/api/doc/:id/delta`. |

---

## 3. UX / Flow

1. **Clic droit** → _menu contextuel_ « Ajouter un article ».
2. **Modal** (Radix Dialog) :
   - **Sélecteur parent** : autocomplete des headings ≤ niveau du clic.
   - **Formulaire** : champs définis § 4.
   - Switchs « Inclure critères », « Inclure docs » (ON par défaut).
3. **Valider** :
   - Génération du **prompt** (template § 5).
   - POST `/api/generateArticle { prompt, parentId }`.
4. Backend → OpenAI → `{ title, bodyMarkdown }`.
5. Front :
   - Calcule le **numéro** (dernier enfant + 1).
   - `editor.chain().insertContent([...]).run()`.
6. **Autosave** (diff JSON) → FastAPI.
7. Fermeture de la modal.

---

## 4. Formulaire : champs & validation

| ID | Libellé | Type / Règle |
| -- | ------- | ------------ |
| `prestation` | Description de la prestation | **Obligatoire** (textarea) |
| `localisation` | Localisation de l’ouvrage | optionnel |
| `marque` | Marque ou équivalent | optionnel |
| `reference` | Référence technique | optionnel |
| `nature` | Nature des matériaux | optionnel |
| `criteresQualite` | Critères qualité | optionnel |
| `dimensions` | Dimensions | optionnel |
| `couleur` | Couleur | optionnel |
| `particularite` | Particularité de la prestation | optionnel |
| `descriptionPose` | Description mise en œuvre | optionnel |
| `typePose` | Type de pose | optionnel |
| `marquePose` | Marque matériels pose | optionnel |
| `referencePose` | Référence technique pose | optionnel |
| `inclureCriteres` | Inclure § 4 (essais) | bool (✓) |
| `inclureDocs` | Inclure § 5 (docs) | bool (✓) |
| `unite` | Unité de mesure | select (ml, m², u, …) |
| `quantite` | Quantité | number ≥ 0 |

---

## 5. Template de prompt

```text
Rédige un article de CCTP bâtiment.
1) Description : {{prestation}}
   Localisation : {{localisation}}
2) Fourniture / matériaux :
   Marque : {{marque}}
   Référence : {{reference}}
   Nature : {{nature}}
   Critères qualité : {{criteresQualite}}
   Dimensions : {{dimensions}}
   Couleur : {{couleur}}
3) Mise en œuvre :
   Particularité : {{particularite}}
   Description pose : {{descriptionPose}}
   Type de pose : {{typePose}}
   Marque matériels pose : {{marquePose}}
   Référence pose : {{referencePose}}
{% if inclureCriteres %}
4) Définis les critères d’essais, mise en service et réception sans citer de normes.
{% endif %}
{% if inclureDocs %}
5) Référence les documents techniques à fournir sans les citer.
{% endif %}
6) Termine par : « Unité : {{unite}} – Quantité : {{quantite}} ».
Style : technique, précis, sans listes à puces ni abréviations.
