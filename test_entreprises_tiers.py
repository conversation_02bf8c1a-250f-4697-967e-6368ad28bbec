#!/usr/bin/env python3
"""
Test du système d'entreprises tierces
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_entreprises_tiers():
    """Test complet du système d'entreprises tierces"""
    print("🧪 Test du système d'entreprises tierces")
    print("="*50)
    
    # 1. Connexion
    print("1. 🔐 Connexion...")
    login_response = requests.post(f"{API_BASE}/api/v1/fast-auth/login", json={
        "email": "<EMAIL>",
        "password": "orbis123!"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Erreur de connexion: {login_response.status_code}")
        print(f"   Response: {login_response.text}")
        return False
    
    token_data = login_response.json()
    token = token_data['access_token']
    user = token_data['user']
    print(f"✅ Connexion réussie pour: {user['email']}")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. Test de récupération des entreprises tierces (liste vide au début)
    print("\n2. 📋 Récupération des entreprises tierces...")
    entreprises_response = requests.get(f"{API_BASE}/api/v1/entreprises-tiers/", headers=headers)
    print(f"   Status: {entreprises_response.status_code}")
    
    if entreprises_response.status_code == 200:
        entreprises_data = entreprises_response.json()
        print(f"✅ {entreprises_data.get('total', 0)} entreprise(s) trouvée(s)")
    else:
        print(f"❌ Erreur: {entreprises_response.text}")
        return False
    
    # 3. Test de création d'une entreprise tierce
    print("\n3. ➕ Création d'une entreprise tierce...")
    nouvelle_entreprise = {
        "nom_entreprise": "Entreprise Test SARL",
        "activite": "Construction",
        "adresse": "123 Rue de la Paix",
        "code_postal": "75001",
        "ville": "Paris",
        "pays": "France",
        "telephone": "01 23 45 67 89",
        "email": "<EMAIL>",
        "siret": "12345678901234",
        "tva_intracommunautaire": "FR12345678901"
    }
    
    create_response = requests.post(
        f"{API_BASE}/api/v1/entreprises-tiers/",
        headers=headers,
        json=nouvelle_entreprise
    )
    print(f"   Status: {create_response.status_code}")
    
    if create_response.status_code == 201:
        entreprise_creee = create_response.json()
        entreprise_id = entreprise_creee['id']
        print(f"✅ Entreprise créée avec l'ID: {entreprise_id}")
        print(f"   Nom: {entreprise_creee['nom_entreprise']}")
        
        # 4. Test de récupération d'une entreprise spécifique
        print(f"\n4. 🎯 Récupération de l'entreprise {entreprise_id}...")
        get_response = requests.get(f"{API_BASE}/api/v1/entreprises-tiers/{entreprise_id}", headers=headers)
        print(f"   Status: {get_response.status_code}")
        
        if get_response.status_code == 200:
            entreprise = get_response.json()
            print(f"✅ Entreprise récupérée: {entreprise['nom_entreprise']}")
            
            # 5. Test de modification
            print(f"\n5. ✏️  Modification de l'entreprise {entreprise_id}...")
            modification = {
                "nom_entreprise": "Entreprise Test SARL (Modifiée)",
                "activite": "Construction et Rénovation"
            }
            
            update_response = requests.put(
                f"{API_BASE}/api/v1/entreprises-tiers/{entreprise_id}",
                headers=headers,
                json=modification
            )
            print(f"   Status: {update_response.status_code}")
            
            if update_response.status_code == 200:
                entreprise_modifiee = update_response.json()
                print(f"✅ Entreprise modifiée: {entreprise_modifiee['nom_entreprise']}")
                
                # 6. Test des statistiques
                print(f"\n6. 📊 Récupération des statistiques...")
                stats_response = requests.get(f"{API_BASE}/api/v1/entreprises-tiers/stats", headers=headers)
                print(f"   Status: {stats_response.status_code}")
                
                if stats_response.status_code == 200:
                    stats = stats_response.json()
                    print(f"✅ Statistiques récupérées:")
                    print(f"   Total: {stats['total_entreprises']}")
                    print(f"   Actives: {stats['entreprises_actives']}")
                    print(f"   Avec SIRET: {stats['entreprises_avec_siret']}")
                    
                    # 7. Test de suppression (soft delete)
                    print(f"\n7. 🗑️  Suppression de l'entreprise {entreprise_id}...")
                    delete_response = requests.delete(f"{API_BASE}/api/v1/entreprises-tiers/{entreprise_id}", headers=headers)
                    print(f"   Status: {delete_response.status_code}")
                    
                    if delete_response.status_code == 204:
                        print("✅ Entreprise supprimée (soft delete)")
                        return True
                    else:
                        print(f"❌ Erreur de suppression: {delete_response.text}")
                        return False
                else:
                    print(f"❌ Erreur stats: {stats_response.text}")
                    return False
            else:
                print(f"❌ Erreur de modification: {update_response.text}")
                return False
        else:
            print(f"❌ Erreur de récupération: {get_response.text}")
            return False
    else:
        print(f"❌ Erreur de création: {create_response.text}")
        return False

if __name__ == "__main__":
    success = test_entreprises_tiers()
    print(f"\n{'='*50}")
    if success:
        print("✅ Tous les tests sont passés avec succès!")
        print("🎉 Le système d'entreprises tierces fonctionne correctement")
    else:
        print("❌ Certains tests ont échoué")
        print("🔧 Vérifiez la configuration et les logs du serveur")
