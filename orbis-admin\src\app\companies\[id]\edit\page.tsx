'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { ArrowLeft } from 'lucide-react'
import { useToast } from '@/components/ui/Toast'
import { FastCompanyService } from '@/lib/fast-auth'
import AuthGuard from '@/components/AuthGuard'
import CompanyForm from '@/components/CompanyForm'

interface Company {
  id: number
  name: string
  code: string
  description?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  is_active: boolean
  user_count: number
}

function EditCompanyPageContent() {
  const { success, error: showError } = useToast()
  const [company, setCompany] = useState<Company | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const params = useParams()
  const router = useRouter()
  const companyId = parseInt(params.id as string)

  useEffect(() => {
    loadCompany()
  }, [companyId])

  const loadCompany = async () => {
    try {
      setIsLoadingData(true)
      console.log('🔄 Chargement entreprise:', companyId)
      
      const companies = await FastCompanyService.getCompanies()
      const foundCompany = companies.find(c => c.id === companyId)
      
      if (foundCompany) {
        console.log('✅ Entreprise trouvée:', foundCompany.name)
        setCompany(foundCompany)
      } else {
        console.log('❌ Entreprise non trouvée')
        showError('Erreur', 'Entreprise non trouvée')
        router.push('/companies')
      }
    } catch (error) {
      console.error('❌ Erreur chargement entreprise:', error)
      showError('Erreur', 'Impossible de charger l\'entreprise')
      router.push('/companies')
    } finally {
      setIsLoadingData(false)
    }
  }

  const handleSave = async (companyData: {
    name: string
    code: string
    description?: string
    address?: string
    phone?: string
    email?: string
    website?: string
    is_active?: boolean
  }) => {
    try {
      setIsLoading(true)
      console.log('🔄 Mise à jour de l\'entreprise...', companyData)
      
      const updatedCompany = await FastCompanyService.updateCompany(companyId, companyData)
      
      console.log('✅ Entreprise mise à jour:', updatedCompany)
      success('Succès', 'Entreprise mise à jour avec succès')
      
      // Rediriger vers la page de détail
      router.push(`/companies/${companyId}`)
      
    } catch (error) {
      console.error('❌ Erreur mise à jour entreprise:', error)
      showError('Erreur', error instanceof Error ? error.message : 'Impossible de mettre à jour l\'entreprise')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.push(`/companies/${companyId}`)
  }

  if (isLoadingData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement de l'entreprise...</p>
        </div>
      </div>
    )
  }

  if (!company) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* Header avec bouton retour */}
      <div className="flex items-center gap-4">
        <button
          onClick={handleCancel}
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Modifier {company.name}
          </h1>
          <p className="text-gray-600 mt-1">
            Modifiez les informations de l'entreprise
          </p>
        </div>
      </div>

      {/* Formulaire */}
      <div className="max-w-4xl">
        <CompanyForm
          company={company}
          onSave={handleSave}
          onCancel={handleCancel}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}

export default function EditCompanyPage() {
  return (
    <AuthGuard requireSuperAdmin={true}>
      <EditCompanyPageContent />
    </AuthGuard>
  )
}
