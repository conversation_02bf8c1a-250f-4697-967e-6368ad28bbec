# app/api/api_v1/endpoints/technical_documents.py
"""
API endpoints pour les documents techniques (CCTP/DPGF)
"""

from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, delete
from sqlalchemy.orm import selectinload

from app.api import deps
from app.models.user import User
from app.models.workspace import UserWorkspace, Workspace
from app.models.project import Project
from app.models.lot import Lot
from app.models.document import TechnicalDocument, TechnicalDocumentCompany, DocumentType
from app.models.tcompany import TCompany
from app.schemas.technical_document import (
    TechnicalDocumentCreate,
    TechnicalDocumentUpdate,
    TechnicalDocumentResponse,
    TechnicalDocumentList,
    TechnicalDocumentCompanyCreate,
    TechnicalDocumentCompanyResponse,
    TechnicalDocumentFilter,
    TechnicalDocumentSearchResponse,
    TechnicalDocumentStats,
    TextEnhancementRequest,
    TextEnhancementResponse,
    ArticleGenerationRequest,
    ArticleGenerationResponse
)
from app.middleware.auth_sync_middleware import require_auth
from app.services.chatgpt_service import chatgpt_service

router = APIRouter()

async def get_user_workspace_id(current_user: Dict[str, Any], db: AsyncSession) -> int:
    """Récupère l'ID de l'espace de travail de l'utilisateur connecté"""
    user_id = current_user.get("user_id")
    if not user_id:
        raise HTTPException(status_code=400, detail="Token utilisateur invalide")

    # Récupérer l'espace de travail de l'utilisateur
    user_workspace_result = await db.execute(
        select(UserWorkspace).where(
            and_(
                UserWorkspace.user_id == user_id,
                UserWorkspace.is_active == True
            )
        ).limit(1)
    )
    user_workspace = user_workspace_result.scalar_one_or_none()
    if not user_workspace:
        raise HTTPException(status_code=403, detail="Utilisateur non associé à un espace de travail")

    return user_workspace.workspace_id

async def check_document_access(document_id: int, user_workspace_id: int, db: AsyncSession) -> TechnicalDocument:
    """Vérifie l'accès à un document technique via le workspace de l'utilisateur"""
    # Récupérer le document avec ses relations
    result = await db.execute(
        select(TechnicalDocument)
        .options(
            selectinload(TechnicalDocument.companies),
            selectinload(TechnicalDocument.lot),
            selectinload(TechnicalDocument.creator),
            selectinload(TechnicalDocument.updater)
        )
        .where(TechnicalDocument.id == document_id)
    )
    document = result.scalar_one_or_none()

    if not document:
        raise HTTPException(status_code=404, detail="Document non trouvé")

    # Vérifier l'accès via le lot (le document doit appartenir au même workspace)
    if document.lot.workspace_id != user_workspace_id:
        raise HTTPException(status_code=403, detail="Accès refusé à ce document")

    # Optionnel : Vérifier aussi via les entreprises tierces si elles sont définies
    if document.companies:
        # Récupérer les IDs des TCompanies du workspace
        workspace_companies_result = await db.execute(
            select(TCompany.id).where(TCompany.workspace_id == user_workspace_id)
        )
        workspace_company_ids = {row[0] for row in workspace_companies_result.fetchall()}

        # Vérifier si le document est associé à au moins une TCompany du workspace
        has_access = any(
            company_rel.company_id in workspace_company_ids
            for company_rel in document.companies
        )

        if not has_access:
            raise HTTPException(status_code=403, detail="Accès refusé à ce document via les entreprises tierces")

    return document

@router.get("/", response_model=List[TechnicalDocumentList])
async def get_technical_documents(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
    lot_id: Optional[int] = Query(None, description="Filtrer par lot"),
    project_id: Optional[int] = Query(None, description="Filtrer par projet (via les lots)"),
    type_document: Optional[DocumentType] = Query(None, description="Filtrer par type de document"),
    skip: int = Query(0, ge=0, description="Nombre d'éléments à ignorer"),
    limit: int = Query(100, ge=1, le=1000, description="Nombre d'éléments à retourner"),
) -> Any:
    """
    Récupérer la liste des documents techniques accessibles à l'utilisateur
    """
    try:
        print(f"🔍 GET /technical-documents - User: {current_user}")
        print(f"🔍 Lot ID filter: {lot_id}, Project ID filter: {project_id}")

        user_workspace_id = await get_user_workspace_id(current_user, db)
        print(f"🔍 User workspace ID: {user_workspace_id}")

        # Construire la requête de base - filtrer par lot ET workspace
        query = select(TechnicalDocument).options(
            selectinload(TechnicalDocument.lot),
            selectinload(TechnicalDocument.creator),
            selectinload(TechnicalDocument.companies)
        ).join(Lot).where(
            and_(
                TechnicalDocument.is_active == True,
                Lot.workspace_id == user_workspace_id
            )
        )

        # Appliquer les filtres
        if lot_id:
            query = query.where(TechnicalDocument.lot_id == lot_id)
        
        if project_id:
            # Filtrer par projet via les lots
            query = query.where(Lot.project_id == project_id)

        if type_document:
            query = query.where(TechnicalDocument.type_document == type_document)

        # Pagination
        query = query.offset(skip).limit(limit)

        print(f"🔍 Executing query for technical documents...")
        result = await db.execute(query)
        documents = result.scalars().all()
        print(f"✅ Found {len(documents)} technical documents")

        # Transformer en réponse avec comptage des companies
        response_documents = []
        for doc in documents:
            doc_dict = {
                "id": doc.id,
                "name": doc.name,
                "type_document": doc.type_document,
                "content": None,  # Pas de contenu dans la liste
                "lot_id": doc.lot_id,
                "created_by": doc.created_by,
                "updated_by": doc.updated_by,
                "is_active": doc.is_active,
                "created_at": doc.created_at,
                "updated_at": doc.updated_at,
                "lot": {
                    "id": doc.lot.id,
                    "name": doc.lot.name,
                    "code": doc.lot.code,
                    "project_id": doc.lot.project_id
                } if doc.lot else None,
                "creator": {
                    "id": doc.creator.id,
                    "email": doc.creator.email,
                    "first_name": doc.creator.first_name,
                    "last_name": doc.creator.last_name
                } if doc.creator else None,
                "company_count": len(doc.companies)
            }
            response_documents.append(doc_dict)

        print(f"✅ Returning {len(response_documents)} technical documents")
        return response_documents

    except Exception as e:
        print(f"❌ Error in get_technical_documents: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de la récupération des documents techniques: {str(e)}"
        )

@router.get("/{document_id}", response_model=TechnicalDocumentResponse)
async def get_technical_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Récupérer un document technique par son ID
    """
    user_workspace_id = await get_user_workspace_id(current_user, db)
    document = await check_document_access(document_id, user_workspace_id, db)
    
    # Récupérer les TCompanies associées
    companies_result = await db.execute(
        select(TCompany)
        .join(TechnicalDocumentCompany)
        .where(TechnicalDocumentCompany.technical_document_id == document_id)
    )
    companies = companies_result.scalars().all()
    
    return {
        "id": document.id,
        "name": document.name,
        "type_document": document.type_document,
        "content": document.content,
        "lot_id": document.lot_id,
        "created_by": document.created_by,
        "updated_by": document.updated_by,
        "is_active": document.is_active,
        "created_at": document.created_at,
        "updated_at": document.updated_at,
        "lot": {
            "id": document.lot.id,
            "name": document.lot.name,
            "code": document.lot.code,
            "project_id": document.lot.project_id
        } if document.lot else None,
        "creator": {
            "id": document.creator.id,
            "email": document.creator.email,
            "first_name": document.creator.first_name,
            "last_name": document.creator.last_name
        } if document.creator else None,
        "updater": {
            "id": document.updater.id,
            "email": document.updater.email,
            "first_name": document.updater.first_name,
            "last_name": document.updater.last_name
        } if document.updater else None,
        "companies": [
            {
                "id": company.id,
                "name": company.company_name,
                "code": company.code if hasattr(company, 'code') else None
            }
            for company in companies
        ]
    }

@router.post("/", response_model=TechnicalDocumentResponse)
async def create_technical_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_in: TechnicalDocumentCreate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Créer un nouveau document technique
    """
    try:
        print(f"🔄 Création document technique: {document_in.name}")
        
        user_id = current_user.get("user_id")
        user_workspace_id = await get_user_workspace_id(current_user, db)
        
        # Vérifier que le lot existe et appartient au workspace de l'utilisateur
        lot_result = await db.execute(
            select(Lot).where(
                and_(
                    Lot.id == document_in.lot_id,
                    Lot.workspace_id == user_workspace_id
                )
            )
        )
        lot = lot_result.scalar_one_or_none()
        if not lot:
            raise HTTPException(
                status_code=404,
                detail=f"Lot {document_in.lot_id} non trouvé ou non accessible"
            )
        
        # Créer le document
        document_data = document_in.model_dump(exclude={"company_ids"})
        document_data["created_by"] = user_id
        
        document = TechnicalDocument(**document_data)
        db.add(document)
        await db.flush()  # Pour obtenir l'ID
        
        # Associer les entreprises tierces (valider qu'elles appartiennent au workspace)
        company_ids = set(document_in.company_ids or [])

        for company_id in company_ids:
            # Vérifier que la TCompany existe et appartient au workspace
            company_result = await db.execute(
                select(TCompany).where(
                    and_(
                        TCompany.id == company_id,
                        TCompany.workspace_id == user_workspace_id
                    )
                )
            )
            if company_result.scalar_one_or_none():
                doc_company = TechnicalDocumentCompany(
                    technical_document_id=document.id,
                    company_id=company_id
                )
                db.add(doc_company)
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Entreprise tierce {company_id} non trouvée ou non accessible"
                )
        
        await db.commit()
        await db.refresh(document)

        # Charger les relations pour la réponse
        result = await db.execute(
            select(TechnicalDocument)
            .options(
                selectinload(TechnicalDocument.companies),
                selectinload(TechnicalDocument.lot),
                selectinload(TechnicalDocument.creator),
                selectinload(TechnicalDocument.updater)
            )
            .where(TechnicalDocument.id == document.id)
        )
        document_with_relations = result.scalar_one()

        # Récupérer les TCompanies associées
        companies_result = await db.execute(
            select(TCompany)
            .join(TechnicalDocumentCompany)
            .where(TechnicalDocumentCompany.technical_document_id == document.id)
        )
        companies = companies_result.scalars().all()

        # Construire la réponse avec gestion d'erreur
        try:
            response_data = {
                "id": document_with_relations.id,
                "name": document_with_relations.name,
                "type_document": document_with_relations.type_document,
                "content": document_with_relations.content,
                "lot_id": document_with_relations.lot_id,
                "created_by": document_with_relations.created_by,
                "updated_by": document_with_relations.updated_by,
                "created_at": document_with_relations.created_at,
                "updated_at": document_with_relations.updated_at,
                "lot": {
                    "id": document_with_relations.lot.id,
                    "name": document_with_relations.lot.name,
                    "code": document_with_relations.lot.code,
                    "project_id": document_with_relations.lot.project_id
                } if document_with_relations.lot else None,
                "companies": [
                    {
                        "id": company.id,
                        "name": company.company_name,
                        "code": getattr(company, 'code', None)  # Gestion sécurisée du code
                    }
                    for company in companies
                ]
            }
            
            print(f"✅ Document créé avec succès: {document_with_relations.id}")
            return response_data
            
        except Exception as validation_error:
            print(f"❌ Erreur de validation de la réponse: {validation_error}")
            raise HTTPException(
                status_code=500,
                detail=f"Erreur lors de la construction de la réponse: {str(validation_error)}"
            )
            
    except HTTPException:
        # Re-raise les HTTPException (erreurs métier)
        raise
    except Exception as e:
        print(f"❌ Erreur inattendue lors de la création: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Erreur interne lors de la création du document: {str(e)}"
        )

@router.put("/{document_id}", response_model=TechnicalDocumentResponse)
async def update_technical_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_id: int,
    document_in: TechnicalDocumentUpdate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Mettre à jour un document technique
    """
    user_id = current_user.get("user_id")
    user_workspace_id = await get_user_workspace_id(current_user, db)
    document = await check_document_access(document_id, user_workspace_id, db)

    # Mettre à jour les champs du document
    update_data = document_in.model_dump(exclude_unset=True, exclude={"company_ids"})
    if update_data:
        update_data["updated_by"] = user_id
        for field, value in update_data.items():
            setattr(document, field, value)

    # Gérer les relations companies si spécifiées
    if document_in.company_ids is not None:
        # Supprimer les relations existantes
        await db.execute(
            delete(TechnicalDocumentCompany).where(
                TechnicalDocumentCompany.technical_document_id == document_id
            )
        )

        # Ajouter les nouvelles relations (valider qu'elles appartiennent au workspace)
        company_ids = set(document_in.company_ids)

        for company_id in company_ids:
            # Vérifier que la TCompany existe et appartient au workspace
            company_result = await db.execute(
                select(TCompany).where(
                    and_(
                        TCompany.id == company_id,
                        TCompany.workspace_id == user_workspace_id
                    )
                )
            )
            if company_result.scalar_one_or_none():
                doc_company = TechnicalDocumentCompany(
                    technical_document_id=document_id,
                    company_id=company_id
                )
                db.add(doc_company)
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Entreprise tierce {company_id} non trouvée ou non accessible"
                )

    await db.commit()
    await db.refresh(document)

    return await get_technical_document(
        db=db,
        document_id=document_id,
        current_user=current_user
    )

@router.delete("/{document_id}")
async def delete_technical_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Supprimer un document technique (soft delete)
    """
    user_workspace_id = await get_user_workspace_id(current_user, db)
    document = await check_document_access(document_id, user_workspace_id, db)

    # Soft delete
    document.is_active = False
    document.updated_by = current_user.get("user_id")

    await db.commit()

    return {"message": "Document supprimé avec succès"}

# Endpoints pour la gestion des relations companies

@router.post("/{document_id}/companies", response_model=List[TechnicalDocumentCompanyResponse])
async def add_companies_to_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_id: int,
    company_ids: List[int],
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Associer des companies à un document technique
    """
    user_workspace_id = await get_user_workspace_id(current_user, db)
    document = await check_document_access(document_id, user_workspace_id, db)

    added_relations = []

    for company_id in company_ids:
        # Vérifier que la TCompany existe et appartient au workspace
        company_result = await db.execute(
            select(TCompany).where(
                and_(
                    TCompany.id == company_id,
                    TCompany.workspace_id == user_workspace_id
                )
            )
        )
        if not company_result.scalar_one_or_none():
            raise HTTPException(
                status_code=400,
                detail=f"Entreprise tierce {company_id} non trouvée ou non accessible"
            )

        # Vérifier si la relation existe déjà
        existing_result = await db.execute(
            select(TechnicalDocumentCompany).where(
                and_(
                    TechnicalDocumentCompany.technical_document_id == document_id,
                    TechnicalDocumentCompany.company_id == company_id
                )
            )
        )
        if existing_result.scalar_one_or_none():
            continue

        # Créer la nouvelle relation
        doc_company = TechnicalDocumentCompany(
            technical_document_id=document_id,
            company_id=company_id
        )
        db.add(doc_company)
        await db.flush()
        added_relations.append(doc_company)

    await db.commit()

    return [
        {
            "id": rel.id,
            "technical_document_id": rel.technical_document_id,
            "workspace_id": rel.workspace_id,
            "created_at": rel.created_at
        }
        for rel in added_relations
    ]

@router.delete("/{document_id}/companies/{workspace_id}")
async def remove_company_from_document(
    *,
    db: AsyncSession = Depends(deps.get_db),
    document_id: int,
    company_id: int,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Supprimer l'association entre un document et une company
    """
    user_workspace_id = await get_user_workspace_id(current_user, db)
    document = await check_document_access(document_id, user_workspace_id, db)

    # Vérifier que la TCompany appartient au workspace
    company_result = await db.execute(
        select(TCompany).where(
            and_(
                TCompany.id == company_id,
                TCompany.workspace_id == user_workspace_id
            )
        )
    )
    if not company_result.scalar_one_or_none():
        raise HTTPException(
            status_code=400,
            detail="Entreprise tierce non trouvée ou non accessible"
        )

    # Supprimer la relation
    result = await db.execute(
        delete(TechnicalDocumentCompany).where(
            and_(
                TechnicalDocumentCompany.technical_document_id == document_id,
                TechnicalDocumentCompany.company_id == company_id
            )
        )
    )

    if result.rowcount == 0:
        raise HTTPException(status_code=404, detail="Association non trouvée")

    await db.commit()

    return {"message": "Association supprimée avec succès"}

# Endpoints pour l'amélioration de texte avec ChatGPT

@router.post("/enhance-text", response_model=TextEnhancementResponse)
async def enhance_text(
    *,
    enhancement_request: TextEnhancementRequest,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Améliorer un texte avec ChatGPT
    """
    # Vérifier que le service ChatGPT est disponible
    if not chatgpt_service.is_available():
        raise HTTPException(
            status_code=503,
            detail="Service d'amélioration de texte non disponible. Veuillez configurer OPENAI_API_KEY."
        )

    try:
        result = await chatgpt_service.enhance_text(
            text=enhancement_request.text,
            prompt_type=enhancement_request.prompt_type,
            document_type=enhancement_request.document_type,
            context=enhancement_request.context
        )

        if not result.get("success", False):
            raise HTTPException(
                status_code=500,
                detail=f"Erreur lors de l'amélioration du texte: {result.get('error', 'Erreur inconnue')}"
            )

        return {
            "original_text": result["original_text"],
            "enhanced_text": result["enhanced_text"],
            "prompt_type": result["prompt_type"],
            "document_type": result["document_type"],
            "processing_time": result.get("processing_time")
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de l'amélioration du texte: {str(e)}"
        )

@router.get("/prompt-types/{document_type}")
async def get_available_prompt_types(
    *,
    document_type: DocumentType,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Récupérer les types de prompts disponibles pour un type de document
    """
    try:
        prompt_types = chatgpt_service.get_available_prompt_types(document_type)
        return {
            "document_type": document_type,
            "available_prompt_types": prompt_types
        }
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Erreur: {str(e)}"
        )

@router.get("/supported-document-types")
async def get_supported_document_types(
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Récupérer les types de documents supportés pour l'amélioration de texte
    """
    return {
        "supported_document_types": chatgpt_service.get_supported_document_types()
    }

@router.post("/generate-article", response_model=ArticleGenerationResponse)
async def generate_article(
    *,
    request: ArticleGenerationRequest,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Générer un article CCTP à partir des données du formulaire
    """
    print("=" * 80)
    print("🎯 ENDPOINT /generate-article APPELÉ")
    print("=" * 80)
    print(f"👤 Utilisateur: {current_user.get('email', 'N/A')}")
    print(f"📋 Données reçues: {request}")
    print(f"📋 Type de request: {type(request)}")

    # Vérifier que le service ChatGPT est disponible
    print(f"🔧 Service ChatGPT disponible: {chatgpt_service.is_available()}")
    if not chatgpt_service.is_available():
        print("❌ Service ChatGPT NON DISPONIBLE")
        raise HTTPException(
            status_code=503,
            detail="Service de génération d'articles non disponible. Veuillez configurer OPENAI_API_KEY."
        )

    try:
        import time
        start_time = time.time()

        print(f"🚀 Génération d'article - Données reçues: {request}")
        print(f"🚀 Service ChatGPT prêt, démarrage de la génération...")

        # Convertir la requête en dictionnaire pour la nouvelle fonction
        article_data = {
            "prestation": request.prestation,
            "localisation": request.localisation,
            "marque": request.marque,
            "reference": request.reference,
            "nature": request.nature,
            "criteresQualite": request.criteres_qualite,
            "dimensions": request.dimensions,
            "couleur": request.couleur,
            "particularite": request.particularite,
            "descriptionPose": request.description_pose,
            "typePose": request.type_pose,
            "marquePose": request.marque_pose,
            "referencePose": request.reference_pose,
            "inclureCriteres": request.inclure_criteres,
            "inclureDocs": request.inclure_docs,
            "unite": request.unite,
            "quantite": request.quantite
        }

        # Utiliser la nouvelle fonction ajout_article_cctp
        result = await chatgpt_service.ajout_article_cctp(article_data)

        # Vérifier le succès de la génération
        if not result.get("success", False):
            raise HTTPException(
                status_code=500,
                detail=f"Erreur lors de la génération: {result.get('error', 'Erreur inconnue')}"
            )

        enhanced_text = result.get("article_content", "")
        print(f"✅ Article généré: {enhanced_text[:200]}...")

        # Extraire le titre du texte généré
        title = _extract_title_from_generated_text(enhanced_text)
        print(f"📋 Titre extrait: {title}")

        # Calculer le numéro d'article (logique simple pour l'instant)
        article_number = _calculate_article_number(request.parent_id)
        print(f"🔢 Numéro d'article: {article_number}")

        # Convertir en HTML (simple pour l'instant)
        body_html = _markdown_to_html(enhanced_text)
        print(f"🌐 HTML généré: {body_html[:200]}...")

        processing_time = time.time() - start_time

        return ArticleGenerationResponse(
            title=title,
            body_markdown=enhanced_text,
            body_html=body_html,
            article_number=article_number,
            parent_id=request.parent_id,
            processing_time=processing_time
        )

    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Erreur lors de la génération de l'article: {str(e)}"
        )



def _extract_title_from_generated_text(text: str) -> str:
    """Extraire le titre du texte généré"""
    lines = text.split('\n')
    for line in lines:
        line = line.strip()
        if line and not line.startswith('1)') and not line.startswith('2)'):
            # Prendre les premiers mots comme titre
            words = line.split()[:8]  # Limiter à 8 mots
            return ' '.join(words)
    return "Nouvel article"

def _calculate_article_number(parent_id: Optional[str]) -> str:
    """Calculer le numéro d'article basé sur la hiérarchie"""
    if not parent_id:
        # Si pas de parent, commencer à 1
        return "1"

    # Extraire le niveau du parent_id (format: heading-position)
    try:
        # Pour l'instant, logique simple basée sur la position
        # Dans une vraie implémentation, on analyserait la structure du document
        import random
        section = random.randint(1, 5)
        subsection = random.randint(1, 10)
        return f"{section}.{subsection}"
    except:
        return "1.1"

def _markdown_to_html(markdown_text: str) -> str:
    """Convertir le markdown en HTML (conversion simple)"""
    # Conversion basique - à améliorer avec une vraie librairie markdown
    html = markdown_text.replace('\n\n', '</p><p>')
    html = f'<p>{html}</p>'

    # Remplacer les titres
    html = html.replace('# ', '<h1>').replace('\n', '</h1>\n')
    html = html.replace('## ', '<h2>').replace('\n', '</h2>\n')
    html = html.replace('### ', '<h3>').replace('\n', '</h3>\n')

    # Gras et italique
    html = html.replace('**', '<strong>').replace('**', '</strong>')
    html = html.replace('*', '<em>').replace('*', '</em>')

    return html


@router.post("/generate-article-json")
async def generate_article_json(
    *,
    request: ArticleGenerationRequest,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """
    Générer un article CCTP à partir des données du formulaire (version JSON)
    """
    print("=" * 80)
    print("🎯 ENDPOINT /generate-article-json APPELÉ")
    print("=" * 80)
    print(f"👤 Utilisateur: {current_user.get('email', 'N/A')}")
    print(f"📋 Données reçues: {request}")

    # Vérifier que le service ChatGPT est disponible
    print(f"🔧 Service ChatGPT disponible: {chatgpt_service.is_available()}")
    if not chatgpt_service.is_available():
        print("❌ Service ChatGPT NON DISPONIBLE")
        raise HTTPException(
            status_code=503,
            detail="Service de génération d'articles non disponible. Veuillez configurer OPENAI_API_KEY."
        )

    try:
        import time
        start_time = time.time()

        print(f"🚀 Génération d'article JSON - Données reçues")
        print(f"🚀 Service ChatGPT prêt, démarrage de la génération JSON...")

        # Préparer les données pour le service ChatGPT
        article_data = {
            "prestation": request.prestation,
            "localisation": request.localisation,
            "marque": request.marque,
            "reference": request.reference,
            "nature": request.nature,
            "criteresQualite": request.criteres_qualite,
            "dimensions": request.dimensions,
            "couleur": request.couleur,
            "particularite": request.particularite,
            "descriptionPose": request.description_pose,
            "typePose": request.type_pose,
            "marquePose": request.marque_pose,
            "referencePose": request.reference_pose,
            "inclureCriteres": request.inclure_criteres,
            "inclureDocs": request.inclure_docs,
            "unite": request.unite,
            "quantite": request.quantite
        }

        # Appel au service ChatGPT pour génération JSON
        result = await chatgpt_service.ajout_article_cctp_json(article_data)

        end_time = time.time()
        print(f"⏱️ Temps total de traitement: {end_time - start_time:.2f} secondes")
        print(f"✅ Génération d'article JSON terminée avec succès")

        # Retourner les données JSON brutes pour traitement côté frontend
        return {
            "success": result.get("success", False),
            "article_data": result.get("article_data", {}),
            "processing_time": result.get("processing_time", 0.0),
            "model_used": result.get("model_used", "gpt-3.5-turbo"),
            "error": result.get("error")
        }

    except Exception as e:
        print(f"❌ Erreur lors de la génération de l'article JSON: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de la génération de l'article JSON: {str(e)}"
        )
