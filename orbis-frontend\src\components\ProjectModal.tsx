import React, { useState, useEffect } from 'react'
import { Modal } from '@/components/ui/Modal'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import PhotoUpload from '@/components/projects/PhotoUpload'
import { useProjectPhoto } from '@/hooks/useProjectPhoto'
import { cleanFormData } from '@/utils/formUtils'

interface ProjectFormData {
  name: string
  code: string
  description: string
  status: string
  nature: string
  start_date: string
  end_date: string
  budget_total: string
  address: string
  client_name: string
  client_contact: string
  photo_url?: string
}

interface ProjectModalProps {
  isOpen: boolean
  onClose: () => void
  project?: Record<string, any> | null
  onSubmit: (data: ProjectFormData) => Promise<void>
  onRefresh?: () => void
}

export default function ProjectModal({
  isOpen,
  onClose,
  project,
  onSubmit,
  onRefresh
}: ProjectModalProps) {
  const isEdit = !!project
  const projectId = project?.id

  const [formData, setFormData] = useState<ProjectFormData>({
    name: '',
    code: '',
    description: '',
    status: 'EN_COURS',
    nature: 'DEVIS',
    start_date: '',
    end_date: '',
    budget_total: '',
    address: '',
    client_name: '',
    client_contact: ''
  })

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Hook pour gérer la photo du projet (seulement en mode édition)
  const photoHook = projectId ? useProjectPhoto({
    projectId,
    initialPhotoUrl: formData.photo_url,
    onSuccess: () => {
      if (onRefresh) {
        onRefresh()
      }
    }
  }) : null

  // Initialiser le formulaire avec les données du projet
  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name || '',
        code: project.code || '',
        description: project.description || '',
        status: project.status || 'EN_COURS',
        nature: project.nature || 'DEVIS',
        start_date: project.start_date ? project.start_date.split('T')[0] : '',
        end_date: project.end_date ? project.end_date.split('T')[0] : '',
        budget_total: project.budget_total ? project.budget_total.toString() : '',
        address: project.address || '',
        client_name: project.client_name || '',
        client_contact: project.client_contact || '',
        photo_url: project.photo_url || undefined
      })
    } else {
      // Reset pour nouveau projet
      setFormData({
        name: '',
        code: '',
        description: '',
        status: 'EN_COURS',
        nature: 'DEVIS',
        start_date: '',
        end_date: '',
        budget_total: '',
        address: '',
        client_name: '',
        client_contact: ''
      })
    }
    setError(null)
  }, [project, isOpen])

  const handleInputChange = (field: keyof ProjectFormData, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    })
  }

  const handlePhotoUpload = async (file: File) => {
    if (photoHook && projectId) {
      try {
        const result = await photoHook.uploadPhoto(file)
        // Mettre à jour formData avec la nouvelle URL de la photo
        if (result && result.photo_url) {
          setFormData({
            ...formData,
            photo_url: result.photo_url
          })
          console.log('✅ FormData mis à jour avec nouvelle photo:', result.photo_url)
        }
      } catch (error) {
        console.error('❌ Erreur upload photo:', error)
      }
    }
  }

  const handlePhotoDelete = async () => {
    if (photoHook && projectId) {
      try {
        await photoHook.deletePhoto()
        // Mettre à jour formData pour supprimer l'URL de la photo
        setFormData({
          ...formData,
          photo_url: undefined
        })
        console.log('✅ FormData mis à jour - photo supprimée')
      } catch (error) {
        console.error('❌ Erreur suppression photo:', error)
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Nettoyer les données : remplacer les chaînes vides par null
      const cleanedFormData = cleanFormData(formData)
      
      await onSubmit(cleanedFormData)
      onClose()
    } catch (err) {
      console.error('❌ Erreur lors de la soumission:', err)
      setError(err instanceof Error ? err.message : 'Erreur lors de la sauvegarde')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEdit ? 'Modifier le projet' : 'Nouveau projet'}
      size="large"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* Section Photo (seulement en mode édition) */}
        {isEdit && projectId && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Photo du projet
            </label>
            <PhotoUpload
              key={`photo-upload-${projectId}`}
              currentPhotoUrl={photoHook?.photoUrl || formData.photo_url}
              onUpload={handlePhotoUpload}
              onDelete={handlePhotoDelete}
              loading={photoHook?.loading}
              photoDeleted={photoHook?.photoDeleted}
              width={300}
              height={200}
            />
          </div>
        )}

        {/* Informations de base */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Nom du projet *"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            required
          />
          <Input
            label="Code du projet"
            value={formData.code}
            onChange={(e) => handleInputChange('code', e.target.value)}
            placeholder="Généré automatiquement si vide"
          />
        </div>

        <Input
          label="Description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          multiline
          rows={3}
        />

        {/* Statut et Nature */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Statut
            </label>
            <select
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="EN_COURS">En cours</option>
              <option value="EN_ATTENTE">En attente</option>
              <option value="TERMINE">Terminé</option>
              <option value="ARCHIVE">Archivé</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nature
            </label>
            <select
              value={formData.nature}
              onChange={(e) => handleInputChange('nature', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="DEVIS">Devis</option>
              <option value="AO">AO</option>
              <option value="AFFAIRE">Affaire</option>
            </select>
          </div>
        </div>

        {/* Dates et Budget */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input
            label="Date de début"
            type="date"
            value={formData.start_date}
            onChange={(e) => handleInputChange('start_date', e.target.value)}
          />
          <Input
            label="Date de fin prévue"
            type="date"
            value={formData.end_date}
            onChange={(e) => handleInputChange('end_date', e.target.value)}
          />
          <Input
            label="Budget total (€)"
            type="number"
            step="0.01"
            value={formData.budget_total}
            onChange={(e) => handleInputChange('budget_total', e.target.value)}
          />
        </div>

        {/* Client et Contact */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Nom du client"
            value={formData.client_name}
            onChange={(e) => handleInputChange('client_name', e.target.value)}
          />
          <Input
            label="Contact client"
            value={formData.client_contact}
            onChange={(e) => handleInputChange('client_contact', e.target.value)}
          />
        </div>

        {/* Adresse */}
        <Input
          label="Adresse du projet"
          value={formData.address}
          onChange={(e) => handleInputChange('address', e.target.value)}
          multiline
          rows={2}
        />

        {/* Boutons */}
        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Annuler
          </Button>
          <Button
            type="submit"
            disabled={loading || !formData.name.trim()}
          >
            {loading ? 'Sauvegarde...' : (isEdit ? 'Modifier' : 'Créer')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
