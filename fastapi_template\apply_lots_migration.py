#!/usr/bin/env python3
"""
Script pour appliquer directement la migration des lots
en contournant les problèmes de chaîne de révisions Alembic
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

def get_database_url():
    """Récupère l'URL de la base de données depuis les settings"""
    try:
        return settings.DATABASE_URL
    except:
        # Fallback avec variables d'environnement
        db_user = os.getenv("POSTGRES_USER", "postgres")
        db_password = os.getenv("POSTGRES_PASSWORD", "password")
        db_host = os.getenv("POSTGRES_SERVER", "localhost")
        db_port = os.getenv("POSTGRES_PORT", "5432")
        db_name = os.getenv("POSTGRES_DB", "orbis")
        return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

def check_table_exists(engine, table_name):
    """Vérifie si une table existe"""
    with engine.connect() as conn:
        result = conn.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = :table_name
            );
        """), {"table_name": table_name})
        return result.scalar()

def apply_lots_migration(engine):
    """Applique la migration des lots directement"""
    
    print("🔄 Application de la migration des lots...")
    
    with engine.connect() as conn:
        # Commencer une transaction
        trans = conn.begin()
        
        try:
            # 1. Créer l'enum pour les phases de lot si il n'existe pas
            print("📋 1. Création de l'enum LotPhase...")
            conn.execute(text("""
                DO $$ BEGIN
                    CREATE TYPE lotphase AS ENUM ('ESQ', 'APD', 'PRODCE', 'EXE');
                EXCEPTION
                    WHEN duplicate_object THEN null;
                END $$;
            """))
            
            # 2. Créer la table lots si elle n'existe pas
            if not check_table_exists(engine, 'lots'):
                print("📋 2. Création de la table lots...")
                conn.execute(text("""
                    CREATE TABLE lots (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR NOT NULL,
                        code VARCHAR NOT NULL,
                        description TEXT,
                        project_id INTEGER NOT NULL,
                        current_phase lotphase DEFAULT 'ESQ',
                        
                        -- Historique des phases validées - ESQ
                        esq_validated BOOLEAN DEFAULT FALSE,
                        esq_validated_at TIMESTAMP,
                        esq_validated_by INTEGER,
                        
                        -- Historique des phases validées - APD
                        apd_validated BOOLEAN DEFAULT FALSE,
                        apd_validated_at TIMESTAMP,
                        apd_validated_by INTEGER,
                        
                        -- Historique des phases validées - PRODCE
                        prodce_validated BOOLEAN DEFAULT FALSE,
                        prodce_validated_at TIMESTAMP,
                        prodce_validated_by INTEGER,
                        
                        -- Historique des phases validées - EXE
                        exe_validated BOOLEAN DEFAULT FALSE,
                        exe_validated_at TIMESTAMP,
                        exe_validated_by INTEGER,
                        
                        -- Métadonnées
                        workspace_id INTEGER NOT NULL,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT NOW(),
                        updated_at TIMESTAMP DEFAULT NOW(),
                        created_by INTEGER,
                        
                        -- Contraintes de clés étrangères
                        CONSTRAINT fk_lots_project_id FOREIGN KEY (project_id) REFERENCES projects(id),
                        CONSTRAINT fk_lots_workspace_id FOREIGN KEY (workspace_id) REFERENCES workspaces(id),
                        CONSTRAINT fk_lots_created_by FOREIGN KEY (created_by) REFERENCES users(id),
                        CONSTRAINT fk_lots_esq_validated_by FOREIGN KEY (esq_validated_by) REFERENCES users(id),
                        CONSTRAINT fk_lots_apd_validated_by FOREIGN KEY (apd_validated_by) REFERENCES users(id),
                        CONSTRAINT fk_lots_prodce_validated_by FOREIGN KEY (prodce_validated_by) REFERENCES users(id),
                        CONSTRAINT fk_lots_exe_validated_by FOREIGN KEY (exe_validated_by) REFERENCES users(id)
                    );
                """))
                
                # Créer les index
                print("📋 3. Création des index pour la table lots...")
                conn.execute(text("CREATE INDEX ix_lots_id ON lots(id);"))
                conn.execute(text("CREATE INDEX ix_lots_name ON lots(name);"))
                conn.execute(text("CREATE INDEX ix_lots_code ON lots(code);"))
                conn.execute(text("CREATE INDEX ix_lots_project_id ON lots(project_id);"))
                conn.execute(text("CREATE INDEX ix_lots_workspace_id ON lots(workspace_id);"))
                conn.execute(text("CREATE INDEX ix_lots_current_phase ON lots(current_phase);"))
            else:
                print("✅ Table lots existe déjà")
            
            # 3. Créer la table lot_intervenants si elle n'existe pas
            if not check_table_exists(engine, 'lot_intervenants'):
                print("📋 4. Création de la table lot_intervenants...")
                conn.execute(text("""
                    CREATE TABLE lot_intervenants (
                        id SERIAL PRIMARY KEY,
                        lot_id INTEGER NOT NULL,
                        company_id INTEGER NOT NULL,
                        role VARCHAR(100),
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT NOW(),
                        created_by INTEGER,
                        
                        CONSTRAINT fk_lot_intervenants_lot_id FOREIGN KEY (lot_id) REFERENCES lots(id),
                        CONSTRAINT fk_lot_intervenants_company_id FOREIGN KEY (company_id) REFERENCES tcompanies(id),
                        CONSTRAINT fk_lot_intervenants_created_by FOREIGN KEY (created_by) REFERENCES users(id)
                    );
                """))
                
                # Créer les index
                conn.execute(text("CREATE INDEX ix_lot_intervenants_id ON lot_intervenants(id);"))
                conn.execute(text("CREATE INDEX ix_lot_intervenants_lot_id ON lot_intervenants(lot_id);"))
                conn.execute(text("CREATE INDEX ix_lot_intervenants_company_id ON lot_intervenants(company_id);"))
            else:
                print("✅ Table lot_intervenants existe déjà")
            
            # 4. Créer la table lot_documents si elle n'existe pas
            if not check_table_exists(engine, 'lot_documents'):
                print("📋 5. Création de la table lot_documents...")
                conn.execute(text("""
                    CREATE TABLE lot_documents (
                        id SERIAL PRIMARY KEY,
                        lot_id INTEGER NOT NULL,
                        document_id INTEGER NOT NULL,
                        phase lotphase,
                        created_at TIMESTAMP DEFAULT NOW(),
                        created_by INTEGER,
                        
                        CONSTRAINT fk_lot_documents_lot_id FOREIGN KEY (lot_id) REFERENCES lots(id),
                        CONSTRAINT fk_lot_documents_document_id FOREIGN KEY (document_id) REFERENCES documents(id),
                        CONSTRAINT fk_lot_documents_created_by FOREIGN KEY (created_by) REFERENCES users(id)
                    );
                """))
                
                # Créer les index
                conn.execute(text("CREATE INDEX ix_lot_documents_id ON lot_documents(id);"))
                conn.execute(text("CREATE INDEX ix_lot_documents_lot_id ON lot_documents(lot_id);"))
                conn.execute(text("CREATE INDEX ix_lot_documents_document_id ON lot_documents(document_id);"))
                conn.execute(text("CREATE INDEX ix_lot_documents_phase ON lot_documents(phase);"))
            else:
                print("✅ Table lot_documents existe déjà")
            
            # 5. Créer un lot par défaut pour chaque projet existant
            print("📋 6. Création des lots par défaut pour les projets existants...")
            
            # Vérifier s'il y a déjà des lots
            result = conn.execute(text("SELECT COUNT(*) FROM lots;"))
            lot_count = result.scalar()
            
            if lot_count == 0:
                # Récupérer tous les projets existants
                projects_result = conn.execute(text("""
                    SELECT id, name, code, workspace_id, created_at, updated_at 
                    FROM projects 
                    WHERE is_archived = false OR is_archived IS NULL
                """))
                
                projects = projects_result.fetchall()
                
                for project in projects:
                    project_id, project_name, project_code, workspace_id, created_at, updated_at = project
                    
                    # Générer un code pour le lot
                    lot_code = f"{project_code}-L01" if project_code else f"LOT-{project_id}-01"
                    lot_name = f"Lot principal - {project_name}"
                    
                    # Insérer le lot par défaut
                    conn.execute(text("""
                        INSERT INTO lots (name, code, description, project_id, current_phase, workspace_id, 
                                        is_active, created_at, updated_at, esq_validated, apd_validated, 
                                        prodce_validated, exe_validated)
                        VALUES (:name, :code, :description, :project_id, 'ESQ', :workspace_id, 
                                true, :created_at, :updated_at, false, false, false, false)
                    """), {
                        'name': lot_name,
                        'code': lot_code,
                        'description': f'Lot principal créé automatiquement pour le projet {project_name}',
                        'project_id': project_id,
                        'workspace_id': workspace_id,
                        'created_at': created_at,
                        'updated_at': updated_at
                    })
                
                print(f"✅ {len(projects)} lots créés pour les projets existants")
            else:
                print(f"✅ {lot_count} lots existent déjà")
            
            # 6. Modifier la table technical_documents pour ajouter lot_id si nécessaire
            print("📋 7. Modification de la table technical_documents...")
            
            # Vérifier si la colonne lot_id existe déjà
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = 'technical_documents' 
                    AND column_name = 'lot_id'
                );
            """))
            
            if not result.scalar():
                # Ajouter la colonne lot_id
                conn.execute(text("ALTER TABLE technical_documents ADD COLUMN lot_id INTEGER;"))
                
                # Migrer les données existantes
                conn.execute(text("""
                    UPDATE technical_documents 
                    SET lot_id = (
                        SELECT l.id 
                        FROM lots l 
                        WHERE l.project_id = technical_documents.project_id 
                        LIMIT 1
                    )
                    WHERE project_id IS NOT NULL
                """))
                
                # Ajouter la contrainte de clé étrangère
                conn.execute(text("""
                    ALTER TABLE technical_documents 
                    ADD CONSTRAINT fk_technical_documents_lot_id 
                    FOREIGN KEY (lot_id) REFERENCES lots(id)
                """))
                
                print("✅ Colonne lot_id ajoutée à technical_documents")
            else:
                print("✅ Colonne lot_id existe déjà dans technical_documents")
            
            # Valider la transaction
            trans.commit()
            print("✅ Migration des lots appliquée avec succès!")
            
        except Exception as e:
            # Annuler la transaction en cas d'erreur
            trans.rollback()
            print(f"❌ Erreur lors de la migration: {e}")
            raise

def main():
    """Fonction principale"""
    print("🚀 Démarrage de l'application de la migration des lots...")
    
    try:
        # Récupérer l'URL de la base de données
        database_url = get_database_url()
        print(f"📊 Connexion à la base de données...")
        
        # Créer le moteur de base de données
        engine = create_engine(database_url)
        
        # Tester la connexion
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✅ Connexion à la base de données réussie")
        
        # Appliquer la migration
        apply_lots_migration(engine)
        
        print("🎉 Migration des lots terminée avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
