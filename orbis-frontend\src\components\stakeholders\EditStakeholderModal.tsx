'use client'

import { useState, useEffect } from 'react'
import { Modal } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Stakeholder, StakeholderUpdate } from '@/types/stakeholder'
import { stakeholdersApiService } from '@/lib/api/stakeholders'
import { RoleService, BusinessRole } from '@/services/roleService'

interface EditStakeholderModalProps {
  isOpen: boolean
  onClose: () => void
  stakeholder: Stakeholder | null
  onStakeholderUpdated: () => void
}

export function EditStakeholderModal({
  isOpen,
  onClose,
  stakeholder,
  onStakeholderUpdated,
}: EditStakeholderModalProps) {
  const [role, setRole] = useState('')
  const [businessRoles, setBusinessRoles] = useState<BusinessRole[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [rolesLoading, setRolesLoading] = useState(false)

  // Charger les rôles métier au montage du composant
  useEffect(() => {
    if (isOpen) {
      loadBusinessRoles()
    }
  }, [isOpen])

  // Initialiser le rôle quand le stakeholder change
  useEffect(() => {
    if (stakeholder) {
      setRole(stakeholder.role || '')
    }
  }, [stakeholder])

  const loadBusinessRoles = async () => {
    try {
      setRolesLoading(true)
      const roles = await RoleService.getBusinessRoles()
      setBusinessRoles(roles)
    } catch (err: any) {
      console.error('Erreur lors du chargement des rôles:', err)
      setError('Erreur lors du chargement des rôles métier')
    } finally {
      setRolesLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!stakeholder) return

    try {
      setLoading(true)
      setError(null)

      const updateData: StakeholderUpdate = {
        role: role.trim() || undefined,
      }

      await stakeholdersApiService.update(stakeholder.id, updateData)
      onStakeholderUpdated()
      handleClose()
    } catch (err: any) {
      setError(err.message || 'Erreur lors de la modification du rôle')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setRole('')
    setError(null)
    onClose()
  }

  const handleRoleSelect = (selectedRole: string) => {
    setRole(selectedRole)
  }

  if (!stakeholder) return null

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Modifier le rôle de l'intervenant">
      <div className="w-full max-w-md">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Informations de l'entreprise */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Entreprise</h4>
            <p className="text-sm text-gray-600">{stakeholder.company?.company_name}</p>
            {stakeholder.company?.activity && (
              <p className="text-xs text-gray-500 mt-1">Activité: {stakeholder.company.activity}</p>
            )}
          </div>

          {/* Sélection du rôle */}
          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-2">
              Rôle de l'intervenant
            </label>
            
            {rolesLoading ? (
              <div className="text-sm text-gray-500">Chargement des rôles...</div>
            ) : (
              <>
                {/* Liste des rôles métier */}
                {businessRoles.length > 0 && (
                  <div className="mb-3">
                    <p className="text-xs text-gray-500 mb-2">Rôles métier disponibles :</p>
                    <div className="grid grid-cols-2 gap-2">
                      {businessRoles.map((businessRole) => (
                        <button
                          key={businessRole.id}
                          type="button"
                          onClick={() => handleRoleSelect(businessRole.name)}
                          className={`p-2 text-xs border rounded-md transition-colors text-left ${
                            role === businessRole.name
                              ? 'border-blue-500 bg-blue-50 text-blue-700'
                              : 'border-gray-300 hover:border-gray-400 text-gray-700'
                          }`}
                        >
                          <div className="font-medium">{businessRole.name}</div>
                          <div className="text-gray-500 text-xs">{businessRole.description}</div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Champ de saisie libre */}
                <Input
                  id="role"
                  type="text"
                  value={role}
                  onChange={(e) => setRole(e.target.value)}
                  placeholder="Ou saisissez un rôle personnalisé..."
                  className="w-full"
                />
              </>
            )}
          </div>

          {/* Message d'erreur */}
          {error && (
            <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
              {error}
            </div>
          )}

          {/* Boutons d'action */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? 'Modification...' : 'Modifier'}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  )
}
