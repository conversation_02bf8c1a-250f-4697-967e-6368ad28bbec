#!/usr/bin/env python3
"""
Script pour vérifier la structure de la base de données avant migration
"""

import asyncio
import asyncpg

DATABASE_URL = "**************************************************************************************************/postgres"

async def check_database_structure():
    """Vérifier la structure actuelle de la base de données"""
    print("🔍 Vérification de la structure de la base de données...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Check existing tables with 'compan' in name
        print("\n📋 Tables contenant 'compan':")
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%compan%'
            ORDER BY table_name
        """)
        
        for table in tables:
            print(f"  - {table['table_name']}")
        
        # 2. Check foreign keys related to companies
        print("\n🔗 Foreign keys liées aux companies:")
        fks = await conn.fetch("""
            SELECT 
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name,
                tc.constraint_name
            FROM information_schema.table_constraints AS tc 
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY' 
            AND (ccu.table_name = 'companies' OR tc.table_name LIKE '%compan%')
            ORDER BY tc.table_name
        """)
        
        for fk in fks:
            print(f"  - {fk['table_name']}.{fk['column_name']} → {fk['foreign_table_name']}.{fk['foreign_column_name']} ({fk['constraint_name']})")
        
        # 3. Check columns with company_id
        print("\n🆔 Colonnes company_id:")
        columns = await conn.fetch("""
            SELECT table_name, column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND column_name = 'company_id'
            ORDER BY table_name
        """)
        
        for col in columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"  - {col['table_name']}.{col['column_name']} ({col['data_type']}, {nullable})")
        
        # 4. Check indexes on companies table
        print("\n📊 Index sur les tables companies:")
        indexes = await conn.fetch("""
            SELECT 
                schemaname,
                tablename,
                indexname,
                indexdef
            FROM pg_indexes 
            WHERE tablename LIKE '%compan%'
            ORDER BY tablename, indexname
        """)
        
        for idx in indexes:
            print(f"  - {idx['tablename']}.{idx['indexname']}")
            print(f"    {idx['indexdef']}")
        
        # 5. Count records in companies table
        print("\n📊 Données dans companies:")
        count = await conn.fetchval("SELECT COUNT(*) FROM companies")
        print(f"  - Nombre d'entreprises: {count}")
        
        if count > 0:
            sample = await conn.fetch("SELECT id, name, code FROM companies LIMIT 3")
            print("  - Exemples:")
            for comp in sample:
                print(f"    • ID: {comp['id']}, Nom: {comp['name']}, Code: {comp['code']}")
        
        print("\n✅ Vérification terminée!")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(check_database_structure())
