'use client'

import { useState } from 'react'
import { ArrowLeftIcon, PencilIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/navigation'

interface BreadcrumbItem {
  label: string
  href?: string
}

interface ModernHeaderProps {
  title: string
  subtitle?: string
  breadcrumbs?: BreadcrumbItem[]
  backButton?: {
    label: string
    href: string
  }
  editButton?: {
    label: string
    onClick: () => void
  }
  user?: {
    name: string
    email: string
    avatar?: string
  }
  onLogout?: () => void
  showTitleInNav?: boolean
}

export default function ModernHeader({
  title,
  subtitle,
  breadcrumbs,
  backButton,
  editButton,
  user,
  onLogout,
  showTitleInNav = false
}: ModernHeaderProps) {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const router = useRouter()

  const handleBackClick = () => {
    if (backButton?.href) {
      router.push(backButton.href)
    }
  }

  return (
    <header className="bg-white border-b border-gray-200 shadow-sm">
      {/* Barre de navigation */}
      <div className="px-6 py-3 border-b border-gray-100" style={showTitleInNav ? { paddingTop: '25px' } : {}}>
        <div className="flex items-center justify-between">
          {/* Left side - Navigation */}
          <div className="flex items-center space-x-4">
            {/* Back Button */}
            {backButton && (
              <button
                onClick={handleBackClick}
                className="flex items-center text-primary-600 hover:text-primary-700 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2" />
                <span className="font-medium">{backButton.label}</span>
              </button>
            )}

            {/* Breadcrumbs */}
            {breadcrumbs && breadcrumbs.length > 0 && (
              <nav className="flex items-center space-x-2 text-sm text-gray-500">
                {breadcrumbs.map((item, index) => (
                  <div key={index} className="flex items-center">
                    {index > 0 && <span className="mx-2">/</span>}
                    {item.href ? (
                      <a href={item.href} className="hover:text-gray-700 transition-colors">
                        {item.label}
                      </a>
                    ) : (
                      <span className="text-gray-900 font-medium">{item.label}</span>
                    )}
                  </div>
                ))}
              </nav>
            )}

            {/* Titre dans la navigation (si activé) - à gauche */}
            {showTitleInNav && title && (
              <h1 className="text-gray-900 ml-6" style={{ fontSize: '2em', fontWeight: 'normal' }}>{title}</h1>
            )}
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center space-x-4">
            {/* Edit Button */}
            {editButton && (
              <button
                onClick={editButton.onClick}
                className="flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
              >
                <PencilIcon className="w-4 h-4 mr-2" />
                <span className="font-medium">{editButton.label}</span>
              </button>
            )}

            {/* User menu */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium text-sm">
                    {user?.name?.charAt(0) || 'U'}
                  </span>
                </div>
                <div className="hidden md:block text-left">
                  <p className="text-sm font-medium text-gray-900">{user?.name || 'Utilisateur'}</p>
                  <p className="text-xs text-gray-500">{user?.email || '<EMAIL>'}</p>
                </div>
              </button>

              {/* User dropdown */}
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 z-50">
                  <div className="py-2">
                    <button className="w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors text-left">
                      Mon Profil
                    </button>
                    <button className="w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors text-left">
                      Paramètres
                    </button>
                    <hr className="my-2 border-gray-200" />
                    <button
                      onClick={onLogout}
                      className="w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors text-left"
                    >
                      Déconnexion
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Titre en dessous (seulement si title n'est pas vide et pas dans la nav) */}
      {title && !showTitleInNav && (
        <div className="px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {subtitle && (
            <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
      )}
    </header>
  )
}
