#!/usr/bin/env python3
"""
Corriger le rôle de l'utilisateur pour utiliser SUPER_ADMIN en majuscules
"""
import asyncio
import asyncpg
from app.core.config import settings

async def fix_user_role():
    print("🔧 Correction du rôle utilisateur en MAJUSCULES...")
    
    try:
        conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)
        
        # 1. Vérifier l'utilisateur actuel
        user = await conn.fetchrow("SELECT id, email, role FROM users WHERE email = '<EMAIL>'")
        if user:
            print(f"🔍 Utilisateur trouvé:")
            print(f"  - ID: {user['id']}")
            print(f"  - Email: {user['email']}")
            print(f"  - Rôle actuel: {user['role']}")
        else:
            print("❌ Utilisateur <EMAIL> non trouvé!")
            await conn.close()
            return False
        
        # 2. Vérifier les valeurs enum autorisées
        enum_values = await conn.fetch("SELECT unnest(enum_range(NULL::userrole)) as role_value")
        print(f"\n📋 Valeurs enum autorisées:")
        for row in enum_values:
            print(f"  - {row['role_value']}")
        
        # 3. Mettre à jour l'enum pour inclure SUPER_ADMIN
        print(f"\n🔄 Ajout de SUPER_ADMIN à l'enum...")
        try:
            await conn.execute("ALTER TYPE userrole ADD VALUE 'SUPER_ADMIN'")
            print("  ✅ SUPER_ADMIN ajouté à l'enum")
        except Exception as e:
            if "already exists" in str(e):
                print("  ℹ️ SUPER_ADMIN existe déjà dans l'enum")
            else:
                print(f"  ❌ Erreur ajout enum: {e}")
        
        # 4. Mettre à jour le rôle de l'utilisateur
        print(f"\n🔄 Mise à jour du rôle utilisateur...")
        await conn.execute("""
            UPDATE users 
            SET role = 'SUPER_ADMIN'
            WHERE email = '<EMAIL>'
        """)
        print("  ✅ Rôle mis à jour vers SUPER_ADMIN")
        
        # 5. Vérification finale
        user_updated = await conn.fetchrow("SELECT id, email, role, is_superuser FROM users WHERE email = '<EMAIL>'")
        print(f"\n✅ Vérification finale:")
        print(f"  - ID: {user_updated['id']}")
        print(f"  - Email: {user_updated['email']}")
        print(f"  - Rôle: {user_updated['role']}")
        print(f"  - Is Superuser: {user_updated['is_superuser']}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_user_role())
    if success:
        print('\n🎉 Correction terminée avec succès!')
        print('✅ Redémarrez le serveur et testez la connexion.')
    else:
        print('\n❌ Échec de la correction')
