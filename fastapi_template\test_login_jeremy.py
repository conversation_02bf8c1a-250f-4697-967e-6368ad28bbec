#!/usr/bin/env python3
"""
Test de <NAME_EMAIL>
"""

import requests
import json
import jwt
import time

def test_login():
    """Test de <NAME_EMAIL>"""

    # URL de l'API
    login_url = "http://localhost:8000/api/v1/auth/login"

    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "orbis123!"
    }

    print("🔐 Test de <NAME_EMAIL>")
    print(f"URL: {login_url}")
    print(f"Données: {json.dumps(login_data, indent=2)}")
    print("-" * 50)

    try:
        # Faire la requête de login
        response = requests.post(
            login_url,
            json=login_data,
            headers={"Content-Type": "application/json"}
        )

        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print("✅ LOGIN RÉUSSI!")
            print(f"Access Token: {data.get('access_token', 'N/A')[:50]}...")
            print(f"User: {json.dumps(data.get('user', {}), indent=2)}")

            # Analyser le token JWT
            analyze_jwt_token(data.get('access_token'))

            # Test d'un endpoint admin avec le token
            test_admin_endpoint(data.get('access_token'))

        else:
            print("❌ LOGIN ÉCHOUÉ!")
            try:
                error_data = response.json()
                print(f"Erreur: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Erreur brute: {response.text}")

    except Exception as e:
        print(f"❌ ERREUR DE CONNEXION: {e}")

def analyze_jwt_token(token):
    """Analyser le contenu du token JWT"""

    if not token:
        print("❌ Pas de token à analyser")
        return

    print("\n" + "="*50)
    print("🔍 Analyse du token JWT")

    try:
        # Décoder le token sans vérification de signature
        decoded = jwt.decode(token, options={"verify_signature": False})

        print(f"Token décodé: {json.dumps(decoded, indent=2)}")

        # Vérifier l'expiration
        exp = decoded.get('exp', 0)
        current_time = time.time()

        if exp > current_time:
            print(f"✅ Token valide (expire dans {int(exp - current_time)} secondes)")
        else:
            print(f"❌ Token expiré depuis {int(current_time - exp)} secondes")

        # Vérifier les claims importants
        print(f"User ID: {decoded.get('sub', 'N/A')}")
        print(f"Email: {decoded.get('email', 'N/A')}")
        print(f"Role: {decoded.get('role', 'N/A')}")
        print(f"Issuer: {decoded.get('iss', 'N/A')}")

    except Exception as e:
        print(f"❌ Erreur décodage token: {e}")

def test_admin_endpoint(token):
    """Test d'un endpoint admin avec le token"""

    if not token:
        print("❌ Pas de token pour tester l'endpoint admin")
        return

    print("\n" + "="*50)
    print("🔧 Test de l'endpoint admin /api/v1/admin/users")

    admin_url = "http://localhost:8000/api/v1/admin/users"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    print(f"Headers envoyés: {json.dumps(headers, indent=2)}")

    try:
        response = requests.get(admin_url, headers=headers)

        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print("✅ ENDPOINT ADMIN ACCESSIBLE!")
            print(f"Nombre d'utilisateurs: {len(data) if isinstance(data, list) else 'N/A'}")
            if isinstance(data, list) and len(data) > 0:
                print(f"Premier utilisateur: {json.dumps(data[0], indent=2)}")
        else:
            print("❌ ENDPOINT ADMIN INACCESSIBLE!")
            try:
                error_data = response.json()
                print(f"Erreur: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Erreur brute: {response.text}")

    except Exception as e:
        print(f"❌ ERREUR ENDPOINT ADMIN: {e}")

def test_middleware_debug():
    """Test pour déboguer le middleware"""

    print("\n" + "="*50)
    print("🐛 Test de débogage du middleware")

    # Test d'un endpoint simple d'abord
    simple_url = "http://localhost:8000/api/v1/auth/me"

    # Récupérer un token d'abord
    login_url = "http://localhost:8000/api/v1/auth/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "orbis123!"
    }

    try:
        login_response = requests.post(login_url, json=login_data)
        if login_response.status_code == 200:
            token = login_response.json().get('access_token')

            # Test de l'endpoint /me
            headers = {"Authorization": f"Bearer {token}"}
            me_response = requests.get(simple_url, headers=headers)

            print(f"Test /auth/me - Status: {me_response.status_code}")
            if me_response.status_code == 200:
                print(f"✅ Middleware fonctionne pour /auth/me")
                print(f"Données: {json.dumps(me_response.json(), indent=2)}")
            else:
                print(f"❌ Middleware échoue pour /auth/me")
                print(f"Erreur: {me_response.text}")

    except Exception as e:
        print(f"❌ Erreur test middleware: {e}")

if __name__ == "__main__":
    test_login()
    test_middleware_debug()
