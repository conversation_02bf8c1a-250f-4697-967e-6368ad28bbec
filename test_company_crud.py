#!/usr/bin/env python3
"""
Test du CRUD complet des entreprises
"""

import requests
import json

API_BASE = "http://localhost:8000"

def get_auth_token():
    """Obtenir un token d'authentification"""
    response = requests.post(f"{API_BASE}/api/v1/fast-auth/login", json={
        "email": "<EMAIL>",
        "password": "orbis123!"
    })
    
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        raise Exception(f"Login failed: {response.text}")

def test_crud_companies():
    """Test CRUD complet des entreprises"""
    print("🏢 Test CRUD Entreprises\n")
    
    # Obtenir le token
    token = get_auth_token()
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 1. CREATE - Créer une nouvelle entreprise
    print("1. 🆕 Test CREATE...")
    new_company_data = {
        "name": "Test Company CRUD",
        "code": "TEST_CRUD_001",
        "description": "Entreprise de test pour le CRUD",
        "address": "123 Rue du Test, 75001 Paris",
        "phone": "***********.89",
        "email": "<EMAIL>",
        "website": "https://www.test-crud.com"
    }
    
    response = requests.post(f"{API_BASE}/api/v1/admin/companies/", 
                           headers=headers, json=new_company_data)
    
    if response.status_code == 200:
        created_company = response.json()
        company_id = created_company['id']
        print(f"✅ CREATE réussi - ID: {company_id}, Nom: {created_company['name']}")
    else:
        print(f"❌ CREATE échoué: {response.text}")
        return False
    
    # 2. READ - Lire la liste des entreprises
    print("\n2. 📖 Test READ (liste)...")
    response = requests.get(f"{API_BASE}/api/v1/admin/companies/", headers=headers)
    
    if response.status_code == 200:
        companies = response.json()
        print(f"✅ READ réussi - {len(companies)} entreprises trouvées")
        
        # Vérifier que notre entreprise est dans la liste
        found = any(c['id'] == company_id for c in companies)
        if found:
            print(f"✅ Entreprise créée trouvée dans la liste")
        else:
            print(f"❌ Entreprise créée non trouvée dans la liste")
    else:
        print(f"❌ READ échoué: {response.text}")
        return False
    
    # 3. UPDATE - Modifier l'entreprise
    print("\n3. ✏️ Test UPDATE...")
    update_data = {
        "name": "Test Company CRUD - MODIFIÉE",
        "description": "Description modifiée pour le test",
        "website": "https://www.test-crud-modified.com"
    }
    
    response = requests.put(f"{API_BASE}/api/v1/admin/companies/{company_id}", 
                          headers=headers, json=update_data)
    
    if response.status_code == 200:
        updated_company = response.json()
        print(f"✅ UPDATE réussi - Nouveau nom: {updated_company['name']}")
        print(f"✅ Description: {updated_company['description']}")
    else:
        print(f"❌ UPDATE échoué: {response.text}")
        return False
    
    # 4. TOGGLE STATUS - Changer le statut
    print("\n4. 🔄 Test TOGGLE STATUS...")
    response = requests.post(f"{API_BASE}/api/v1/admin/companies/{company_id}/toggle-status", 
                           headers=headers)
    
    if response.status_code == 200:
        toggled_company = response.json()
        print(f"✅ TOGGLE STATUS réussi - Statut: {'Actif' if toggled_company['is_active'] else 'Inactif'}")
    else:
        print(f"❌ TOGGLE STATUS échoué: {response.text}")
        return False
    
    # 5. DELETE - Supprimer l'entreprise
    print("\n5. 🗑️ Test DELETE...")
    response = requests.delete(f"{API_BASE}/api/v1/admin/companies/{company_id}", 
                             headers=headers)
    
    if response.status_code == 200:
        print(f"✅ DELETE réussi - Entreprise supprimée")
    else:
        print(f"❌ DELETE échoué: {response.text}")
        return False
    
    # 6. Vérifier que l'entreprise n'existe plus
    print("\n6. 🔍 Vérification suppression...")
    response = requests.get(f"{API_BASE}/api/v1/admin/companies/", headers=headers)
    
    if response.status_code == 200:
        companies = response.json()
        found = any(c['id'] == company_id for c in companies)
        if not found:
            print(f"✅ Vérification réussie - Entreprise bien supprimée")
        else:
            print(f"❌ Vérification échouée - Entreprise encore présente")
            return False
    else:
        print(f"❌ Vérification échouée: {response.text}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Test CRUD complet des entreprises\n")
    
    try:
        success = test_crud_companies()
        
        if success:
            print("\n🎉 Tous les tests CRUD ont réussi !")
            print("✅ CREATE, READ, UPDATE, TOGGLE STATUS, DELETE fonctionnent")
        else:
            print("\n❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"\n💥 Erreur lors des tests: {e}")
    
    print("\n✅ Tests terminés")
