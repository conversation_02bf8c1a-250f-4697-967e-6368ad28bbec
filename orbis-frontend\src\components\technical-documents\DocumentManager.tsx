'use client'

import React, { useState, useEffect } from 'react'
import {
  DocumentType,
  TechnicalDocumentList,
  TechnicalDocumentResponse,
  TechnicalDocumentFilter
} from '@/types/technical-document'
import { FastAuthService } from '@/lib/auth'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
import { 
  PlusIcon, 
  DocumentTextIcon, 
  FunnelIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  UserIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline'

interface DocumentManagerProps {
  projectId?: number
  lotId?: number
  onDocumentSelect: (document: TechnicalDocumentResponse) => void
  onDocumentCreate: (type: DocumentType) => void
}

export default function DocumentManager({
  projectId,
  lotId,
  onDocumentSelect,
  onDocumentCreate
}: DocumentManagerProps) {
  const [documents, setDocuments] = useState<TechnicalDocumentList[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<TechnicalDocumentFilter>({
    project_id: projectId,
    lot_id: lotId,
    is_active: true
  })
  const [showFilters, setShowFilters] = useState(false)

  // Charger les documents
  const loadDocuments = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (filters.project_id) params.append('project_id', filters.project_id.toString())
      if (filters.lot_id) params.append('lot_id', filters.lot_id.toString())
      if (filters.type_document) params.append('type_document', filters.type_document)
      if (filters.search_term) params.append('search_term', filters.search_term)

      const token = FastAuthService.getToken()
      const response = await fetch(`${API_BASE_URL}/api/v1/technical-documents?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des documents')
      }

      const data = await response.json()
      setDocuments(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur inconnue')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadDocuments()
  }, [filters])

  // Gérer la sélection d'un document
  const handleDocumentClick = async (document: TechnicalDocumentList) => {
    try {
      const token = FastAuthService.getToken()
      const response = await fetch(`${API_BASE_URL}/api/v1/technical-documents/${document.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Erreur lors du chargement du document')
      }

      const fullDocument = await response.json()
      onDocumentSelect(fullDocument)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement du document')
    }
  }

  // Filtrer par type de document
  const filterByType = (type: DocumentType | null) => {
    setFilters(prev => ({
      ...prev,
      type_document: type || undefined
    }))
  }

  // Recherche
  const handleSearch = (searchTerm: string) => {
    setFilters(prev => ({
      ...prev,
      search_term: searchTerm || undefined
    }))
  }

  const getDocumentTypeColor = (type: DocumentType) => {
    return type === DocumentType.CCTP 
      ? 'bg-blue-100 text-blue-800 border-blue-200'
      : 'bg-green-100 text-green-800 border-green-200'
  }

  const getDocumentTypeIcon = (type: DocumentType) => {
    return type === DocumentType.CCTP ? '📋' : '📊'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Chargement des documents...</span>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* En-tête avec actions */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Documents Techniques</h2>
          <div className="flex space-x-2">
            <button
              onClick={() => onDocumentCreate(DocumentType.CCTP)}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              CCTP
            </button>
            <button
              onClick={() => onDocumentCreate(DocumentType.DPGF)}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              DPGF
            </button>
          </div>
        </div>

        {/* Filtres rapides */}
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher dans les documents..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={() => filterByType(null)}
              className={`px-3 py-2 text-sm font-medium rounded-md border ${
                !filters.type_document 
                  ? 'bg-gray-100 text-gray-900 border-gray-300' 
                  : 'text-gray-500 border-gray-300 hover:bg-gray-50'
              }`}
            >
              Tous
            </button>
            <button
              onClick={() => filterByType(DocumentType.CCTP)}
              className={`px-3 py-2 text-sm font-medium rounded-md border ${
                filters.type_document === DocumentType.CCTP
                  ? 'bg-blue-100 text-blue-900 border-blue-300'
                  : 'text-gray-500 border-gray-300 hover:bg-gray-50'
              }`}
            >
              CCTP
            </button>
            <button
              onClick={() => filterByType(DocumentType.DPGF)}
              className={`px-3 py-2 text-sm font-medium rounded-md border ${
                filters.type_document === DocumentType.DPGF
                  ? 'bg-green-100 text-green-900 border-green-300'
                  : 'text-gray-500 border-gray-300 hover:bg-gray-50'
              }`}
            >
              DPGF
            </button>
          </div>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FunnelIcon className="h-4 w-4 mr-1" />
            Filtres
          </button>
        </div>
      </div>

      {/* Message d'erreur */}
      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Liste des documents */}
      <div className="divide-y divide-gray-200">
        {documents.length === 0 ? (
          <div className="p-8 text-center">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun document</h3>
            <p className="mt-1 text-sm text-gray-500">
              Commencez par créer votre premier document technique.
            </p>
          </div>
        ) : (
          documents.map((document) => (
            <div
              key={document.id}
              onClick={() => handleDocumentClick(document)}
              className="p-4 hover:bg-gray-50 cursor-pointer transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">
                    {getDocumentTypeIcon(document.type_document!)}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">
                      {document.name}
                    </h3>
                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                      {document.lot && (
                        <span className="flex items-center">
                          <BuildingOfficeIcon className="h-3 w-3 mr-1" />
                          {document.lot.name} {document.lot.project && `(${document.lot.project.name})`}
                        </span>
                      )}
                      {document.creator && (
                        <span className="flex items-center">
                          <UserIcon className="h-3 w-3 mr-1" />
                          {document.creator.first_name} {document.creator.last_name}
                        </span>
                      )}
                      {document.created_at && (
                        <span className="flex items-center">
                          <CalendarIcon className="h-3 w-3 mr-1" />
                          {new Date(document.created_at).toLocaleDateString('fr-FR')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getDocumentTypeColor(document.type_document!)}`}>
                    {document.type_document}
                  </span>
                  {document.indice && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                      v{document.indice}
                    </span>
                  )}
                  {document.company_count && document.company_count > 0 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {document.company_count} entreprise{document.company_count > 1 ? 's' : ''}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}
