import { renderHook, waitFor } from '@testing-library/react'
import { useCreateTechnicalDocument } from '../useTechnicalDocument'
import { FastAuthService } from '@/lib/auth'
import { DocumentType } from '@/types/technical-document'

// Mock FastAuthService
jest.mock('@/lib/auth', () => ({
  FastAuthService: {
    getToken: jest.fn(),
  },
}))

const mockFastAuthService = FastAuthService as jest.Mocked<typeof FastAuthService>

describe('useCreateTechnicalDocument', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    global.fetch = jest.fn()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  it('should create a technical document successfully', async () => {
    // Arrange
    const mockToken = 'mock-jwt-token'
    const mockResponse = {
      id: 1,
      name: 'Test CCTP',
      type_document: DocumentType.CCTP,
      project_id: 7,
      content: '<h1>Test content</h1>',
      companies: []
    }

    mockFastAuthService.getToken.mockReturnValue(mockToken)
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    })

    const { result } = renderHook(() => useCreateTechnicalDocument())

    // Act
    const documentData = {
      name: 'Test CCTP',
      type_document: DocumentType.CCTP,
      project_id: 7,
      content: '<h1>Test content</h1>',
      company_ids: [1]
    }

    let createdDocument
    await waitFor(async () => {
      createdDocument = await result.current.createDocument(documentData)
    })

    // Assert
    expect(mockFastAuthService.getToken).toHaveBeenCalled()
    expect(global.fetch).toHaveBeenCalledWith(
      'http://localhost:8000/api/v1/technical-documents',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(documentData)
      }
    )
    expect(createdDocument).toEqual(mockResponse)
    expect(result.current.loading).toBe(false)
    expect(result.current.error).toBe(null)
  })

  it('should handle authentication error when no token is available', async () => {
    // Arrange
    mockFastAuthService.getToken.mockReturnValue(null)

    const { result } = renderHook(() => useCreateTechnicalDocument())

    // Act & Assert
    const documentData = {
      name: 'Test CCTP',
      type_document: DocumentType.CCTP,
      project_id: 7,
      company_ids: []
    }

    await expect(result.current.createDocument(documentData)).rejects.toThrow('Token d\'authentification manquant')
    expect(global.fetch).not.toHaveBeenCalled()
  })

  it('should handle API error response', async () => {
    // Arrange
    const mockToken = 'mock-jwt-token'
    mockFastAuthService.getToken.mockReturnValue(mockToken)
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 403,
      json: async () => ({ detail: 'Accès refusé' }),
    })

    const { result } = renderHook(() => useCreateTechnicalDocument())

    // Act & Assert
    const documentData = {
      name: 'Test CCTP',
      type_document: DocumentType.CCTP,
      project_id: 7,
      company_ids: []
    }

    await expect(result.current.createDocument(documentData)).rejects.toThrow('Erreur lors de la création du document')
    expect(result.current.loading).toBe(false)
    expect(result.current.error).toBe('Erreur lors de la création du document')
  })

  it('should handle network error', async () => {
    // Arrange
    const mockToken = 'mock-jwt-token'
    mockFastAuthService.getToken.mockReturnValue(mockToken)
    ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

    const { result } = renderHook(() => useCreateTechnicalDocument())

    // Act & Assert
    const documentData = {
      name: 'Test CCTP',
      type_document: DocumentType.CCTP,
      project_id: 7,
      company_ids: []
    }

    await expect(result.current.createDocument(documentData)).rejects.toThrow('Network error')
    expect(result.current.loading).toBe(false)
    expect(result.current.error).toBe('Network error')
  })

  it('should validate required fields', async () => {
    // Arrange
    const mockToken = 'mock-jwt-token'
    mockFastAuthService.getToken.mockReturnValue(mockToken)

    const { result } = renderHook(() => useCreateTechnicalDocument())

    // Act & Assert - Test missing name
    const invalidDocumentData = {
      name: '',
      type_document: DocumentType.CCTP,
      project_id: 7,
      company_ids: []
    }

    await expect(result.current.createDocument(invalidDocumentData)).rejects.toThrow()
  })
})
