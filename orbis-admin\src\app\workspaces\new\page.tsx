'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft } from 'lucide-react'
import { useToast } from '@/components/ui/Toast'
import { FastWorkspaceService } from '@/lib/fast-auth'
import WorkspaceForm from '@/components/WorkspaceForm'
import AuthGuard from '@/components/AuthGuard'

function NewWorkspacePageContent() {
  const { success, error: showError } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleSave = async (workspaceData: {
    name: string
    code: string
    description?: string
    address?: string
    phone?: string
    email?: string
    website?: string
  }) => {
    try {
      setIsLoading(true)
      console.log('🔄 Création de l\'espace de travail...', workspaceData)
      
      const newWorkspace = await FastWorkspaceService.createWorkspace(workspaceData)
      
      console.log('✅ Espace de travail créé:', newWorkspace)
      success('Succès', 'Espace de travail créé avec succès')
      
      // Rediriger vers la liste des espaces de travail
      router.push('/workspaces')
    } catch (error) {
      console.error('❌ Erreur création espace de travail:', error)
      showError('Erreur', error instanceof Error ? error.message : 'Impossible de créer l\'espace de travail')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.push('/workspaces')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={handleCancel}
          className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Nouvel espace de travail
          </h1>
          <p className="text-gray-600 mt-2">
            Créez un nouvel espace de travail pour votre organisation
          </p>
        </div>
      </div>

      {/* Form */}
      <WorkspaceForm
        onSave={handleSave}
        onCancel={handleCancel}
        isLoading={isLoading}
      />
    </div>
  )
}

export default function NewWorkspacePage() {
  return (
    <AuthGuard>
      <NewWorkspacePageContent />
    </AuthGuard>
  )
}
