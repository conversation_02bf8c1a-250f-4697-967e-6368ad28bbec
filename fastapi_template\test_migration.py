#!/usr/bin/env python3
"""
Script pour tester la migration companies → workspaces
"""

import asyncio
import asyncpg

DATABASE_URL = "**************************************************************************************************/postgres"

async def test_migration_dry_run():
    """Test de la migration en mode dry-run"""
    print("🧪 Test de la migration companies → workspaces (DRY RUN)")
    print("="*60)
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Vérifier les tables existantes
        print("\n📋 1. Tables existantes avec 'compan':")
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%compan%'
            ORDER BY table_name
        """)
        
        for table in tables:
            print(f"  - {table['table_name']}")
        
        # 2. Vérifier les contraintes FK
        print("\n🔗 2. Contraintes FK à modifier:")
        fks = await conn.fetch("""
            SELECT 
                tc.table_name,
                tc.constraint_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name
            FROM information_schema.table_constraints AS tc 
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY' 
            AND ccu.table_name = 'companies'
            AND tc.table_name NOT IN ('technical_document_companies', 'project_company')
            ORDER BY tc.table_name
        """)
        
        for fk in fks:
            print(f"  - {fk['table_name']}.{fk['column_name']} → {fk['foreign_table_name']} ({fk['constraint_name']})")
        
        # 3. Vérifier les colonnes company_id à renommer
        print("\n🆔 3. Colonnes company_id à renommer en workspace_id:")
        columns = await conn.fetch("""
            SELECT table_name, column_name
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND column_name = 'company_id'
            AND table_name NOT IN ('technical_document_companies', 'project_company')
            ORDER BY table_name
        """)
        
        for col in columns:
            print(f"  - {col['table_name']}.{col['column_name']}")
        
        # 4. Vérifier les index à mettre à jour
        print("\n📊 4. Index à mettre à jour:")
        indexes = await conn.fetch("""
            SELECT tablename, indexname
            FROM pg_indexes 
            WHERE tablename LIKE '%compan%'
            AND tablename NOT IN ('technical_document_companies', 'project_company')
            ORDER BY tablename, indexname
        """)
        
        for idx in indexes:
            new_name = idx['indexname'].replace('compan', 'workspace')
            if new_name != idx['indexname']:
                print(f"  - {idx['indexname']} → {new_name}")
        
        # 5. Compter les données
        print("\n📊 5. Données existantes:")
        count = await conn.fetchval("SELECT COUNT(*) FROM companies")
        print(f"  - Workspaces (companies): {count}")
        
        user_companies_count = await conn.fetchval("SELECT COUNT(*) FROM user_companies")
        print(f"  - Relations user-workspace: {user_companies_count}")
        
        # 6. Tables à préserver
        print("\n🚫 6. Tables à PRÉSERVER (entreprises tiers):")
        preserved_tables = ['technical_document_companies', 'project_company']
        for table in preserved_tables:
            try:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                print(f"  - {table}: {count} enregistrements")
            except Exception as e:
                print(f"  - {table}: Table non trouvée ({e})")
        
        print("\n✅ Test terminé - Migration prête à être exécutée!")
        print("\n📝 Résumé:")
        print("  - Tables à renommer: companies, company_settings, user_companies, company_role_permissions, company_invitations")
        print("  - Colonnes à renommer: company_id → workspace_id (sauf dans technical_document_companies et project_company)")
        print("  - Contraintes FK à mettre à jour: toutes sauf technical_document_companies et project_company")
        print("  - Tables préservées: technical_document_companies, project_company")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        await conn.close()

async def check_migration_readiness():
    """Vérifier que la migration peut être exécutée en toute sécurité"""
    print("\n🔍 Vérification de la préparation à la migration...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier qu'il n'y a pas de tables workspaces existantes
        existing_workspaces = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%workspace%'
        """)
        
        if existing_workspaces:
            print("❌ ATTENTION: Des tables 'workspace' existent déjà!")
            for table in existing_workspaces:
                print(f"  - {table['table_name']}")
            return False
        
        # Vérifier que les tables companies existent
        companies_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'companies'
            )
        """)
        
        if not companies_exists:
            print("❌ ERREUR: Table 'companies' non trouvée!")
            return False
        
        print("✅ Migration prête à être exécutée!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False
    finally:
        await conn.close()

if __name__ == "__main__":
    async def main():
        await test_migration_dry_run()
        await check_migration_readiness()
    
    asyncio.run(main())
