"""
Tests CRUD complets pour l'entité TCompany
Tests SQLAlchemy et Pydantic avec validation française améliorée
"""

import pytest
import asyncio
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from fastapi.testclient import TestClient
from fastapi import status

# Imports des modèles et schémas
from app.models.tcompany import TCompany
from app.models.workspace import Workspace
from app.models.user import User, UserRole
from app.schemas.tcompany import TCompanyCreate, TCompanyUpdate, TCompanyResponse
from app.core.database import get_db, engine
from app.core.security import create_access_token
from app.main import app

# Configuration des tests
pytestmark = pytest.mark.asyncio

class TestTCompanyCRUD:
    """Tests CRUD pour l'entité TCompany"""
    
    @pytest.fixture
    async def db_session(self):
        """Fixture pour la session de base de données"""
        async with AsyncSession(engine) as session:
            yield session
            await session.rollback()
    
    @pytest.fixture
    async def test_workspace(self, db_session: AsyncSession):
        """Fixture pour créer un workspace de test"""
        workspace = Workspace(
            name="Test Workspace",
            code="TEST_WS",
            description="Workspace pour les tests TCompany",
            is_active=True
        )
        db_session.add(workspace)
        await db_session.commit()
        await db_session.refresh(workspace)
        return workspace
    
    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        """Fixture pour créer un utilisateur de test"""
        user = User(
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            role=UserRole.ADMIN,
            is_active=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user
    
    @pytest.fixture
    def client(self):
        """Client de test FastAPI"""
        return TestClient(app)
    
    # Tests SQLAlchemy
    
    async def test_create_tcompany_sqlalchemy(
        self, 
        db_session: AsyncSession, 
        test_workspace: Workspace,
        test_user: User
    ):
        """Test création d'une TCompany avec SQLAlchemy"""
        # Données de test avec noms anglais
        tcompany_data = {
            "company_name": "SARL Construction Test",
            "activity": "Maçonnerie générale",
            "address": "123 Avenue de la Construction",
            "postal_code": "75001",
            "city": "Paris",
            "country": "France",
            "phone": "0*********",
            "fax": "0123456790",
            "email": "<EMAIL>",
            "siret": "*********01234",
            "vat_number": "FR*********01",
            "legal_representative_id": test_user.id,
            "workspace_id": test_workspace.id,
            "created_by": test_user.id,
            "is_active": True
        }
        
        # Création
        tcompany = TCompany(**tcompany_data)
        db_session.add(tcompany)
        await db_session.commit()
        await db_session.refresh(tcompany)
        
        # Vérifications
        assert tcompany.id is not None
        assert tcompany.company_name == "SARL Construction Test"
        assert tcompany.activity == "Maçonnerie générale"
        assert tcompany.siret == "*********01234"
        assert tcompany.workspace_id == test_workspace.id
        assert tcompany.created_by == test_user.id
        assert tcompany.created_at is not None
        assert tcompany.updated_at is not None
        assert tcompany.is_active is True
        
        # Test des propriétés de compatibilité
        assert tcompany.nom_entreprise == "SARL Construction Test"
        assert tcompany.activite == "Maçonnerie générale"
        assert tcompany.adresse == "123 Avenue de la Construction"
        assert tcompany.code_postal == "75001"
        assert tcompany.ville == "Paris"
        assert tcompany.pays == "France"
        assert tcompany.telephone == "0*********"
        assert tcompany.tva_intracommunautaire == "FR*********01"
        assert tcompany.representant_legal_id == test_user.id
        
        # Test des méthodes utilitaires
        assert tcompany.is_siret_valid() is True
        assert "123 Avenue de la Construction" in tcompany.get_full_address()
        assert "75001 Paris" in tcompany.get_full_address()
        
        contact_info = tcompany.get_contact_info()
        assert contact_info["phone"] == "0*********"
        assert contact_info["email"] == "<EMAIL>"
    
    # Tests de validation Pydantic avec validation française
    
    def test_tcompany_create_schema_validation(self):
        """Test validation du schéma TCompanyCreate avec validation française"""
        # Données valides
        valid_data = {
            "company_name": "Nouvelle Entreprise",
            "activity": "Construction générale",
            "address": "456 Rue de la Paix",
            "postal_code": "69001",
            "city": "Lyon",
            "email": "<EMAIL>",
            "siret": "*********01234",
            "phone": "0412345678"
        }
        
        tcompany_create = TCompanyCreate(**valid_data)
        assert tcompany_create.company_name == "Nouvelle Entreprise"
        assert tcompany_create.postal_code == "69001"
        assert tcompany_create.siret == "*********01234"
        assert tcompany_create.phone == "0412345678"
    
    def test_siret_validation(self):
        """Test validation spécifique du SIRET français"""
        # SIRET valide
        valid_data = {
            "company_name": "Test SIRET",
            "siret": "*********01234"
        }
        tcompany = TCompanyCreate(**valid_data)
        assert tcompany.siret == "*********01234"
        
        # SIRET avec espaces (doit être nettoyé)
        valid_data_spaces = {
            "company_name": "Test SIRET Espaces",
            "siret": "*********** 01234"
        }
        tcompany_spaces = TCompanyCreate(**valid_data_spaces)
        assert tcompany_spaces.siret == "*********01234"
        
        # SIRET invalides
        invalid_sirets = [
            "*********",  # Trop court
            "*********012345",  # Trop long
            "*********0123A",  # Contient une lettre
        ]
        
        for invalid_siret in invalid_sirets:
            with pytest.raises(ValueError):
                TCompanyCreate(company_name="Test", siret=invalid_siret)
    
    def test_phone_validation(self):
        """Test validation du numéro de téléphone français"""
        # Numéros valides
        valid_phones = [
            "0*********",  # Format national
            "+33*********",  # Format international avec +
            "33*********",  # Format international sans +
        ]
        
        for phone in valid_phones:
            tcompany = TCompanyCreate(company_name="Test Phone", phone=phone)
            assert tcompany.phone is not None
        
        # Numéros avec formatage (doivent être nettoyés)
        formatted_phone = "01.23.45.67.89"
        tcompany_formatted = TCompanyCreate(company_name="Test Formatted", phone=formatted_phone)
        assert tcompany_formatted.phone == "0*********"
        
        # Numéros invalides
        invalid_phones = [
            "*********",  # Trop court
            "0*********0",  # Trop long
            "0023456789",  # Commence par 00
            "+44*********",  # Pas français
        ]
        
        for invalid_phone in invalid_phones:
            with pytest.raises(ValueError):
                TCompanyCreate(company_name="Test", phone=invalid_phone)
    
    def test_postal_code_validation(self):
        """Test validation du code postal français"""
        # Codes postaux valides
        valid_codes = ["75001", "69000", "13000", "59000"]
        
        for code in valid_codes:
            tcompany = TCompanyCreate(company_name="Test Code", postal_code=code)
            assert tcompany.postal_code == code
        
        # Code postal avec formatage (doit être nettoyé)
        formatted_code = "7 5 0 0 1"
        tcompany_formatted = TCompanyCreate(company_name="Test Formatted", postal_code=formatted_code)
        assert tcompany_formatted.postal_code == "75001"
        
        # Codes postaux invalides
        invalid_codes = [
            "7500",  # Trop court
            "750001",  # Trop long
            "00000",  # Invalide
            "A5001",  # Contient une lettre
        ]
        
        for invalid_code in invalid_codes:
            with pytest.raises(ValueError):
                TCompanyCreate(company_name="Test", postal_code=invalid_code)


if __name__ == "__main__":
    # Exécution des tests
    pytest.main([__file__, "-v"])
