'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { Building2, Mail, User, CheckCircle, XCircle, Clock } from 'lucide-react'
import Link from 'next/link'

interface InvitationData {
  invitation: {
    id: number
    email: string
    role: string
    company_name: string
    expires_at: string
  }
  company: {
    id: number
    name: string
    address: string
  }
}

export default function AcceptInvitationPage() {
  const searchParams = useSearchParams()
  const token = searchParams.get('token')
  
  const [invitationData, setInvitationData] = useState<InvitationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [accepting, setAccepting] = useState(false)
  const [accepted, setAccepted] = useState(false)
  
  const [userForm, setUserForm] = useState({
    first_name: '',
    last_name: '',
    password: '',
    confirm_password: ''
  })

  useEffect(() => {
    if (token) {
      fetchInvitationData()
    } else {
      setError('Token d\'invitation manquant')
      setLoading(false)
    }
  }, [token])

  const fetchInvitationData = async () => {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/invitations/accept/${token}`)
      // if (!response.ok) throw new Error('Invalid invitation')
      // const data = await response.json()
      
      // Mock data for now
      const mockData: InvitationData = {
        invitation: {
          id: 1,
          email: "<EMAIL>",
          role: "user",
          company_name: "ORBIS",
          expires_at: "2024-12-31T23:59:59Z"
        },
        company: {
          id: 1,
          name: "ORBIS",
          address: "123 Avenue de la Construction, 75001 Paris"
        }
      }
      
      setInvitationData(mockData)
    } catch (error) {
      console.error('Error fetching invitation:', error)
      setError('Invitation invalide ou expirée')
    } finally {
      setLoading(false)
    }
  }

  const handleAccept = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (userForm.password !== userForm.confirm_password) {
      alert('Les mots de passe ne correspondent pas')
      return
    }
    
    setAccepting(true)
    
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/invitations/confirm/${token}`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     first_name: userForm.first_name,
      //     last_name: userForm.last_name,
      //     password: userForm.password
      //   })
      // })
      
      // Mock success
      setAccepted(true)
      
    } catch (error) {
      console.error('Error accepting invitation:', error)
      alert('Erreur lors de l\'acceptation de l\'invitation')
    } finally {
      setAccepting(false)
    }
  }

  const getRoleName = (role: string) => {
    const roles = {
      admin: 'Administrateur',
      manager: 'Manager',
      user: 'Utilisateur',
      viewer: 'Lecteur'
    }
    return roles[role as keyof typeof roles] || role
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <XCircle className="mx-auto h-16 w-16 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Invitation Invalide</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg inline-block"
          >
            Retour à l'accueil
          </Link>
        </div>
      </div>
    )
  }

  if (accepted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Invitation Acceptée!</h2>
          <p className="text-gray-600 mb-6">
            Votre compte a été créé avec succès. Vous pouvez maintenant vous connecter à l'application.
          </p>
          <Link
            href={process.env.NEXT_PUBLIC_CUSTOMER_APP_URL || 'http://localhost:3000'}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg inline-block"
          >
            Se connecter à l'application
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white rounded-lg shadow-lg p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <Building2 className="mx-auto h-12 w-12 text-blue-600 mb-4" />
            <h2 className="text-3xl font-bold text-gray-900">Invitation ORBIS</h2>
            <p className="mt-2 text-gray-600">
              Vous avez été invité à rejoindre une entreprise
            </p>
          </div>

          {/* Invitation Details */}
          {invitationData && (
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Détails de l'invitation</h3>
              
              <div className="space-y-3">
                <div className="flex items-center">
                  <Building2 className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">{invitationData.company.name}</p>
                    <p className="text-sm text-gray-500">{invitationData.company.address}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">{invitationData.invitation.email}</p>
                    <p className="text-sm text-gray-500">Votre adresse email</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <User className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">{getRoleName(invitationData.invitation.role)}</p>
                    <p className="text-sm text-gray-500">Votre rôle dans l'entreprise</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">
                      {new Date(invitationData.invitation.expires_at).toLocaleDateString('fr-FR')}
                    </p>
                    <p className="text-sm text-gray-500">Date d'expiration</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* User Form */}
          <form onSubmit={handleAccept} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prénom
                </label>
                <input
                  type="text"
                  required
                  value={userForm.first_name}
                  onChange={(e) => setUserForm({ ...userForm, first_name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom
                </label>
                <input
                  type="text"
                  required
                  value={userForm.last_name}
                  onChange={(e) => setUserForm({ ...userForm, last_name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mot de passe
              </label>
              <input
                type="password"
                required
                value={userForm.password}
                onChange={(e) => setUserForm({ ...userForm, password: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                minLength={8}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Confirmer le mot de passe
              </label>
              <input
                type="password"
                required
                value={userForm.confirm_password}
                onChange={(e) => setUserForm({ ...userForm, confirm_password: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                minLength={8}
              />
            </div>

            <button
              type="submit"
              disabled={accepting}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-2 px-4 rounded-md font-medium transition-colors"
            >
              {accepting ? 'Acceptation en cours...' : 'Accepter l\'invitation'}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500">
              En acceptant cette invitation, vous acceptez les conditions d'utilisation d'ORBIS.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
