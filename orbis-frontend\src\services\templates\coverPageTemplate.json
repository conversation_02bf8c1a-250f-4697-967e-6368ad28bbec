{"name": "Template Page de Garde Standard", "version": "1.0", "description": "Template par défaut pour les pages de garde CCTP", "sections": [{"id": "main-container", "type": "container", "styles": {"pageBreakAfter": "always", "fontFamily": "<PERSON>l, sans-serif", "padding": "30px", "margin": "0", "backgroundColor": "white", "textAlign": "center"}, "children": [{"id": "content-wrapper", "type": "container", "styles": {"display": "inline-block", "textAlign": "center", "maxWidth": "1200px"}, "children": [{"id": "title-section", "type": "table", "styles": {"width": "100%", "borderCollapse": "collapse", "border": "2px solid #000", "marginBottom": "10px", "fontFamily": "<PERSON>l, sans-serif"}, "content": {"rows": [{"cells": [{"styles": {"padding": "30px", "textAlign": "center", "backgroundColor": "white", "border": "none"}, "children": [{"type": "text", "content": "{{title}}", "styles": {"fontSize": "30px", "fontWeight": "bold", "color": "#333", "marginBottom": "8px", "textAlign": "center"}}, {"type": "text", "content": "{{projectName}}", "styles": {"fontSize": "25px", "color": "#333", "textAlign": "center"}}]}]}]}}, {"id": "moa-section", "type": "table", "styles": {"width": "100%", "borderCollapse": "collapse", "border": "2px solid #000", "marginBottom": "10px"}, "content": {"rows": [{"cells": [{"content": "MAITRE D'OUVRAGE", "styles": {"padding": "8px", "textAlign": "center", "backgroundColor": "#f0f0f0", "fontWeight": "bold", "fontSize": "14px", "border": "none"}}]}, {"cells": [{"type": "company-with-logo", "content": "{{moaName}}", "logoUrl": "{{moaLogoUrl}}", "styles": {"padding": "15px", "textAlign": "center", "backgroundColor": "white", "fontSize": "16px", "fontWeight": "bold", "border": "none"}}]}, {"cells": [{"content": "{{moa<PERSON><PERSON><PERSON>}}", "styles": {"padding": "10px", "textAlign": "center", "backgroundColor": "white", "fontSize": "12px", "border": "none"}}]}]}}, {"id": "moe-section", "type": "table", "styles": {"width": "100%", "borderCollapse": "collapse", "border": "2px solid #000", "marginBottom": "10px"}, "content": {"rows": [{"cells": [{"content": "MAITRISE D'ŒUVRE", "styles": {"padding": "8px", "textAlign": "center", "backgroundColor": "#f0f0f0", "fontWeight": "bold", "fontSize": "14px", "border": "none"}}]}, {"cells": [{"type": "company-with-logo", "content": "{{moeName}}", "logoUrl": "{{moeLogoUrl}}", "styles": {"padding": "15px", "textAlign": "center", "backgroundColor": "white", "fontSize": "16px", "fontWeight": "bold", "border": "none"}}]}]}}, {"id": "other-companies-section", "type": "dynamic-table", "condition": "{{hasOtherCompanies}}", "styles": {"width": "100%", "borderCollapse": "collapse", "border": "2px solid #000", "marginBottom": "10px"}, "dataSource": "otherCompanies", "template": {"header": {"content": "AUTRES INTERVENANTS", "styles": {"padding": "8px", "textAlign": "center", "backgroundColor": "#f0f0f0", "fontWeight": "bold", "fontSize": "14px", "border": "none"}}, "row": {"cells": [{"content": "{{role}} : {{name}}", "styles": {"padding": "8px", "textAlign": "center", "backgroundColor": "white", "fontSize": "12px", "border": "none"}}]}}}, {"id": "lot-section", "type": "table", "styles": {"width": "100%", "borderCollapse": "collapse", "border": "2px solid #000", "marginBottom": "10px"}, "content": {"rows": [{"cells": [{"content": "{{lotTitle}}", "styles": {"padding": "15px", "textAlign": "center", "backgroundColor": "white", "fontSize": "18px", "fontWeight": "bold", "border": "none"}}]}]}}, {"id": "document-info-section", "type": "table", "styles": {"width": "100%", "borderCollapse": "collapse", "border": "2px solid #000"}, "content": {"rows": [{"cells": [{"content": "DOCUMENT", "styles": {"padding": "8px", "textAlign": "center", "backgroundColor": "white", "fontSize": "12px", "fontWeight": "bold", "border": "none", "width": "33%"}}, {"content": "INDICE", "styles": {"padding": "8px", "textAlign": "center", "backgroundColor": "white", "fontSize": "12px", "fontWeight": "bold", "border": "none", "width": "33%"}}, {"content": "DATE", "styles": {"padding": "8px", "textAlign": "center", "backgroundColor": "white", "fontSize": "12px", "fontWeight": "bold", "border": "none", "width": "34%"}}]}, {"cells": [{"content": "{{documentType}}", "styles": {"padding": "8px", "textAlign": "center", "backgroundColor": "white", "fontSize": "12px", "fontWeight": "bold", "border": "none"}}, {"content": "{{documentIndice}}", "styles": {"padding": "8px", "textAlign": "center", "backgroundColor": "white", "fontSize": "12px", "fontWeight": "bold", "border": "none"}}, {"content": "{{documentDate}}", "styles": {"padding": "8px", "textAlign": "center", "backgroundColor": "white", "fontSize": "12px", "fontWeight": "bold", "border": "none"}}]}]}}]}]}]}