'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { projectsAPI, Project } from '@/lib/api/projects'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import DataList, { FilterField, DataListAction } from '@/components/ui/DataList'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import {
  Plus,
  Building2,
  Calendar,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Edit,
  Trash2
} from 'lucide-react'

// Types pour les dossiers (utilise le type Project de l'API)
type Dossier = Project

function DossiersPageContent() {
  const { user, signOut } = useAuth()
  const searchParams = useSearchParams()
  const [dossiers, setDossiers] = useState<Dossier[]>([])
  const [filteredDossiers, setFilteredDossiers] = useState<Dossier[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState<any>(null)

  // Détecter le type de page basé sur les paramètres URL
  const filterParam = searchParams.get('filter')
  const isFilteredView = ['devis', 'ao', 'affaires'].includes(filterParam || '')

  // Déterminer le titre et le filtre de nature
  const getPageConfig = () => {
    switch (filterParam) {
      case 'devis':
        return { title: 'Devis', nature: 'DEVIS' }
      case 'ao':
        return { title: 'AO', nature: 'AO' }
      case 'affaires':
        return { title: 'Affaires', nature: 'AFFAIRE' }
      default:
        return { title: 'Dossiers', nature: null }
    }
  }

  const pageConfig = getPageConfig()

  // Gérer les filtres depuis l'URL
  useEffect(() => {
    const urlFilter = searchParams.get('filter')
    if (urlFilter === 'devis') {
      handleFiltersChange({ nature: 'DEVIS' })
    } else if (urlFilter === 'ao') {
      handleFiltersChange({ nature: 'AO' })
    } else if (urlFilter === 'affaires') {
      handleFiltersChange({ nature: 'AFFAIRE' })
    }
  }, [searchParams])

  useEffect(() => {
    const fetchDossiers = async () => {
      try {
        setLoading(true)

        // Récupérer les projets et les statistiques en parallèle
        const [projectsData, statsData] = await Promise.all([
          projectsAPI.getProjects({
            limit: 1000 // Récupérer tous les projets pour l'instant
          }),
          projectsAPI.getProjectsStats()
        ])

        const projects = projectsData || []
        console.log('🔍 Projets récupérés:', projects)
        console.log('📊 Stats:', statsData)
        setDossiers(projects)
        setFilteredDossiers(projects)
        setStats(statsData)
        setError(null)
      } catch (err: any) {
        console.error('Error fetching dossiers:', err)
        setError(err.message || 'Erreur lors du chargement des dossiers')
        setDossiers([])
        setFilteredDossiers([])
        setStats(null)
      } finally {
        setLoading(false)
      }
    }

    fetchDossiers()
  }, [])

  // Fonction de déconnexion
  const handleLogout = () => {
    signOut()
  }

  // Fonction pour formater les montants
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Fonctions de conversion entre backend et frontend
  const convertStatusToDisplay = (status: string) => {
    const statusMap: Record<string, string> = {
      'EN_COURS': 'En cours',
      'EN_ATTENTE': 'En attente',
      'TERMINE': 'Terminé',
      'ARCHIVE': 'Archivé'
    }
    return statusMap[status] || status
  }

  const convertNatureToDisplay = (nature: string) => {
    const natureMap: Record<string, string> = {
      'DEVIS': 'Devis',
      'AO': 'AO',
      'AFFAIRE': 'Affaire'
    }
    return natureMap[nature] || nature
  }

  // Configuration des filtres
  const getFilters = (): FilterField[] => {
    if (isFilteredView) {
      // Filtres simplifiés pour les vues filtrées (sans nature, sans labels)
      return [
        {
          key: 'name',
          label: '',
          type: 'text',
          placeholder: 'Rechercher par nom...'
        },
        {
          key: 'client_name',
          label: '',
          type: 'text',
          placeholder: 'Rechercher par client...'
        },
        {
          key: 'status',
          label: '',
          type: 'select',
          placeholder: 'Statut',
          options: [
            { value: '', label: 'Tous' },
            { value: 'EN_COURS', label: 'En cours' },
            { value: 'EN_ATTENTE', label: 'En attente' },
            { value: 'TERMINE', label: 'Terminé' },
            { value: 'ARCHIVE', label: 'Archivé' }
          ]
        }
      ]
    } else {
      // Filtres complets pour la vue générale
      return [
        {
          key: 'name',
          label: 'Nom du projet',
          type: 'text',
          placeholder: 'Rechercher par nom...'
        },
        {
          key: 'client_name',
          label: 'Client',
          type: 'text',
          placeholder: 'Rechercher par client...'
        },
        {
          key: 'nature',
          label: 'Nature',
          type: 'select',
          options: [
            { value: 'DEVIS', label: '📋 Devis' },
            { value: 'AO', label: '📢 AO' },
            { value: 'AFFAIRE', label: '🤝 Affaire' }
          ]
        },
        {
          key: 'status',
          label: 'Statut',
          type: 'select',
          options: [
            { value: 'EN_COURS', label: 'En cours' },
            { value: 'EN_ATTENTE', label: 'En attente' },
            { value: 'TERMINE', label: 'Terminé' },
            { value: 'ARCHIVE', label: 'Archivé' }
          ]
        }
      ]
    }
  }

  const filters = getFilters()

  // Configuration des actions
  const getActionLabel = () => {
    switch (filterParam) {
      case 'devis':
        return 'Nouveau Devis'
      case 'ao':
        return 'Nouveau AO'
      case 'affaires':
        return 'Nouvelle Affaire'
      default:
        return 'Nouveau Dossier'
    }
  }

  const actions: DataListAction[] = [
    {
      label: getActionLabel(),
      onClick: () => window.location.href = '/projects/create',
      icon: <Plus className="w-5 h-5" />,
      variant: 'primary'
    }
  ]

  // Gérer les filtres
  const handleFiltersChange = (filterValues: Record<string, string>) => {
    console.log('🔍 Filtrage avec:', filterValues)
    console.log('📋 Dossiers disponibles:', dossiers.length)
    let filtered = [...dossiers]

    Object.entries(filterValues).forEach(([key, value]) => {
      if (value) {
        filtered = filtered.filter(dossier => {
          switch (key) {
            case 'name':
              return dossier.name?.toLowerCase().includes(value.toLowerCase()) ||
                     dossier.code?.toLowerCase().includes(value.toLowerCase())
            case 'client_name':
              return dossier.client_name?.toLowerCase().includes(value.toLowerCase())
            case 'nature':
              return dossier.nature === value
            case 'status':
              return dossier.status === value
            default:
              return true
          }
        })
      }
    })

    console.log('✅ Dossiers filtrés:', filtered.length, filtered)
    setFilteredDossiers(filtered)
  }

  // Fonction pour obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'En cours':
        return 'bg-blue-100 text-blue-800'
      case 'Terminé':
        return 'bg-green-100 text-green-800'
      case 'En attente':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Fonction pour obtenir l'icône du statut
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'En cours':
        return <Clock className="w-4 h-4" />
      case 'Terminé':
        return <CheckCircle className="w-4 h-4" />
      case 'En attente':
        return <AlertCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // Fonction pour obtenir la couleur de la nature
  const getNatureColor = (nature: string) => {
    switch (nature) {
      case 'Devis':
        return 'bg-purple-100 text-purple-800'
      case 'AO':
        return 'bg-orange-100 text-orange-800'
      case 'Affaire':
        return 'bg-teal-100 text-teal-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Fonction pour obtenir l'icône de la nature
  const getNatureIcon = (nature: string) => {
    switch (nature) {
      case 'Devis':
        return '📋'
      case 'AO':
        return '📢'
      case 'Affaire':
        return '🤝'
      default:
        return '📁'
    }
  }

  // Rendu d'un élément en grille
  const renderGridItem = (dossier: Dossier) => (
    <div className="p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-lg font-semibold text-gray-900">{dossier.name}</h3>
            <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getNatureColor(convertNatureToDisplay(dossier.nature))}`}>
              <span className="mr-1">{getNatureIcon(convertNatureToDisplay(dossier.nature))}</span>
              {convertNatureToDisplay(dossier.nature)}
            </div>
          </div>
          <p className="text-sm text-gray-500 mb-2">{dossier.code}</p>
          <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getStatusColor(convertStatusToDisplay(dossier.status))}`}>
            {getStatusIcon(convertStatusToDisplay(dossier.status))}
            <span className="ml-1">{convertStatusToDisplay(dossier.status)}</span>
          </div>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-4 line-clamp-2">{dossier.description}</p>

      <div className="space-y-3">
        <div className="flex items-center text-sm text-gray-600">
          <Users className="w-4 h-4 mr-2" />
          <span>{dossier.client_name}</span>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <Calendar className="w-4 h-4 mr-2" />
          <span>{dossier.start_date ? new Date(dossier.start_date).toLocaleDateString('fr-FR') : 'N/A'} - {dossier.end_date ? new Date(dossier.end_date).toLocaleDateString('fr-FR') : 'N/A'}</span>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <TrendingUp className="w-4 h-4 mr-2" />
          <span>{formatCurrency(dossier.budget_total || 0)}</span>
        </div>
      </div>

      <div className="mt-4">
        <div className="flex items-center justify-between text-sm mb-2">
          <span className="text-gray-600">Progression</span>
          <span className="font-medium text-gray-900">{(dossier as any).progress || 0}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-500"
            style={{ width: `${(dossier as any).progress || 0}%` }}
          ></div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end pt-4 border-t border-gray-100 mt-4">
        <div className="flex items-center space-x-2">
          <button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              // TODO: Implémenter l'édition
            }}
            className="p-1.5 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <Edit className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              // TODO: Implémenter la suppression
            }}
            className="p-1.5 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )

  // Rendu d'un élément en liste
  const renderListItem = (dossier: Dossier) => (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 flex-1">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center text-2xl">
              {getNatureIcon(convertNatureToDisplay(dossier.nature))}
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-1">
              <h3 className="text-lg font-semibold text-gray-900">{dossier.name}</h3>
              <span className="text-sm text-gray-500">({dossier.code})</span>
              <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getNatureColor(convertNatureToDisplay(dossier.nature))}`}>
                <span className="mr-1">{getNatureIcon(convertNatureToDisplay(dossier.nature))}</span>
                {convertNatureToDisplay(dossier.nature)}
              </div>
              <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getStatusColor(convertStatusToDisplay(dossier.status))}`}>
                {getStatusIcon(convertStatusToDisplay(dossier.status))}
                <span className="ml-1">{convertStatusToDisplay(dossier.status)}</span>
              </div>
            </div>
            <p className="text-sm text-gray-600 mb-2">{dossier.description}</p>
            <div className="flex items-center space-x-6 text-sm text-gray-500">
              <span className="flex items-center">
                <Users className="w-4 h-4 mr-1" />
                {dossier.client_name}
              </span>
              <span className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {dossier.end_date ? new Date(dossier.end_date).toLocaleDateString('fr-FR') : 'N/A'}
              </span>
              <span className="flex items-center">
                <TrendingUp className="w-4 h-4 mr-1" />
                {formatCurrency(dossier.budget_total || 0)}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">{(dossier as any).progress || 0}%</div>
            <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
              <div
                className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full"
                style={{ width: `${(dossier as any).progress || 0}%` }}
              ></div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                // TODO: Implémenter l'édition
              }}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                // TODO: Implémenter la suppression
              }}
              className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  if (error && dossiers.length === 0) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-white">
          <div className="flex">
            <ModernSidebar user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined} />
            <div className="flex-1 lg:ml-72">
              <ModernHeader
                title="Dossiers"
                subtitle="Erreur de chargement"
                user={user ? {
                  name: `${user.first_name} ${user.last_name}`,
                  email: user.email
                } : undefined}
                onLogout={handleLogout}
              />
              <main className="p-6">
                <div className="text-center py-12">
                  <div className="text-red-500 text-4xl mb-4">⚠️</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Erreur de chargement</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <button 
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
                  >
                    Réessayer
                  </button>
                </div>
              </main>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="flex">
        <ModernSidebar user={user ? {
          name: `${user.first_name} ${user.last_name}`,
          email: user.email
        } : undefined} />

        <div className="flex-1 lg:ml-72">
          <ModernHeader
            title={pageConfig.title}
            subtitle={isFilteredView
              ? ""
              : `Gestion de vos dossiers (Devis, AO, Affaires) • ${filteredDossiers.length} dossier${filteredDossiers.length > 1 ? 's' : ''}`
            }
            showTitleInNav={isFilteredView}
            user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined}
            onLogout={handleLogout}
          />

          <main className="p-6">
            {/* Statistiques rapides (seulement pour la vue générale) */}
            {!isFilteredView && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Budget Total</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats ? formatCurrency(stats.total_budget) : formatCurrency(0)}
                    </p>
                  </div>
                  <div className="p-3 bg-primary-100 rounded-lg">
                    <Building2 className="w-6 h-6 text-primary-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Devis</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats ? formatCurrency(stats.budgets_by_nature?.DEVIS || 0) : formatCurrency(0)}
                    </p>
                  </div>
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <div className="w-6 h-6 text-purple-600 text-sm font-bold flex items-center justify-center">📋</div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Appels d'Offres</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats ? formatCurrency(stats.budgets_by_nature?.AO || 0) : formatCurrency(0)}
                    </p>
                  </div>
                  <div className="p-3 bg-orange-100 rounded-lg">
                    <div className="w-6 h-6 text-orange-600 text-sm font-bold flex items-center justify-center">📢</div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Affaires</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats ? formatCurrency(stats.budgets_by_nature?.AFFAIRE || 0) : formatCurrency(0)}
                    </p>
                  </div>
                  <div className="p-3 bg-teal-100 rounded-lg">
                    <div className="w-6 h-6 text-teal-600 text-sm font-bold flex items-center justify-center">🤝</div>
                  </div>
                </div>
              </div>
              </div>
            )}

            <DataList
              items={filteredDossiers}
              loading={loading}
              title={isFilteredView ? "" : "Dossiers"}
              subtitle={isFilteredView ? "" : `${filteredDossiers.length} dossier${filteredDossiers.length > 1 ? 's' : ''} trouvé${filteredDossiers.length > 1 ? 's' : ''}`}
              emptyMessage="Aucun dossier trouvé. Commencez par créer votre premier dossier."
              emptyIcon={<Building2 />}
              getItemUrl={(item) => `/projects/${item.id}`}
              filters={filters}
              onFiltersChange={handleFiltersChange}
              hideFiltersTitle={isFilteredView}
              actions={actions}
              actionsPosition={isFilteredView ? "left" : "right"}
              renderGridItem={renderGridItem}
              renderListItem={renderListItem}
              defaultViewMode="grid"
              gridCols={3}
            />
          </main>
        </div>
      </div>
    </div>
  )
}

export default function Dossiers() {
  return (
    <ProtectedRoute>
      <DossiersPageContent />
    </ProtectedRoute>
  )
}
