#!/usr/bin/env python3
"""
Script pour appliquer la migration des stakeholders
Renomme la table lot_intervenants en stakeholders
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from alembic.config import Config
from alembic import command
from app.core.database import engine
from sqlalchemy import text

def apply_migration():
    """Applique la migration Alembic"""
    print("🚀 Application de la migration stakeholders...")
    
    try:
        # Configuration Alembic
        alembic_cfg = Config("alembic.ini")
        
        # Appliquer la migration
        command.upgrade(alembic_cfg, "head")
        print("✅ Migration appliquée avec succès")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors de l'application de la migration: {e}")
        return False

def verify_migration():
    """Vérifie que la migration a été appliquée correctement"""
    print("\n🔍 Vérification de la migration...")
    
    try:
        with engine.connect() as conn:
            # Vérifier que la table stakeholders existe
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'stakeholders'
            """))
            
            if result.fetchone():
                print("✅ Table 'stakeholders' créée avec succès")
            else:
                print("❌ Table 'stakeholders' non trouvée")
                return False
            
            # Vérifier que l'ancienne table lot_intervenants n'existe plus
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'lot_intervenants'
            """))
            
            if not result.fetchone():
                print("✅ Ancienne table 'lot_intervenants' supprimée")
            else:
                print("⚠️  Ancienne table 'lot_intervenants' existe encore")
            
            # Vérifier les index
            result = conn.execute(text("""
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = 'stakeholders'
            """))
            
            indexes = [row[0] for row in result.fetchall()]
            expected_indexes = ['ix_stakeholders_id', 'ix_stakeholders_lot_id', 'ix_stakeholders_company_id']
            
            for expected in expected_indexes:
                if expected in indexes:
                    print(f"✅ Index '{expected}' trouvé")
                else:
                    print(f"❌ Index '{expected}' manquant")
            
            # Vérifier les contraintes de clés étrangères
            result = conn.execute(text("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_name = 'stakeholders' 
                AND constraint_type = 'FOREIGN KEY'
            """))
            
            constraints = [row[0] for row in result.fetchall()]
            expected_constraints = ['stakeholders_lot_id_fkey', 'stakeholders_company_id_fkey', 'stakeholders_created_by_fkey']
            
            for expected in expected_constraints:
                if expected in constraints:
                    print(f"✅ Contrainte '{expected}' trouvée")
                else:
                    print(f"❌ Contrainte '{expected}' manquante")
            
            # Compter les enregistrements
            result = conn.execute(text("SELECT COUNT(*) FROM stakeholders"))
            count = result.fetchone()[0]
            print(f"✅ {count} enregistrements dans la table stakeholders")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔄 Migration des Stakeholders (lot_intervenants → stakeholders)")
    print("=" * 70)
    
    # Étape 1: Appliquer la migration
    if not apply_migration():
        print("❌ Échec de l'application de la migration")
        return False
    
    # Étape 2: Vérifier la migration
    if not verify_migration():
        print("❌ Échec de la vérification de la migration")
        return False
    
    print("\n" + "=" * 70)
    print("🎉 Migration des stakeholders terminée avec succès !")
    print("\nProchaines étapes:")
    print("1. Tester l'implémentation avec: python test_stakeholders_implementation.py")
    print("2. Mettre à jour le frontend pour utiliser les nouveaux endpoints")
    print("3. Supprimer les alias de compatibilité après validation complète")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
