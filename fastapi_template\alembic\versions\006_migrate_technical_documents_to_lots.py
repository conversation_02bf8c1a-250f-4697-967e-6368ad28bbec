"""migrate technical documents to use lot_id instead of project_id

Revision ID: 006
Revises: 005
Create Date: 2025-07-15 12:55:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = '006'
down_revision = '005_rename_stakeholders'
branch_labels = None
depends_on = None

def upgrade():
    """Migrate technical documents from project_id to lot_id"""
    
    # Bind to get connection for data migration
    connection = op.get_bind()
    
    print("🔄 Migration des documents techniques vers lot_id...")
    
    # 1. First, check if there are any technical documents without lot_id
    result = connection.execute(text("""
        SELECT COUNT(*) FROM technical_documents WHERE lot_id IS NULL
    """))
    null_lot_count = result.scalar()
    
    if null_lot_count > 0:
        print(f"⚠️  {null_lot_count} documents techniques sans lot_id trouvés")
        
        # 2. For documents without lot_id, try to find a lot in the same project
        print("🔄 Attribution des lots aux documents techniques...")
        
        # Update documents to use the first lot found in their project
        connection.execute(text("""
            UPDATE technical_documents 
            SET lot_id = (
                SELECT l.id 
                FROM lots l 
                WHERE l.project_id = technical_documents.project_id 
                LIMIT 1
            )
            WHERE lot_id IS NULL
            AND project_id IS NOT NULL
        """))
        
        # Check if there are still documents without lot_id
        result = connection.execute(text("""
            SELECT COUNT(*) FROM technical_documents WHERE lot_id IS NULL
        """))
        remaining_null = result.scalar()
        
        if remaining_null > 0:
            print(f"❌ {remaining_null} documents techniques n'ont pas pu être migrés (pas de lot trouvé)")
            # For these documents, we'll need to create a default lot or handle them manually
            # For now, let's delete them as they're likely test data
            connection.execute(text("""
                DELETE FROM technical_documents WHERE lot_id IS NULL
            """))
            print("🗑️  Documents orphelins supprimés")
    
    # 3. Make lot_id NOT NULL
    print("🔄 Modification de lot_id en NOT NULL...")
    op.alter_column('technical_documents', 'lot_id',
                    existing_type=sa.INTEGER(),
                    nullable=False)
    
    # 4. Add foreign key constraint for lot_id if it doesn't exist
    print("🔄 Ajout de la contrainte de clé étrangère pour lot_id...")
    try:
        op.create_foreign_key(
            'fk_technical_documents_lot_id',
            'technical_documents', 'lots',
            ['lot_id'], ['id']
        )
    except Exception as e:
        print(f"⚠️  Contrainte FK déjà existante ou erreur: {e}")
    
    # 5. Remove the project_id column
    print("🔄 Suppression de la colonne project_id...")
    op.drop_constraint('technical_documents_project_id_fkey', 'technical_documents', type_='foreignkey')
    op.drop_column('technical_documents', 'project_id')
    
    print("✅ Migration terminée avec succès!")

def downgrade():
    """Rollback migration - restore project_id column"""
    
    connection = op.get_bind()
    
    print("🔄 Rollback: restauration de project_id...")
    
    # 1. Add back project_id column
    op.add_column('technical_documents', sa.Column('project_id', sa.INTEGER(), nullable=True))
    
    # 2. Populate project_id from lot.project_id
    connection.execute(text("""
        UPDATE technical_documents 
        SET project_id = (
            SELECT l.project_id 
            FROM lots l 
            WHERE l.id = technical_documents.lot_id
        )
    """))
    
    # 3. Make project_id NOT NULL
    op.alter_column('technical_documents', 'project_id',
                    existing_type=sa.INTEGER(),
                    nullable=False)
    
    # 4. Add back foreign key constraint
    op.create_foreign_key(
        'technical_documents_project_id_fkey',
        'technical_documents', 'projects',
        ['project_id'], ['id']
    )
    
    # 5. Make lot_id nullable again
    op.alter_column('technical_documents', 'lot_id',
                    existing_type=sa.INTEGER(),
                    nullable=True)
    
    # 6. Drop lot_id foreign key constraint
    try:
        op.drop_constraint('fk_technical_documents_lot_id', 'technical_documents', type_='foreignkey')
    except Exception as e:
        print(f"⚠️  Erreur lors de la suppression de la contrainte FK: {e}")
    
    print("✅ Rollback terminé!")
