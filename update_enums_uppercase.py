#!/usr/bin/env python3
"""
Script pour mettre à jour les enums en majuscule dans la base de données
"""

import asyncio
import asyncpg

DATABASE_URL = "**************************************************************************************************/postgres"

async def update_enums_to_uppercase():
    """Mettre à jour les enums en majuscule"""
    print("🚀 Mise à jour des enums en majuscule...")
    
    try:
        # Connexion à la base de données avec statement_cache_size=0 pour pgbouncer
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        print("✅ Connexion à la base de données réussie")
        
        # 1. Ajouter les nouvelles valeurs en majuscule à l'enum ProjectStatus
        print("\n📊 Mise à jour de l'enum ProjectStatus...")
        new_status_values = ['EN_COURS', 'EN_ATTENTE', 'TERMINE', 'ARCHIVE']
        
        for new_value in new_status_values:
            try:
                await conn.execute(f"ALTER TYPE projectstatus ADD VALUE '{new_value}'")
                print(f"✅ Ajouté '{new_value}' à l'enum ProjectStatus")
            except Exception as e:
                if "already exists" in str(e):
                    print(f"ℹ️ '{new_value}' existe déjà dans ProjectStatus")
                else:
                    print(f"⚠️ Erreur lors de l'ajout de '{new_value}': {e}")
        
        # 2. Ajouter les nouvelles valeurs en majuscule à l'enum ProjectNature
        print("\n📊 Mise à jour de l'enum ProjectNature...")
        new_nature_values = ['DEVIS', 'AFFAIRE']  # AO existe déjà
        
        for new_value in new_nature_values:
            try:
                await conn.execute(f"ALTER TYPE projectnature ADD VALUE '{new_value}'")
                print(f"✅ Ajouté '{new_value}' à l'enum ProjectNature")
            except Exception as e:
                if "already exists" in str(e):
                    print(f"ℹ️ '{new_value}' existe déjà dans ProjectNature")
                else:
                    print(f"⚠️ Erreur lors de l'ajout de '{new_value}': {e}")
        
        # 3. Mettre à jour les projets existants avec les nouvelles valeurs
        print("\n🔄 Mise à jour des projets existants...")
        
        # Mapping des anciens statuts vers les nouveaux
        status_mapping = {
            'En cours': 'EN_COURS',
            'En attente': 'EN_ATTENTE', 
            'Terminé': 'TERMINE',
            'Archivé': 'ARCHIVE',
            'DAO': 'EN_COURS',
            'EXE': 'EN_COURS',
            'COMPLETED': 'TERMINE',
            'ARCHIVED': 'ARCHIVE'
        }
        
        total_status_updated = 0
        for old_status, new_status in status_mapping.items():
            try:
                count = await conn.fetchval("""
                    SELECT COUNT(*) FROM projects WHERE status = $1
                """, old_status)
                
                if count > 0:
                    await conn.execute("""
                        UPDATE projects 
                        SET status = $1
                        WHERE status = $2
                    """, new_status, old_status)
                    
                    print(f"✅ Mis à jour {count} projet(s) de '{old_status}' vers '{new_status}'")
                    total_status_updated += count
                    
            except Exception as e:
                print(f"⚠️ Erreur lors de la mise à jour status {old_status} -> {new_status}: {e}")
        
        # Mapping des anciennes natures vers les nouvelles
        nature_mapping = {
            'Devis': 'DEVIS',
            'Affaire': 'AFFAIRE'
            # 'AO' reste 'AO'
        }
        
        total_nature_updated = 0
        for old_nature, new_nature in nature_mapping.items():
            try:
                count = await conn.fetchval("""
                    SELECT COUNT(*) FROM projects WHERE nature = $1
                """, old_nature)
                
                if count > 0:
                    await conn.execute("""
                        UPDATE projects 
                        SET nature = $1
                        WHERE nature = $2
                    """, new_nature, old_nature)
                    
                    print(f"✅ Mis à jour {count} projet(s) de '{old_nature}' vers '{new_nature}'")
                    total_nature_updated += count
                    
            except Exception as e:
                print(f"⚠️ Erreur lors de la mise à jour nature {old_nature} -> {new_nature}: {e}")
        
        print(f"\n📈 Total mis à jour:")
        print(f"   • Status: {total_status_updated} projet(s)")
        print(f"   • Nature: {total_nature_updated} projet(s)")
        
        # 4. Vérifier les résultats
        print("\n📊 Vérification des résultats...")
        
        # Statuts finaux
        final_statuses = await conn.fetch("""
            SELECT status, COUNT(*) as count
            FROM projects 
            GROUP BY status
            ORDER BY status
        """)
        
        print(f"📊 Statuts finaux:")
        for row in final_statuses:
            print(f"   • {row['status']}: {row['count']} projet(s)")
        
        # Natures finales
        final_natures = await conn.fetch("""
            SELECT nature, COUNT(*) as count
            FROM projects 
            GROUP BY nature
            ORDER BY nature
        """)
        
        print(f"📊 Natures finales:")
        for row in final_natures:
            print(f"   • {row['nature']}: {row['count']} projet(s)")
        
        await conn.close()
        print("\n🎉 Mise à jour des enums en majuscule terminée avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_uppercase_enums():
    """Tester les enums en majuscule"""
    print("\n🧪 Test des enums en majuscule...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        # Test: récupérer les projets avec leurs nouveaux enums
        projects = await conn.fetch("""
            SELECT name, code, status, nature
            FROM projects
            LIMIT 5
        """)
        
        print("✅ Test de lecture réussi:")
        for project in projects:
            print(f"   • {project['name']} ({project['code']}) → {project['status']} / {project['nature']}")
        
        # Test: essayer d'insérer un projet avec les nouveaux enums
        try:
            await conn.execute("""
                INSERT INTO projects (name, code, status, nature, created_at, updated_at)
                VALUES ('Test Enum Majuscule', 'TEST-MAJ-001', 'EN_COURS', 'DEVIS', NOW(), NOW())
            """)
            print("✅ Test d'insertion avec enums majuscule réussi")
            
            # Supprimer le projet de test
            await conn.execute("DELETE FROM projects WHERE code = 'TEST-MAJ-001'")
            print("✅ Projet de test supprimé")
            
        except Exception as e:
            print(f"⚠️ Erreur lors du test d'insertion: {e}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Mise à jour des enums en majuscule")
    print("=" * 50)
    
    async def main():
        # Mettre à jour les enums
        success = await update_enums_to_uppercase()
        
        if success:
            # Tester les nouveaux enums
            await test_uppercase_enums()
        
        return success
    
    result = asyncio.run(main())
    
    if result:
        print("\n✅ Enums mis à jour en majuscule avec succès!")
        print("🚀 Le CRUD des projets devrait maintenant fonctionner")
    else:
        print("\n❌ Échec de la mise à jour des enums")
