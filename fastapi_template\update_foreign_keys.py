#!/usr/bin/env python3
"""
Script pour mettre à jour les foreign keys vers tcompanies
"""
import asyncio
from app.core.database import engine
from sqlalchemy import text

async def update_foreign_keys():
    """Mettre à jour les foreign keys pour pointer vers tcompanies"""
    
    async with engine.begin() as conn:
        try:
            print("🔗 Mise à jour des foreign keys...")
            
            # 1. Mettre à jour technical_document_companies
            print("📋 1. Mise à jour de technical_document_companies...")
            
            # Supprimer l'ancienne contrainte si elle existe
            try:
                await conn.execute(text("""
                    ALTER TABLE technical_document_companies 
                    DROP CONSTRAINT IF EXISTS technical_document_companies_company_id_fkey
                """))
            except:
                pass
            
            # Ajouter la nouvelle contrainte
            await conn.execute(text("""
                ALTER TABLE technical_document_companies 
                ADD CONSTRAINT fk_technical_document_companies_tcompany_id 
                FOREIGN KEY (company_id) REFERENCES tcompanies(id)
            """))
            print("✅ technical_document_companies mis à jour")
            
            # 2. Mettre à jour project_company
            print("📋 2. Mise à jour de project_company...")
            
            # Supprimer l'ancienne contrainte si elle existe
            try:
                await conn.execute(text("""
                    ALTER TABLE project_company 
                    DROP CONSTRAINT IF EXISTS project_company_company_id_fkey
                """))
            except:
                pass
            
            # Ajouter la nouvelle contrainte
            await conn.execute(text("""
                ALTER TABLE project_company 
                ADD CONSTRAINT fk_project_company_tcompany_id 
                FOREIGN KEY (company_id) REFERENCES tcompanies(id)
            """))
            print("✅ project_company mis à jour")
            
            # 3. Vérifier les contraintes
            print("🔍 Vérification des contraintes...")
            result = await conn.execute(text("""
                SELECT 
                    tc.table_name,
                    tc.constraint_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                    ON ccu.constraint_name = tc.constraint_name
                    AND ccu.table_schema = tc.table_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND ccu.table_name = 'tcompanies'
                ORDER BY tc.table_name
            """))
            
            constraints = result.fetchall()
            print("📋 Contraintes pointant vers tcompanies:")
            for constraint in constraints:
                print(f"  - {constraint[0]}.{constraint[2]} → {constraint[3]}.{constraint[4]} ({constraint[1]})")
            
            print("🎉 Mise à jour des foreign keys terminée!")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            raise

if __name__ == "__main__":
    asyncio.run(update_foreign_keys())
