import React from 'react'
import { NodeViewWrapper } from '@tiptap/react'
import '../styles/cover-page-node.css'

interface CoverPageNodeViewProps {
  node: {
    attrs: {
      content: string
      class?: string
    }
  }
  updateAttributes: (attributes: Record<string, any>) => void
  selected: boolean
}

const CoverPageNodeView: React.FC<CoverPageNodeViewProps> = ({
  node,
  updateAttributes,
  selected,
}) => {
  const { content } = node.attrs

  return (
    <NodeViewWrapper
      className={`cover-page-wrapper ${selected ? 'ProseMirror-selectednode' : ''}`}
      data-type="cover-page"
    >
      <div className="cover-page-container">
        {/* Indicateur de sélection */}
        {selected && (
          <div className="cover-page-selected-indicator">
            📄 Page de garde sélectionnée
          </div>
        )}
        
        {/* Contenu de la page de garde */}
        <div 
          className="cover-page-content"
          dangerouslySetInnerHTML={{ __html: content }}
        />
        
        {/* Overlay pour empêcher l'édition directe */}
        <div className="cover-page-overlay">
          <div className="cover-page-controls">
            <button
              onClick={() => {
                // TODO: Ouvrir modal de régénération
                console.log('Régénérer la page de garde')
              }}
              className="cover-page-button"
            >
              🔄 Régénérer
            </button>
            <button
              onClick={() => {
                // TODO: Ouvrir modal d'édition
                console.log('Éditer la page de garde')
              }}
              className="cover-page-button"
            >
              ✏️ Éditer
            </button>
          </div>
        </div>
      </div>
    </NodeViewWrapper>
  )
}

export default CoverPageNodeView
