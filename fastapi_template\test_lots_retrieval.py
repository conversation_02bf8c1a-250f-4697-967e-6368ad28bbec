"""
Script pour tester la récupération des lots existants
Utilisateur: <EMAIL>
Mot de passe: orbis123!
"""

import requests
import json
from typing import List, Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "orbis123!"

def authenticate() -> str:
    """Authentification et récupération du token"""
    print("🔐 Authentification...")
    
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/auth/login",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        token_data = response.json()
        access_token = token_data.get("access_token")
        print(f"✅ Authentification réussie!")
        return access_token
    else:
        print(f"❌ Erreur d'authentification: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def get_all_lots(token: str) -> List[Dict[str, Any]]:
    """Récupérer tous les lots"""
    print("\n📋 Récupération de tous les lots...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/lots/",
        headers=headers
    )
    
    print(f"Status Code: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        lots = response.json()
        print(f"✅ {len(lots)} lot(s) récupéré(s)")
        for lot in lots:
            print(f"   - {lot.get('name', 'N/A')} (ID: {lot.get('id', 'N/A')}, Code: {lot.get('code', 'N/A')}, Phase: {lot.get('current_phase', 'N/A')})")
        return lots
    else:
        print(f"❌ Erreur récupération lots: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def get_projects(token: str) -> List[Dict[str, Any]]:
    """Récupérer les projets pour tester les lots par projet"""
    print(f"\n🏗️ Récupération des projets...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/projects/",
        headers=headers
    )
    
    if response.status_code == 200:
        projects = response.json()
        print(f"✅ {len(projects)} projet(s) trouvé(s)")
        for project in projects:
            print(f"   - {project.get('name', 'N/A')} (ID: {project.get('id', 'N/A')})")
        return projects
    else:
        print(f"❌ Erreur récupération projets: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def get_lots_by_project(token: str, project_id: int) -> List[Dict[str, Any]]:
    """Récupérer les lots d'un projet spécifique"""
    print(f"\n📁 Récupération des lots du projet {project_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{BASE_URL}/api/v1/lots/project/{project_id}",
        headers=headers
    )
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        lots = response.json()
        print(f"✅ {len(lots)} lot(s) trouvé(s) dans le projet {project_id}")
        for lot in lots:
            print(f"   - {lot.get('name', 'N/A')} (ID: {lot.get('id', 'N/A')}, Phase: {lot.get('current_phase', 'N/A')})")
        return lots
    else:
        print(f"❌ Erreur récupération lots du projet: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def test_lots_retrieval():
    """Test complet de récupération des lots"""
    print("🚀 TEST DE RÉCUPÉRATION DES LOTS")
    print("=" * 50)
    
    # 1. Authentification
    token = authenticate()
    if not token:
        return
    
    # 2. Test récupération de tous les lots
    all_lots = get_all_lots(token)
    
    # 3. Récupération des projets
    projects = get_projects(token)
    
    # 4. Test récupération des lots par projet
    if projects:
        for project in projects:
            project_id = project.get('id')
            project_name = project.get('name', 'Projet sans nom')
            
            print(f"\n📁 Test lots pour le projet: {project_name}")
            project_lots = get_lots_by_project(token, project_id)
    
    # 5. Résumé
    print(f"\n📈 RÉSUMÉ")
    print("=" * 20)
    print(f"📋 Total des lots récupérés: {len(all_lots)}")
    print(f"🏗️ Projets testés: {len(projects)}")

if __name__ == "__main__":
    print("🚀 Test de récupération des lots ORBIS")
    
    try:
        test_lots_retrieval()
        print("\n✅ Test terminé!")
    except requests.exceptions.ConnectionError:
        print("❌ Serveur non accessible sur http://localhost:8000")
        print("💡 Assurez-vous que le serveur FastAPI est démarré")
    except Exception as e:
        print(f"❌ Erreur durant le test: {e}")
        import traceback
        traceback.print_exc()
