'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

interface TimeTrackerProps {
  employeeId: number
}

export const TimeTracker: React.FC<TimeTrackerProps> = ({ employeeId }) => {
  const [timeEntry, setTimeEntry] = useState({
    date: new Date().toISOString().split('T')[0],
    startTime: '',
    endTime: '',
    project: '',
    description: '',
    hours: 0
  })

  const [isTracking, setIsTracking] = useState(false)
  const [startTime, setStartTime] = useState<Date | null>(null)

  const handleStartTracking = () => {
    const now = new Date()
    setStartTime(now)
    setIsTracking(true)
    setTimeEntry(prev => ({
      ...prev,
      startTime: now.toTimeString().slice(0, 5)
    }))
  }

  const handleStopTracking = () => {
    if (startTime) {
      const now = new Date()
      const hours = (now.getTime() - startTime.getTime()) / (1000 * 60 * 60)
      setTimeEntry(prev => ({
        ...prev,
        endTime: now.toTimeString().slice(0, 5),
        hours: Math.round(hours * 100) / 100
      }))
    }
    setIsTracking(false)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Nouvelle entrée de temps:', timeEntry)
    // Reset form
    setTimeEntry({
      date: new Date().toISOString().split('T')[0],
      startTime: '',
      endTime: '',
      project: '',
      description: '',
      hours: 0
    })
  }

  const calculateHours = () => {
    if (timeEntry.startTime && timeEntry.endTime) {
      const start = new Date(`2000-01-01T${timeEntry.startTime}:00`)
      const end = new Date(`2000-01-01T${timeEntry.endTime}:00`)
      const hours = (end.getTime() - start.getTime()) / (1000 * 60 * 60)
      setTimeEntry(prev => ({ ...prev, hours: Math.round(hours * 100) / 100 }))
    }
  }

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Suivi du temps</h2>
      
      {/* Quick Timer */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium mb-3">Chronomètre rapide</h3>
        <div className="flex items-center gap-4">
          {isTracking ? (
            <>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse mr-2"></div>
                <span className="text-sm text-gray-600">
                  Démarré à {startTime?.toTimeString().slice(0, 5)}
                </span>
              </div>
              <Button
                onClick={handleStopTracking}
                className="bg-red-600 hover:bg-red-700"
              >
                Arrêter
              </Button>
            </>
          ) : (
            <Button
              onClick={handleStartTracking}
              className="bg-green-600 hover:bg-green-700"
            >
              Démarrer le chrono
            </Button>
          )}
        </div>
      </div>

      {/* Manual Entry Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Date"
            type="date"
            value={timeEntry.date}
            onChange={(e) => setTimeEntry(prev => ({ ...prev, date: e.target.value }))}
            required
          />
          <Input
            label="Projet"
            value={timeEntry.project}
            onChange={(e) => setTimeEntry(prev => ({ ...prev, project: e.target.value }))}
            placeholder="Sélectionner un projet"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input
            label="Heure de début"
            type="time"
            value={timeEntry.startTime}
            onChange={(e) => setTimeEntry(prev => ({ ...prev, startTime: e.target.value }))}
            onBlur={calculateHours}
            required
          />
          <Input
            label="Heure de fin"
            type="time"
            value={timeEntry.endTime}
            onChange={(e) => setTimeEntry(prev => ({ ...prev, endTime: e.target.value }))}
            onBlur={calculateHours}
            required
          />
          <Input
            label="Heures totales"
            type="number"
            step="0.25"
            value={timeEntry.hours}
            onChange={(e) => setTimeEntry(prev => ({ ...prev, hours: parseFloat(e.target.value) }))}
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description des tâches
          </label>
          <textarea
            value={timeEntry.description}
            onChange={(e) => setTimeEntry(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Décrivez les tâches effectuées..."
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            rows={3}
            required
          />
        </div>

        <div className="flex justify-end">
          <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
            Enregistrer
          </Button>
        </div>
      </form>
    </Card>
  )
}