import { api } from '@/lib/api'
import { TechnicalDocumentResponse, DocumentType } from '@/types/technical-document'

export interface CreateDocumentRequest {
  name: string
  type_document: DocumentType
  lot_id: number
  content?: string
  company_ids?: number[]
}

export interface UpdateDocumentRequest {
  name?: string
  content?: string
  company_ids?: number[]
}

export const createDocument = async (data: CreateDocumentRequest): Promise<TechnicalDocumentResponse> => {
  const response = await api.post('/technical-documents/', data)
  return response.data
}

export const updateDocument = async (id: number, data: UpdateDocumentRequest): Promise<TechnicalDocumentResponse> => {
  const response = await api.put(`/technical-documents/${id}`, data)
  return response.data
}

export const getDocument = async (id: number): Promise<TechnicalDocumentResponse> => {
  const response = await api.get(`/technical-documents/${id}`)
  return response.data
}

export const deleteDocument = async (id: number): Promise<void> => {
  await api.delete(`/technical-documents/${id}`)
}
