#!/usr/bin/env python3
"""
Script de test pour le CRUD des projets avec authentification JWT
"""

import requests
import json
from datetime import datetime

API_BASE = "http://localhost:8000"

def test_projects_crud():
    """Test complet du CRUD des projets"""
    print("🧪 Test du CRUD des projets avec JWT\n")
    
    # 1. Connexion pour obtenir le token JWT
    print("🔐 1. Connexion...")
    login_response = requests.post(f"{API_BASE}/api/v1/fast-auth/login", json={
        "email": "<EMAIL>",
        "password": "orbis123!"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Erreur de connexion: {login_response.text}")
        return False
    
    login_data = login_response.json()
    token = login_data['access_token']
    print(f"✅ Connexion réussie - Token: {token[:50]}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. Test de création d'un projet
    print("\n📝 2. Création d'un projet...")
    project_data = {
        "name": "Test Projet CRUD",
        "description": "Projet de test pour le CRUD",
        "nature": "Devis",
        "status": "En cours",
        "client_name": "Client Test",
        "client_contact": "<EMAIL>",
        "address": "123 Rue de Test, 75001 Paris",
        "budget_total": 50000.00,
        "start_date": "2025-01-15T00:00:00",
        "end_date": "2025-06-15T00:00:00"
    }
    
    create_response = requests.post(f"{API_BASE}/api/v1/projects/", 
                                  headers=headers, 
                                  json=project_data)
    
    if create_response.status_code != 200:
        print(f"❌ Erreur création: {create_response.text}")
        return False
    
    created_project = create_response.json()
    project_id = created_project['id']
    print(f"✅ Projet créé avec ID: {project_id}")
    print(f"   Code: {created_project['code']}")
    print(f"   Nature: {created_project['nature']}")
    
    # 3. Test de lecture de tous les projets
    print("\n📋 3. Lecture de tous les projets...")
    list_response = requests.get(f"{API_BASE}/api/v1/projects/", headers=headers)
    
    if list_response.status_code != 200:
        print(f"❌ Erreur lecture: {list_response.text}")
        return False
    
    projects = list_response.json()
    print(f"✅ {len(projects)} projet(s) trouvé(s)")
    
    # 4. Test de lecture d'un projet spécifique
    print(f"\n🔍 4. Lecture du projet {project_id}...")
    get_response = requests.get(f"{API_BASE}/api/v1/projects/{project_id}", headers=headers)
    
    if get_response.status_code != 200:
        print(f"❌ Erreur lecture projet: {get_response.text}")
        return False
    
    project = get_response.json()
    print(f"✅ Projet récupéré: {project['name']}")
    
    # 5. Test de mise à jour
    print(f"\n✏️ 5. Mise à jour du projet {project_id}...")
    update_data = {
        "name": "Test Projet CRUD - Modifié",
        "nature": "Affaire",
        "status": "En attente",
        "budget_total": 75000.00
    }
    
    update_response = requests.put(f"{API_BASE}/api/v1/projects/{project_id}", 
                                 headers=headers, 
                                 json=update_data)
    
    if update_response.status_code != 200:
        print(f"❌ Erreur mise à jour: {update_response.text}")
        return False
    
    updated_project = update_response.json()
    print(f"✅ Projet mis à jour:")
    print(f"   Nom: {updated_project['name']}")
    print(f"   Nature: {updated_project['nature']}")
    print(f"   Budget: {updated_project['budget_total']}")
    
    # 6. Test des filtres
    print("\n🔍 6. Test des filtres...")
    
    # Filtre par nature
    filter_response = requests.get(f"{API_BASE}/api/v1/projects/?nature=Affaire", headers=headers)
    if filter_response.status_code == 200:
        filtered_projects = filter_response.json()
        print(f"✅ Filtrage par nature 'Affaire': {len(filtered_projects)} projet(s)")
    
    # Recherche textuelle
    search_response = requests.get(f"{API_BASE}/api/v1/projects/?search=CRUD", headers=headers)
    if search_response.status_code == 200:
        search_projects = search_response.json()
        print(f"✅ Recherche 'CRUD': {len(search_projects)} projet(s)")
    
    # 7. Test des statistiques
    print("\n📊 7. Test des statistiques...")
    stats_response = requests.get(f"{API_BASE}/api/v1/projects/stats/summary", headers=headers)
    
    if stats_response.status_code != 200:
        print(f"❌ Erreur statistiques: {stats_response.text}")
        return False
    
    stats = stats_response.json()
    print(f"✅ Statistiques récupérées:")
    print(f"   Total actif: {stats['total_active']}")
    print(f"   Par nature: {stats['by_nature']}")
    print(f"   Par statut: {stats['by_status']}")
    
    # 8. Test de suppression (archivage)
    print(f"\n🗑️ 8. Suppression (archivage) du projet {project_id}...")
    delete_response = requests.delete(f"{API_BASE}/api/v1/projects/{project_id}", headers=headers)
    
    if delete_response.status_code != 200:
        print(f"❌ Erreur suppression: {delete_response.text}")
        return False
    
    delete_result = delete_response.json()
    print(f"✅ {delete_result['message']}")
    
    # 9. Test de restauration
    print(f"\n🔄 9. Restauration du projet {project_id}...")
    restore_response = requests.post(f"{API_BASE}/api/v1/projects/{project_id}/restore", headers=headers)
    
    if restore_response.status_code != 200:
        print(f"❌ Erreur restauration: {restore_response.text}")
        return False
    
    restore_result = restore_response.json()
    print(f"✅ {restore_result['message']}")
    
    # 10. Nettoyage final - suppression définitive pour les tests
    print(f"\n🧹 10. Nettoyage final...")
    requests.delete(f"{API_BASE}/api/v1/projects/{project_id}", headers=headers)
    print("✅ Nettoyage terminé")
    
    print("\n🎉 Tous les tests CRUD sont passés avec succès!")
    return True

def test_authentication_errors():
    """Test des erreurs d'authentification"""
    print("\n🔒 Test des erreurs d'authentification...")
    
    # Test sans token
    response = requests.get(f"{API_BASE}/api/v1/projects/")
    if response.status_code == 401:
        print("✅ Accès refusé sans token (401)")
    else:
        print(f"❌ Erreur: accès autorisé sans token ({response.status_code})")
    
    # Test avec token invalide
    headers = {"Authorization": "Bearer invalid-token"}
    response = requests.get(f"{API_BASE}/api/v1/projects/", headers=headers)
    if response.status_code == 401:
        print("✅ Accès refusé avec token invalide (401)")
    else:
        print(f"❌ Erreur: accès autorisé avec token invalide ({response.status_code})")

if __name__ == "__main__":
    print("🚀 Démarrage des tests du CRUD des projets")
    print("=" * 50)
    
    try:
        # Test des erreurs d'authentification
        test_authentication_errors()
        
        # Test du CRUD complet
        success = test_projects_crud()
        
        if success:
            print("\n✅ Tous les tests sont passés!")
        else:
            print("\n❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"\n💥 Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
