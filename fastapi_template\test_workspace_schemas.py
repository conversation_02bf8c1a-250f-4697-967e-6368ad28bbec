#!/usr/bin/env python3
"""
Script pour tester les nouveaux schémas Workspace Pydantic
"""

import sys
from pathlib import Path

# Ajouter le répertoire app au path Python
sys.path.insert(0, str(Path(__file__).parent))

def test_workspace_schema_imports():
    """Tester que tous les imports de schémas fonctionnent"""
    print("🔍 Test des imports des schémas Workspace...")
    
    try:
        # Test des nouveaux schémas workspace
        from app.schemas.workspace import (
            Workspace, WorkspaceCreate, WorkspaceUpdate, WorkspaceSettings,
            UserWorkspace, UserWorkspaceCreate, WorkspaceInvitation,
            WorkspaceRolePermission, WorkspaceWithUsers
        )
        print("   ✅ Schémas Workspace importés")
        
        # Test des alias de compatibilité
        from app.schemas.workspace import (
            Company, CompanyCreate, CompanyUpdate, CompanySettings,
            UserCompany, UserCompanyCreate, CompanyInvitation
        )
        print("   ✅ Alias de compatibilité importés")
        
        # Test de l'import global
        from app.schemas import Workspace, WorkspaceCreate, UserWorkspace
        print("   ✅ Import global fonctionne")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Erreur d'import: {e}")
        return False

def test_workspace_schema_creation():
    """Tester la création d'instances de schémas"""
    print("\n🏗️ Test de création des schémas...")
    
    try:
        from app.schemas.workspace import (
            WorkspaceCreate, WorkspaceUpdate, UserWorkspaceCreate,
            WorkspaceSettings, WorkspaceInvitation
        )
        
        # Test WorkspaceCreate
        workspace_data = {
            "name": "Test Workspace",
            "code": "TEST_WS",
            "description": "Espace de travail de test",
            "email": "<EMAIL>"
        }
        workspace_create = WorkspaceCreate(**workspace_data)
        print(f"   ✅ WorkspaceCreate: {workspace_create.name}")
        
        # Test WorkspaceUpdate
        workspace_update = WorkspaceUpdate(name="Updated Workspace")
        print(f"   ✅ WorkspaceUpdate: {workspace_update.name}")
        
        # Test UserWorkspaceCreate
        user_workspace_data = {
            "user_id": 1,
            "workspace_id": 1,
            "role_name": "ADMIN"
        }
        user_workspace_create = UserWorkspaceCreate(**user_workspace_data)
        print(f"   ✅ UserWorkspaceCreate: {user_workspace_create.role_name}")
        
        # Test WorkspaceInvitation
        invitation_data = {
            "workspace_id": 1,
            "email": "<EMAIL>",
            "role": "USER"
        }
        workspace_invitation = WorkspaceInvitation(**invitation_data)
        print(f"   ✅ WorkspaceInvitation: {workspace_invitation.email}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_compatibility_aliases():
    """Tester les alias de compatibilité"""
    print("\n🔄 Test des alias de compatibilité...")
    
    try:
        from app.schemas.workspace import (
            Company, CompanyCreate, UserCompany, CompanySettings
        )
        from app.schemas.workspace import (
            Workspace, WorkspaceCreate, UserWorkspace, WorkspaceSettings
        )
        
        # Vérifier que les alias pointent vers les bonnes classes
        aliases = [
            (Company, Workspace, "Company → Workspace"),
            (CompanyCreate, WorkspaceCreate, "CompanyCreate → WorkspaceCreate"),
            (UserCompany, UserWorkspace, "UserCompany → UserWorkspace"),
            (CompanySettings, WorkspaceSettings, "CompanySettings → WorkspaceSettings")
        ]
        
        for alias, original, description in aliases:
            if alias is original:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - alias incorrect")
        
        # Test de création avec les alias
        company_data = {
            "name": "Test Company",
            "code": "TEST_COMP"
        }
        company_create = CompanyCreate(**company_data)
        print(f"   ✅ CompanyCreate fonctionne: {company_create.name}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_user_schema_updates():
    """Tester les mises à jour des schémas utilisateur"""
    print("\n👥 Test des schémas utilisateur mis à jour...")
    
    try:
        from app.schemas.user import (
            UserWithWorkspaces, UserWorkspaceRole, UserPermissionSummary
        )
        
        # Test UserWithWorkspaces
        user_data = {
            "id": 1,
            "email": "<EMAIL>",
            "first_name": "Test",
            "last_name": "User",
            "workspaces": [],
            "total_workspaces": 0
        }
        user_with_workspaces = UserWithWorkspaces(**user_data)
        print(f"   ✅ UserWithWorkspaces: {user_with_workspaces.email}")
        
        # Test UserWorkspaceRole
        role_data = {
            "user_id": 1,
            "workspace_id": 1,
            "workspace_name": "Test Workspace",
            "role_name": "ADMIN",
            "permissions": ["read", "write"]
        }
        user_role = UserWorkspaceRole(**role_data)
        print(f"   ✅ UserWorkspaceRole: {user_role.role_name}")
        
        # Test de compatibilité
        from app.schemas.user import UserWithCompanies, UserCompanyRole
        
        # Vérifier que l'alias fonctionne
        if UserWithCompanies is UserWithWorkspaces:
            print("   ✅ UserWithCompanies alias fonctionne")
        else:
            print("   ❌ UserWithCompanies alias incorrect")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_rbac_schema_updates():
    """Tester les mises à jour des schémas RBAC"""
    print("\n🔐 Test des schémas RBAC mis à jour...")
    
    try:
        from app.schemas.rbac import (
            WorkspaceRolePermission, RolePermissionsConfig,
            UserPermissions, PermissionCheck, WorkspaceRolesSummary
        )
        
        # Test WorkspaceRolePermission
        role_perm_data = {
            "workspace_id": 1,
            "role_name": "ADMIN",
            "permission_id": 1
        }
        workspace_role_perm = WorkspaceRolePermission(
            id=1,
            created_at="2024-01-01T00:00:00",
            **role_perm_data
        )
        print(f"   ✅ WorkspaceRolePermission: {workspace_role_perm.role_name}")
        
        # Test RolePermissionsConfig
        config_data = {
            "workspace_id": 1,
            "role_name": "ADMIN",
            "permissions": ["read", "write", "delete"]
        }
        role_config = RolePermissionsConfig(**config_data)
        print(f"   ✅ RolePermissionsConfig: {len(role_config.permissions)} permissions")
        
        # Test de compatibilité
        from app.schemas.rbac import CompanyRolePermission
        
        if CompanyRolePermission is WorkspaceRolePermission:
            print("   ✅ CompanyRolePermission alias fonctionne")
        else:
            print("   ❌ CompanyRolePermission alias incorrect")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_schema_validation():
    """Tester la validation des schémas"""
    print("\n✅ Test de validation des schémas...")
    
    try:
        from app.schemas.workspace import WorkspaceCreate
        from pydantic import ValidationError
        
        # Test validation réussie
        valid_data = {
            "name": "Valid Workspace",
            "code": "VALID"
        }
        workspace = WorkspaceCreate(**valid_data)
        print(f"   ✅ Validation réussie: {workspace.name}")
        
        # Test validation échouée (nom manquant)
        try:
            invalid_data = {"code": "INVALID"}
            WorkspaceCreate(**invalid_data)
            print("   ❌ Validation devrait échouer")
            return False
        except ValidationError:
            print("   ✅ Validation échoue correctement pour données invalides")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 TEST DES SCHÉMAS WORKSPACE PYDANTIC")
    print("="*50)
    
    tests = [
        test_workspace_schema_imports,
        test_workspace_schema_creation,
        test_compatibility_aliases,
        test_user_schema_updates,
        test_rbac_schema_updates,
        test_schema_validation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   💥 Erreur inattendue dans {test.__name__}: {e}")
            results.append(False)
    
    # Résumé
    print(f"\n📊 RÉSUMÉ: {sum(results)}/{len(results)} tests réussis")
    
    if all(results):
        print("🎉 Tous les tests sont passés!")
        return True
    else:
        print("❌ Certains tests ont échoué")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
