#!/usr/bin/env python3
"""
Script pour vérifier quelles tables existent dans la base de données
"""
import asyncio
from app.core.database import engine
from sqlalchemy import text

async def check_tables():
    """Vérifier quelles tables existent"""
    
    async with engine.begin() as conn:
        try:
            # Lister toutes les tables
            result = await conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result.fetchall()]
            
            print("📋 Tables existantes dans la base de données:")
            for table in tables:
                print(f"  - {table}")
            
            # Vérifier spécifiquement les tables qui nous intéressent
            print(f"\n🔍 Tables importantes:")
            important_tables = ['entreprises_tiers', 'tcompanies', 'workspaces', 'employees']
            for table in important_tables:
                exists = table in tables
                print(f"  {'✅' if exists else '❌'} {table}")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    asyncio.run(check_tables())
