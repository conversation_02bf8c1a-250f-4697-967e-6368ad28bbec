#!/usr/bin/env python3
"""
Test direct CRUD operations bypassing the problematic authentication
"""

import requests
import json
import jwt
from datetime import datetime, timedelta

def create_jwt_token():
    """Create a JWT token for testing"""
    SECRET_KEY = "orbis-jwt-secret-key-change-in-production"
    ALGORITHM = "HS256"
    
    payload = {
        "user_id": 1,
        "email": "<EMAIL>",
        "role": "SUPER_ADMIN",
        "is_superuser": True,
        "token_type": "access",
        "exp": datetime.utcnow() + timedelta(hours=24),
        "iat": datetime.utcnow()
    }
    
    token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    return token

def test_companies_endpoint():
    """Test the companies endpoint"""
    print("🏢 Testing companies endpoint...")
    
    token = create_jwt_token()
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get("http://localhost:8000/api/v1/admin/companies", headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            companies = response.json()
            print(f"✅ Found {len(companies)} companies")
            for company in companies:
                print(f"  - {company['name']} (ID: {company['id']})")
            return companies
        else:
            print(f"❌ Error: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Exception: {e}")
        return []

def test_user_creation(company_id):
    """Test user creation"""
    print(f"\n👤 Testing user creation for company {company_id}...")
    
    token = create_jwt_token()
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    user_data = {
        "email": f"test.user.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "User",
        "role": "admin",
        "company_id": company_id
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/admin/users",
            headers=headers,
            json=user_data
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            user = response.json()
            print(f"✅ User created successfully!")
            print(f"  - ID: {user['id']}")
            print(f"  - Email: {user['email']}")
            print(f"  - Name: {user['first_name']} {user['last_name']}")
            return user
        else:
            print(f"❌ Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Exception: {e}")
        return None

def test_user_list(company_id):
    """Test user listing"""
    print(f"\n📋 Testing user list for company {company_id}...")
    
    token = create_jwt_token()
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"http://localhost:8000/api/v1/admin/company/{company_id}", headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            users = response.json()
            print(f"✅ Found {len(users)} users")
            for user in users:
                print(f"  - {user['email']} ({user['first_name']} {user['last_name']})")
            return users
        else:
            print(f"❌ Error: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Exception: {e}")
        return []

if __name__ == "__main__":
    print("🚀 Testing CRUD operations directly\n")
    
    # Test companies first
    companies = test_companies_endpoint()
    
    if companies:
        company_id = companies[0]['id']
        print(f"\nUsing company ID: {company_id}")
        
        # Test user creation
        new_user = test_user_creation(company_id)
        
        if new_user:
            # Test user listing
            users = test_user_list(company_id)
            
            if users:
                print(f"\n🎉 CRUD operations working! Found {len(users)} users including the new one.")
            else:
                print(f"\n⚠️ User creation worked but listing failed.")
        else:
            print(f"\n❌ User creation failed.")
    else:
        print(f"\n❌ Cannot test user operations without companies.")
    
    print("\n✅ Test completed")
