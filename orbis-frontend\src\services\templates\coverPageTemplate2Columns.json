{"name": "Template Page de Garde 2 Colonnes", "version": "1.0", "description": "Template 2 colonnes pour reproduire le style du screenshot", "sections": [{"id": "main-container", "type": "container", "styles": {"pageBreakAfter": "always", "padding": "30px", "margin": "0", "backgroundColor": "white", "border": "2px solid #000", "minHeight": "280mm", "width": "200mm", "position": "relative", "fontFamily": "<PERSON>l, sans-serif"}, "children": [{"id": "header-logos", "type": "container", "styles": {"position": "absolute", "top": "15px", "right": "15px", "display": "flex", "gap": "15px", "alignItems": "center"}, "children": [{"type": "image", "condition": "{{moaLogoUrl}}", "src": "{{moaLogoUrl}}", "styles": {"height": "50px", "width": "auto"}}]}, {"id": "content-wrapper", "type": "container", "styles": {"display": "flex", "height": "calc(100% - 80px)", "gap": "30px", "marginTop": "80px"}, "children": [{"id": "left-column", "type": "container", "styles": {"width": "45%", "paddingRight": "15px"}, "children": [{"id": "companies-list", "type": "dynamic-container", "dataSource": "otherCompanies", "template": {"type": "container", "styles": {"marginBottom": "25px", "borderBottom": "1px solid #ccc", "paddingBottom": "18px"}, "children": [{"type": "container", "styles": {"display": "flex", "alignItems": "flex-start", "gap": "12px", "marginBottom": "10px"}, "children": [{"type": "image", "condition": "{{logoUrl}}", "src": "{{logoUrl}}", "styles": {"height": "35px", "width": "auto", "flexShrink": "0", "border": "1px solid #eee"}}, {"type": "container", "styles": {"flex": "1"}, "children": [{"type": "text", "content": "{{role}} :", "styles": {"fontSize": "11px", "fontWeight": "bold", "marginBottom": "4px", "textTransform": "uppercase"}}, {"type": "text", "content": "{{name}}", "styles": {"fontSize": "10px", "fontWeight": "bold", "marginBottom": "3px", "lineHeight": "1.2"}}, {"type": "text", "condition": "{{activity}}", "content": "{{activity}}", "styles": {"fontSize": "9px", "color": "#666", "marginBottom": "3px", "fontStyle": "italic"}}]}]}, {"type": "text", "condition": "{{address}}", "content": "{{address}}", "styles": {"fontSize": "9px", "color": "#666", "marginBottom": "3px", "lineHeight": "1.3"}}, {"type": "text", "condition": "{{phone}}", "content": "Tél : {{phone}}", "styles": {"fontSize": "9px", "color": "#666", "marginBottom": "2px"}}, {"type": "text", "condition": "{{email}}", "content": "Mail : {{email}}", "styles": {"fontSize": "9px", "color": "#666"}}]}}]}, {"id": "right-column", "type": "container", "styles": {"width": "55%", "paddingLeft": "15px"}, "children": [{"id": "project-title", "type": "text", "content": "{{projectName}}", "styles": {"fontSize": "16px", "fontWeight": "bold", "textAlign": "center", "marginBottom": "8px", "lineHeight": "1.3"}}, {"id": "project-location", "type": "text", "content": "Quartier <PERSON>", "styles": {"fontSize": "14px", "textAlign": "center", "marginBottom": "5px", "fontWeight": "500"}}, {"id": "project-address", "type": "text", "content": "74000 ANNECY", "styles": {"fontSize": "12px", "textAlign": "center", "marginBottom": "25px", "fontWeight": "500"}}, {"id": "project-image", "type": "image", "condition": "{{projectImageUrl}}", "src": "{{projectImageUrl}}", "styles": {"width": "100%", "height": "180px", "objectFit": "cover", "marginBottom": "25px", "border": "1px solid #ccc", "borderRadius": "4px"}}, {"id": "moa-section", "type": "container", "styles": {"textAlign": "right", "marginBottom": "20px", "padding": "12px", "border": "1px solid #000"}, "children": [{"type": "text", "content": "MAÎTRE D'OUVRAGE", "styles": {"fontSize": "11px", "fontWeight": "bold", "marginBottom": "8px", "textAlign": "center"}}, {"type": "container", "styles": {"display": "flex", "alignItems": "center", "justifyContent": "center", "gap": "12px"}, "children": [{"type": "image", "condition": "{{moaLogoUrl}}", "src": "{{moaLogoUrl}}", "styles": {"height": "35px", "width": "auto"}}, {"type": "container", "styles": {"textAlign": "left"}, "children": [{"type": "text", "content": "{{moaName}}", "styles": {"fontSize": "10px", "fontWeight": "bold", "marginBottom": "3px"}}, {"type": "text", "condition": "{{moa<PERSON><PERSON><PERSON>}}", "content": "{{moa<PERSON><PERSON><PERSON>}}", "styles": {"fontSize": "9px", "color": "#666", "lineHeight": "1.2"}}]}]}]}, {"id": "lot-section", "type": "container", "styles": {"textAlign": "center", "marginBottom": "20px"}, "children": [{"type": "text", "content": "{{lotTitle}}", "styles": {"fontSize": "14px", "fontWeight": "bold", "marginBottom": "8px"}}, {"type": "text", "content": "{{lotDescription}}", "styles": {"fontSize": "12px", "fontWeight": "bold", "textTransform": "uppercase", "letterSpacing": "0.5px"}}]}, {"id": "document-section", "type": "container", "styles": {"border": "2px solid #000", "borderRadius": "12px", "backgroundColor": "#f5f5f5", "padding": "20px", "textAlign": "center", "marginBottom": "20px"}, "children": [{"type": "text", "content": "{{documentType}}", "styles": {"fontSize": "20px", "fontWeight": "bold", "letterSpacing": "1px"}}]}, {"id": "info-table", "type": "table", "styles": {"width": "100%", "borderCollapse": "collapse", "border": "2px solid #000", "fontSize": "11px"}, "content": {"rows": [{"cells": [{"content": "Dossier", "styles": {"padding": "8px", "border": "1px solid #000", "fontSize": "11px", "fontWeight": "bold", "backgroundColor": "#f8f8f8", "width": "40%"}}, {"content": "{{dossier<PERSON>umber}}", "styles": {"padding": "8px", "border": "1px solid #000", "fontSize": "11px", "textAlign": "center"}}]}, {"cells": [{"content": "Date", "styles": {"padding": "8px", "border": "1px solid #000", "fontSize": "11px", "fontWeight": "bold", "backgroundColor": "#f8f8f8"}}, {"content": "{{documentDate}}", "styles": {"padding": "8px", "border": "1px solid #000", "fontSize": "11px", "textAlign": "center"}}]}, {"cells": [{"content": "Phase", "styles": {"padding": "8px", "border": "1px solid #000", "fontSize": "11px", "fontWeight": "bold", "backgroundColor": "#f8f8f8"}}, {"content": "{{lotPhase}}", "styles": {"padding": "8px", "border": "1px solid #000", "fontSize": "11px", "textAlign": "center", "fontWeight": "bold"}}]}, {"cells": [{"content": "Indice", "styles": {"padding": "8px", "border": "1px solid #000", "fontSize": "11px", "fontWeight": "bold", "backgroundColor": "#f8f8f8"}}, {"content": "{{documentIndice}}", "styles": {"padding": "8px", "border": "1px solid #000", "fontSize": "11px", "textAlign": "center"}}]}]}}]}]}]}]}