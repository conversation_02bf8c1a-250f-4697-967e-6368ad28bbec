#!/usr/bin/env python3
"""
Script de test des modèles de l'application ORBIS
Ce script teste la connexion et les opérations CRUD de base sur les modèles
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path

# Ajouter le répertoire app au path Python
sys.path.insert(0, str(Path(__file__).parent))

from sqlalchemy import text
from app.core.database import AsyncSessionLocal, init_db
from app.models.user import User
from app.models.workspace import Company
from app.models.project import Project
from app.models.employee import Employee


def print_header(title: str):
    """Affiche un en-tête formaté"""
    print(f"\n{'='*50}")
    print(f"🔧 {title}")
    print(f"{'='*50}")


def print_test_result(test_name: str, success: bool, details: str = ""):
    """Affiche le résultat d'un test"""
    status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
    print(f"{status} - {test_name}")
    if details:
        print(f"   📝 {details}")


async def test_database_connection():
    """Test de connexion de base"""
    print_header("Test de Connexion de Base")
    
    try:
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT 1 as test"))
            test_value = result.fetchone()[0]
            
        print_test_result("Connexion de base", test_value == 1, "Requête SELECT 1 réussie")
        return True
        
    except Exception as e:
        print_test_result("Connexion de base", False, str(e))
        return False


async def test_table_creation():
    """Test de création des tables"""
    print_header("Test de Création des Tables")
    
    try:
        # Initialiser la base de données (créer les tables)
        await init_db()
        print_test_result("Création des tables", True, "Tables créées avec succès")
        return True
        
    except Exception as e:
        print_test_result("Création des tables", False, str(e))
        return False


async def check_tables_exist():
    """Vérifier que les tables principales existent"""
    print_header("Vérification des Tables Principales")
    
    tables_to_check = [
        'users', 'companies', 'projects', 'employees', 
        'suppliers', 'materials', 'budgets', 'invoices'
    ]
    
    existing_tables = []
    missing_tables = []
    
    try:
        async with AsyncSessionLocal() as session:
            for table in tables_to_check:
                try:
                    result = await session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.fetchone()[0]
                    existing_tables.append((table, count))
                    print_test_result(f"Table {table}", True, f"{count} enregistrements")
                except Exception:
                    missing_tables.append(table)
                    print_test_result(f"Table {table}", False, "Table non trouvée")
        
        if missing_tables:
            print(f"\n⚠️  Tables manquantes: {', '.join(missing_tables)}")
        
        return len(missing_tables) == 0
        
    except Exception as e:
        print_test_result("Vérification des tables", False, str(e))
        return False


async def test_user_model():
    """Test du modèle User"""
    print_header("Test du Modèle User")
    
    try:
        async with AsyncSessionLocal() as session:
            # Compter les utilisateurs existants
            result = await session.execute(text("SELECT COUNT(*) FROM users"))
            user_count = result.fetchone()[0]
            
            print_test_result("Lecture des utilisateurs", True, f"{user_count} utilisateurs trouvés")
            
            # Si des utilisateurs existent, en afficher quelques-uns
            if user_count > 0:
                result = await session.execute(text("SELECT email, first_name, last_name FROM users LIMIT 5"))
                users = result.fetchall()
                
                print("   👥 Utilisateurs existants:")
                for user in users:
                    print(f"      - {user[1]} {user[2]} ({user[0]})")
            
            return True
            
    except Exception as e:
        print_test_result("Test du modèle User", False, str(e))
        return False


async def test_company_model():
    """Test du modèle Company"""
    print_header("Test du Modèle Company")
    
    try:
        async with AsyncSessionLocal() as session:
            # Compter les entreprises existantes
            result = await session.execute(text("SELECT COUNT(*) FROM companies"))
            company_count = result.fetchone()[0]
            
            print_test_result("Lecture des entreprises", True, f"{company_count} entreprises trouvées")
            
            # Si des entreprises existent, en afficher quelques-unes
            if company_count > 0:
                result = await session.execute(text("SELECT name, siret FROM companies LIMIT 5"))
                companies = result.fetchall()
                
                print("   🏢 Entreprises existantes:")
                for company in companies:
                    print(f"      - {company[0]} (SIRET: {company[1] or 'N/A'})")
            
            return True
            
    except Exception as e:
        print_test_result("Test du modèle Company", False, str(e))
        return False


async def test_project_model():
    """Test du modèle Project"""
    print_header("Test du Modèle Project")
    
    try:
        async with AsyncSessionLocal() as session:
            # Compter les projets existants
            result = await session.execute(text("SELECT COUNT(*) FROM projects"))
            project_count = result.fetchone()[0]
            
            print_test_result("Lecture des projets", True, f"{project_count} projets trouvés")
            
            # Si des projets existent, en afficher quelques-uns
            if project_count > 0:
                result = await session.execute(text("""
                    SELECT p.name, p.status, c.name as company_name 
                    FROM projects p 
                    LEFT JOIN companies c ON p.company_id = c.id 
                    LIMIT 5
                """))
                projects = result.fetchall()
                
                print("   🏗️ Projets existants:")
                for project in projects:
                    print(f"      - {project[0]} ({project[1]}) - {project[2] or 'Pas d\'entreprise'}")
            
            return True
            
    except Exception as e:
        print_test_result("Test du modèle Project", False, str(e))
        return False


async def test_basic_crud():
    """Test des opérations CRUD de base"""
    print_header("Test CRUD de Base")
    
    try:
        async with AsyncSessionLocal() as session:
            # Test d'insertion simple (sans commit pour ne pas polluer)
            test_query = text("""
                SELECT 
                    'Test CRUD' as operation,
                    NOW() as timestamp,
                    'OK' as status
            """)
            
            result = await session.execute(test_query)
            test_result = result.fetchone()
            
            print_test_result("Opération CRUD test", True, f"Timestamp: {test_result[1]}")
            return True
            
    except Exception as e:
        print_test_result("Test CRUD de base", False, str(e))
        return False


async def test_database_info():
    """Afficher des informations sur la base de données"""
    print_header("Informations sur la Base de Données")
    
    try:
        async with AsyncSessionLocal() as session:
            # Version PostgreSQL
            result = await session.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            print(f"🐘 PostgreSQL: {version.split(',')[0]}")
            
            # Nom de la base de données
            result = await session.execute(text("SELECT current_database()"))
            db_name = result.fetchone()[0]
            print(f"🗄️  Base de données: {db_name}")
            
            # Utilisateur actuel
            result = await session.execute(text("SELECT current_user"))
            user = result.fetchone()[0]
            print(f"👤 Utilisateur: {user}")
            
            # Nombre total de tables
            result = await session.execute(text("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """))
            table_count = result.fetchone()[0]
            print(f"📊 Nombre de tables: {table_count}")
            
            return True
            
    except Exception as e:
        print_test_result("Informations base de données", False, str(e))
        return False


async def main():
    """Fonction principale de test"""
    print("🚀 ORBIS - Test des Modèles de l'Application")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Compteur de tests réussis
    tests_passed = 0
    total_tests = 7
    
    # Tests
    if await test_database_connection():
        tests_passed += 1
    
    if await test_table_creation():
        tests_passed += 1
    
    if await check_tables_exist():
        tests_passed += 1
    
    if await test_user_model():
        tests_passed += 1
    
    if await test_company_model():
        tests_passed += 1
    
    if await test_project_model():
        tests_passed += 1
    
    if await test_basic_crud():
        tests_passed += 1
    
    # Informations supplémentaires
    await test_database_info()
    
    # Résumé final
    print_header("Résumé des Tests")
    success_rate = (tests_passed / total_tests) * 100
    
    if tests_passed == total_tests:
        print(f"🎉 TOUS LES TESTS RÉUSSIS! ({tests_passed}/{total_tests}) - {success_rate:.0f}%")
        print("✅ Vos modèles sont correctement configurés et fonctionnels.")
    elif tests_passed > 0:
        print(f"⚠️  TESTS PARTIELLEMENT RÉUSSIS ({tests_passed}/{total_tests}) - {success_rate:.0f}%")
        print("🔧 Certains modèles fonctionnent, vérifiez les erreurs ci-dessus.")
    else:
        print(f"❌ TOUS LES TESTS ONT ÉCHOUÉ ({tests_passed}/{total_tests}) - {success_rate:.0f}%")
        print("🚨 Problème avec les modèles de l'application.")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
