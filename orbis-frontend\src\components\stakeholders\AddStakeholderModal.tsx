'use client';

import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/ui/Modal';
import { TCompany } from '@/types/tcompany';
import { StakeholderCreate } from '@/types/stakeholder';
import { tcompaniesApi } from '@/lib/api/tcompanies';
import { stakeholdersApiService } from '@/lib/api/stakeholders';
import { RoleService, BusinessRole } from '@/services/roleService';

interface AddStakeholderModalProps {
  isOpen: boolean;
  onClose: () => void;
  lotId: number;
  onStakeholderAdded: () => void;
}

type TabType = 'select' | 'create';

export function AddStakeholderModal({
  isOpen,
  onClose,
  lotId,
  onStakeholderAdded,
}: AddStakeholderModalProps) {
  const [activeTab, setActiveTab] = useState<TabType>('select');
  const [companies, setCompanies] = useState<TCompany[]>([]);
  const [filteredCompanies, setFilteredCompanies] = useState<TCompany[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCompany, setSelectedCompany] = useState<TCompany | null>(null);
  const [role, setRole] = useState('');
  const [businessRoles, setBusinessRoles] = useState<BusinessRole[]>([]);
  const [rolesLoading, setRolesLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // État pour le formulaire de création d'entreprise
  const [newCompanyForm, setNewCompanyForm] = useState({
    company_name: '',
    activity: '',
    email: '',
    phone: '',
    address: '',
    postal_code: '',
    city: '',
    country: 'France',
    siret: '',
  });

  // Charger les entreprises
  useEffect(() => {
    if (isOpen && activeTab === 'select') {
      loadCompanies();
    }
  }, [isOpen, activeTab]);

  // Filtrer les entreprises en temps réel
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredCompanies(companies);
    } else {
      const filtered = companies.filter(company =>
        company.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (company.activity && company.activity.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredCompanies(filtered);
    }
  }, [searchTerm, companies]);

  const loadCompanies = async () => {
    try {
      setLoading(true);
      const data = await tcompaniesApi.getAll();
      setCompanies(data);
      setFilteredCompanies(data);
    } catch (err) {
      setError('Erreur lors du chargement des entreprises');
      console.error('Error loading companies:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectCompany = (company: TCompany) => {
    setSelectedCompany(company);
  };

  const handleAddExistingCompany = async () => {
    if (!selectedCompany) return;

    try {
      setLoading(true);
      setError(null);

      const stakeholderData: StakeholderCreate = {
        lot_id: lotId,
        company_id: selectedCompany.id,
        role: role.trim() || undefined,
        is_active: true,
      };

      await stakeholdersApiService.create(stakeholderData);
      onStakeholderAdded();
      handleClose();
    } catch (err: any) {
      setError(err.message || 'Erreur lors de l\'ajout de l\'intervenant');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNewCompany = async () => {
    if (!newCompanyForm.company_name.trim()) {
      setError('Le nom de l\'entreprise est obligatoire');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const stakeholderData: StakeholderCreate = {
        lot_id: lotId,
        company_data: {
          company_name: newCompanyForm.company_name.trim(),
          activity: newCompanyForm.activity.trim() || undefined,
          email: newCompanyForm.email.trim() || undefined,
          phone: newCompanyForm.phone.trim() || undefined,
          address: newCompanyForm.address.trim() || undefined,
          postal_code: newCompanyForm.postal_code.trim() || undefined,
          city: newCompanyForm.city.trim() || undefined,
          country: newCompanyForm.country.trim() || 'France',
          siret: newCompanyForm.siret.trim() || undefined,
        },
        role: role.trim() || undefined,
        is_active: true,
      };

      await stakeholdersApiService.create(stakeholderData);
      onStakeholderAdded();
      handleClose();
    } catch (err: any) {
      setError(err.message || 'Erreur lors de la création de l\'intervenant');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setActiveTab('select');
    setSearchTerm('');
    setSelectedCompany(null);
    setRole('');
    setError(null);
    setNewCompanyForm({
      company_name: '',
      activity: '',
      email: '',
      phone: '',
      address: '',
      postal_code: '',
      city: '',
      country: 'France',
      siret: '',
    });
    onClose();
  };

  const updateNewCompanyForm = (field: string, value: string) => {
    setNewCompanyForm(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Ajouter un intervenant">
      <div className="w-full max-w-4xl">
        {/* Onglets */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'select'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('select')}
          >
            Sélectionner un intervenant
          </button>
          <button
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'create'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('create')}
          >
            Ajouter un intervenant
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Contenu des onglets */}
        {activeTab === 'select' && (
          <div className="space-y-4">
            {/* Champ de recherche */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rechercher une entreprise
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Tapez le nom ou l'activité de l'entreprise..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Liste des entreprises */}
            <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-md">
              {loading ? (
                <div className="p-4 text-center text-gray-500">
                  Chargement des entreprises...
                </div>
              ) : filteredCompanies.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  {searchTerm ? 'Aucune entreprise trouvée' : 'Aucune entreprise disponible'}
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredCompanies.map((company) => (
                    <div
                      key={company.id}
                      className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors ${
                        selectedCompany?.id === company.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                      }`}
                      onClick={() => handleSelectCompany(company)}
                    >
                      <div className="font-medium text-gray-900">{company.company_name}</div>
                      {company.activity && (
                        <div className="text-sm text-gray-500">{company.activity}</div>
                      )}
                      {company.email && (
                        <div className="text-sm text-gray-500">{company.email}</div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Champ rôle */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rôle (optionnel)
              </label>
              <input
                type="text"
                value={role}
                onChange={(e) => setRole(e.target.value)}
                placeholder="Ex: Architecte, Bureau d'études, Entreprise générale..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Boutons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                Annuler
              </button>
              <button
                type="button"
                onClick={handleAddExistingCompany}
                disabled={!selectedCompany || loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? 'Ajout...' : 'Ajouter l\'intervenant'}
              </button>
            </div>
          </div>
        )}

        {activeTab === 'create' && (
          <div className="space-y-4">
            {/* Formulaire de création d'entreprise */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nom de l'entreprise *
                </label>
                <input
                  type="text"
                  value={newCompanyForm.company_name}
                  onChange={(e) => updateNewCompanyForm('company_name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Activité
                </label>
                <input
                  type="text"
                  value={newCompanyForm.activity}
                  onChange={(e) => updateNewCompanyForm('activity', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  value={newCompanyForm.email}
                  onChange={(e) => updateNewCompanyForm('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Téléphone
                </label>
                <input
                  type="tel"
                  value={newCompanyForm.phone}
                  onChange={(e) => updateNewCompanyForm('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Adresse
                </label>
                <input
                  type="text"
                  value={newCompanyForm.address}
                  onChange={(e) => updateNewCompanyForm('address', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Code postal
                </label>
                <input
                  type="text"
                  value={newCompanyForm.postal_code}
                  onChange={(e) => updateNewCompanyForm('postal_code', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Ville
                </label>
                <input
                  type="text"
                  value={newCompanyForm.city}
                  onChange={(e) => updateNewCompanyForm('city', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SIRET
                </label>
                <input
                  type="text"
                  value={newCompanyForm.siret}
                  onChange={(e) => updateNewCompanyForm('siret', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rôle (optionnel)
                </label>
                <input
                  type="text"
                  value={role}
                  onChange={(e) => setRole(e.target.value)}
                  placeholder="Ex: Architecte, Bureau d'études..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Boutons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                Annuler
              </button>
              <button
                type="button"
                onClick={handleCreateNewCompany}
                disabled={!newCompanyForm.company_name.trim() || loading}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? 'Création...' : 'Créer et ajouter'}
              </button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
