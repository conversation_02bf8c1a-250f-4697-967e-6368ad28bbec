#!/usr/bin/env python3
"""
Script pour tester les nouveaux endpoints Workspace API
"""

import sys
from pathlib import Path

# Ajouter le répertoire app au path Python
sys.path.insert(0, str(Path(__file__).parent))

def test_endpoint_imports():
    """Tester que tous les imports d'endpoints fonctionnent"""
    print("🔍 Test des imports des endpoints Workspace...")
    
    try:
        # Test des nouveaux endpoints workspace
        from app.api.api_v1.endpoints import workspaces, admin_workspaces
        print("   ✅ Endpoints Workspace importés")
        
        # Test des endpoints de compatibilité
        from app.api.api_v1.endpoints import companies, admin_companies
        print("   ✅ Endpoints de compatibilité importés")
        
        # Test de l'import du router principal
        from app.api.api_v1.api import api_router
        print("   ✅ Router principal importé")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Erreur d'import: {e}")
        return False

def test_router_structure():
    """Tester la structure des routers"""
    print("\n🏗️ Test de la structure des routers...")
    
    try:
        from app.api.api_v1.endpoints.workspaces import router as workspace_router
        from app.api.api_v1.endpoints.admin_workspaces import router as admin_workspace_router
        
        # Vérifier que les routers ont des routes
        workspace_routes = [route.path for route in workspace_router.routes]
        admin_workspace_routes = [route.path for route in admin_workspace_router.routes]
        
        print(f"   📋 Routes workspace: {len(workspace_routes)}")
        for route in workspace_routes[:5]:  # Afficher les 5 premières
            print(f"     - {route}")
        
        print(f"   🔧 Routes admin workspace: {len(admin_workspace_routes)}")
        for route in admin_workspace_routes[:5]:  # Afficher les 5 premières
            print(f"     - {route}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_schema_compatibility():
    """Tester la compatibilité des schémas dans les endpoints"""
    print("\n🔄 Test de compatibilité des schémas...")
    
    try:
        # Vérifier que les endpoints peuvent importer les schémas
        from app.api.api_v1.endpoints.workspaces import WorkspaceSchema, WorkspaceCreate
        from app.api.api_v1.endpoints.admin_workspaces import AdminWorkspaceResponse
        
        print("   ✅ Schémas workspace importés dans les endpoints")
        
        # Vérifier les alias de compatibilité
        from app.schemas.workspace import Company, CompanyCreate
        from app.schemas.workspace import Workspace, WorkspaceCreate as WsCreate
        
        if Company is Workspace and CompanyCreate is WsCreate:
            print("   ✅ Alias de compatibilité fonctionnent")
        else:
            print("   ❌ Problème avec les alias de compatibilité")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_model_imports():
    """Tester les imports de modèles dans les endpoints"""
    print("\n📦 Test des imports de modèles...")
    
    try:
        # Vérifier que les endpoints peuvent importer les modèles
        from app.models.workspace import Workspace, UserWorkspace, WorkspaceSettings
        print("   ✅ Modèles Workspace importés")
        
        # Vérifier les alias de compatibilité
        from app.models.workspace import Company, UserCompany, CompanySettings
        print("   ✅ Alias de modèles importés")
        
        # Vérifier que les modèles ont les bonnes tables
        expected_tables = {
            Workspace: "workspaces",
            UserWorkspace: "user_workspaces",
            WorkspaceSettings: "workspace_settings"
        }
        
        for model, expected_table in expected_tables.items():
            actual_table = model.__tablename__
            if actual_table == expected_table:
                print(f"   ✅ {model.__name__}: {actual_table}")
            else:
                print(f"   ❌ {model.__name__}: attendu '{expected_table}', trouvé '{actual_table}'")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_deps_functions():
    """Tester les fonctions de dépendances"""
    print("\n🔗 Test des fonctions de dépendances...")
    
    try:
        from app.api.deps import get_workspace_access
        print("   ✅ Fonctions de dépendances importées")
        
        # Vérifier que les fonctions existent et sont callables
        if callable(get_workspace_access):
            print("   ✅ get_workspace_access est callable")
        else:
            print("   ❌ get_workspace_access n'est pas callable")
            return False
        
        # Test supprimé : get_company_access remplacé par get_workspace_access
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_api_router_registration():
    """Tester l'enregistrement des routes dans le router principal"""
    print("\n🌐 Test de l'enregistrement des routes...")
    
    try:
        from app.api.api_v1.api import api_router
        
        # Récupérer toutes les routes
        all_routes = []
        for route in api_router.routes:
            if hasattr(route, 'path'):
                all_routes.append(route.path)
        
        # Vérifier que les nouvelles routes workspace sont présentes
        workspace_routes = [route for route in all_routes if '/workspaces' in route]
        company_routes = [route for route in all_routes if '/companies' in route]
        admin_workspace_routes = [route for route in all_routes if '/admin/workspaces' in route]
        admin_company_routes = [route for route in all_routes if '/admin/companies' in route]
        
        print(f"   📋 Routes /workspaces: {len(workspace_routes)}")
        print(f"   📋 Routes /companies: {len(company_routes)} (compatibilité)")
        print(f"   🔧 Routes /admin/workspaces: {len(admin_workspace_routes)}")
        print(f"   🔧 Routes /admin/companies: {len(admin_company_routes)} (compatibilité)")
        
        if len(workspace_routes) > 0 and len(admin_workspace_routes) > 0:
            print("   ✅ Nouvelles routes workspace enregistrées")
        else:
            print("   ❌ Routes workspace manquantes")
            return False
        
        if len(company_routes) > 0 and len(admin_company_routes) > 0:
            print("   ✅ Routes de compatibilité présentes")
        else:
            print("   ❌ Routes de compatibilité manquantes")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_endpoint_functions():
    """Tester que les fonctions d'endpoints sont bien définies"""
    print("\n⚙️ Test des fonctions d'endpoints...")
    
    try:
        from app.api.api_v1.endpoints.workspaces import (
            read_workspaces, create_workspace, read_workspace,
            update_workspace, delete_workspace
        )
        print("   ✅ Fonctions workspace CRUD importées")
        
        from app.api.api_v1.endpoints.admin_workspaces import (
            list_all_workspaces, get_workspace_by_id, create_workspace_admin,
            update_workspace_admin, delete_workspace_admin
        )
        print("   ✅ Fonctions admin workspace importées")
        
        # Vérifier que les fonctions sont callables
        functions_to_test = [
            read_workspaces, create_workspace, read_workspace,
            list_all_workspaces, get_workspace_by_id
        ]
        
        for func in functions_to_test:
            if callable(func):
                print(f"   ✅ {func.__name__} est callable")
            else:
                print(f"   ❌ {func.__name__} n'est pas callable")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 TEST DES ENDPOINTS WORKSPACE API")
    print("="*50)
    
    tests = [
        test_endpoint_imports,
        test_router_structure,
        test_schema_compatibility,
        test_model_imports,
        test_deps_functions,
        test_api_router_registration,
        test_endpoint_functions
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   💥 Erreur inattendue dans {test.__name__}: {e}")
            results.append(False)
    
    # Résumé
    print(f"\n📊 RÉSUMÉ: {sum(results)}/{len(results)} tests réussis")
    
    if all(results):
        print("🎉 Tous les tests sont passés!")
        return True
    else:
        print("❌ Certains tests ont échoué")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
