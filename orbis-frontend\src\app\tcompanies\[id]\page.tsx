'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { BusinessDataService } from '@/lib/auth'
import { useParams, useRouter } from 'next/navigation'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import TCompanyModal from '@/components/TCompanyModal'
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  BuildingOfficeIcon, 
  PhoneIcon, 
  EnvelopeIcon,
  MapPinIcon,
  IdentificationIcon,
  UserIcon,
  GlobeAltIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'
import Image from 'next/image'
import Link from 'next/link'
import { cleanFormData } from '@/utils/formUtils'

interface TCompany {
  id: number
  company_name: string
  activity?: string
  address?: string
  postal_code?: string
  city?: string
  country?: string
  phone?: string
  fax?: string
  email?: string
  siret?: string
  vat_number?: string
  legal_representative_name?: string
  legal_representative_email?: string
  logo_url?: string
  logo_filename?: string
  is_active: boolean
  created_at: string
  updated_at: string
  full_address?: string
  is_siret_valid?: boolean
}

interface TCompanyFormData {
  company_name: string
  activity: string
  address: string
  postal_code: string
  city: string
  country: string
  phone: string
  fax: string
  email: string
  siret: string
  vat_number: string
  logo_url?: string
  logo_filename?: string
}

export default function TCompanyDetailPage() {
  const { user, signOut } = useAuth()
  const params = useParams()
  const router = useRouter()
  const tcompanyId = params.id as string

  const [tcompany, setTCompany] = useState<TCompany | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [formData, setFormData] = useState<TCompanyFormData>({
    company_name: '',
    activity: '',
    address: '',
    postal_code: '',
    city: '',
    country: 'France',
    phone: '',
    fax: '',
    email: '',
    siret: '',
    vat_number: '',
    logo_url: '',
    logo_filename: ''
  })

  // Fonction de déconnexion
  const handleLogout = () => {
    signOut()
  }

  // Charger les détails de la TCompany
  const fetchTCompany = async () => {
    try {
      setLoading(true)
      const response = await BusinessDataService.makeAuthenticatedRequest(`/tcompanies/${tcompanyId}`)

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Entreprise non trouvée')
        }
        throw new Error('Erreur lors du chargement de l\'entreprise')
      }

      const data = await response.json()
      setTCompany(data)
      setError(null)
    } catch (err: any) {
      console.error('Erreur:', err)
      setError(err.message || 'Erreur lors du chargement de l\'entreprise')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (tcompanyId) {
      fetchTCompany()
    }
  }, [tcompanyId])

  // Ouvrir le modal d'édition
  const handleEdit = () => {
    if (!tcompany) return

    setFormData({
      company_name: tcompany.company_name || '',
      activity: tcompany.activity || '',
      address: tcompany.address || '',
      postal_code: tcompany.postal_code || '',
      city: tcompany.city || '',
      country: tcompany.country || 'France',
      phone: tcompany.phone || '',
      fax: tcompany.fax || '',
      email: tcompany.email || '',
      siret: tcompany.siret || '',
      vat_number: tcompany.vat_number || '',
      logo_url: tcompany.logo_url || '',
      logo_filename: tcompany.logo_filename || ''
    })
    setIsEditModalOpen(true)
  }

  // Gérer la soumission du formulaire d'édition
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      // Nettoyer les données : remplacer les chaînes vides par null
      const cleanedFormData = cleanFormData(formData)

      console.log('🔄 Mise à jour TCompany:', { tcompanyId, formData: cleanedFormData })

      const response = await BusinessDataService.makeAuthenticatedRequest(`/tcompanies/${tcompanyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanedFormData)
      })

      console.log('📡 Réponse mise à jour:', { status: response.status, statusText: response.statusText })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Erreur détaillée mise à jour:', errorText)
        throw new Error(`Erreur ${response.status}: ${errorText}`)
      }

      // Recharger les détails
      await fetchTCompany()
      setIsEditModalOpen(false)

    } catch (err) {
      console.error('Erreur:', err)
      setError('Erreur lors de la mise à jour')
    }
  }

  // Formater la date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-white">
          <div className="flex">
            <ModernSidebar user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined} />

            <div className="flex-1 lg:ml-72">
              <ModernHeader
                title="Détails de l'entreprise"
                subtitle="Chargement..."
                user={user ? {
                  name: `${user.first_name} ${user.last_name}`,
                  email: user.email
                } : undefined}
                onLogout={handleLogout}
              />

              <main className="p-6">
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Chargement des détails...</p>
                  </div>
                </div>
              </main>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  if (error || !tcompany) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-white">
          <div className="flex">
            <ModernSidebar user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined} />

            <div className="flex-1 lg:ml-72">
              <ModernHeader
                title="Erreur"
                subtitle="Entreprise non trouvée"
                user={user ? {
                  name: `${user.first_name} ${user.last_name}`,
                  email: user.email
                } : undefined}
                onLogout={handleLogout}
              />

              <main className="p-6">
                <div className="text-center py-12">
                  <div className="text-red-500 text-4xl mb-4">⚠️</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Erreur</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <Link href="/tcompanies" className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
                    <ArrowLeftIcon className="h-4 w-4 mr-2" />
                    Retour au carnet d'adresses
                  </Link>
                </div>
              </main>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-white">
        <div className="flex">
          <ModernSidebar user={user ? {
            name: `${user.first_name} ${user.last_name}`,
            email: user.email
          } : undefined} />

          <div className="flex-1 lg:ml-72">
            <ModernHeader
              breadcrumbs={[
                { label: 'Carnet d\'adresses', href: '/tcompanies' },
                { label: tcompany.company_name }
              ]}
              title={tcompany.company_name}
              subtitle={tcompany.activity || 'Détails de l\'entreprise'}
              backButton={{
                label: 'Retour au carnet d\'adresses',
                href: '/tcompanies'
              }}
              editButton={{
                label: 'Modifier',
                onClick: handleEdit
              }}
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={handleLogout}
            />

            <main className="p-6">
              {/* Contenu principal */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Informations principales */}
                <div className="lg:col-span-2 space-y-6">
                  {/* En-tête avec logo */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <div className="flex items-start gap-4">
                      {tcompany.logo_url ? (
                        <div className="w-20 h-20 rounded-xl overflow-hidden bg-gray-100 flex-shrink-0">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${tcompany.logo_url}`}
                            alt={`Logo ${tcompany.company_name}`}
                            width={80}
                            height={80}
                            className="w-full h-full object-contain"
                          />
                        </div>
                      ) : (
                        <div className="w-20 h-20 rounded-xl bg-primary-100 flex items-center justify-center flex-shrink-0">
                          <BuildingOfficeIcon className="h-10 w-10 text-primary-600" />
                        </div>
                      )}
                      
                      <div className="flex-1">
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">{tcompany.company_name}</h1>
                        {tcompany.activity && (
                          <p className="text-lg text-gray-600 mb-3">{tcompany.activity}</p>
                        )}
                        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          tcompany.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {tcompany.is_active ? 'Entreprise active' : 'Entreprise inactive'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Informations de contact */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">Informations de contact</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {tcompany.phone && (
                        <div className="flex items-center gap-3">
                          <PhoneIcon className="h-5 w-5 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-500">Téléphone</p>
                            <p className="font-medium">{tcompany.phone}</p>
                          </div>
                        </div>
                      )}

                      {tcompany.fax && (
                        <div className="flex items-center gap-3">
                          <PhoneIcon className="h-5 w-5 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-500">Fax</p>
                            <p className="font-medium">{tcompany.fax}</p>
                          </div>
                        </div>
                      )}

                      {tcompany.email && (
                        <div className="flex items-center gap-3">
                          <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-500">Email</p>
                            <a href={`mailto:${tcompany.email}`} className="font-medium text-primary-600 hover:text-primary-700">
                              {tcompany.email}
                            </a>
                          </div>
                        </div>
                      )}

                      {tcompany.full_address && (
                        <div className="flex items-start gap-3 md:col-span-2">
                          <MapPinIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                          <div>
                            <p className="text-sm text-gray-500">Adresse</p>
                            <p className="font-medium">{tcompany.full_address}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Informations légales */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">Informations légales</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {tcompany.siret && (
                        <div className="flex items-center gap-3">
                          <IdentificationIcon className="h-5 w-5 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-500">SIRET</p>
                            <div className="flex items-center gap-2">
                              <p className="font-medium">{tcompany.siret}</p>
                              {tcompany.is_siret_valid !== undefined && (
                                <span className={`text-xs px-2 py-1 rounded-full ${
                                  tcompany.is_siret_valid 
                                    ? 'bg-green-100 text-green-800' 
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {tcompany.is_siret_valid ? 'Valide' : 'Invalide'}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {tcompany.vat_number && (
                        <div className="flex items-center gap-3">
                          <GlobeAltIcon className="h-5 w-5 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-500">TVA Intracommunautaire</p>
                            <p className="font-medium">{tcompany.vat_number}</p>
                          </div>
                        </div>
                      )}

                      {tcompany.legal_representative_name && (
                        <div className="flex items-center gap-3 md:col-span-2">
                          <UserIcon className="h-5 w-5 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-500">Représentant légal</p>
                            <p className="font-medium">{tcompany.legal_representative_name}</p>
                            {tcompany.legal_representative_email && (
                              <a href={`mailto:${tcompany.legal_representative_email}`} className="text-sm text-primary-600 hover:text-primary-700">
                                {tcompany.legal_representative_email}
                              </a>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Sidebar avec métadonnées */}
                <div className="space-y-6">
                  {/* Métadonnées */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Métadonnées</h3>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <CalendarIcon className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-500">Créée le</p>
                          <p className="font-medium">{formatDate(tcompany.created_at)}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <CalendarIcon className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-500">Modifiée le</p>
                          <p className="font-medium">{formatDate(tcompany.updated_at)}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <IdentificationIcon className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-500">ID</p>
                          <p className="font-medium">#{tcompany.id}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions rapides */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h3>
                    <div className="space-y-3">
                      <button
                        onClick={handleEdit}
                        className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
                      >
                        <PencilIcon className="h-4 w-4" />
                        Modifier l'entreprise
                      </button>
                      
                      {tcompany.email && (
                        <a
                          href={`mailto:${tcompany.email}`}
                          className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                        >
                          <EnvelopeIcon className="h-4 w-4" />
                          Envoyer un email
                        </a>
                      )}

                      {tcompany.phone && (
                        <a
                          href={`tel:${tcompany.phone}`}
                          className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                        >
                          <PhoneIcon className="h-4 w-4" />
                          Appeler
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>

        {/* Modal d'édition */}
        <TCompanyModal
          key={`edit-modal-${tcompany.id}`}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={handleSubmit}
          formData={formData}
          setFormData={setFormData}
          isEdit={true}
          tcompanyId={tcompany.id}
          onRefresh={fetchTCompany}
        />
      </div>
    </ProtectedRoute>
  )
}
