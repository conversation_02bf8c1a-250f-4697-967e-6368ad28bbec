#!/usr/bin/env python3
"""
Script pour corriger la structure de la table users
- Changer id de UUID vers INTEGER auto-incrémenté
- Garder supabase_user_id comme VARCHAR pour la liaison
"""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def fix_users_table():
    """Corriger la structure de la table users"""
    
    # Configuration de la base de données
    DATABASE_URL = os.getenv("DATABASE_URL")
    if not DATABASE_URL:
        print("❌ DATABASE_URL non trouvé dans .env")
        return False
    
    print("🔧 Correction de la structure de la table users")
    print(f"📍 Base de données: {DATABASE_URL.split('@')[1] if '@' in DATABASE_URL else 'localhost'}")
    
    try:
        # Connexion à la base de données (avec statement_cache_size=0 pour pgbouncer)
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        print("✅ Connexion à la base de données réussie")
        
        # 1. Vérifier la structure actuelle
        print("\n1. 📋 Vérification de la structure actuelle...")

        # Vérifier si la table existe dans le schéma public
        table_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'users'
            );
        """)

        if not table_exists:
            print("⚠️ La table public.users n'existe pas encore. Création d'une nouvelle table...")
            users_data = []
        else:
            result = await conn.fetch("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_schema = 'public' AND table_name = 'users'
                ORDER BY ordinal_position;
            """)
        
            print("Structure actuelle de la table users:")
            for row in result:
                print(f"  - {row['column_name']}: {row['data_type']} ({'NULL' if row['is_nullable'] == 'YES' else 'NOT NULL'})")

            # 2. Sauvegarder les données existantes
            print("\n2. 💾 Sauvegarde des données existantes...")
            users_data = await conn.fetch("SELECT * FROM public.users")
            print(f"✅ {len(users_data)} utilisateurs sauvegardés")
        
        # 3. Supprimer les contraintes de clé étrangère
        print("\n3. 🔗 Suppression des contraintes de clé étrangère...")
        
        # Trouver toutes les contraintes FK qui référencent users.id
        fk_constraints = await conn.fetch("""
            SELECT 
                tc.constraint_name,
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name 
            FROM 
                information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY' 
                AND ccu.table_name = 'users'
                AND ccu.column_name = 'id';
        """)
        
        dropped_constraints = []
        for fk in fk_constraints:
            constraint_name = fk['constraint_name']
            table_name = fk['table_name']
            try:
                await conn.execute(f"ALTER TABLE {table_name} DROP CONSTRAINT {constraint_name}")
                dropped_constraints.append((table_name, constraint_name, fk['column_name']))
                print(f"  ✅ Contrainte {constraint_name} supprimée de {table_name}")
            except Exception as e:
                print(f"  ⚠️ Erreur suppression contrainte {constraint_name}: {e}")
        
        # 4. Recréer la table avec la bonne structure
        print("\n4. 🏗️ Recréation de la table users...")
        
        # Supprimer l'ancienne table si elle existe
        if table_exists:
            await conn.execute("DROP TABLE IF EXISTS public.users CASCADE")
            print("  ✅ Ancienne table supprimée")
        else:
            print("  ℹ️ Aucune table à supprimer")
        
        # Créer la nouvelle table avec id INTEGER
        await conn.execute("""
            CREATE TABLE public.users (
                id SERIAL PRIMARY KEY,
                supabase_user_id VARCHAR UNIQUE,
                email VARCHAR UNIQUE NOT NULL,
                hashed_password VARCHAR,
                first_name VARCHAR NOT NULL,
                last_name VARCHAR NOT NULL,
                role VARCHAR DEFAULT 'employe',
                is_active BOOLEAN DEFAULT true,
                is_superuser BOOLEAN DEFAULT false,
                is_verified BOOLEAN DEFAULT false,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
                last_login TIMESTAMP WITHOUT TIME ZONE,
                failed_login_attempts INTEGER DEFAULT 0,
                locked_until TIMESTAMP WITHOUT TIME ZONE,
                phone VARCHAR,
                avatar_url VARCHAR,
                notes TEXT
            )
        """)
        print("  ✅ Nouvelle table créée avec id INTEGER")
        
        # 5. Restaurer les données avec de nouveaux IDs
        print("\n5. 📥 Restauration des données...")
        
        # Créer un mapping ancien UUID -> nouveau ID
        id_mapping = {}
        
        for i, user in enumerate(users_data, 1):
            old_id = user['id']
            
            # Insérer avec un nouvel ID auto-généré
            new_id = await conn.fetchval("""
                INSERT INTO users (
                    supabase_user_id, email, hashed_password, first_name, last_name,
                    role, is_active, is_superuser, is_verified, created_at, updated_at,
                    last_login, failed_login_attempts, locked_until, phone, avatar_url, notes
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
                RETURNING id
            """, 
                user['supabase_user_id'], user['email'], user['hashed_password'],
                user['first_name'], user['last_name'], user['role'], user['is_active'],
                user['is_superuser'], user['is_verified'], user['created_at'], user['updated_at'],
                user['last_login'], user['failed_login_attempts'], user['locked_until'],
                user['phone'], user['avatar_url'], user['notes']
            )
            
            id_mapping[str(old_id)] = new_id
            print(f"  ✅ Utilisateur {user['email']}: {old_id} -> {new_id}")
        
        # 6. Recréer les contraintes de clé étrangère
        print("\n6. 🔗 Recréation des contraintes de clé étrangère...")
        
        for table_name, constraint_name, column_name in dropped_constraints:
            try:
                await conn.execute(f"""
                    ALTER TABLE {table_name} 
                    ADD CONSTRAINT {constraint_name} 
                    FOREIGN KEY ({column_name}) REFERENCES users(id)
                """)
                print(f"  ✅ Contrainte {constraint_name} recréée sur {table_name}")
            except Exception as e:
                print(f"  ⚠️ Erreur recréation contrainte {constraint_name}: {e}")
        
        # 7. Mettre à jour les références dans les autres tables
        print("\n7. 🔄 Mise à jour des références...")
        
        # Mettre à jour user_companies
        for old_id, new_id in id_mapping.items():
            try:
                await conn.execute("""
                    UPDATE user_companies 
                    SET user_id = $1 
                    WHERE user_id = $2::uuid
                """, new_id, old_id)
            except Exception as e:
                print(f"  ⚠️ Erreur mise à jour user_companies pour {old_id}: {e}")
        
        print(f"  ✅ {len(id_mapping)} références mises à jour")
        
        # 8. Vérification finale
        print("\n8. ✅ Vérification finale...")
        final_count = await conn.fetchval("SELECT COUNT(*) FROM users")
        print(f"  ✅ {final_count} utilisateurs dans la nouvelle table")
        
        # Vérifier la nouvelle structure
        new_structure = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name IN ('id', 'supabase_user_id')
            ORDER BY ordinal_position;
        """)
        
        print("  Nouvelle structure:")
        for row in new_structure:
            print(f"    - {row['column_name']}: {row['data_type']}")
        
        await conn.close()
        print("\n🎉 Migration terminée avec succès !")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la migration: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Script de correction de la table users")
    print("⚠️  ATTENTION: Ce script va modifier la structure de la base de données")
    
    confirm = input("\nContinuer ? (oui/non): ").lower().strip()
    if confirm in ['oui', 'o', 'yes', 'y']:
        success = asyncio.run(fix_users_table())
        if success:
            print("\n✅ Migration réussie ! Vous pouvez maintenant redémarrer le serveur.")
        else:
            print("\n❌ Migration échouée. Vérifiez les erreurs ci-dessus.")
    else:
        print("❌ Migration annulée.")
