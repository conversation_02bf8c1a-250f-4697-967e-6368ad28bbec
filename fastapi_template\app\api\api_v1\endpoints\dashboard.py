# app/api/api_v1/endpoints/dashboard.py
from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from sqlalchemy import func, select
from datetime import datetime, timedelta

from app.api import deps
from app.models.user import User
from app.models.workspace import UserWorkspace, Workspace
from app.models.project import Project
from app.models.employee import Employee, TimeEntry
from app.models.supplier import Supplier
from app.models.material import Material
from app.models.financial import Budget, Invoice
from app.models.purchase_order import PurchaseOrder
from app.models.quote import Quote
from app.models.document import Document

router = APIRouter()

# Fonction pour vérifier l'accès dashboard (super admin ou utilisateur avec entreprises)
def require_dashboard_access(
    request: Request,
    db: Session = Depends(deps.get_db)
) -> Dict[str, Any]:
    """Vérifie l'accès au dashboard - super admin ou utilisateur avec entreprises"""
    from app.middleware.auth_sync_middleware import get_current_user

    user_data = get_current_user(request)
    if not user_data:
        raise HTTPException(status_code=401, detail="Non authentifié")

    user_role = user_data.get("role", "").upper()
    user_id = user_data.get("id")

    # Les super admins ont accès à tout
    if user_role == "SUPER_ADMIN":
        return user_data

    # Pour les autres utilisateurs, vérifier qu'ils ont au moins une entreprise
    user_workspaces = db.query(UserWorkspace).filter(UserWorkspace.user_id == user_id).all()
    if not user_workspaces:
        raise HTTPException(
            status_code=403,
            detail="Accès refusé. Aucune entreprise associée."
        )

    return user_data

@router.get("/stats", response_model=Dict[str, Any])
def get_dashboard_stats(
    request: Request,
    db: Session = Depends(deps.get_db),
    workspace_id: int = None,
    user_data: Dict[str, Any] = Depends(require_dashboard_access),
) -> Any:
    """
    Get dashboard statistics for company or all user companies.
    """
    user_role = user_data.get("role", "").upper()
    user_id = user_data.get("id")

    if workspace_id:
        # Pour un workspace_id spécifique, vérifier l'accès si pas super admin
        if user_role != "SUPER_ADMIN":
            user_workspace = db.query(UserWorkspace).filter(
                UserWorkspace.user_id == user_id,
                UserWorkspace.workspace_id == workspace_id
            ).first()
            if not user_workspace:
                raise HTTPException(status_code=403, detail="Accès refusé à cette entreprise")
        workspace_ids = [workspace_id]
    else:
        # Si l'utilisateur est SUPER_ADMIN, récupérer toutes les entreprises
        if user_role == "SUPER_ADMIN":
            from app.models.workspace import Workspace
            result = db.execute(select(Workspace))
            all_workspaces = result.scalars().all()
            workspace_ids = [c.id for c in all_workspaces]
        else:
            result = db.execute(select(UserWorkspace).filter(UserWorkspace.user_id == user_id))
            user_workspaces = result.scalars().all()
            workspace_ids = [uc.workspace_id for uc in user_workspaces]
    
    # Projects statistics
    total_projects = db.query(Project).filter(Project.workspace_id.in_(workspace_ids)).count()
    active_projects = db.query(Project).filter(
        Project.workspace_id.in_(workspace_ids),
        Project.is_archived == False
    ).count()
    
    # Employee statistics
    total_employees = db.query(Employee).filter(
        Employee.workspace_id.in_(workspace_ids),
        Employee.is_active == True
    ).count()
    
    # Supplier statistics
    total_suppliers = db.query(Supplier).filter(
        Supplier.workspace_id.in_(workspace_ids),
        Supplier.is_active == True
    ).count()
    
    # Material statistics
    total_materials = db.query(Material).filter(
        Material.workspace_id.in_(workspace_ids),
        Material.is_active == True
    ).count()
    
    # Financial statistics
    total_budgets = db.query(Budget).filter(Budget.workspace_id.in_(workspace_ids)).count()
    pending_invoices = db.query(Invoice).filter(
        Invoice.workspace_id.in_(workspace_ids),
        Invoice.status.in_(["draft", "sent"])
    ).count()
    
    # Purchase Order statistics
    pending_orders = db.query(PurchaseOrder).filter(
        PurchaseOrder.workspace_id.in_(workspace_ids),
        PurchaseOrder.status.in_(["draft", "sent", "confirmed"])
    ).count()
    
    # Quote statistics
    pending_quotes = db.query(Quote).filter(
        Quote.workspace_id.in_(workspace_ids),
        Quote.status.in_(["draft", "sent"])
    ).count()
    
    # Document statistics
    total_documents = db.query(Document).filter(
        Document.workspace_id.in_(workspace_ids),
        Document.is_active == True
    ).count()
    
    return {
        "projects": {
            "total": total_projects,
            "active": active_projects,
            "archived": total_projects - active_projects
        },
        "employees": {
            "total": total_employees
        },
        "suppliers": {
            "total": total_suppliers
        },
        "materials": {
            "total": total_materials
        },
        "financial": {
            "total_budgets": total_budgets,
            "pending_invoices": pending_invoices
        },
        "purchase_orders": {
            "pending": pending_orders
        },
        "quotes": {
            "pending": pending_quotes
        },
        "documents": {
            "total": total_documents
        }
    }

@router.get("/recent-activity", response_model=Dict[str, Any])
def get_recent_activity(
    request: Request,
    db: Session = Depends(deps.get_db),
    workspace_id: int = None,
    limit: int = 10,
    user_data: Dict[str, Any] = Depends(require_dashboard_access),
) -> Any:
    """
    Get recent activity across all modules.
    """
    user_role = user_data.get("role", "").upper()
    user_id = user_data.get("id")

    if workspace_id:
        # Pour un workspace_id spécifique, vérifier l'accès si pas super admin
        if user_role != "SUPER_ADMIN":
            user_workspace = db.query(UserWorkspace).filter(
                UserWorkspace.user_id == user_id,
                UserWorkspace.workspace_id == workspace_id
            ).first()
            if not user_workspace:
                raise HTTPException(status_code=403, detail="Accès refusé à cette entreprise")
        workspace_ids = [workspace_id]
    else:
        # Si l'utilisateur est SUPER_ADMIN, récupérer toutes les entreprises
        if user_role == "SUPER_ADMIN":
            from app.models.workspace import Workspace
            all_workspaces = db.query(Workspace).all()
            workspace_ids = [c.id for c in all_workspaces]
        else:
            user_workspaces = db.query(UserWorkspace).filter(UserWorkspace.user_id == user_id).all()
            workspace_ids = [uc.workspace_id for uc in user_workspaces]
    
    # Recent projects
    recent_projects = db.query(Project).filter(
        Project.workspace_id.in_(workspace_ids)
    ).order_by(Project.created_at.desc()).limit(5).all()
    
    # Recent invoices
    recent_invoices = db.query(Invoice).filter(
        Invoice.workspace_id.in_(workspace_ids)
    ).order_by(Invoice.created_at.desc()).limit(5).all()
    
    # Recent purchase orders
    recent_orders = db.query(PurchaseOrder).filter(
        PurchaseOrder.workspace_id.in_(workspace_ids)
    ).order_by(PurchaseOrder.created_at.desc()).limit(5).all()
    
    # Recent quotes
    recent_quotes = db.query(Quote).filter(
        Quote.workspace_id.in_(workspace_ids)
    ).order_by(Quote.created_at.desc()).limit(5).all()
    
    # Recent documents
    recent_documents = db.query(Document).filter(
        Document.workspace_id.in_(workspace_ids),
        Document.is_active == True
    ).order_by(Document.created_at.desc()).limit(5).all()
    
    return {
        "recent_projects": [
            {
                "id": p.id,
                "name": p.name,
                "code": p.code,
                "status": p.status,
                "created_at": p.created_at
            } for p in recent_projects
        ],
        "recent_invoices": [
            {
                "id": i.id,
                "invoice_number": i.invoice_number,
                "status": i.status,
                "total_amount_ttc": float(i.total_amount_ttc) if i.total_amount_ttc else 0,
                "created_at": i.created_at
            } for i in recent_invoices
        ],
        "recent_orders": [
            {
                "id": o.id,
                "order_number": o.order_number,
                "status": o.status,
                "total_amount_ttc": float(o.total_amount_ttc) if o.total_amount_ttc else 0,
                "created_at": o.created_at
            } for o in recent_orders
        ],
        "recent_quotes": [
            {
                "id": q.id,
                "quote_number": q.quote_number,
                "title": q.title,
                "status": q.status,
                "total_amount_ttc": float(q.total_amount_ttc) if q.total_amount_ttc else 0,
                "created_at": q.created_at
            } for q in recent_quotes
        ],
        "recent_documents": [
            {
                "id": d.id,
                "name": d.name,
                "category": d.category,
                "file_size": d.file_size,
                "created_at": d.created_at
            } for d in recent_documents
        ]
    }

@router.get("/project-progress", response_model=Dict[str, Any])
def get_project_progress(
    request: Request,
    db: Session = Depends(deps.get_db),
    workspace_id: int = None,
    user_data: Dict[str, Any] = Depends(require_dashboard_access),
) -> Any:
    """
    Get project progress analytics.
    """
    user_role = user_data.get("role", "").upper()
    user_id = user_data.get("id")

    if workspace_id:
        # Pour un workspace_id spécifique, vérifier l'accès si pas super admin
        if user_role != "SUPER_ADMIN":
            user_workspace = db.query(UserWorkspace).filter(
                UserWorkspace.user_id == user_id,
                UserWorkspace.workspace_id == workspace_id
            ).first()
            if not user_workspace:
                raise HTTPException(status_code=403, detail="Accès refusé à cette entreprise")
        workspace_ids = [workspace_id]
    else:
        # Si l'utilisateur est SUPER_ADMIN, récupérer toutes les entreprises
        if user_role == "SUPER_ADMIN":
            from app.models.workspace import Workspace
            all_workspaces = db.query(Workspace).all()
            workspace_ids = [c.id for c in all_workspaces]
        else:
            user_workspaces = db.query(UserWorkspace).filter(UserWorkspace.user_id == user_id).all()
            workspace_ids = [uc.workspace_id for uc in user_workspaces]
    
    # Project status distribution
    project_status = db.query(
        Project.status,
        func.count(Project.id).label('count')
    ).filter(
        Project.workspace_id.in_(workspace_ids)
    ).group_by(Project.status).all()
    
    # Projects by month (last 12 months)
    twelve_months_ago = datetime.utcnow() - timedelta(days=365)
    projects_by_month = db.query(
        func.date_trunc('month', Project.created_at).label('month'),
        func.count(Project.id).label('count')
    ).filter(
        Project.workspace_id.in_(workspace_ids),
        Project.created_at >= twelve_months_ago
    ).group_by(func.date_trunc('month', Project.created_at)).all()
    
    # Budget vs actual analysis
    budget_analysis = []
    projects_with_budgets = db.query(Project).filter(
        Project.workspace_id.in_(workspace_ids),
        Project.budget_total.isnot(None)
    ).all()
    
    for project in projects_with_budgets:
        budgets = db.query(Budget).filter(Budget.project_id == project.id).all()
        total_budget = sum([float(b.total_amount) for b in budgets])
        budget_analysis.append({
            "project_id": project.id,
            "project_name": project.name,
            "planned_budget": float(project.budget_total),
            "actual_budget": total_budget,
            "variance": total_budget - float(project.budget_total) if project.budget_total else 0
        })
    
    return {
        "status_distribution": [
            {"status": status, "count": count} for status, count in project_status
        ],
        "projects_by_month": [
            {"month": month.strftime("%Y-%m"), "count": count} for month, count in projects_by_month
        ],
        "budget_analysis": budget_analysis
    }

@router.get("/employee-performance", response_model=Dict[str, Any])
def get_employee_performance(
    request: Request,
    db: Session = Depends(deps.get_db),
    workspace_id: int = None,
    user_data: Dict[str, Any] = Depends(require_dashboard_access),
) -> Any:
    """
    Get employee performance analytics.
    """
    user_role = user_data.get("role", "").upper()
    user_id = user_data.get("id")

    if workspace_id:
        # Pour un workspace_id spécifique, vérifier l'accès si pas super admin
        if user_role != "SUPER_ADMIN":
            user_workspace = db.query(UserWorkspace).filter(
                UserWorkspace.user_id == user_id,
                UserWorkspace.workspace_id == workspace_id
            ).first()
            if not user_workspace:
                raise HTTPException(status_code=403, detail="Accès refusé à cette entreprise")
        workspace_ids = [workspace_id]
    else:
        # Si l'utilisateur est SUPER_ADMIN, récupérer toutes les entreprises
        if user_role == "SUPER_ADMIN":
            from app.models.workspace import Workspace
            all_workspaces = db.query(Workspace).all()
            workspace_ids = [c.id for c in all_workspaces]
        else:
            user_workspaces = db.query(UserWorkspace).filter(UserWorkspace.user_id == user_id).all()
            workspace_ids = [uc.workspace_id for uc in user_workspaces]
    
    # Employee time tracking summary (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    
    employee_hours = db.query(
        Employee.id,
        Employee.first_name,
        Employee.last_name,
        func.sum(TimeEntry.hours).label('total_hours')
    ).join(
        TimeEntry, Employee.id == TimeEntry.employee_id
    ).filter(
        Employee.workspace_id.in_(workspace_ids),
        TimeEntry.date >= thirty_days_ago.date()
    ).group_by(
        Employee.id, Employee.first_name, Employee.last_name
    ).all()
    
    # Time entries by day (last 30 days)
    daily_hours = db.query(
        TimeEntry.date,
        func.sum(TimeEntry.hours).label('total_hours')
    ).join(
        Employee, TimeEntry.employee_id == Employee.id
    ).filter(
        Employee.workspace_id.in_(workspace_ids),
        TimeEntry.date >= thirty_days_ago.date()
    ).group_by(TimeEntry.date).all()
    
    return {
        "employee_hours": [
            {
                "employee_id": emp_id,
                "employee_name": f"{first_name} {last_name}",
                "total_hours": float(total_hours) if total_hours else 0
            } for emp_id, first_name, last_name, total_hours in employee_hours
        ],
        "daily_hours": [
            {
                "date": date.strftime("%Y-%m-%d"),
                "total_hours": float(total_hours) if total_hours else 0
            } for date, total_hours in daily_hours
        ]
    }

@router.get("/financial-overview", response_model=Dict[str, Any])
def get_financial_overview(
    request: Request,
    db: Session = Depends(deps.get_db),
    workspace_id: int = None,
    user_data: Dict[str, Any] = Depends(require_dashboard_access),
) -> Any:
    """
    Get financial overview and analytics.
    """
    user_role = user_data.get("role", "").upper()
    user_id = user_data.get("id")

    if workspace_id:
        # Pour un workspace_id spécifique, vérifier l'accès si pas super admin
        if user_role != "SUPER_ADMIN":
            user_workspace = db.query(UserWorkspace).filter(
                UserWorkspace.user_id == user_id,
                UserWorkspace.workspace_id == workspace_id
            ).first()
            if not user_workspace:
                raise HTTPException(status_code=403, detail="Accès refusé à cette entreprise")
        workspace_ids = [workspace_id]
    else:
        # Si l'utilisateur est SUPER_ADMIN, récupérer toutes les entreprises
        if user_role == "SUPER_ADMIN":
            from app.models.workspace import Workspace
            all_workspaces = db.query(Workspace).all()
            workspace_ids = [c.id for c in all_workspaces]
        else:
            user_workspaces = db.query(UserWorkspace).filter(UserWorkspace.user_id == user_id).all()
            workspace_ids = [uc.workspace_id for uc in user_workspaces]
    
    # Invoice status summary
    invoice_summary = db.query(
        Invoice.status,
        func.count(Invoice.id).label('count'),
        func.sum(Invoice.total_amount_ttc).label('total_amount')
    ).filter(
        Invoice.workspace_id.in_(workspace_ids)
    ).group_by(Invoice.status).all()
    
    # Monthly revenue (last 12 months)
    twelve_months_ago = datetime.utcnow() - timedelta(days=365)
    monthly_revenue = db.query(
        func.date_trunc('month', Invoice.invoice_date).label('month'),
        func.sum(Invoice.total_amount_ttc).label('revenue')
    ).filter(
        Invoice.workspace_id.in_(workspace_ids),
        Invoice.status == 'paid',
        Invoice.invoice_date >= twelve_months_ago
    ).group_by(func.date_trunc('month', Invoice.invoice_date)).all()
    
    # Purchase order spending
    po_spending = db.query(
        func.sum(PurchaseOrder.total_amount_ttc).label('total_spending')
    ).filter(
        PurchaseOrder.workspace_id.in_(workspace_ids)
    ).scalar()
    
    # Budget utilization
    budget_utilization = db.query(
        func.sum(Budget.total_amount).label('total_budgeted')
    ).filter(
        Budget.workspace_id.in_(workspace_ids)
    ).scalar()
    
    return {
        "invoice_summary": [
            {
                "status": status,
                "count": count,
                "total_amount": float(total_amount) if total_amount else 0
            } for status, count, total_amount in invoice_summary
        ],
        "monthly_revenue": [
            {
                "month": month.strftime("%Y-%m"),
                "revenue": float(revenue) if revenue else 0
            } for month, revenue in monthly_revenue
        ],
        "spending_summary": {
            "total_purchase_orders": float(po_spending) if po_spending else 0,
            "total_budgeted": float(budget_utilization) if budget_utilization else 0
        }
    }
