{"accessibility_tree": [{"nodeId": "2", "ignored": false, "role": {"type": "internalRole", "value": "RootWebArea"}, "chromeRole": {"type": "internalRole", "value": 144}, "name": {"type": "computedString", "value": "503", "sources": [{"type": "relatedElement", "attribute": "aria-<PERSON>by"}, {"type": "attribute", "attribute": "aria-label"}, {"type": "attribute", "attribute": "aria-label", "superseded": true}, {"type": "relatedElement", "value": {"type": "computedString", "value": "503"}, "nativeSource": "title"}, {"type": "attribute", "attribute": "title", "superseded": true}]}, "properties": [{"name": "focusable", "value": {"type": "booleanOrUndefined", "value": true}}, {"name": "focused", "value": {"type": "booleanOrUndefined", "value": true}}], "childIds": ["6"], "backendDOMNodeId": 2, "frameId": "3DDE3C049943894CD77D45C50735D628"}, {"nodeId": "6", "ignored": true, "ignoredReasons": [{"name": "uninteresting", "value": {"type": "boolean", "value": true}}], "role": {"type": "role", "value": "none"}, "chromeRole": {"type": "internalRole", "value": 0}, "parentId": "2", "childIds": ["7", "8"], "backendDOMNodeId": 6}, {"nodeId": "7", "ignored": false, "role": {"type": "role", "value": "generic"}, "chromeRole": {"type": "internalRole", "value": 88}, "name": {"type": "computedString", "value": "", "sources": [{"type": "relatedElement", "attribute": "aria-<PERSON>by"}, {"type": "attribute", "attribute": "aria-label"}, {"type": "attribute", "attribute": "title"}]}, "properties": [], "parentId": "6", "childIds": ["5"], "backendDOMNodeId": 7}, {"nodeId": "8", "ignored": false, "role": {"type": "role", "value": "generic"}, "chromeRole": {"type": "internalRole", "value": 88}, "name": {"type": "computedString", "value": "", "sources": [{"type": "relatedElement", "attribute": "aria-<PERSON>by"}, {"type": "attribute", "attribute": "aria-label"}, {"type": "attribute", "attribute": "title"}]}, "properties": [], "parentId": "6", "childIds": ["9"], "backendDOMNodeId": 8}, {"nodeId": "5", "ignored": true, "ignoredReasons": [{"name": "uninteresting", "value": {"type": "boolean", "value": true}}], "role": {"type": "role", "value": "none"}, "chromeRole": {"type": "internalRole", "value": 0}, "parentId": "7", "childIds": ["10", "3", "11"], "backendDOMNodeId": 5}, {"nodeId": "10", "ignored": false, "role": {"type": "role", "value": "image"}, "chromeRole": {"type": "internalRole", "value": 99}, "name": {"type": "computedString", "value": "", "sources": [{"type": "relatedElement", "attribute": "aria-<PERSON>by"}, {"type": "attribute", "attribute": "aria-label"}, {"type": "relatedElement", "nativeSource": "title"}, {"type": "attribute", "attribute": "title"}]}, "properties": [], "parentId": "5", "childIds": ["23"], "backendDOMNodeId": 10}, {"nodeId": "3", "ignored": true, "ignoredReasons": [{"name": "uninteresting", "value": {"type": "boolean", "value": true}}], "role": {"type": "role", "value": "none"}, "chromeRole": {"type": "internalRole", "value": 0}, "parentId": "5", "childIds": ["42"], "backendDOMNodeId": 3}, {"nodeId": "42", "ignored": false, "role": {"type": "internalRole", "value": "StaticText"}, "chromeRole": {"type": "internalRole", "value": 158}, "name": {"type": "computedString", "value": "This page link has expired.", "sources": [{"type": "contents", "value": {"type": "computedString", "value": "This page link has expired."}}]}, "properties": [], "parentId": "3", "childIds": ["-1000000002"], "backendDOMNodeId": 42}, {"nodeId": "11", "ignored": true, "ignoredReasons": [{"name": "uninteresting", "value": {"type": "boolean", "value": true}}], "role": {"type": "role", "value": "none"}, "chromeRole": {"type": "internalRole", "value": 0}, "parentId": "5", "childIds": ["43"], "backendDOMNodeId": 11}, {"nodeId": "43", "ignored": false, "role": {"type": "role", "value": "button"}, "chromeRole": {"type": "internalRole", "value": 9}, "name": {"type": "computedString", "value": "Go to app world", "sources": [{"type": "relatedElement", "attribute": "aria-<PERSON>by"}, {"type": "attribute", "attribute": "aria-label"}, {"type": "relatedElement", "nativeSource": "label"}, {"type": "contents", "value": {"type": "computedString", "value": "Go to app world"}}, {"type": "attribute", "attribute": "title", "superseded": true}]}, "properties": [{"name": "invalid", "value": {"type": "token", "value": "false"}}, {"name": "focusable", "value": {"type": "booleanOrUndefined", "value": true}}], "parentId": "11", "childIds": ["45", "46"], "backendDOMNodeId": 43}, {"nodeId": "9", "ignored": false, "role": {"type": "role", "value": "image"}, "chromeRole": {"type": "internalRole", "value": 99}, "name": {"type": "computedString", "value": "", "sources": [{"type": "relatedElement", "attribute": "aria-<PERSON>by"}, {"type": "attribute", "attribute": "aria-label"}, {"type": "relatedElement", "nativeSource": "title"}, {"type": "attribute", "attribute": "title"}]}, "properties": [], "parentId": "8", "childIds": [], "backendDOMNodeId": 9}, {"nodeId": "23", "ignored": true, "ignoredReasons": [{"name": "uninteresting", "value": {"type": "boolean", "value": true}}], "role": {"type": "role", "value": "none"}, "chromeRole": {"type": "internalRole", "value": 0}, "parentId": "10", "childIds": [], "backendDOMNodeId": 23}, {"nodeId": "-1000000002", "ignored": false, "role": {"type": "internalRole", "value": "InlineTextBox"}, "chromeRole": {"type": "internalRole", "value": 101}, "properties": [], "parentId": "42", "childIds": [], "backendDOMNodeId": 42}, {"nodeId": "45", "ignored": false, "role": {"type": "role", "value": "image"}, "chromeRole": {"type": "internalRole", "value": 99}, "name": {"type": "computedString", "value": "", "sources": [{"type": "relatedElement", "attribute": "aria-<PERSON>by"}, {"type": "attribute", "attribute": "aria-label"}, {"type": "relatedElement", "nativeSource": "title"}, {"type": "attribute", "attribute": "title"}]}, "properties": [], "parentId": "43", "childIds": [], "backendDOMNodeId": 45}, {"nodeId": "46", "ignored": true, "ignoredReasons": [{"name": "uninteresting", "value": {"type": "boolean", "value": true}}], "role": {"type": "role", "value": "none"}, "chromeRole": {"type": "internalRole", "value": 0}, "parentId": "43", "childIds": ["48"], "backendDOMNodeId": 46}, {"nodeId": "48", "ignored": false, "role": {"type": "internalRole", "value": "StaticText"}, "chromeRole": {"type": "internalRole", "value": 158}, "name": {"type": "computedString", "value": "Go to app world", "sources": [{"type": "contents", "value": {"type": "computedString", "value": "Go to app world"}}]}, "properties": [], "parentId": "46", "childIds": ["-1000000003"], "backendDOMNodeId": 48}, {"nodeId": "-1000000003", "ignored": false, "role": {"type": "internalRole", "value": "InlineTextBox"}, "chromeRole": {"type": "internalRole", "value": 101}, "properties": [], "parentId": "48", "childIds": [], "backendDOMNodeId": 48}], "headless": true, "proxy": null, "is_empty_page": false, "reporter": {"block": "Browser", "uuid": "02c742df-5453-424a-a2fc-35451e74e1c3", "enable_llm_stream": false, "callback_url": ""}, "url": "https://orbis-frontend-83ebvd-u2nimd-ef5f63.mgx.dev/auth/register"}