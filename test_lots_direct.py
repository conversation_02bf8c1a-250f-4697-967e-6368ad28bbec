"""
Test direct de l'API des lots
"""

import asyncio
import aiohttp
import json

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

async def test_lots_direct():
    """Test direct de l'API des lots"""
    async with aiohttp.ClientSession() as session:
        # Authentification
        auth_data = {
            "email": "<EMAIL>",
            "password": "orbis123!"
        }
        
        async with session.post(
            f"{API_BASE}/auth/login",
            json=auth_data,
            headers={"Content-Type": "application/json"}
        ) as response:
            if response.status == 200:
                data = await response.json()
                token = data.get("access_token")
                print(f"✅ Authentification réussie")
            else:
                error_text = await response.text()
                print(f"❌ Échec authentification: {response.status} - {error_text}")
                return
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        # Test GET /lots
        print("\n🧪 Test GET /lots")
        async with session.get(f"{API_BASE}/lots/", headers=headers) as response:
            print(f"Status: {response.status}")
            if response.status == 200:
                lots = await response.json()
                print(f"✅ {len(lots)} lots récupérés")
                for lot in lots:
                    print(f"   - {lot['name']} ({lot['code']}) - Phase: {lot['current_phase']}")
            else:
                error_text = await response.text()
                print(f"❌ Erreur: {error_text}")
        
        # Test POST /lots (création)
        print("\n🧪 Test POST /lots")
        lot_data = {
            "name": "Test Lot Direct",
            "code": "TEST-DIRECT",
            "description": "Lot créé pour test direct",
            "project_id": 7  # ID du projet trouvé
        }
        
        async with session.post(f"{API_BASE}/lots/", json=lot_data, headers=headers) as response:
            print(f"Status: {response.status}")
            if response.status == 201:
                lot = await response.json()
                print(f"✅ Lot créé: {lot['name']} (ID: {lot['id']})")
            else:
                error_text = await response.text()
                print(f"❌ Erreur création: {error_text}")

if __name__ == "__main__":
    asyncio.run(test_lots_direct())
