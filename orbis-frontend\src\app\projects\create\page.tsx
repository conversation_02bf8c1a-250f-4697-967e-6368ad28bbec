'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { FastAuthService } from '@/lib/auth'
import { projectsAPI, ProjectCreate } from '@/lib/api/projects'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  Building2,
  Calendar,
  Users,
  Euro,
  FileText,
  MapPin,
  Clock,
  AlertCircle,
  CheckCircle,
  Save
} from 'lucide-react'

type DossierNature = 'Devis' | 'AO' | 'Affaire'

interface DossierFormData {
  name: string
  code: string
  nature: DossierNature
  client_name: string
  description: string
  location: string
  budget_total: number
  start_date: string
  end_date: string
  manager: string
  priority: 'low' | 'medium' | 'high'
  notes: string
}

function CreateDossierPageContent() {
  const { user, signOut } = useAuth()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const [formData, setFormData] = useState<DossierFormData>({
    name: 'Aménagement Restaurant Le Gourmet',
    code: '',
    nature: 'Affaire',
    client_name: 'Restaurant Le Gourmet SARL',
    description: 'Aménagement complet d\'un restaurant gastronomique de 150m² avec cuisine professionnelle, salle de restaurant et terrasse',
    location: 'Paris 8ème arrondissement',
    budget_total: 85000,
    start_date: '2024-02-01',
    end_date: '2024-06-30',
    manager: 'Jean Dupont',
    priority: 'high',
    notes: 'Projet urgent - Ouverture prévue pour la saison estivale. Contraintes acoustiques importantes.'
  })

  // Fonction de déconnexion
  const handleLogout = () => {
    signOut()
  }

  // Générer automatiquement le code en fonction de la nature
  const generateCode = (nature: DossierNature) => {
    const prefix = nature === 'Devis' ? 'DEV' : nature === 'AO' ? 'AO' : 'AFF'
    const year = new Date().getFullYear()
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `${prefix}-${year}-${random}`
  }

  // Mettre à jour un champ du formulaire
  const updateField = (field: keyof DossierFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value }

      // Générer automatiquement le code si la nature change
      if (field === 'nature') {
        newData.code = generateCode(value as DossierNature)
      }

      return newData
    })

    // Supprimer l'erreur si le champ est corrigé
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // Validation du formulaire
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) newErrors.name = 'Le nom est requis'
    if (!formData.client_name.trim()) newErrors.client_name = 'Le client est requis'
    if (!formData.description.trim()) newErrors.description = 'La description est requise'
    if (!formData.start_date) newErrors.start_date = 'La date de début est requise'
    if (!formData.end_date) newErrors.end_date = 'La date de fin est requise'
    if (formData.budget_total <= 0) newErrors.budget_total = 'Le budget doit être supérieur à 0'

    // Vérifier que la date de fin est après la date de début
    if (formData.start_date && formData.end_date && formData.end_date <= formData.start_date) {
      newErrors.end_date = 'La date de fin doit être après la date de début'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Soumettre le formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      // Préparer les données pour l'API
      const projectData: ProjectCreate = {
        name: formData.name,
        code: formData.code,
        description: formData.description,
        nature: formData.nature.toUpperCase() as 'DEVIS' | 'AO' | 'AFFAIRE', // Convertir en majuscules
        client_name: formData.client_name,
        address: formData.location,
        budget_total: formData.budget_total,
        start_date: formData.start_date || undefined,
        end_date: formData.end_date || undefined,
        status: 'EN_COURS', // Statut par défaut en format backend
        entreprise_tiers_id: null // Champ requis par le backend, null par défaut
      }

      console.log('Envoi des données:', projectData)

      // Utiliser la nouvelle API
      const createdProject = await projectsAPI.createProject(projectData)
      console.log('Dossier créé avec succès:', createdProject)

      // Rediriger vers la liste des dossiers
      router.push('/projects')
    } catch (error) {
      console.error('Erreur lors de la création du dossier:', error)

      // Gérer les erreurs de validation du backend (422)
      if (error && typeof error === 'object' && 'response' in error) {
        try {
          const errorData = await error.response.json()
          console.error('Détails de l\'erreur:', errorData)

          if (errorData.detail) {
            if (Array.isArray(errorData.detail)) {
              // Erreurs de validation Pydantic
              const validationErrors = errorData.detail.map((err: any) =>
                `${err.loc?.join('.')} : ${err.msg}`
              ).join(', ')
              setErrors({ submit: `Erreurs de validation: ${validationErrors}` })
            } else {
              setErrors({ submit: errorData.detail })
            }
          } else {
            setErrors({ submit: JSON.stringify(errorData) })
          }
        } catch (parseError) {
          setErrors({ submit: `Erreur ${error.status || 'inconnue'}: ${error.statusText || 'Erreur lors de la création'}` })
        }
      } else {
        setErrors({ submit: error instanceof Error ? error.message : 'Erreur lors de la création du dossier' })
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Initialiser le code au premier rendu
  if (!formData.code) {
    setFormData(prev => ({ ...prev, code: generateCode(prev.nature) }))
  }

  // Fonction pour obtenir les couleurs et icônes des natures
  const getNatureInfo = (nature: DossierNature) => {
    switch (nature) {
      case 'Devis':
        return { color: 'purple', icon: '📋', description: 'Estimation de coûts pour un projet' }
      case 'AO':
        return { color: 'orange', icon: '📢', description: 'Réponse à un appel d\'offres' }
      case 'Affaire':
        return { color: 'teal', icon: '🤝', description: 'Projet confirmé en cours de réalisation' }
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        <ModernSidebar user={user ? {
          name: `${user.first_name} ${user.last_name}`,
          email: user.email
        } : undefined} />

        <div className="flex-1 lg:ml-0">
          <ModernHeader
            title="Nouveau Dossier"
            subtitle="Créez un nouveau dossier (Devis, AO ou Affaire)"
            user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined}
            onLogout={handleLogout}
          />

          <main className="p-6">
            <div className="max-w-4xl mx-auto space-y-6">
              {/* Header avec bouton retour */}
              <div className="flex items-center justify-between">
                <Link href="/projects">
                  <button className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Retour aux dossiers
                  </button>
                </Link>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Sélection de la nature */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Type de dossier</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {(['Devis', 'AO', 'Affaire'] as DossierNature[]).map((nature) => {
                      const info = getNatureInfo(nature)
                      const isSelected = formData.nature === nature
                      return (
                        <button
                          key={nature}
                          type="button"
                          onClick={() => updateField('nature', nature)}
                          className={`p-4 rounded-xl border-2 transition-all ${
                            isSelected
                              ? 'border-primary-500 bg-primary-50 shadow-lg'
                              : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-md'
                          }`}
                        >
                          <div className="text-center">
                            <div className="text-3xl mb-2">{info.icon}</div>
                            <div className={`font-semibold ${isSelected ? 'text-primary-700' : 'text-gray-900'}`}>
                              {nature}
                            </div>
                            <div className={`text-sm mt-1 ${isSelected ? 'text-primary-600' : 'text-gray-600'}`}>
                              {info.description}
                            </div>
                          </div>
                        </button>
                      )
                    })}
                  </div>
                </div>

                {/* Informations générales */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Informations générales</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nom du dossier *
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => updateField('name', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.name ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Ex: Rénovation bureau central"
                      />
                      {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Code du dossier
                      </label>
                      <input
                        type="text"
                        value={formData.code}
                        onChange={(e) => updateField('code', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Généré automatiquement"
                      />
                      <p className="text-gray-500 text-sm mt-1">Code généré automatiquement selon la nature</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Client *
                      </label>
                      <input
                        type="text"
                        value={formData.client_name}
                        onChange={(e) => updateField('client_name', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.client_name ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Ex: Entreprise ABC"
                      />
                      {errors.client_name && <p className="text-red-500 text-sm mt-1">{errors.client_name}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Localisation
                      </label>
                      <input
                        type="text"
                        value={formData.location}
                        onChange={(e) => updateField('location', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Ex: Paris 15ème"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Description *
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => updateField('description', e.target.value)}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.description ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Décrivez le projet en détail..."
                      />
                      {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
                    </div>
                  </div>
                </div>

                {/* Budget et dates */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Budget et planning</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Budget total (€) *
                      </label>
                      <input
                        type="number"
                        value={formData.budget_total}
                        onChange={(e) => updateField('budget_total', parseFloat(e.target.value) || 0)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.budget_total ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="150000"
                        min="0"
                        step="1000"
                      />
                      {errors.budget_total && <p className="text-red-500 text-sm mt-1">{errors.budget_total}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Date de début *
                      </label>
                      <input
                        type="date"
                        value={formData.start_date}
                        onChange={(e) => updateField('start_date', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.start_date ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                      {errors.start_date && <p className="text-red-500 text-sm mt-1">{errors.start_date}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Date de fin *
                      </label>
                      <input
                        type="date"
                        value={formData.end_date}
                        onChange={(e) => updateField('end_date', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                          errors.end_date ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                      {errors.end_date && <p className="text-red-500 text-sm mt-1">{errors.end_date}</p>}
                    </div>
                  </div>
                </div>

                {/* Gestion et priorité */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Gestion du projet</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Responsable
                      </label>
                      <input
                        type="text"
                        value={formData.manager}
                        onChange={(e) => updateField('manager', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Nom du chef de projet"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Priorité
                      </label>
                      <select
                        value={formData.priority}
                        onChange={(e) => updateField('priority', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value="low">🟢 Basse</option>
                        <option value="medium">🟡 Moyenne</option>
                        <option value="high">🔴 Haute</option>
                      </select>
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Notes additionnelles
                      </label>
                      <textarea
                        value={formData.notes}
                        onChange={(e) => updateField('notes', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Informations complémentaires, contraintes particulières..."
                      />
                    </div>
                  </div>
                </div>

                {/* Erreur de soumission */}
                {errors.submit && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div className="flex items-center">
                      <AlertCircle className="w-5 h-5 text-red-600 mr-3" />
                      <div>
                        <div className="text-red-800 font-medium">Erreur lors de la création</div>
                        <div className="text-red-700 text-sm">{errors.submit}</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Boutons d'action */}
                <div className="flex items-center justify-between pt-6">
                  <Link href="/projects">
                    <button
                      type="button"
                      className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Annuler
                    </button>
                  </Link>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex items-center px-6 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white rounded-lg transition-colors"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Création en cours...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Créer le dossier
                      </>
                    )}
                  </button>
                </div>
              </form>

              {/* Section d'aide */}
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">💡 Conseils pour créer un dossier</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-blue-800">
                  <div>
                    <h4 className="font-medium mb-2">📋 Devis</h4>
                    <ul className="text-sm space-y-1">
                      <li>• Estimation précise des coûts</li>
                      <li>• Délais de validité clairs</li>
                      <li>• Détail des prestations</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">📢 Appel d'Offres</h4>
                    <ul className="text-sm space-y-1">
                      <li>• Respect du cahier des charges</li>
                      <li>• Proposition technique détaillée</li>
                      <li>• Planning de réalisation</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">🤝 Affaire</h4>
                    <ul className="text-sm space-y-1">
                      <li>• Suivi régulier de l'avancement</li>
                      <li>• Gestion des ressources</li>
                      <li>• Communication client</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}

export default function CreateDossier() {
  return (
    <ProtectedRoute>
      <CreateDossierPageContent />
    </ProtectedRoute>
  )
}