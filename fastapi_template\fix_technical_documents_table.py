#!/usr/bin/env python3
"""
Script pour corriger directement la table technical_documents
en supprimant project_id et en rendant lot_id obligatoire
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(__file__))

from sqlalchemy import text
from app.core.database import get_db

async def fix_technical_documents_table():
    """Corrige la structure de la table technical_documents"""
    print("🔄 Correction de la table technical_documents...")
    
    async for db in get_db():
        try:
            # 1. Vérifier l'état actuel de la table
            print("\n1. Vérification de l'état actuel...")
            result = await db.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'technical_documents' 
                ORDER BY ordinal_position
            """))
            columns = result.fetchall()
            
            print("Colonnes actuelles:")
            has_project_id = False
            has_lot_id = False
            lot_id_nullable = True
            
            for col in columns:
                print(f"  - {col[0]}: {col[1]} (nullable: {col[2]})")
                if col[0] == 'project_id':
                    has_project_id = True
                elif col[0] == 'lot_id':
                    has_lot_id = True
                    lot_id_nullable = col[2] == 'YES'
            
            # 2. Migrer les données si nécessaire
            if has_project_id and has_lot_id:
                print("\n2. Migration des données...")
                
                # Vérifier s'il y a des documents sans lot_id
                result = await db.execute(text("""
                    SELECT COUNT(*) FROM technical_documents WHERE lot_id IS NULL
                """))
                null_lot_count = result.scalar()
                
                if null_lot_count > 0:
                    print(f"⚠️  {null_lot_count} documents sans lot_id trouvés")
                    
                    # Attribuer un lot basé sur le project_id
                    await db.execute(text("""
                        UPDATE technical_documents 
                        SET lot_id = (
                            SELECT l.id 
                            FROM lots l 
                            WHERE l.project_id = technical_documents.project_id 
                            LIMIT 1
                        )
                        WHERE lot_id IS NULL
                        AND project_id IS NOT NULL
                    """))
                    
                    # Vérifier s'il reste des documents sans lot_id
                    result = await db.execute(text("""
                        SELECT COUNT(*) FROM technical_documents WHERE lot_id IS NULL
                    """))
                    remaining_null = result.scalar()
                    
                    if remaining_null > 0:
                        print(f"❌ {remaining_null} documents n'ont pas pu être migrés")
                        # Supprimer les documents orphelins
                        await db.execute(text("""
                            DELETE FROM technical_documents WHERE lot_id IS NULL
                        """))
                        print("🗑️  Documents orphelins supprimés")
                    
                    print("✅ Migration des données terminée")
            
            # 3. Rendre lot_id NOT NULL si nécessaire
            if has_lot_id and lot_id_nullable:
                print("\n3. Modification de lot_id en NOT NULL...")
                await db.execute(text("""
                    ALTER TABLE technical_documents 
                    ALTER COLUMN lot_id SET NOT NULL
                """))
                print("✅ lot_id est maintenant NOT NULL")
            
            # 4. Ajouter la contrainte de clé étrangère pour lot_id si elle n'existe pas
            print("\n4. Vérification de la contrainte FK pour lot_id...")
            result = await db.execute(text("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_name = 'technical_documents' 
                AND constraint_type = 'FOREIGN KEY'
                AND constraint_name LIKE '%lot_id%'
            """))
            fk_exists = result.fetchone() is not None
            
            if not fk_exists:
                print("Ajout de la contrainte FK pour lot_id...")
                await db.execute(text("""
                    ALTER TABLE technical_documents 
                    ADD CONSTRAINT fk_technical_documents_lot_id 
                    FOREIGN KEY (lot_id) REFERENCES lots(id)
                """))
                print("✅ Contrainte FK ajoutée")
            else:
                print("✅ Contrainte FK déjà présente")
            
            # 5. Supprimer project_id si elle existe
            if has_project_id:
                print("\n5. Suppression de la colonne project_id...")
                
                # Supprimer d'abord la contrainte FK
                try:
                    await db.execute(text("""
                        ALTER TABLE technical_documents 
                        DROP CONSTRAINT technical_documents_project_id_fkey
                    """))
                    print("✅ Contrainte FK project_id supprimée")
                except Exception as e:
                    print(f"⚠️  Contrainte FK project_id déjà supprimée ou erreur: {e}")
                
                # Supprimer la colonne
                await db.execute(text("""
                    ALTER TABLE technical_documents 
                    DROP COLUMN project_id
                """))
                print("✅ Colonne project_id supprimée")
            
            # 6. Vérifier le résultat final
            print("\n6. Vérification finale...")
            result = await db.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'technical_documents' 
                ORDER BY ordinal_position
            """))
            columns = result.fetchall()
            
            print("Structure finale:")
            for col in columns:
                print(f"  - {col[0]}: {col[1]} (nullable: {col[2]})")
            
            await db.commit()
            print("\n✅ Correction de la table terminée avec succès!")
            return True
            
        except Exception as e:
            print(f"❌ Erreur: {str(e)}")
            import traceback
            traceback.print_exc()
            await db.rollback()
            return False

async def main():
    """Fonction principale"""
    print("🚀 Correction de la table technical_documents")
    print("=" * 50)
    
    success = await fix_technical_documents_table()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 CORRECTION RÉUSSIE!")
        print("✅ La table technical_documents utilise maintenant lot_id au lieu de project_id")
        print("✅ L'erreur 'project_id is an invalid keyword argument' est corrigée")
    else:
        print("❌ CORRECTION ÉCHOUÉE")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
