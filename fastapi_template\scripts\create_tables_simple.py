#!/usr/bin/env python3
"""
Script simple pour créer les tables des documents techniques
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from app.core.database import engine

async def create_tables():
    """Créer les tables pour les documents techniques"""
    
    print("🔄 Création des tables pour les documents techniques...")
    
    # SQL pour créer l'enum DocumentType
    create_enum_sql = """
    DO $$ BEGIN
        CREATE TYPE documenttype AS ENUM ('CCTP', 'DPGF');
    EXCEPTION
        WHEN duplicate_object THEN 
            RAISE NOTICE 'Type documenttype existe déjà';
    END $$;
    """
    
    # SQL pour créer la table technical_documents
    create_technical_documents_sql = """
    CREATE TABLE IF NOT EXISTS technical_documents (
        id SERIAL PRIMARY KEY,
        name VARCHAR NOT NULL,
        type_document documenttype NOT NULL,
        content TEXT,
        project_id INTEGER NOT NULL,
        created_by INTEGER NOT NULL,
        updated_by INTEGER,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    # SQL pour créer la table de relation technical_document_companies
    create_relation_table_sql = """
    CREATE TABLE IF NOT EXISTS technical_document_companies (
        id SERIAL PRIMARY KEY,
        technical_document_id INTEGER NOT NULL,
        company_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(technical_document_id, company_id)
    );
    """
    
    # SQL pour créer les index
    create_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_technical_documents_name ON technical_documents(name);",
        "CREATE INDEX IF NOT EXISTS idx_technical_documents_type ON technical_documents(type_document);",
        "CREATE INDEX IF NOT EXISTS idx_technical_documents_project ON technical_documents(project_id);",
        "CREATE INDEX IF NOT EXISTS idx_technical_documents_created_by ON technical_documents(created_by);",
        "CREATE INDEX IF NOT EXISTS idx_technical_documents_active ON technical_documents(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_technical_document_companies_doc ON technical_document_companies(technical_document_id);",
        "CREATE INDEX IF NOT EXISTS idx_technical_document_companies_company ON technical_document_companies(company_id);"
    ]
    
    try:
        async with engine.begin() as conn:
            print("  ✅ Connexion à la base de données établie")
            
            print("  🔄 Création de l'enum DocumentType...")
            await conn.execute(text(create_enum_sql))
            
            print("  🔄 Création de la table technical_documents...")
            await conn.execute(text(create_technical_documents_sql))
            
            print("  🔄 Création de la table technical_document_companies...")
            await conn.execute(text(create_relation_table_sql))
            
            print("  🔄 Création des index...")
            for i, index_sql in enumerate(create_indexes_sql):
                await conn.execute(text(index_sql))
                print(f"    ✅ Index {i+1}/{len(create_indexes_sql)} créé")
            
            print("✅ Tables créées avec succès!")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création des tables: {e}")
        raise

async def verify_tables():
    """Vérifier que les tables ont été créées"""
    
    print("\n🔍 Vérification des tables...")
    
    verification_queries = [
        ("Table technical_documents", "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'technical_documents'"),
        ("Table technical_document_companies", "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'technical_document_companies'"),
        ("Enum documenttype", "SELECT COUNT(*) FROM information_schema.types WHERE typname = 'documenttype'"),
    ]
    
    try:
        async with engine.begin() as conn:
            for name, query in verification_queries:
                result = await conn.execute(text(query))
                count = result.scalar()
                status = "✅" if count > 0 else "❌"
                print(f"  {status} {name}: {'OK' if count > 0 else 'MANQUANT'}")
            
            print("✅ Vérification terminée!")
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

async def main():
    """Fonction principale"""
    print("🚀 Création des tables pour les documents techniques")
    print("=" * 50)
    
    try:
        await create_tables()
        success = await verify_tables()
        
        if success:
            print("\n🎉 Tables créées avec succès!")
            print("\nProchaines étapes:")
            print("1. Redémarrer le serveur FastAPI")
            print("2. Tester les endpoints: http://localhost:8000/docs")
            print("3. Configurer l'utilisateur test: python scripts/setup_test_user.py")
        else:
            print("\n⚠️  Création incomplète")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Échec de la création: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
