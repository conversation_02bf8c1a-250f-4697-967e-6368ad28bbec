#!/usr/bin/env python3
"""
Script pour tester l'API en contournant temporairement l'authentification
"""

import asyncio
import httpx

async def test_api_endpoints():
    """Tester les endpoints API"""
    print("🔍 Test des endpoints API...")
    
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        try:
            # Test 1: Endpoint de santé (public)
            print("\n1. Test endpoint de santé...")
            response = await client.get(f"{base_url}/health")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print(f"   Response: {response.json()}")
            
            # Test 2: Endpoint root (public)
            print("\n2. Test endpoint root...")
            response = await client.get(f"{base_url}/")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print(f"   Response: {response.json()}")
            
            # Test 3: Endpoint admin companies (protégé)
            print("\n3. Test endpoint admin companies (sans auth)...")
            response = await client.get(f"{base_url}/api/v1/admin/companies/")
            print(f"   Status: {response.status_code}")
            if response.status_code != 200:
                print(f"   Error: {response.text}")
            
            # Test 4: Avec un faux token
            print("\n4. Test endpoint admin companies (avec faux token)...")
            headers = {"Authorization": "Bearer fake-token"}
            response = await client.get(f"{base_url}/api/v1/admin/companies/", headers=headers)
            print(f"   Status: {response.status_code}")
            if response.status_code != 200:
                print(f"   Error: {response.text}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du test: {e}")
            return False

async def main():
    """Fonction principale"""
    print("🚀 Test des endpoints API ORBIS")
    print("="*50)
    
    success = await test_api_endpoints()
    
    if success:
        print("\n✅ Tests terminés")
        print("\n💡 Si l'endpoint admin retourne 401/403, c'est normal (authentification requise)")
        print("💡 Si l'endpoint admin retourne 500, il y a un problème de code")
    else:
        print("\n❌ Erreur lors des tests")

if __name__ == "__main__":
    asyncio.run(main())
