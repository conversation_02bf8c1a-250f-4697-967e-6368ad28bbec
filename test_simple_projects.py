#!/usr/bin/env python3
"""
Test simple du CRUD des projets
"""

import requests

API_BASE = "http://localhost:8000"

def test_simple():
    print("🧪 Test simple du CRUD des projets")
    
    # Connexion
    login_response = requests.post(f"{API_BASE}/api/v1/fast-auth/login", json={
        "email": "<EMAIL>",
        "password": "orbis123!"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Erreur de connexion: {login_response.text}")
        return False
    
    token = login_response.json()['access_token']
    print("✅ Connexion réussie")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test GET projects
    print("\n📋 Test GET /projects/")
    projects_response = requests.get(f"{API_BASE}/api/v1/projects/", headers=headers)
    
    print(f"Status: {projects_response.status_code}")
    if projects_response.status_code == 200:
        projects = projects_response.json()
        print(f"✅ {len(projects)} projets récupérés")
        for project in projects:
            print(f"   • {project['name']} ({project['code']})")
        return True
    else:
        print(f"❌ Erreur: {projects_response.text}")
        return False

if __name__ == "__main__":
    test_simple()
