"""Create lots tables

Revision ID: 004
Revises: 003
Create Date: 2025-01-07 13:28:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade():
    # Créer l'enum pour les phases de lot
    lot_phase_enum = postgresql.ENUM('ESQ', 'APD', 'PRODCE', 'EXE', name='lotphase')
    lot_phase_enum.create(op.get_bind())
    
    # Créer la table lots
    op.create_table('lots',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('code', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('current_phase', sa.Enum('ESQ', 'APD', 'PRODCE', 'EXE', name='lotphase'), nullable=True),
        
        # Historique des phases validées - ESQ
        sa.Column('esq_validated', sa.Boolean(), nullable=True, default=False),
        sa.Column('esq_validated_at', sa.DateTime(), nullable=True),
        sa.Column('esq_validated_by', sa.Integer(), nullable=True),
        
        # Historique des phases validées - APD
        sa.Column('apd_validated', sa.Boolean(), nullable=True, default=False),
        sa.Column('apd_validated_at', sa.DateTime(), nullable=True),
        sa.Column('apd_validated_by', sa.Integer(), nullable=True),
        
        # Historique des phases validées - PRODCE
        sa.Column('prodce_validated', sa.Boolean(), nullable=True, default=False),
        sa.Column('prodce_validated_at', sa.DateTime(), nullable=True),
        sa.Column('prodce_validated_by', sa.Integer(), nullable=True),
        
        # Historique des phases validées - EXE
        sa.Column('exe_validated', sa.Boolean(), nullable=True, default=False),
        sa.Column('exe_validated_at', sa.DateTime(), nullable=True),
        sa.Column('exe_validated_by', sa.Integer(), nullable=True),
        
        # Métadonnées
        sa.Column('workspace_id', sa.Integer(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=True),
        
        # Contraintes
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.ForeignKeyConstraint(['workspace_id'], ['workspaces.id'], ),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['esq_validated_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['apd_validated_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['prodce_validated_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['exe_validated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Créer les index pour la table lots
    op.create_index(op.f('ix_lots_id'), 'lots', ['id'], unique=False)
    op.create_index(op.f('ix_lots_name'), 'lots', ['name'], unique=False)
    op.create_index(op.f('ix_lots_code'), 'lots', ['code'], unique=False)
    op.create_index('ix_lots_project_id', 'lots', ['project_id'], unique=False)
    op.create_index('ix_lots_workspace_id', 'lots', ['workspace_id'], unique=False)
    op.create_index('ix_lots_current_phase', 'lots', ['current_phase'], unique=False)
    
    # Créer la table lot_intervenants
    op.create_table('lot_intervenants',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('lot_id', sa.Integer(), nullable=False),
        sa.Column('company_id', sa.Integer(), nullable=False),
        sa.Column('role', sa.String(length=100), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=True),
        
        # Contraintes
        sa.ForeignKeyConstraint(['lot_id'], ['lots.id'], ),
        sa.ForeignKeyConstraint(['company_id'], ['tcompanies.id'], ),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Créer les index pour la table lot_intervenants
    op.create_index(op.f('ix_lot_intervenants_id'), 'lot_intervenants', ['id'], unique=False)
    op.create_index('ix_lot_intervenants_lot_id', 'lot_intervenants', ['lot_id'], unique=False)
    op.create_index('ix_lot_intervenants_company_id', 'lot_intervenants', ['company_id'], unique=False)
    
    # Créer la table lot_documents
    op.create_table('lot_documents',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('lot_id', sa.Integer(), nullable=False),
        sa.Column('document_id', sa.Integer(), nullable=False),
        sa.Column('phase', sa.Enum('ESQ', 'APD', 'PRODCE', 'EXE', name='lotphase'), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=True),
        
        # Contraintes
        sa.ForeignKeyConstraint(['lot_id'], ['lots.id'], ),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Créer les index pour la table lot_documents
    op.create_index(op.f('ix_lot_documents_id'), 'lot_documents', ['id'], unique=False)
    op.create_index('ix_lot_documents_lot_id', 'lot_documents', ['lot_id'], unique=False)
    op.create_index('ix_lot_documents_document_id', 'lot_documents', ['document_id'], unique=False)
    op.create_index('ix_lot_documents_phase', 'lot_documents', ['phase'], unique=False)
    
    # Modifier la table technical_documents pour remplacer project_id par lot_id
    # D'abord, ajouter la nouvelle colonne lot_id
    op.add_column('technical_documents', sa.Column('lot_id', sa.Integer(), nullable=True))
    op.create_foreign_key('fk_technical_documents_lot_id', 'technical_documents', 'lots', ['lot_id'], ['id'])
    
    # Migration des données existantes
    # Créer un lot par défaut pour chaque projet existant
    connection = op.get_bind()
    
    # Récupérer tous les projets existants
    projects_result = connection.execute(sa.text("""
        SELECT id, name, code, workspace_id, created_at, updated_at 
        FROM projects 
        WHERE is_archived = false OR is_archived IS NULL
    """))
    
    projects = projects_result.fetchall()
    
    # Créer un lot par défaut pour chaque projet
    for project in projects:
        project_id, project_name, project_code, workspace_id, created_at, updated_at = project
        
        # Générer un code pour le lot
        lot_code = f"{project_code}-L01" if project_code else f"LOT-{project_id}-01"
        lot_name = f"Lot principal - {project_name}"
        
        # Insérer le lot par défaut
        connection.execute(sa.text("""
            INSERT INTO lots (name, code, description, project_id, current_phase, workspace_id, 
                            is_active, created_at, updated_at, esq_validated, apd_validated, 
                            prodce_validated, exe_validated)
            VALUES (:name, :code, :description, :project_id, 'ESQ', :workspace_id, 
                    true, :created_at, :updated_at, false, false, false, false)
        """), {
            'name': lot_name,
            'code': lot_code,
            'description': f'Lot principal créé automatiquement pour le projet {project_name}',
            'project_id': project_id,
            'workspace_id': workspace_id,
            'created_at': created_at,
            'updated_at': updated_at
        })
    
    # Migrer les documents techniques existants vers les lots
    # Pour chaque document technique, l'associer au lot principal du projet
    connection.execute(sa.text("""
        UPDATE technical_documents 
        SET lot_id = (
            SELECT l.id 
            FROM lots l 
            WHERE l.project_id = technical_documents.project_id 
            LIMIT 1
        )
        WHERE project_id IS NOT NULL
    """))
    
    # Maintenant, rendre lot_id obligatoire et supprimer project_id
    op.alter_column('technical_documents', 'lot_id', nullable=False)
    op.drop_constraint('technical_documents_project_id_fkey', 'technical_documents', type_='foreignkey')
    op.drop_column('technical_documents', 'project_id')


def downgrade():
    # Rétablir project_id dans technical_documents
    op.add_column('technical_documents', sa.Column('project_id', sa.Integer(), nullable=True))
    
    # Migrer les données de lot_id vers project_id
    connection = op.get_bind()
    connection.execute(sa.text("""
        UPDATE technical_documents 
        SET project_id = (
            SELECT l.project_id 
            FROM lots l 
            WHERE l.id = technical_documents.lot_id
        )
        WHERE lot_id IS NOT NULL
    """))
    
    # Rendre project_id obligatoire et supprimer lot_id
    op.alter_column('technical_documents', 'project_id', nullable=False)
    op.create_foreign_key('technical_documents_project_id_fkey', 'technical_documents', 'projects', ['project_id'], ['id'])
    op.drop_constraint('fk_technical_documents_lot_id', 'technical_documents', type_='foreignkey')
    op.drop_column('technical_documents', 'lot_id')
    
    # Supprimer les tables dans l'ordre inverse
    op.drop_index('ix_lot_documents_phase', table_name='lot_documents')
    op.drop_index('ix_lot_documents_document_id', table_name='lot_documents')
    op.drop_index('ix_lot_documents_lot_id', table_name='lot_documents')
    op.drop_index(op.f('ix_lot_documents_id'), table_name='lot_documents')
    op.drop_table('lot_documents')
    
    op.drop_index('ix_lot_intervenants_company_id', table_name='lot_intervenants')
    op.drop_index('ix_lot_intervenants_lot_id', table_name='lot_intervenants')
    op.drop_index(op.f('ix_lot_intervenants_id'), table_name='lot_intervenants')
    op.drop_table('lot_intervenants')
    
    op.drop_index('ix_lots_current_phase', table_name='lots')
    op.drop_index('ix_lots_workspace_id', table_name='lots')
    op.drop_index('ix_lots_project_id', table_name='lots')
    op.drop_index(op.f('ix_lots_code'), table_name='lots')
    op.drop_index(op.f('ix_lots_name'), table_name='lots')
    op.drop_index(op.f('ix_lots_id'), table_name='lots')
    op.drop_table('lots')
    
    # Supprimer l'enum
    lot_phase_enum = postgresql.ENUM('ESQ', 'APD', 'PRODCE', 'EXE', name='lotphase')
    lot_phase_enum.drop(op.get_bind())
