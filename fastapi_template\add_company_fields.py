#!/usr/bin/env python3
"""
Script pour ajouter les colonnes description et website à la table companies
"""

import asyncio
import asyncpg
from app.core.config import settings

async def add_company_fields():
    """Ajouter les colonnes description et website à la table companies"""
    print("🔧 Ajout des colonnes description et website à la table companies...")
    
    conn = await asyncpg.connect(settings.DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier si la colonne description existe déjà
        description_exists = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_name = 'companies' AND column_name = 'description'
        """)
        
        if description_exists == 0:
            await conn.execute("""
                ALTER TABLE companies 
                ADD COLUMN description TEXT
            """)
            print("   ✅ Colonne 'description' ajoutée")
        else:
            print("   ⚠️ Colonne 'description' existe déjà")
        
        # Vérifier si la colonne website existe déjà
        website_exists = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_name = 'companies' AND column_name = 'website'
        """)
        
        if website_exists == 0:
            await conn.execute("""
                ALTER TABLE companies 
                ADD COLUMN website VARCHAR(255)
            """)
            print("   ✅ Colonne 'website' ajoutée")
        else:
            print("   ⚠️ Colonne 'website' existe déjà")
        
        # Mettre à jour les entreprises existantes avec des descriptions
        print("   📝 Mise à jour des descriptions des entreprises existantes...")
        
        # Descriptions pour les entreprises de test
        company_descriptions = {
            'TECH001': 'Société spécialisée dans le développement de solutions logicielles innovantes et la transformation digitale des entreprises.',
            'BUILD002': 'Entreprise de construction et rénovation offrant des services complets pour tous types de projets immobiliers.',
            'GREEN003': 'Spécialiste des solutions énergétiques durables et des technologies vertes pour un avenir plus écologique.',
            'ORBIS001': 'Plateforme SaaS de gestion de projets de construction et de suivi des travaux.'
        }
        
        company_websites = {
            'TECH001': 'https://techcorp-solutions.fr',
            'BUILD002': 'https://buildpro-construction.fr',
            'GREEN003': 'https://greenenergy-sarl.fr',
            'ORBIS001': 'https://orbis-construction.fr'
        }
        
        for code, description in company_descriptions.items():
            website = company_websites.get(code, '')
            
            result = await conn.execute("""
                UPDATE companies 
                SET description = $1, website = $2
                WHERE code = $3 AND (description IS NULL OR description = '')
            """, description, website, code)
            
            if result == 'UPDATE 1':
                print(f"   ✅ Entreprise {code} mise à jour")
        
        # Vérifier le résultat
        print("\n   📊 Vérification des colonnes ajoutées...")
        companies = await conn.fetch("""
            SELECT code, name, description, website 
            FROM companies 
            ORDER BY name
        """)
        
        for company in companies:
            desc_preview = (company['description'][:50] + '...') if company['description'] and len(company['description']) > 50 else company['description']
            print(f"   🏢 {company['name']} ({company['code']})")
            print(f"      📝 Description: {desc_preview or 'Non définie'}")
            print(f"      🌐 Website: {company['website'] or 'Non défini'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await conn.close()

async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Ajout des champs description et website")
    print("="*60)
    
    if await add_company_fields():
        print("\n✅ Colonnes ajoutées avec succès!")
        print("\n🎯 Prochaines étapes:")
        print("   1. Mettre à jour le modèle Company dans app/models/company.py")
        print("   2. Mettre à jour les schémas Pydantic")
        print("   3. Corriger l'API admin pour utiliser les vraies valeurs")
        print("   4. Tester l'interface admin")
        
        return True
    else:
        print("\n❌ Échec de l'ajout des colonnes")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
