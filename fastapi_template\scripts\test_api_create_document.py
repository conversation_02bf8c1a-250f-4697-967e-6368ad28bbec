#!/usr/bin/env python3
"""
Script pour tester la création d'un document technique via l'API
"""

import asyncio
import aiohttp
import json

async def test_create_technical_document():
    """Tester la création d'un document technique via l'API"""
    
    print("🔄 Test de création de document technique via API...")
    
    # D'abord, se connecter pour obtenir un token
    login_data = {
        "email": "<EMAIL>",
        "password": "orbis123!"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            # Login
            print("🔐 Connexion...")
            async with session.post(
                "http://localhost:8000/api/v1/auth/login",
                json=login_data
            ) as response:
                if response.status != 200:
                    print(f"❌ Erreur de connexion: {response.status}")
                    text = await response.text()
                    print(f"Response: {text}")
                    return
                
                auth_data = await response.json()
                token = auth_data.get("access_token")
                print(f"✅ Connexion réussie, token: {token[:20]}...")
            
            # Créer un document technique
            document_data = {
                "name": "CCTP - Test Maçonnerie",
                "type_document": "CCTP",
                "content": "<h1>Cahier des Clauses Techniques Particulières</h1><p>Test de création...</p>",
                "project_id": 7,  # ID du projet de test
                "company_ids": [1]  # ID de l'entreprise tierce de test
            }
            
            print("📝 Création du document technique...")
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            async with session.post(
                "http://localhost:8000/api/v1/technical-documents",
                json=document_data,
                headers=headers
            ) as response:
                print(f"📊 Status: {response.status}")
                
                if response.status == 200 or response.status == 201:
                    result = await response.json()
                    print(f"✅ Document créé avec succès!")
                    print(f"   - ID: {result.get('id')}")
                    print(f"   - Nom: {result.get('name')}")
                    print(f"   - Type: {result.get('type_document')}")
                    print(f"   - Projet ID: {result.get('project_id')}")
                else:
                    text = await response.text()
                    print(f"❌ Erreur lors de la création: {response.status}")
                    print(f"Response: {text}")
                    
                    # Essayer de parser comme JSON pour plus de détails
                    try:
                        error_data = json.loads(text)
                        print(f"Détail de l'erreur: {error_data}")
                    except:
                        pass
            
            # Tester la récupération des documents du projet
            print("\n🔍 Test de récupération des documents du projet...")
            async with session.get(
                f"http://localhost:8000/api/v1/technical-documents?project_id=7",
                headers=headers
            ) as response:
                print(f"📊 Status: {response.status}")
                
                if response.status == 200:
                    documents = await response.json()
                    print(f"✅ {len(documents)} document(s) trouvé(s)")
                    for doc in documents:
                        print(f"   - {doc.get('name')} ({doc.get('type_document')})")
                else:
                    text = await response.text()
                    print(f"❌ Erreur lors de la récupération: {response.status}")
                    print(f"Response: {text}")
                    
        except Exception as e:
            print(f"❌ Erreur lors du test: {e}")

if __name__ == "__main__":
    asyncio.run(test_create_technical_document())
