# Rapport de Correction - Documents Techniques

## Problème Initial

L'erreur suivante se produisait lors de la création de documents techniques :

```
TypeError: 'project_id' is an invalid keyword argument for TechnicalDocument
```

**Cause racine :** Incohérence entre le modèle de données et l'API :
- Le modèle `TechnicalDocument` utilisait `lot_id` (correct selon l'architecture)
- L'API et les schémas utilisaient encore `project_id` (incorrect)
- La table de base de données avait les deux colonnes avec `project_id` NOT NULL et `lot_id` nullable

## Architecture Correcte

Selon l'architecture du système, les documents techniques doivent être rattachés aux **lots**, pas directement aux projets :

```
Project → Lot → TechnicalDocument
```

Cette approche est logique car :
- Un projet contient plusieurs lots
- Chaque lot peut avoir ses propres documents techniques (CCTP, DPGF)
- Les documents sont spécifiques à un lot particulier

## Corrections Apportées

### 1. Mise à jour des Schémas Pydantic

**Fichier :** `fastapi_template/app/schemas/technical_document.py`

**Changements :**
- `project_id` → `lot_id` dans `TechnicalDocumentBase` et `TechnicalDocumentCreate`
- Ajout du schéma `LotSimple` pour les réponses
- `ProjectSimple` → `LotSimple` dans les schémas de réponse
- Mise à jour des filtres pour supporter `lot_id` et `project_id` (via les lots)

### 2. Mise à jour de l'API

**Fichier :** `fastapi_template/app/api/api_v1/endpoints/technical_documents.py`

**Changements :**
- Import du modèle `Lot`
- Mise à jour de `check_document_access()` pour utiliser `document.lot`
- Modification de `get_technical_documents()` :
  - Join avec `Lot` au lieu de `Project`
  - Support du filtrage par `lot_id` et `project_id` (via les lots)
  - Réponses incluant les informations du lot
- Mise à jour de `create_technical_document()` :
  - Validation du `lot_id` au lieu du `project_id`
  - Vérification que le lot appartient au workspace de l'utilisateur
- Mise à jour de `get_technical_document()` pour retourner les informations du lot

### 3. Correction de la Base de Données

**Script :** `fastapi_template/fix_technical_documents_simple.py`

**Actions réalisées :**
1. Migration des données existantes : attribution d'un `lot_id` basé sur le `project_id`
2. Modification de `lot_id` : `nullable=YES` → `nullable=NO`
3. Suppression de la colonne `project_id` et de ses contraintes
4. Vérification de la contrainte de clé étrangère pour `lot_id`

**Structure finale de la table :**
```sql
technical_documents:
  - id: integer NOT NULL
  - name: varchar NOT NULL
  - type_document: enum NOT NULL
  - content: text
  - lot_id: integer NOT NULL (FK vers lots.id)
  - created_by: integer NOT NULL
  - updated_by: integer
  - is_active: boolean
  - created_at: timestamp
  - updated_at: timestamp
```

## Tests de Validation

**Script :** `test_technical_documents_fix.py`

**Tests réalisés :**
1. ✅ Création d'un document technique avec `lot_id`
2. ✅ Récupération avec relations
3. ✅ Vérification de la relation avec le lot
4. ✅ Filtrage par lot
5. ✅ Filtrage par projet via les lots
6. ✅ Compatibilité des schémas Pydantic

**Résultat :** Tous les tests passent avec succès.

## Fonctionnalités Maintenues

### API Endpoints

- `GET /technical-documents/` : Liste des documents avec filtrage par `lot_id` ou `project_id`
- `GET /technical-documents/{id}` : Détails d'un document avec informations du lot
- `POST /technical-documents/` : Création avec `lot_id`
- `PUT /technical-documents/{id}` : Mise à jour
- `DELETE /technical-documents/{id}` : Suppression (soft delete)

### Filtrage Flexible

L'API supporte maintenant :
- Filtrage direct par `lot_id`
- Filtrage par `project_id` (via les lots du projet)
- Filtrage par type de document
- Pagination

### Sécurité

- Vérification que le lot appartient au workspace de l'utilisateur
- Validation des entreprises tierces associées
- Contrôle d'accès via les relations lot → projet → workspace

## Impact sur le Frontend

✅ **Frontend mis à jour :**
1. **Types TypeScript** : Mise à jour de `orbis-frontend/src/types/technical-document.ts`
   - `project_id` → `lot_id` dans les interfaces de création
   - `ProjectSimple` → `LotSimple` dans les réponses
   - Ajout du support du filtrage par `lot_id`

2. **Page des documents techniques** : Mise à jour de `orbis-frontend/src/app/documents-techniques/page.tsx`
   - Utilisation de `lot_id` au lieu de `project_id` lors de la création
   - Affichage des informations du lot avec le projet parent
   - Support des paramètres URL avec `lot_id`

3. **Hooks** : Les hooks `useTechnicalDocument` utilisent maintenant les nouveaux types

## Conclusion

✅ **Problème résolu :** L'erreur `'project_id' is an invalid keyword argument` est corrigée

✅ **Architecture cohérente :** Les documents techniques sont maintenant correctement rattachés aux lots

✅ **Fonctionnalités préservées :** Toutes les fonctionnalités existantes sont maintenues

✅ **Compatibilité :** Support du filtrage par projet via les lots pour la transition

La correction respecte l'architecture prévue du système où les documents techniques appartiennent aux lots, qui eux-mêmes appartiennent aux projets.
