#!/usr/bin/env python3
"""
Test de l'implémentation des Stakeholders (Intervenants)
Vérifie que la migration de lot_intervenants vers stakeholders fonctionne correctement
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models import Stakeholder, Lot, TCompany, User
from app.schemas.stakeholder import StakeholderCreate
from app.schemas.tcompany import TCompanyCreate
from app.crud.stakeholder import stakeholder_crud

# Créer un engine synchrone pour les tests
sync_url = settings.ASYNC_DATABASE_URL.replace("+asyncpg", "")
engine = create_engine(sync_url, echo=False)
SessionLocal = sessionmaker(bind=engine)

def test_stakeholder_model():
    """Test du modèle Stakeholder"""
    print("🧪 Test du modèle Stakeholder...")
    
    db = SessionLocal()
    try:
        # Vérifier que la table stakeholders existe
        stakeholders = db.query(Stakeholder).all()
        print(f"✅ Table stakeholders accessible, {len(stakeholders)} enregistrements trouvés")
        
        # Vérifier les relations
        if stakeholders:
            stakeholder = stakeholders[0]
            print(f"✅ Premier stakeholder: {stakeholder}")
            
            # Tester les relations
            if stakeholder.lot:
                print(f"✅ Relation lot: {stakeholder.lot.name}")
            if stakeholder.company:
                print(f"✅ Relation company: {stakeholder.company.company_name}")
        
        return True
    except Exception as e:
        print(f"❌ Erreur test modèle: {e}")
        return False
    finally:
        db.close()

def test_stakeholder_crud():
    """Test des opérations CRUD"""
    print("\n🧪 Test des opérations CRUD...")
    
    db = SessionLocal()
    try:
        # Récupérer un lot existant
        lot = db.query(Lot).first()
        if not lot:
            print("❌ Aucun lot trouvé pour les tests")
            return False
        
        # Récupérer une entreprise existante
        company = db.query(TCompany).first()
        if not company:
            print("❌ Aucune entreprise trouvée pour les tests")
            return False
        
        # Récupérer un utilisateur
        user = db.query(User).first()
        if not user:
            print("❌ Aucun utilisateur trouvé pour les tests")
            return False
        
        print(f"✅ Données de test: Lot {lot.name}, Entreprise {company.company_name}")
        
        # Test création stakeholder
        stakeholder_data = StakeholderCreate(
            lot_id=lot.id,
            company_id=company.id,
            role="Architecte Test",
            is_active=True
        )
        
        # Vérifier si le stakeholder existe déjà
        existing = stakeholder_crud.get_stakeholder_by_lot_and_company(db, lot.id, company.id)
        if existing:
            print(f"✅ Stakeholder existant trouvé: {existing}")
            return True
        
        # Créer un nouveau stakeholder
        new_stakeholder = stakeholder_crud.create_stakeholder(
            db=db,
            stakeholder_data=stakeholder_data,
            current_user_id=user.id,
            workspace_id=1  # Valeur par défaut
        )
        
        print(f"✅ Stakeholder créé: {new_stakeholder}")
        
        # Test lecture
        retrieved = stakeholder_crud.get_stakeholder(db, new_stakeholder.id)
        print(f"✅ Stakeholder récupéré: {retrieved}")
        
        # Test mise à jour
        from app.schemas.stakeholder import StakeholderUpdate
        update_data = StakeholderUpdate(role="Bureau d'études Test")
        updated = stakeholder_crud.update_stakeholder(db, new_stakeholder.id, update_data)
        print(f"✅ Stakeholder mis à jour: {updated.role}")
        
        # Test suppression (soft delete)
        deleted = stakeholder_crud.delete_stakeholder(db, new_stakeholder.id)
        print(f"✅ Stakeholder supprimé (soft): {deleted}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test CRUD: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def test_stakeholder_with_new_company():
    """Test création stakeholder avec nouvelle entreprise"""
    print("\n🧪 Test création stakeholder avec nouvelle entreprise...")
    
    db = SessionLocal()
    try:
        # Récupérer un lot existant
        lot = db.query(Lot).first()
        if not lot:
            print("❌ Aucun lot trouvé pour les tests")
            return False
        
        # Récupérer un utilisateur
        user = db.query(User).first()
        if not user:
            print("❌ Aucun utilisateur trouvé pour les tests")
            return False
        
        # Créer un stakeholder avec une nouvelle entreprise
        company_data = TCompanyCreate(
            company_name="Test Architecture SARL",
            activity="Architecture",
            email="<EMAIL>",
            phone="***********.89"
        )
        
        stakeholder_data = StakeholderCreate(
            lot_id=lot.id,
            company_data=company_data,
            role="Architecte Principal",
            is_active=True
        )
        
        new_stakeholder = stakeholder_crud.create_stakeholder(
            db=db,
            stakeholder_data=stakeholder_data,
            current_user_id=user.id,
            workspace_id=8  # Utiliser un workspace existant
        )
        
        print(f"✅ Stakeholder créé avec nouvelle entreprise: {new_stakeholder}")
        print(f"✅ Entreprise créée: {new_stakeholder.company.company_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test nouvelle entreprise: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def test_stakeholder_stats():
    """Test des statistiques"""
    print("\n🧪 Test des statistiques...")
    
    db = SessionLocal()
    try:
        stats = stakeholder_crud.get_stakeholder_stats(db)
        print(f"✅ Statistiques globales: {stats}")
        
        # Stats pour un lot spécifique
        lot = db.query(Lot).first()
        if lot:
            lot_stats = stakeholder_crud.get_stakeholder_stats(db, lot_id=lot.id)
            print(f"✅ Statistiques lot {lot.name}: {lot_stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test statistiques: {e}")
        return False
    finally:
        db.close()

def main():
    """Fonction principale de test"""
    print("🚀 Test de l'implémentation Stakeholders (Intervenants)")
    print("=" * 60)
    
    tests = [
        test_stakeholder_model,
        test_stakeholder_crud,
        test_stakeholder_with_new_company,
        test_stakeholder_stats
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Erreur dans {test.__name__}: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 RÉSULTATS DES TESTS")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\n🎯 Score: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés ! L'implémentation Stakeholders est fonctionnelle.")
        return True
    else:
        print("⚠️  Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
